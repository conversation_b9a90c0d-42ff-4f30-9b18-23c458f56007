<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier que toutes les données ont bien été migrées vers sous_categorie_json
        $proprietes = DB::table('proprietes')
            ->whereNotNull('sous_categorie')
            ->whereNull('sous_categorie_json')
            ->get();

        if ($proprietes->count() > 0) {
            // Il reste des données à migrer
            foreach ($proprietes as $propriete) {
                if (!empty($propriete->sous_categorie)) {
                    // Convertir la chaîne en tableau
                    $sousCategorie = $propriete->sous_categorie;

                    // Supprimer les guillemets et les crochets
                    $sousCategorie = trim($sousCategorie, '[]');

                    // Diviser par virgule et nettoyer
                    $items = array_map('trim', explode(',', $sousCategorie));

                    // Supprimer les guillemets
                    $items = array_map(function($item) {
                        return trim($item, '"\' ');
                    }, $items);

                    // Filtrer les valeurs vides
                    $items = array_filter($items);

                    // Encoder en JSON
                    $jsonValue = json_encode($items);

                    // Mettre à jour la colonne sous_categorie_json
                    DB::table('proprietes')
                        ->where('id', $propriete->id)
                        ->update(['sous_categorie_json' => $jsonValue]);
                }
            }
        }

        // Supprimer la colonne sous_categorie
        Schema::table('proprietes', function (Blueprint $table) {
            $table->dropColumn('sous_categorie');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recréer la colonne sous_categorie
        Schema::table('proprietes', function (Blueprint $table) {
            $table->string('sous_categorie')->nullable()->after('sous_categorie_json');
        });

        // Restaurer les données depuis sous_categorie_json
        $proprietes = DB::table('proprietes')
            ->whereNotNull('sous_categorie_json')
            ->get();

        foreach ($proprietes as $propriete) {
            if (!empty($propriete->sous_categorie_json)) {
                // Décoder le JSON
                $decoded = json_decode($propriete->sous_categorie_json, true);

                if (is_array($decoded) && !empty($decoded)) {
                    // Convertir le tableau en chaîne
                    $sousCategorie = implode(', ', $decoded);

                    // Mettre à jour la colonne sous_categorie
                    DB::table('proprietes')
                        ->where('id', $propriete->id)
                        ->update(['sous_categorie' => $sousCategorie]);
                }
            }
        }
    }
};

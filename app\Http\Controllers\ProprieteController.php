<?php

namespace App\Http\Controllers;

use App\Models\Icon;
use App\Models\Propriete;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ProprieteController extends Controller
{
    public function index(Request $request, $type = null)
    {
        // Récupérer les prix minimum et maximum des propriétés
        $minPriceInDb = Propriete::whereIn('statut', ['Actif', 'Actif warm', 'Actif réservé'])
            ->where('prix', '>', 0)
            ->min('prix') ?? 0;
        $maxPriceInDb = Propriete::whereIn('statut', ['Actif', 'Actif warm', 'Actif réservé'])
            ->max('prix') ?? 50000000;

        $proprietes = Propriete::with('imagesProprietes')
            ->whereIn('statut', ['Actif', 'Actif warm', 'Actif réservé'])
            ->when($type, function ($query, $type) {
                return $query->where('categorie', $type);

            })
            ->when($request->input('statut'), function ($query, $statut) {
                return $query->where('transaction', $statut);
            })
            ->when($request->input('type-de-propriete'), function ($query, $typesDePropriete) {
                // Filtrer par sous_categorie_json pour type de propriété
                return $query->where(function ($q) use ($typesDePropriete) {
                    foreach ((array)$typesDePropriete as $type) {
                        $q->orWhereJsonContains('sous_categorie_json', $type);
                    }
                });
            })
            ->when($request->input('quartiers'), function ($query, $quartiers) {
                return $query->whereIn('ville', $quartiers); // Filtrer par ville pour quartiers (ajuster la colonne si nécessaire)
            })
            ->when($request->input('chambre'), function ($query, $chambres) {
                return $query->whereIn('chambres', $chambres);
            })

            ->when($request->filled('rng_min_price') || $request->filled('rng_max_price'), function ($query) use ($request) {
                // Récupérer les valeurs min et max
                $minPrice = $request->filled('rng_min_price') ? floatval($request->input('rng_min_price')) : 0;
                $maxPrice = $request->filled('rng_max_price') ? floatval($request->input('rng_max_price')) : PHP_FLOAT_MAX;

                // Journaliser les valeurs pour le débogage
                \Log::info('Filtrage par prix', ['min' => $minPrice, 'max' => $maxPrice]);

                // Ensure minPrice is not greater than maxPrice to avoid errors
                if ($minPrice <= $maxPrice) {
                    if ($request->filled('rng_min_price') && $request->filled('rng_max_price')) {
                        // Les deux valeurs sont renseignées, utiliser whereBetween
                        return $query->whereBetween('prix', [$minPrice, $maxPrice]);
                    } elseif ($request->filled('rng_min_price')) {
                        // Seulement la valeur minimale est renseignée
                        return $query->where('prix', '>=', $minPrice);
                    } else {
                        // Seulement la valeur maximale est renseignée
                        return $query->where('prix', '<=', $maxPrice);
                    }
                } else {
                    // Handle the case where minPrice is greater than maxPrice, maybe return no results or handle as needed
                    return $query->where('prix', 0); // Return no results in this case, adjust as needed
                }
            })
            ->latest()
            ->get();


        // Récupérer les types de propriétés uniques
        $proprietesPourTypes = Propriete::whereNotNull('sous_categorie_json')->get();
        $typesDePropriete = collect();

        foreach ($proprietesPourTypes as $propriete) {
            $sousCategorieJson = $propriete->sous_categorie_json;

            // Si c'est déjà un tableau (casté automatiquement), l'utiliser directement
            if (is_array($sousCategorieJson)) {
                $typesDePropriete = $typesDePropriete->concat($sousCategorieJson);
            }
            // Si c'est une chaîne JSON, la décoder
            elseif (is_string($sousCategorieJson)) {
                $decoded = json_decode($sousCategorieJson, true);
                if (is_array($decoded)) {
                    $typesDePropriete = $typesDePropriete->concat($decoded);
                }
            }
        }

        $typesDePropriete = $typesDePropriete->unique()->values();
        // Récupérer les quartiers uniques
        $quartiers = Propriete::distinct()->pluck('ville')->filter()->values();
        // Récupérer les nombres de chambres uniques
        $nombresDeChambres = Propriete::distinct()->pluck('chambres')->filter()->values();

        $icons = Icon::where('type', 'hero')->get();

        // Arrondir les prix pour une meilleure expérience utilisateur
        // Arrondir le prix minimum à la centaine de milliers inférieure
        $minPriceInDb = floor($minPriceInDb / 100000) * 100000;
        // Arrondir le prix maximum à la centaine de milliers supérieure
        $maxPriceInDb = ceil($maxPriceInDb / 100000) * 100000;

        // Ajouter une marge de 10% au prix maximum pour inclure les biens les plus prestigieux
        $maxPriceInDb = ceil($maxPriceInDb * 1.1);

        // Débogage - Afficher le nombre de propriétés après filtrage
        // Commenter cette ligne après avoir vérifié que les filtres fonctionnent
        // dd("Nombre de propriétés après filtrage: " . $proprietes->count(), $request->all());

        return view('web.acheter', compact('proprietes', 'type', 'typesDePropriete', 'quartiers', 'nombresDeChambres', 'icons', 'minPriceInDb', 'maxPriceInDb'));
    }

    /**
     * Afficher les détails d'une propriété.
     *
     * @param  Propriete  $propriete
     * @return View
     */
    public function show(Propriete $propriete): View
    {
        $propriete->load('imagesProprietes');
        $icons = Icon::where('type', 'hero')->get();
        return view('web.single-listing', [
            'propriete' => $propriete,
            'icons' => $icons,

        ]);
    }
}

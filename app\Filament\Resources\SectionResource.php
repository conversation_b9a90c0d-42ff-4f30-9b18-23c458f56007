<?php

namespace App\Filament\Resources;

use App\Filament\Forms\Components\IconSelect;
use App\Filament\Resources\SectionResource\Pages;
use App\Filament\Resources\SectionResource\RelationManagers;
use App\Filament\Resources\SectionResource\RelationManagers\ContenuSectionsRelationManager;
use App\Models\Section;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SectionResource extends Resource
{
    protected static ?string $model = Section::class;

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nom')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('type')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('ordre')
                    
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('page_id')
                    
                    ->numeric(),
                Forms\Components\TextInput::make('titre')
                    
                    ->maxLength(255),
                IconSelect::make('icon_id')
                    ->label('Icône')
                    
                    ->helperText('Sélectionnez une icône pour cette section'),
                Forms\Components\TextInput::make('sous_titre')
                    
                    ->maxLength(255),
                Forms\Components\RichEditor::make('description')
                    
                    ->columnSpanFull(),
                Forms\Components\FileUpload::make('image_fond')
                    
                    ->image(),
                Forms\Components\TextInput::make('etiquette_bouton')
                    
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('lien_bouton')
                    
                    ->maxLength(255)
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nom')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('ordre')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('page_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('titre')
                    ->searchable(),
                Tables\Columns\ViewColumn::make('icon_id')
                    ->label('Icône')
                    ->view('filament.tables.columns.icon-column'),
                Tables\Columns\TextColumn::make('sous_titre')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('image_fond'),
                Tables\Columns\TextColumn::make('etiquette_bouton')
                    ->searchable(),
                Tables\Columns\TextColumn::make('lien_bouton')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
           ContenuSectionsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSections::route('/'),
            'create' => Pages\CreateSection::route('/create'),
            'edit' => Pages\EditSection::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Filament\Resources\ProprieteResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ImagesProprietesRelationManager extends RelationManager
{
    protected static string $relationship = 'imagesProprietes';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\FileUpload::make('url_image')
                    ->image()
                    ->required()
                    ->maxSize(5120) // 5MB max
                    ->disk('public_uploads')
                    ->visibility('public'),
                Forms\Components\Toggle::make('is_main')
                    ->label('Image principale')
                    ->default(false),
                Forms\Components\Select::make('statut')
                    ->options([
                        'public' => 'Public',
                        'prive' => 'Privé',
                    ])
                    ->default('public'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('titre')
            ->columns([
                Tables\Columns\ImageColumn::make('url_image'),
                Tables\Columns\IconColumn::make('is_main')
                    ->boolean()
                    ->label('Image principale')
                    ->sortable(),
                Tables\Columns\TextColumn::make('statut')
                    ->sortable(),
                Tables\Columns\TextColumn::make('order_column')
                    ->label('Ordre')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}

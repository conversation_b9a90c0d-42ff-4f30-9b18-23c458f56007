<?php

namespace App\Policies;

use App\Models\User;
use App\Models\OffMarket;
use Illuminate\Auth\Access\HandlesAuthorization;

class OffMarketPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_off::market');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, OffMarket $offMarket): bool
    {
        return $user->can('view_off::market');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_off::market');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, OffMarket $offMarket): bool
    {
        return $user->can('update_off::market');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, OffMarket $offMarket): bool
    {
        return $user->can('delete_off::market');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_off::market');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, OffMarket $offMarket): bool
    {
        return $user->can('force_delete_off::market');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_off::market');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, OffMarket $offMarket): bool
    {
        return $user->can('restore_off::market');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_off::market');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, OffMarket $offMarket): bool
    {
        return $user->can('replicate_off::market');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_off::market');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer les tables existantes dans le bon ordre (pour éviter les problèmes de clés étrangères)
        Schema::dropIfExists('article_tag');
        Schema::dropIfExists('articles_blog');
        Schema::dropIfExists('blog_tags');
        Schema::dropIfExists('blog_categories');
        
        // Créer la table des catégories
        Schema::create('blog_categories', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Créer la table des tags
        Schema::create('blog_tags', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->string('slug')->unique();
            $table->timestamps();
        });
        
        // Créer la table des articles de blog
        Schema::create('articles_blog', function (Blueprint $table) {
            $table->id();
            $table->string('titre');
            $table->string('slug')->unique();
            $table->text('contenu');
            $table->text('extrait');
            $table->date('date_publication');
            $table->string('url_image');
            $table->unsignedBigInteger('category_id')->nullable();
            $table->enum('statut', ['brouillon', 'publie'])->default('brouillon');
            $table->timestamps();
            
            $table->foreign('category_id')->references('id')->on('blog_categories')->onDelete('set null');
        });
        
        // Créer la table pivot pour les tags
        Schema::create('article_tag', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('article_id');
            $table->unsignedBigInteger('tag_id');
            $table->timestamps();

            $table->foreign('article_id')->references('id')->on('articles_blog')->onDelete('cascade');
            $table->foreign('tag_id')->references('id')->on('blog_tags')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les tables dans le bon ordre
        Schema::dropIfExists('article_tag');
        Schema::dropIfExists('articles_blog');
        Schema::dropIfExists('blog_tags');
        Schema::dropIfExists('blog_categories');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
     public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('slug')->unique(); // e.g., 'home', 'about-us'
            $table->string('titre');          // Page title
            $table->timestamps();
        });

        // Sections (Blocs de contenu flexibles)
        Schema::create('sections', function (Blueprint $table) {
            $table->id();
            $table->string('nom')->unique(); // Identifiant unique (hero, proprietes_mises_en_avant, etc)
            $table->string('type'); // Type de section (hero, grille_proprietes, contact, etc)
            $table->integer('ordre')->default(0);
            $table->foreignId('page_id')->constrained()->onDelete('cascade'); // Foreign key to 'pages' table
            $table->timestamps();
        });


        // Propriétés
        Schema::create('proprietes', function (Blueprint $table) {
            $table->id();
            $table->string('titre');
            $table->enum('type', ['Appartements', 'Villas', 'Bureaux', 'Ateliers', 'Boxes']);
            $table->string('adresse');
            $table->string('localisation');
            $table->string('superficie');
            $table->text('description');
            $table->text('prestation');
            $table->integer('chambres');
            $table->integer('salles_de_bain');
            $table->decimal('surface', 8, 2);
            $table->decimal('prix', 12, 2);
            $table->enum('statut', ['vente', 'location']);
            $table->boolean('est_exclusif')->default(false);
            $table->foreignId('user_id')->constrained();
            $table->timestamps();
            $table->softDeletes();
        });

        // Images des Propriétés
        Schema::create('images_proprietes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('propriete_id')->constrained('proprietes')->onDelete('cascade');
            $table->string('url_image');
            $table->timestamps();
            $table->softDeletes();
        });


        // Développements
        Schema::create('developpements', function (Blueprint $table) {
            $table->id();
            $table->string('titre');
            $table->text('description');
            $table->date('date_livraison');
            $table->string('libelle_bouton');
            $table->string('lien_bouton');
            $table->timestamps();
        });

        // Images des Développements
        Schema::create('images_developpements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('developpement_id')->constrained('developpements')->onDelete('cascade');
            $table->string('url_image');
            $table->timestamps();
        });

        // Articles de Blog
        Schema::create('articles_blog', function (Blueprint $table) {
            $table->id();
            $table->string('titre');
            $table->text('contenu');
            $table->text('extrait');
            $table->date('date_publication');
            $table->string('url_image');
            $table->string('categorie');
            $table->timestamps();
        });

        // Contacts
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('adresse');
            $table->string('lien_facebook');
            $table->string('lien_linkedin');
            $table->string('lien_instagram');
            $table->string('email');
            $table->string('telephone');
            $table->string('logo_white');
            $table->string('logo_black');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('sections');
        Schema::dropIfExists('images_developpements');
        Schema::dropIfExists('developpements');
        Schema::dropIfExists('images_proprietes');
        Schema::dropIfExists('proprietes');
        Schema::dropIfExists('articles_blog');
        Schema::dropIfExists('contacts');
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Icon extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'image',
        'label',
        'description',
        'link',
        'sort_order',
        'type',
        'icon_type',
        'icon_value',
        'content_type',
        'is_active',
        'color',
        'hero_id',
    ];

    /**
     * Get the hero that owns the icon.
     */
    public function hero()
    {
        return $this->belongsTo(Hero::class);
    }

    /**
     * Render the icon HTML based on its type.
     *
     * @return string
     */
    public function renderHtml()
    {
        switch ($this->icon_type) {
            case 'fontawesome':
                return '<i class="' . $this->icon_value . '" style="color: ' . ($this->color ?: 'inherit') . ';"></i>';
            case 'svg':
                return $this->icon_value;
            case 'image':
            default:
                return '<img src="' . asset('uploads/' . $this->image) . '" alt="' . $this->label . '" class="icon-image">';
        }
    }

    /**
     * Scope a query to only include active icons.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by content type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfContentType($query, $type)
    {
        return $query->where('content_type', $type);
    }


}

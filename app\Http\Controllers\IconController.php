<?php

namespace App\Http\Controllers;

use App\Models\Icon;
use Illuminate\Http\Request;

class IconController extends Controller
{
    /**
     * Affiche la liste des icônes disponibles.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $icons = Icon::where('is_active', true)->orderBy('type')->orderBy('sort_order')->get();

        // Regrouper les icônes par type
        $groupedIcons = $icons->groupBy('type');

        return view('admin.icons.index', compact('groupedIcons'));
    }

    /**
     * Récupère les icônes par type.
     *
     * @param string $type Type d'icône à récupérer
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByType($type)
    {
        $icons = Icon::where('type', $type)
                    ->where('is_active', true)
                    ->orderBy('sort_order')
                    ->get();

        return response()->json($icons);
    }

    /**
     * Récupère les icônes par type de contenu.
     *
     * @param string $contentType Type de contenu associé aux icônes
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByContentType($contentType)
    {
        $icons = Icon::where('content_type', $contentType)
                    ->where('is_active', true)
                    ->orderBy('sort_order')
                    ->get();

        return response()->json($icons);
    }

    /**
     * Affiche la page d'exemple d'utilisation des icônes.
     *
     * @return \Illuminate\View\View
     */
    public function examples()
    {
        $fontAwesomeIcons = Icon::where('icon_type', 'fontawesome')
                              ->where('is_active', true)
                              ->take(5)
                              ->get();

        $imageIcons = Icon::where('icon_type', 'image')
                        ->where('is_active', true)
                        ->take(5)
                        ->get();

        $svgIcons = Icon::where('icon_type', 'svg')
                      ->where('is_active', true)
                      ->take(5)
                      ->get();

        return view('examples.icon-usage', compact('fontAwesomeIcons', 'imageIcons', 'svgIcons'));
    }
}

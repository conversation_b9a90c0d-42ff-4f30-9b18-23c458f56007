/**
 * Script pour afficher la carte de localisation d'une propriété
 */
document.addEventListener('DOMContentLoaded', function() {
    // Récupérer l'élément de carte
    const mapContainer = document.getElementById('property-map');
    if (!mapContainer) return;
    
    // Récupérer les coordonnées de la propriété depuis les attributs data
    const latitude = parseFloat(mapContainer.getAttribute('data-latitude') || 46.603354);
    const longitude = parseFloat(mapContainer.getAttribute('data-longitude') || 1.888334);
    
    // Vérifier si les coordonnées sont valides
    if (isNaN(latitude) || isNaN(longitude)) {
        console.error('Coordonnées invalides');
        return;
    }
    
    // Initialiser la carte
    const map = L.map(mapContainer, {
        center: [latitude, longitude],
        zoom: 15
    });
    
    // Ajouter la couche de tuiles OpenStreetMap
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Ajouter un marqueur à la position de la propriété
    L.marker([latitude, longitude]).addTo(map);
});

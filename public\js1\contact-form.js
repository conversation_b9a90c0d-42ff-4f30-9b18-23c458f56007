// Script pour le formulaire de contact
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.querySelector('.wpcf7-form');

    if (contactForm) {
        // Créer un élément de notification pour l'envoi en cours
        const loadingNotification = document.createElement('div');
        loadingNotification.className = 'alert alert-info';
        loadingNotification.id = 'sending-notification';
        loadingNotification.innerHTML = '<strong>Envoi en cours...</strong> Veuillez patienter pendant que nous envoyons votre message.';
        loadingNotification.style.display = 'none';

        // Insérer la notification avant le bouton d'envoi
        const formMessages = document.getElementById('form-messages');
        if (formMessages) {
            formMessages.appendChild(loadingNotification);
        }

        // Le gestionnaire d'événements de soumission est maintenant géré par debug-form.js
    }

    // Vérifier s'il y a des paramètres d'URL pour les messages de succès/erreur
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('success') || urlParams.has('error') || urlParams.has('validation_errors')) {
        // Faire défiler jusqu'au formulaire
        const formMessages = document.getElementById('form-messages');
        if (formMessages) {
            formMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Mettre en évidence le message avec une animation
            const message = formMessages.querySelector('.alert');
            if (message) {
                message.style.animation = 'none';
                setTimeout(function() {
                    message.style.animation = 'fadeIn 0.5s ease-in-out';
                }, 10);
            }
        }

        // Réinitialiser le formulaire en cas de succès
        if (urlParams.has('success') && contactForm) {
            contactForm.reset();
        }
    }
});

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Propriete extends Model
{
    use HasFactory;
    use SoftDeletes;


    protected $fillable = [
        'titre',
        'categorie',
        'adresse',
        'ville',
        'code_postal',
        'rue',
        'numero_rue',
        'description',
        'prestation',
        'chambres',
        'salles_de_bain',
        'surface',
        'surface_habitable',
        'surface_terrasse',
        'surface_terrain',
        'volume',
        'prix',
        'statut',
        'afficher_accueil',
        'transaction',
        'reference',
        'piece',
        'agent',
        'disponible_a_partir',
        'disponible_de_suite',
        'disponibilite_a_convenir',
        'pays',
        'rue',
        'etage',
        'terrasse',
        'place_parc_interieure',
        'place_parc_couverte',
        'place_parc_exterieure',
        'parking',
        'garages',
        'annee_construction',
        'annee_renovation',
        'type_chauffage',
        'type_structure',
        'nombre_etages_batiment',
        'surface_habitable',
        'surface_terrasse',
        'surface_terrain',
        'volume',
        'devise',
        'prix_m2',
        'prix_sur_demande',
        'prix_negociable',
        'prix_garage',
        'prix_parking',
        'afficher_localisation',
        'charges',
        'description_courte',
        'lien_video',
        'url_image',
        'lien_tour_virtuel',
        'wc',
        'sous_categorie',
        'sous_categorie_json',
        'note_prive',
        'description_promotion',
        'description_projet',
        'longitude',
        'latitude',
        'disposition',
        'est_projet',
    ];

    protected $casts = [
        // Ne pas caster sous_categorie pour éviter les conflits avec l'accesseur/mutateur
        // 'sous_categorie' => 'array',
        'sous_categorie_json' => 'json',
        'disponible_de_suite' => 'boolean',
        'disponibilite_a_convenir' => 'boolean',
        'afficher_accueil' => 'boolean',
        'prix_sur_demande' => 'boolean',
        'prix_negociable' => 'boolean',
        'afficher_localisation' => 'boolean',
        'est_projet' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];



    public function imagesProprietes()
    {
        return $this->hasMany(ImagesPropriete::class);
    }

    /**
     * Récupère l'image principale de la propriété
     *
     * @return string|null URL de l'image principale ou null si aucune image
     */
    public function getMainImage()
    {
        // Cherche d'abord une image marquée comme principale (is_main = true)
        $mainImage = $this->imagesProprietes()->where('is_main', true)->first();

        // Si aucune image principale n'est définie, prend la première image
        if (!$mainImage) {
            $mainImage = $this->imagesProprietes()->first();
        }

        return $mainImage ? $mainImage->url_image : null;
    }


    // Accesseur pour sous_categorie qui utilise sous_categorie_json
    public function getSousCategorieAttribute($value)
    {
        // Si sous_categorie_json est null, on retourne null
        if ($this->sous_categorie_json === null) {
            return null;
        }

        // Si sous_categorie_json est déjà un tableau (casté automatiquement), on le retourne
        if (is_array($this->sous_categorie_json)) {
            return $this->sous_categorie_json;
        }

        // Si c'est une chaîne JSON, on la décode
        if (is_string($this->sous_categorie_json) && $this->isJson($this->sous_categorie_json)) {
            return json_decode($this->sous_categorie_json, true);
        }

        // Sinon, on retourne la valeur originale
        return $value;
    }

    // Mutateur pour sous_categorie qui met à jour sous_categorie_json
    public function setSousCategorieAttribute($value)
    {
        // Si $value est déjà un tableau, on le stocke directement
        if (is_array($value)) {
            // S'assurer que le JSON est correctement formaté pour SQL
            $this->attributes['sous_categorie_json'] = json_encode($value);
        }
        // Si $value est une chaîne JSON valide, on la stocke directement
        elseif (is_string($value) && $this->isJson($value)) {
            // S'assurer que c'est bien une chaîne JSON valide
            $this->attributes['sous_categorie_json'] = $value;
        }
        // Si $value est une chaîne non-JSON, on essaie de la convertir
        elseif (is_string($value)) {
            // Supprimer les crochets s'ils existent
            $value = trim($value, '[]');
            // Diviser par virgule et nettoyer
            $items = array_map('trim', explode(',', $value));
            // Supprimer les guillemets
            $items = array_map(function($item) {
                return trim($item, '"\' ');
            }, $items);
            // Filtrer les valeurs vides
            $items = array_filter($items);
            // Encoder en JSON pour sous_categorie_json
            $this->attributes['sous_categorie_json'] = json_encode($items);
        }
        // Si $value est null, on stocke null
        else {
            $this->attributes['sous_categorie_json'] = null;
        }
    }

    // Helper pour vérifier si une chaîne est du JSON valide
    private function isJson($string) {
        if (!is_string($string)) return false;
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}

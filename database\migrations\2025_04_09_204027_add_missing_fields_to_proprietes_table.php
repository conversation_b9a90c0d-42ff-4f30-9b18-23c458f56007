<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('proprietes', function (Blueprint $table) {
            // Ajouter place_parc_exterieure s'il n'existe pas déjà
            if (!Schema::hasColumn('proprietes', 'place_parc_exterieure')) {
                $table->decimal('place_parc_exterieure', 8, 2)->nullable()->after('place_parc_couverte');
            }

            // Ajouter surface_terrain s'il n'existe pas déjà
            if (!Schema::hasColumn('proprietes', 'surface_terrain')) {
                $table->decimal('surface_terrain', 10, 2)->nullable()->after('surface_terrasse');
            }

            // Ajouter volume s'il n'existe pas déjà
            if (!Schema::hasColumn('proprietes', 'volume')) {
                $table->decimal('volume', 10, 2)->nullable()->after('surface_terrain');
            }

            // Modifier les champs annee_construction et annee_renovation pour qu'ils soient des chaînes de caractères
            if (Schema::hasColumn('proprietes', 'annee_construction')) {
                $table->string('annee_construction', 4)->nullable()->change();
            }

            if (Schema::hasColumn('proprietes', 'annee_renovation')) {
                $table->string('annee_renovation', 4)->nullable()->change();
            }

            // Modifier le champ type_structure pour qu'il soit une chaîne de caractères
            if (Schema::hasColumn('proprietes', 'type_structure')) {
                $table->string('type_structure')->nullable()->change();
            }

            // Supprimer le champ superficie (doublon inutile) s'il existe
            if (Schema::hasColumn('proprietes', 'superficie')) {
                $table->dropColumn('superficie');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('proprietes', function (Blueprint $table) {
            // Supprimer les colonnes si elles existent
            $columns = ['place_parc_exterieure', 'surface_terrain', 'volume'];

            foreach ($columns as $column) {
                if (Schema::hasColumn('proprietes', $column)) {
                    $table->dropColumn($column);
                }
            }

            // Restaurer le champ superficie s'il n'existe pas
            if (!Schema::hasColumn('proprietes', 'superficie')) {
                $table->string('superficie')->nullable();
            }
        });
    }
};

<?php

namespace App\Http\Controllers;

use App\Models\Icon;
use App\Models\ProjetEnCours;
use App\Models\Propriete;
use Illuminate\Http\Request;

class ProjetController extends Controller
{
    public function index()
    {

        // Récupérer la page d'accueil en utilisant son slug
        // $page = Page::where('slug', 'home')->firstOrFail();

        // Récupère la section par son nom (par exemple "proprietes_vedettes")
        $projet = ProjetEnCours::with('images')->get()->first();
        $proprietes = Propriete::with('imagesProprietes')
            ->where('est_projet', 1) // Filtre sur plusieurs statuts
            ->latest()
            ->limit(3)
            ->get();
            $icons = Icon::where('type', 'hero')->get();

        // Passage des données à la vue index.blade.php
        // return view('web.index', compact('page', 'sections', 'proprietes', 'developpements', 'articles', 'contact'));
        return view('web.projet', compact('projet', 'proprietes', 'icons'));
    }
}

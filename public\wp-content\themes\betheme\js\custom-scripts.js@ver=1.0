(function ($) {

    setTimeout(() => {
        $(".single-product .fixed-nav.fixed-nav-next").detach().appendTo(".single-product .woocommerce-breadcrumb");
        $(".single-product .fixed-nav.fixed-nav-prev").detach().appendTo(".single-product .woocommerce-breadcrumb");


        $(".prdctfltr_wc.prdctfltr_woocommerce.pf_select .prdctfltr_filter_inner").prepend(`
    <div class="achat-location">
    <div class="vente">${localizedData.venteTranslated}</div>
    <div class="location">${localizedData.locationTranslated}</div>
    </div>
    `);
    }, 400);

    $(document).ajaxComplete(function () {

        $('.achat-location').remove();
        $(".prdctfltr_wc.prdctfltr_woocommerce.pf_select .prdctfltr_filter_inner").prepend(`
    <div class="achat-location">
    <div class="vente">${localizedData.venteTranslated}</div>
    <div class="location">${localizedData.locationTranslated}</div>
    </div>
    `);
    });

    $('body').on('click', '.achat-location .achat', function () {
        console.log('achat');

        $(this).toggleClass("prdctfltr_active");
        $('.achat-location .location').removeClass("prdctfltr_active");
        $('.achat-location .vente').removeClass("prdctfltr_active");

        // $(".prdctfltr_statut .prdctfltr_ft_none").not(".pf_adoptive_hide").trigger("click");
        $(".prdctfltr_ft_achat").trigger("click");
        $(".prdctfltr_ft_Purchase").trigger("click");
        $(".prdctfltr_ft_acquista").trigger("click");

    });


    $('body').on('click', '.achat-location .vente', function () {
        console.log('achat');

        $(this).toggleClass("prdctfltr_active");
        $('.achat-location .location').removeClass("prdctfltr_active");
        $('.achat-location .achat').removeClass("prdctfltr_active");

        // $(".prdctfltr_statut .prdctfltr_ft_none").trigger("click");
        $(".prdctfltr_ft_vente").trigger("click");
        $(".prdctfltr_ft_sale").trigger("click");
        $(".prdctfltr_ft_vendita").trigger("click");

    });


    $('body').on('click', '.achat-location .location', function () {
        console.log('location');
        console.log($(".prdctfltr_ft_affitto"));

        $(this).toggleClass("prdctfltr_active");
        $('.achat-location .vente').removeClass("prdctfltr_active");
        $('.achat-location .achat').removeClass("prdctfltr_active");

        // $(".prdctfltr_statut .prdctfltr_ft_none").trigger("click");
        $(".prdctfltr_ft_location").trigger("click");
        $(".prdctfltr_ft_rental").trigger("click");
        $(".prdctfltr_ft_affitto").trigger("click");

    });




    $(document).on('click', '.pf_select .prdctfltr_filter .prdctfltr_regular_title, .prdctfltr_terms_customized_select.prdctfltr_filter .prdctfltr_regular_title', function () {


        var curr = $(this).closest('.prdctfltr_filter').find('.prdctfltr_add_scroll');

        if (!curr.hasClass('prdctfltr_down')) {
            var prdctfltr_filter = $('.prdctfltr_filter').find('.prdctfltr_down')

            prdctfltr_filter.slideUp(100, function () {
                pf_select_opened = false;
            });

            prdctfltr_filter.removeClass('prdctfltr_down');
        }

    });



})(jQuery);


document.addEventListener('wpcf7mailsent', function(event) {
    console.log('wpcf7mailsent event triggered');
    console.log('Event details:', event);

    if (typeof cf7_output !== 'undefined') {
        console.log('cf7_output is defined:', cf7_output);
        alert('Your custom message here: ' + cf7_output.values.message);
    } else {
        console.log('cf7_output is not defined');
    }
}, false);


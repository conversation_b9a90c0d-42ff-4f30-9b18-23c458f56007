<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\ContactFormMail;

class ContactFormController extends Controller
{
    public function submit(Request $request)
    {
        // Débogage - Journaliser la réception de la requête
        Log::info('Requête reçue dans ContactFormController@submit', [
            'method' => $request->method(),
            'url' => $request->url(),
            'all' => $request->all(),
        ]);

        // Validation des données du formulaire
        $validated = $request->validate([
            'entity' => 'nullable|string|max:255',
            'lastname' => 'required|string|max:255',
            'firstname' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'message' => 'required|string',
        ]);

        try {
            // Débogage des données validées
            Log::info('Données du formulaire de contact', $validated);

            // Envoi de l'email avec Laravel Mail
            Mail::to('<EMAIL>')
                ->send(new ContactFormMail($validated));

            // Journalisation de la réussite
            Log::info('Email de contact envoyé avec succès', ['email' => $validated['email']]);

            // Vérifier si c'est une requête AJAX
            if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                // Renvoyer une réponse JSON pour les requêtes AJAX
                return response()->json([
                    'success' => true,
                    'message' => 'Votre message a été envoyé avec succès. Nous vous contacterons bientôt.'
                ]);
            }

            // Redirection avec message de succès pour les requêtes normales
            return redirect()->back()->with('success', 'Votre message a été envoyé avec succès. Nous vous contacterons bientôt.');
        } catch (\Exception $e) {
            // Journalisation de l'erreur détaillée
            Log::error('Erreur lors de l\'envoi de l\'email de contact', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $validated['email']
            ]);

            // Vérifier si c'est une requête AJAX
            if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                // Renvoyer une réponse JSON pour les requêtes AJAX
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors de l\'envoi de votre message: ' . $e->getMessage()
                ], 500);
            }

            // Redirection avec message d'erreur pour les requêtes normales
            return redirect()->back()->with('error', 'Une erreur est survenue lors de l\'envoi de votre message: ' . $e->getMessage())->withInput();
        }
    }
}

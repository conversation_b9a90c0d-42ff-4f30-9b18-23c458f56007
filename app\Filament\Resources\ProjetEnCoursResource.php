<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProjetEnCoursResource\Pages;
use App\Filament\Resources\ProjetEnCoursResource\RelationManagers;
use App\Models\ProjetEnCours;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProjetEnCoursResource extends Resource
{
    protected static ?string $model = ProjetEnCours::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Projets en cours';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('titre_principale')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('sous_titre_principale')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\FileUpload::make('img_principale')
                    ->label('Image principale')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\TextInput::make('titre_info_projet')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\FileUpload::make('img_info')
                    ->label('Image info')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\FileUpload::make('img_info_1')
                    ->label('Image info 1')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\FileUpload::make('img_info_2')
                    ->label('Image info 2')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\TextInput::make('texte_info_1')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('texte_info_2')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\RichEditor::make('description_information')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('titre_mot_architecte')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\RichEditor::make('texte_mot_architecte')
                    ->columnSpanFull(),
                Forms\Components\FileUpload::make('img_architecte')
                    ->label('Image architecte')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),

                Forms\Components\TextInput::make('mot_promoteur')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\RichEditor::make('description_promoteur')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('titre_prestation')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('sous_titre_prestation')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\RichEditor::make('description_prestation')
                    ->columnSpanFull(),
                Forms\Components\FileUpload::make('image_fond_prestation')
                    ->label('Image fond prestation')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\TextInput::make('titre_projet_img')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('sous_titre_projet_img')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('titre_localisation')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('sous_titre_localisation')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\FileUpload::make('img_localisation')
                    ->label('Image localisation')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\TextInput::make('texte_plaquette')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('etiquette_bouton_plaquette')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('titre_bien_projet')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('sous_titre_bien_projet')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('titre_banniere')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\FileUpload::make('img_banniere')
                    ->label('Image bannière')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\TextInput::make('etiquette_bouton_banniere')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('lien_bouton_banniere')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('lien_bouton_plaquette')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\TextInput::make('titre_contactez_nous')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\RichEditor::make('description_contactez_nous')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('lien_bouton_contactez_nous')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\FileUpload::make('image_fond_contact')
                    ->label('Image fond contact')
                    ->image()
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->default(null),
                Forms\Components\TextInput::make('etiquette_bouton_contactez_nous')
                    ->maxLength(255)
                    ->default(null),

                    Forms\Components\Repeater::make('images')
                        ->relationship()
                        ->schema([
                            Forms\Components\FileUpload::make('url_image')
                                        ->label("Image")
                                        ->image()
                                        ->disk('public_uploads')
                                        ->visibility('public'),
                                    Forms\Components\Select::make('statut')
                                        ->options([
                                            'public' => 'Public',
                                            'prive' => 'Privé',
                                        ])
                                        ->default('public'),
                                        Forms\Components\Select::make('type')
                                        ->options([
                                            'prestation' => 'Prestation',
                                            'standard' => 'Standard',
                                        ])
                                        ->default('public'),
                        ])
                        ->defaultItems(1)
                        ->columns(2)
                        ->columnSpan('full'),

            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('titre_principale')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sous_titre_principale')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('img_principale')
                    ->label('Image principale')
                    ->disk('public_uploads'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProjetEnCours::route('/'),
            'create' => Pages\CreateProjetEnCours::route('/create'),
            'edit' => Pages\EditProjetEnCours::route('/{record}/edit'),
        ];
    }
}

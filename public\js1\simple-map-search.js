/**
 * Script simplifié pour la recherche d'adresse et la mise à jour de la carte
 */

// Fonction pour rechercher une adresse
function searchMapAddress() {
    console.log('Fonction searchMapAddress appelée');

    // Trouver le champ d'adresse par son ID
    let addressInput = document.getElementById('search_address_input');

    // Si le champ n'est pas trouvé, essayer de le trouver par son nom
    if (!addressInput) {
        console.log('Champ d\'adresse non trouvé par ID, recherche par nom');
        const addressInputs = document.querySelectorAll('input[name="search_address"]');
        if (addressInputs.length > 0) {
            addressInput = addressInputs[0];
        } else {
            // Essayer de trouver n'importe quel champ de texte visible
            const allInputs = document.querySelectorAll('input[type="text"]');
            for (const input of allInputs) {
                if (input.offsetParent !== null) { // Vérifier si l'élément est visible
                    console.log('Utilisation du premier champ de texte visible:', input);
                    addressInput = input;
                    break;
                }
            }
        }
    }

    // Si le champ n'est toujours pas trouvé, demander à l'utilisateur de saisir une adresse
    if (!addressInput) {
        console.log('Aucun champ d\'adresse trouvé, demande à l\'utilisateur');
        const address = prompt('Veuillez entrer une adresse à rechercher:');
        if (!address || !address.trim()) {
            alert('Aucune adresse saisie');
            return;
        }
        processAddress(address.trim());
        return;
    }

    // Vérifier si le champ a été trouvé
    if (!addressInput) {
        console.error('Impossible de trouver le champ d\'adresse');
        alert('Erreur: Impossible de trouver le champ d\'adresse');
        return;
    }

    // Récupérer l'adresse
    const address = addressInput.value ? addressInput.value.trim() : '';
    if (!address) {
        const manualAddress = prompt('Veuillez entrer une adresse à rechercher:');
        if (!manualAddress || !manualAddress.trim()) {
            alert('Aucune adresse saisie');
            return;
        }
        processAddress(manualAddress.trim());
        return;
    }

    // Traiter l'adresse trouvée
    processAddress(address);

}

// Fonction pour traiter une adresse
function processAddress(address) {
    console.log('Traitement de l\'adresse:', address);

    // Trouver le bouton de recherche
    const searchButton = document.getElementById('simple_search_button');

    // Afficher un indicateur de chargement
    if (searchButton) {
        searchButton.innerHTML = '<span>Recherche en cours...</span>';
        searchButton.disabled = true;
    }

    // Construire l'URL de l'API Nominatim
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`;

    // Effectuer la requête
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);

                console.log('Coordonnées trouvées:', lat, lng);

                // Mettre à jour les champs de latitude et longitude
                updateCoordinates(lat, lng);

                // Mettre à jour la carte
                updateMap(lat, lng);
            } else {
                console.warn('Aucun résultat trouvé');
                alert('Aucun résultat trouvé pour cette adresse. Veuillez essayer avec une adresse plus précise.');
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche d\'adresse:', error);
            alert('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
        })
        .finally(() => {
            // Restaurer le bouton
            if (searchButton) {
                searchButton.innerHTML = '<span>Rechercher l\'adresse</span>';
                searchButton.disabled = false;
            }
        });
}

// Fonction pour mettre à jour les champs de latitude et longitude
function updateCoordinates(lat, lng) {
    console.log('Mise à jour des coordonnées:', lat, lng);

    // Trouver les champs de latitude et longitude
    const latitudeInput = document.querySelector('input[name="latitude"]');
    const longitudeInput = document.querySelector('input[name="longitude"]');

    // Mettre à jour les valeurs
    if (latitudeInput) {
        latitudeInput.value = lat;
        console.log('Champ latitude mis à jour');
        latitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
    }

    if (longitudeInput) {
        longitudeInput.value = lng;
        console.log('Champ longitude mis à jour');
        longitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
    }

    // Mettre à jour le champ location pour la carte
    const locationInputs = [
        document.querySelector('input[name="data.location"]'),
        document.querySelector('input[name="location"]')
    ].filter(Boolean);

    if (locationInputs.length > 0) {
        const locationState = { lat, lng };

        locationInputs.forEach(input => {
            input.value = JSON.stringify(locationState);
            console.log('Champ location mis à jour');

            // Déclencher plusieurs événements
            ['input', 'change'].forEach(eventType => {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
        });
    }
}

// Fonction pour mettre à jour la carte
function updateMap(lat, lng) {
    console.log('Tentative de mise à jour de la carte avec les coordonnées:', lat, lng);

    // Essayer de déclencher un événement Livewire pour rafraîchir la carte
    if (window.Livewire) {
        console.log('Livewire trouvé, déclenchement de l\'événement refreshMap');
        window.Livewire.dispatch('refreshMap');
    }

    // Essayer de mettre à jour directement la carte Leaflet
    setTimeout(() => {
        try {
            // Trouver tous les conteneurs de carte Leaflet
            const mapContainers = document.querySelectorAll('.leaflet-container');
            console.log('Conteneurs de carte Leaflet trouvés:', mapContainers.length);

            if (mapContainers.length > 0) {
                // Pour chaque conteneur de carte
                mapContainers.forEach((container, index) => {
                    console.log(`Traitement du conteneur de carte #${index}`);

                    // Essayer de trouver l'instance de carte Leaflet
                    let map = null;

                    // Méthode 1: via l'objet global L
                    if (window.L) {
                        // Parcourir toutes les propriétés de L pour trouver la carte
                        for (const key in window.L) {
                            if (window.L[key] && typeof window.L[key] === 'object' && window.L[key]._container === container) {
                                map = window.L[key];
                                console.log('Carte trouvée via L[key]._container');
                                break;
                            }
                        }
                    }

                    // Si la carte n'a pas été trouvée, essayer d'autres méthodes
                    if (!map && container._leaflet) {
                        map = container._leaflet;
                        console.log('Carte trouvée via container._leaflet');
                    }

                    if (map) {
                        console.log('Instance de carte Leaflet trouvée, mise à jour de la vue');

                        // Centrer la carte sur les nouvelles coordonnées
                        map.setView([lat, lng], 13);

                        // Mettre à jour le marqueur s'il existe
                        let markerFound = false;

                        if (map._layers) {
                            for (const layerId in map._layers) {
                                const layer = map._layers[layerId];
                                if (layer instanceof L.Marker) {
                                    console.log('Marqueur trouvé, mise à jour de sa position');
                                    layer.setLatLng([lat, lng]);
                                    markerFound = true;
                                    break;
                                }
                            }
                        }

                        // Si aucun marqueur n'a été trouvé, en créer un nouveau
                        if (!markerFound && window.L) {
                            console.log('Aucun marqueur trouvé, création d\'un nouveau');
                            window.L.marker([lat, lng]).addTo(map);
                        }

                        // Forcer un rafraîchissement de la carte
                        if (map.invalidateSize) {
                            console.log('Invalidation de la taille de la carte pour forcer un rafraîchissement');
                            map.invalidateSize(true);
                        }
                    } else {
                        console.warn('Instance de carte Leaflet non trouvée');
                    }
                });
            } else {
                console.warn('Aucun conteneur de carte Leaflet trouvé, rafraîchissement de la page');
                // Si aucune carte n'est trouvée, forcer un rafraîchissement de la page
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour directe de la carte:', error);
            // En cas d'erreur, forcer un rafraîchissement de la page
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }
    }, 200);
}

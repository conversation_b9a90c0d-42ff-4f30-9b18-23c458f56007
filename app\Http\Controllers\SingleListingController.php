<?php

namespace App\Http\Controllers;

use App\Models\Icon;
use App\Models\Propriete;
use Illuminate\Http\Request;

class SingleListingController extends Controller
{
    public function show($id)
    {
        // Retrieve the property along with its related images. 
        // If the property doesn't exist, a 404 error is automatically thrown.
        $propriete = Propriete::with('imagesProprietes')->findOrFail($id);
        $icons = Icon::where('type', 'hero')->get();

        // Pass the property data to the 'single-listing' view.
        return view('web.single-listing', compact('propriete', 'icons'));
    }
}

<?php

namespace App\Console\Commands;

use App\Models\Propriete;
use Illuminate\Console\Command;

class TestSousCategorie extends Command
{
    protected $signature = 'test:sous-categorie';
    protected $description = 'Test la récupération des sous-catégories';

    public function handle()
    {
        $this->info('Récupération des types de propriétés uniques...');

        // Récupérer toutes les propriétés avec sous_categorie_json non null
        $proprietes = Propriete::whereNotNull('sous_categorie_json')->get();

        $this->info('Propriétés trouvées : ' . $proprietes->count());

        // Afficher le type de sous_categorie_json pour chaque propriété
        foreach ($proprietes as $index => $propriete) {
            $this->info("Propriété #{$index} - Type de sous_categorie_json : " . gettype($propriete->sous_categorie_json));
            $this->info("Valeur : " . (is_array($propriete->sous_categorie_json) ? json_encode($propriete->sous_categorie_json) : $propriete->sous_categorie_json));
        }

        // Collecter les types uniques
        $typesDePropriete = collect();

        foreach ($proprietes as $propriete) {
            $sousCategorieJson = $propriete->sous_categorie_json;

            // Si c'est déjà un tableau (casté automatiquement), l'utiliser directement
            if (is_array($sousCategorieJson)) {
                $typesDePropriete = $typesDePropriete->concat($sousCategorieJson);
            }
            // Si c'est une chaîne JSON, la décoder
            elseif (is_string($sousCategorieJson)) {
                $decoded = json_decode($sousCategorieJson, true);
                if (is_array($decoded)) {
                    $typesDePropriete = $typesDePropriete->concat($decoded);
                }
            }
        }

        $typesDePropriete = $typesDePropriete->unique()->values();

        $this->info('Types de propriétés trouvés : ' . $typesDePropriete->count());
        $this->table(['Type de propriété'], $typesDePropriete->map(function ($type) {
            return [$type];
        })->toArray());

        return Command::SUCCESS;
    }
}

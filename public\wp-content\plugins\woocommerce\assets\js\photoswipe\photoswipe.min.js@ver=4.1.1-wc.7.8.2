/*! PhotoSwipe - v4.1.3 - 2019-01-08
* http://photoswipe.com
* Copyright (c) 2019 <PERSON>; */
!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";return function(m,z,t,e){var _,N,U,p,H,Y,W,B,i,f,G,X,V,K,q,r,$,j,J,Q,ee,te,ne,o,ie,oe,ae,re,le,se,l,ce,ue,de,me,pe,fe,he,s,ye,xe,ge,ve,we,c,u,be,d,Ie,h,Ce,De,Te,Me,Se,Ae,y={features:null,bind:function(e,t,n,i){var o=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[o](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){t=document.createElement(t||"div");return e&&(t.className=e),t},getScrollY:function(){var e=window.pageYOffset;return e!==undefined?e:document.documentElement.scrollTop},unbind:function(e,t,n){y.bind(e,t,n,!0)},removeClass:function(e,t){t=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(t," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){y.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(y.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)!t.hasOwnProperty(i)||n&&e.hasOwnProperty(i)||(e[i]=t[i])},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(y.features)return y.features;for(var e,t,n,i,o,a=y.createEl().style,r="",l={},s=(l.oldIE=document.all&&!document.addEventListener,l.touch="ontouchstart"in window,window.requestAnimationFrame&&(l.raf=window.requestAnimationFrame,l.caf=window.cancelAnimationFrame),l.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,l.pointerEvent||(e=navigator.userAgent,/iP(hone|od)/.test(navigator.platform)&&(t=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/))&&0<t.length&&1<=(t=parseInt(t[1],10))&&t<8&&(l.isOldIOSPhone=!0),t=(t=e.match(/Android\s([0-9\.]*)/))?t[1]:0,1<=(t=parseFloat(t))&&(t<4.4&&(l.isOldAndroid=!0),l.androidVersion=t),l.isMobileOpera=/opera mini|opera mobi/i.test(e)),["transform","perspective","animationName"]),c=["","webkit","Moz","ms","O"],u=0;u<4;u++){for(var r=c[u],d=0;d<3;d++)n=s[d],i=r+(r?n.charAt(0).toUpperCase()+n.slice(1):n),!l[n]&&i in a&&(l[n]=i);r&&!l.raf&&(r=r.toLowerCase(),l.raf=window[r+"RequestAnimationFrame"],l.raf&&(l.caf=window[r+"CancelAnimationFrame"]||window[r+"CancelRequestAnimationFrame"]))}return l.raf||(o=0,l.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-o)),i=window.setTimeout(function(){e(t+n)},n);return o=t+n,i},l.caf=function(e){clearTimeout(e)}),l.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,y.features=l}},x=(y.detectFeatures(),y.features.oldIE&&(y.bind=function(e,t,n,i){t=t.split(" ");for(var o,a=(i?"detach":"attach")+"Event",r=function(){n.handleEvent.call(n)},l=0;l<t.length;l++)if(o=t[l])if("object"==typeof n&&n.handleEvent){if(i){if(!n["oldIE"+o])return!1}else n["oldIE"+o]=r;e[a]("on"+o,n["oldIE"+o])}else e[a]("on"+o,n)}),this),Ee=25,g={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e||t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"},e=(y.extend(g,e),function(){return{x:0,y:0}}),Oe=e(),ke=e(),v=e(),w={},Re=0,Pe={},b=e(),I=0,Ze=!0,Fe=[],Le={},ze=!1,_e=function(e,t){y.extend(x,t.publicMethods),Fe.push(e)},Ne=function(e){var t=P();return t-1<e?e-t:e<0?t+e:e},Ue={},a=function(e,t){return Ue[e]||(Ue[e]=[]),Ue[e].push(t)},C=function(e){var t=Ue[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(x,n)}},D=function(){return(new Date).getTime()},T=function(e){Me=e,x.bg.style.opacity=e*g.bgOpacity},He=function(e,t,n,i,o){(!ze||o&&o!==x.currItem)&&(i/=(o||x.currItem).fitRatio),e[te]=X+t+"px, "+n+"px"+V+" scale("+i+")"},M=function(e){Ie&&(e&&(f>x.currItem.fitRatio?ze||(sn(x.currItem,!1,!0),ze=!0):ze&&(sn(x.currItem),ze=!1)),He(Ie,v.x,v.y,f))},Ye=function(e){e.container&&He(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},We=function(e,t){t[te]=X+e+"px, 0px"+V},Be=function(e,t){var n;!g.loop&&t&&(t=p+(b.x*Re-e)/b.x,n=Math.round(e-R.x),(t<0&&0<n||t>=P()-1&&n<0)&&(e=R.x+n*g.mainScrollEndFriction)),R.x=e,We(e,H)},Ge=function(e,t){var n=vt[e]-Pe[e];return ke[e]+Oe[e]+n-t/G*n},S=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Xe=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},Ve=null,Ke=function(){Ve&&(y.unbind(document,"mousemove",Ke),y.addClass(m,"pswp--has_mouse"),g.mouseUsed=!0,C("mouseUsed")),Ve=setTimeout(function(){Ve=null},100)},qe=function(e,t){e=on(x.currItem,w,e);return t&&(d=e),e},$e=function(e){return(e=e||x.currItem).initialZoomLevel},je=function(e){return 0<(e=e||x.currItem).w?g.maxSpreadZoom:1},Je=function(e,t,n,i){return i===x.currItem.initialZoomLevel?(n[e]=x.currItem.initialPosition[e],!0):(n[e]=Ge(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]&&(n[e]=t.max[e],!0))},Qe=function(e){var t="";g.escKey&&27===e.keyCode?t="close":g.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),!t||e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,x[t]())},et=function(e){e&&(ge||xe||h||fe)&&(e.preventDefault(),e.stopPropagation())},tt=function(){x.setScrollOffset(0,y.getScrollY())},A={},nt=0,it=function(e){A[e]&&(A[e].raf&&oe(A[e].raf),nt--,delete A[e])},ot=function(e){A[e]&&it(e),A[e]||(nt++,A[e]={})},at=function(){for(var e in A)A.hasOwnProperty(e)&&it(e)},rt=function(e,t,n,i,o,a,r){var l,s=D(),c=(ot(e),function(){A[e]&&(l=D()-s,i<=l?(it(e),a(n),r&&r()):(a((n-t)*o(l/i)+t),A[e].raf=ie(c)))});c()},lt={shout:C,listen:a,viewportSize:w,options:g,isMainScrollAnimating:function(){return h},getZoomLevel:function(){return f},getCurrentIndex:function(){return p},isDragging:function(){return s},isZooming:function(){return u},setScrollOffset:function(e,t){Pe.x=e,se=Pe.y=t,C("updateScrollOffset",Pe)},applyZoomPan:function(e,t,n,i){v.x=t,v.y=n,f=e,M(i)},init:function(){if(!_&&!N){x.framework=y,x.template=m,x.bg=y.getChildByClass(m,"pswp__bg"),ae=m.className,_=!0,l=y.detectFeatures(),ie=l.raf,oe=l.caf,te=l.transform,le=l.oldIE,x.scrollWrap=y.getChildByClass(m,"pswp__scroll-wrap"),x.container=y.getChildByClass(x.scrollWrap,"pswp__container"),H=x.container.style,x.itemHolders=r=[{el:x.container.children[0],wrap:0,index:-1},{el:x.container.children[1],wrap:0,index:-1},{el:x.container.children[2],wrap:0,index:-1}],r[0].el.style.display=r[2].el.style.display="none",te?(t=l.perspective&&!o,X="translate"+(t?"3d(":"("),V=l.perspective?", 0px)":")"):(te="left",y.addClass(m,"pswp--ie"),We=function(e,t){t.left=e+"px"},Ye=function(e){var t=1<e.fitRatio?1:e.fitRatio,n=e.container.style,i=t*e.w,t=t*e.h;n.width=i+"px",n.height=t+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},M=function(){var e,t,n,i;Ie&&(e=Ie,n=(i=1<(t=x.currItem).fitRatio?1:t.fitRatio)*t.w,i=i*t.h,e.width=n+"px",e.height=i+"px",e.left=v.x+"px",e.top=v.y+"px")}),i={resize:x.updateSize,orientationchange:function(){clearTimeout(ce),ce=setTimeout(function(){w.x!==x.scrollWrap.clientWidth&&x.updateSize()},500)},scroll:tt,keydown:Qe,click:et};var e,t=l.isOldIOSPhone||l.isOldAndroid||l.isMobileOpera;for(l.animationName&&l.transform&&!t||(g.showAnimationDuration=g.hideAnimationDuration=0),e=0;e<Fe.length;e++)x["init"+Fe[e]]();z&&(x.ui=new z(x,y)).init(),C("firstUpdate"),p=p||g.index||0,(isNaN(p)||p<0||p>=P())&&(p=0),x.currItem=jt(p),(l.isOldIOSPhone||l.isOldAndroid)&&(Ze=!1),m.setAttribute("aria-hidden","false"),g.modal&&(Ze?m.style.position="fixed":(m.style.position="absolute",m.style.top=y.getScrollY()+"px")),se===undefined&&(C("initialLayout"),se=re=y.getScrollY());var n="pswp--open ";for(g.mainClass&&(n+=g.mainClass+" "),g.showHideOpacity&&(n+="pswp--animate_opacity "),n=(n=(n+=o?"pswp--touch":"pswp--notouch")+(l.animationName?" pswp--css_animation":""))+(l.svg?" pswp--svg":""),y.addClass(m,n),x.updateSize(),Y=-1,I=null,e=0;e<3;e++)We((e+Y)*b.x,r[e].el.style);le||y.bind(x.scrollWrap,B,x),a("initialZoomInEnd",function(){x.setContent(r[0],p-1),x.setContent(r[2],p+1),r[0].el.style.display=r[2].el.style.display="block",g.focus&&m.focus(),y.bind(document,"keydown",x),l.transform&&y.bind(x.scrollWrap,"click",x),g.mouseUsed||y.bind(document,"mousemove",Ke),y.bind(window,"resize scroll orientationchange",x),C("bindEvents")}),x.setContent(r[1],p),x.updateCurrItem(),C("afterInit"),Ze||(K=setInterval(function(){nt||s||u||f!==x.currItem.initialZoomLevel||x.updateSize()},1e3)),y.addClass(m,"pswp--visible")}},close:function(){_&&(N=!(_=!1),C("close"),y.unbind(window,"resize scroll orientationchange",x),y.unbind(window,"scroll",i.scroll),y.unbind(document,"keydown",x),y.unbind(document,"mousemove",Ke),l.transform&&y.unbind(x.scrollWrap,"click",x),s&&y.unbind(window,W,x),clearTimeout(ce),C("unbindEvents"),Jt(x.currItem,null,!0,x.destroy))},destroy:function(){C("destroy"),Vt&&clearTimeout(Vt),m.setAttribute("aria-hidden","true"),m.className=ae,K&&clearInterval(K),y.unbind(x.scrollWrap,B,x),y.unbind(window,"scroll",x),Ct(),at(),Ue=null},panTo:function(e,t,n){n||(e>d.min.x?e=d.min.x:e<d.max.x&&(e=d.max.x),t>d.min.y?t=d.min.y:t<d.max.y&&(t=d.max.y)),v.x=e,v.y=t,M()},handleEvent:function(e){e=e||window.event,i[e.type]&&i[e.type](e)},goTo:function(e){var t=(e=Ne(e))-p;I=t,p=e,x.currItem=jt(p),Re-=t,Be(b.x*Re),at(),h=!1,x.updateCurrItem()},next:function(){x.goTo(p+1)},prev:function(){x.goTo(p-1)},updateCurrZoomItem:function(e){var t;e&&C("beforeChange",0),Ie=r[1].el.children.length&&(t=r[1].el.children[0],y.hasClass(t,"pswp__zoom-wrap"))?t.style:null,d=x.currItem.bounds,G=f=x.currItem.initialZoomLevel,v.x=d.center.x,v.y=d.center.y,e&&C("afterChange")},invalidateCurrItems:function(){q=!0;for(var e=0;e<3;e++)r[e].item&&(r[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==I){var t,n=Math.abs(I);if(!(e&&n<2)){x.currItem=jt(p),ze=!1,C("beforeChange",I),3<=n&&(Y+=I+(0<I?-3:3),n=3);for(var i=0;i<n;i++)0<I?(t=r.shift(),r[2]=t,We((++Y+2)*b.x,t.el.style),x.setContent(t,p-n+i+1+1)):(t=r.pop(),r.unshift(t),We(--Y*b.x,t.el.style),x.setContent(t,p+n-i-1-1));!Ie||1!==Math.abs(I)||(e=jt($)).initialZoomLevel!==f&&(on(e,w),sn(e),Ye(e)),I=0,x.updateCurrZoomItem(),$=p,C("afterChange")}}},updateSize:function(e){if(!Ze&&g.modal){var t=y.getScrollY();if(se!==t&&(m.style.top=t+"px",se=t),!e&&Le.x===window.innerWidth&&Le.y===window.innerHeight)return;Le.x=window.innerWidth,Le.y=window.innerHeight,m.style.height=Le.y+"px"}if(w.x=x.scrollWrap.clientWidth,w.y=x.scrollWrap.clientHeight,tt(),b.x=w.x+Math.round(w.x*g.spacing),b.y=w.y,Be(b.x*Re),C("beforeResize"),Y!==undefined){for(var n,i,o,a=0;a<3;a++)n=r[a],We((a+Y)*b.x,n.el.style),o=p+a-1,g.loop&&2<P()&&(o=Ne(o)),(i=jt(o))&&(q||i.needsUpdate||!i.bounds)?(x.cleanSlide(i),x.setContent(n,o),1===a&&(x.currItem=i,x.updateCurrZoomItem(!0)),i.needsUpdate=!1):-1===n.index&&0<=o&&x.setContent(n,o),i&&i.container&&(on(i,w),sn(i),Ye(i));q=!1}G=f=x.currItem.initialZoomLevel,(d=x.currItem.bounds)&&(v.x=d.center.x,v.y=d.center.y,M(!0)),C("resize")},zoomTo:function(t,e,n,i,o){e&&(G=f,vt.x=Math.abs(e.x)-v.x,vt.y=Math.abs(e.y)-v.y,S(ke,v));var e=qe(t,!1),a={},r=(Je("x",e,a,t),Je("y",e,a,t),f),l={x:v.x,y:v.y},e=(Xe(a),function(e){1===e?(f=t,v.x=a.x,v.y=a.y):(f=(t-r)*e+r,v.x=(a.x-l.x)*e+l.x,v.y=(a.y-l.y)*e+l.y),o&&o(e),M(1===e)});n?rt("customZoomTo",0,1,n,i||y.easing.sine.inOut,e):e(1)}},st=30,ct=10,E={},ut={},O={},k={},dt={},mt=[],pt={},ft=[],ht={},yt=0,xt=e(),gt=0,R=e(),vt=e(),wt=e(),bt=function(e,t){return e.x===t.x&&e.y===t.y},It=function(e,t){return ht.x=Math.abs(e.x-t.x),ht.y=Math.abs(e.y-t.y),Math.sqrt(ht.x*ht.x+ht.y*ht.y)},Ct=function(){ve&&(oe(ve),ve=null)},Dt=function(){s&&(ve=ie(Dt),Ut())},Tt=function(){return!("fit"===g.scaleMode&&f===x.currItem.initialZoomLevel)},Mt=function(e,t){return!(!e||e===document)&&(!(e.getAttribute("class")&&-1<e.getAttribute("class").indexOf("pswp__scroll-wrap"))&&(t(e)?e:Mt(e.parentNode,t)))},St={},At=function(e,t){return St.prevent=!Mt(e.target,g.isClickableElement),C("preventDragEvent",e,t,St),St.prevent},Et=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},Ot=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},kt=function(e,t,n){var i;50<e-de&&((i=2<ft.length?ft.shift():{}).x=t,i.y=n,ft.push(i),de=e)},Rt=function(){var e=v.y-x.currItem.initialPosition.y;return 1-Math.abs(e/(w.y/2))},Pt={},Zt={},Ft=[],Lt=function(e){for(;0<Ft.length;)Ft.pop();return ne?(Ae=0,mt.forEach(function(e){0===Ae?Ft[0]=e:1===Ae&&(Ft[1]=e),Ae++})):-1<e.type.indexOf("touch")?e.touches&&0<e.touches.length&&(Ft[0]=Et(e.touches[0],Pt),1<e.touches.length&&(Ft[1]=Et(e.touches[1],Zt))):(Pt.x=e.pageX,Pt.y=e.pageY,Pt.id="",Ft[0]=Pt),Ft},zt=function(e,t){var n,i,o,a=v[e]+t[e],r=0<t[e],l=R.x+t.x,s=R.x-pt.x,c=a>d.min[e]||a<d.max[e]?g.panEndFriction:1,a=v[e]+t[e]*c;if((g.allowPanToNext||f===x.currItem.initialZoomLevel)&&(Ie?"h"!==Ce||"x"!==e||xe||(r?(a>d.min[e]&&(c=g.panEndFriction,d.min[e],n=d.min[e]-ke[e]),(n<=0||s<0)&&1<P()?(o=l,s<0&&l>pt.x&&(o=pt.x)):d.min.x!==d.max.x&&(i=a)):(a<d.max[e]&&(c=g.panEndFriction,d.max[e],n=ke[e]-d.max[e]),(n<=0||0<s)&&1<P()?(o=l,0<s&&l<pt.x&&(o=pt.x)):d.min.x!==d.max.x&&(i=a))):o=l,"x"===e))return o!==undefined&&(Be(o,!0),we=o!==pt.x),d.min.x!==d.max.x&&(i!==undefined?v.x=i:we||(v.x+=t.x*c)),o!==undefined;h||we||f>x.currItem.fitRatio&&(v[e]+=t[e]*c)},_t=function(e){var t;"mousedown"===e.type&&0<e.button||($t?e.preventDefault():he&&"mousedown"===e.type||(At(e,!0)&&e.preventDefault(),C("pointerDown"),ne&&((t=y.arraySearch(mt,e.pointerId,"id"))<0&&(t=mt.length),mt[t]={x:e.pageX,y:e.pageY,id:e.pointerId}),e=(t=Lt(e)).length,c=null,at(),s&&1!==e||(s=De=!0,y.bind(window,W,x),pe=Se=Te=fe=we=ge=ye=xe=!1,Ce=null,C("firstTouchStart",t),S(ke,v),Oe.x=Oe.y=0,S(k,t[0]),S(dt,k),pt.x=b.x*Re,ft=[{x:k.x,y:k.y}],de=ue=D(),qe(f,!0),Ct(),Dt()),!u&&1<e&&!h&&!we&&(G=f,u=ye=!(xe=!1),Oe.y=Oe.x=0,S(ke,v),S(E,t[0]),S(ut,t[1]),Ot(E,ut,wt),vt.x=Math.abs(wt.x)-v.x,vt.y=Math.abs(wt.y)-v.y,be=It(E,ut))))},Nt=function(e){var t;e.preventDefault(),ne&&-1<(t=y.arraySearch(mt,e.pointerId,"id"))&&((t=mt[t]).x=e.pageX,t.y=e.pageY),s&&(t=Lt(e),Ce||ge||u?c=t:R.x!==b.x*Re?Ce="h":(e=Math.abs(t[0].x-k.x)-Math.abs(t[0].y-k.y),Math.abs(e)>=ct&&(Ce=0<e?"h":"v",c=t)))},Ut=function(){if(c){var e,t,n,i,o,a=c.length;if(0!==a)if(S(E,c[0]),O.x=E.x-k.x,O.y=E.y-k.y,u&&1<a)k.x=E.x,k.y=E.y,!O.x&&!O.y&&bt(c[1],ut)||(S(ut,c[1]),xe||(xe=!0,C("zoomGestureStarted")),a=It(E,ut),(e=Gt(a))>x.currItem.initialZoomLevel+x.currItem.initialZoomLevel/15&&(Se=!0),t=1,n=$e(),i=je(),e<n?g.pinchToClose&&!Se&&G<=x.currItem.initialZoomLevel?(T(o=1-(n-e)/(n/1.2)),C("onPinchClose",o),Te=!0):e=n-(t=1<(t=(n-e)/n)?1:t)*(n/3):i<e&&(e=i+(t=1<(t=(e-i)/(6*n))?1:t)*n),t<0&&(t=0),Ot(E,ut,xt),Oe.x+=xt.x-wt.x,Oe.y+=xt.y-wt.y,S(wt,xt),v.x=Ge("x",e),v.y=Ge("y",e),pe=f<e,f=e,M());else if(Ce&&(De&&(De=!1,Math.abs(O.x)>=ct&&(O.x-=c[0].x-dt.x),Math.abs(O.y)>=ct&&(O.y-=c[0].y-dt.y)),k.x=E.x,k.y=E.y,0!==O.x||0!==O.y)){if("v"===Ce&&g.closeOnVerticalDrag)if(!Tt())return Oe.y+=O.y,v.y+=O.y,o=Rt(),fe=!0,C("onVerticalDrag",o),T(o),void M();kt(D(),E.x,E.y),ge=!0,d=x.currItem.bounds,zt("x",O)||(zt("y",O),Xe(v),M())}}},Ht=function(e){if(l.isOldAndroid){if(he&&"mouseup"===e.type)return;-1<e.type.indexOf("touch")&&(clearTimeout(he),he=setTimeout(function(){he=0},600))}C("pointerUp"),At(e,!1)&&e.preventDefault(),ne&&-1<(n=y.arraySearch(mt,e.pointerId,"id"))&&(t=mt.splice(n,1)[0],navigator.msPointerEnabled&&(t.type={4:"mouse",2:"touch",3:"pen"}[e.pointerType],t.type)||(t.type=e.pointerType||"mouse"));var t,n=Lt(e),i=n.length;if(2===(i="mouseup"===e.type?0:i))return!(c=null);1===i&&S(dt,n[0]),0!==i||Ce||h||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),C("touchRelease",e,t));var o,a,n=-1;if(0===i&&(s=!1,y.unbind(window,W,x),Ct(),u?n=0:-1!==gt&&(n=D()-gt)),gt=1===i?D():-1,e=-1!==n&&n<150?"zoom":"swipe",u&&i<2&&(u=!1,1===i&&(e="zoomPointerUp"),C("zoomGestureEnded")),c=null,ge||xe||h||fe)if(at(),(me=me||Yt()).calculateSwipeSpeed("x"),fe)Rt()<g.verticalDragRange?x.close():(o=v.y,a=Me,rt("verticalDrag",0,1,300,y.easing.cubic.out,function(e){v.y=(x.currItem.initialPosition.y-o)*e+o,T((1-a)*e+a),M()}),C("onVerticalDrag",1));else{if((we||h)&&0===i){if(Bt(e,me))return;e="zoomPointerUp"}h||("swipe"!==e?Xt():!we&&f>x.currItem.fitRatio&&Wt(me))}},Yt=function(){var t,n,i={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(e){n=(1<ft.length?(t=D()-de+50,ft[ft.length-2]):(t=D()-ue,dt))[e],i.lastFlickOffset[e]=k[e]-n,i.lastFlickDist[e]=Math.abs(i.lastFlickOffset[e]),20<i.lastFlickDist[e]?i.lastFlickSpeed[e]=i.lastFlickOffset[e]/t:i.lastFlickSpeed[e]=0,Math.abs(i.lastFlickSpeed[e])<.1&&(i.lastFlickSpeed[e]=0),i.slowDownRatio[e]=.95,i.slowDownRatioReverse[e]=1-i.slowDownRatio[e],i.speedDecelerationRatio[e]=1},calculateOverBoundsAnimOffset:function(t,e){i.backAnimStarted[t]||(v[t]>d.min[t]?i.backAnimDestination[t]=d.min[t]:v[t]<d.max[t]&&(i.backAnimDestination[t]=d.max[t]),i.backAnimDestination[t]!==undefined&&(i.slowDownRatio[t]=.7,i.slowDownRatioReverse[t]=1-i.slowDownRatio[t],i.speedDecelerationRatioAbs[t]<.05&&(i.lastFlickSpeed[t]=0,i.backAnimStarted[t]=!0,rt("bounceZoomPan"+t,v[t],i.backAnimDestination[t],e||300,y.easing.sine.out,function(e){v[t]=e,M()}))))},calculateAnimOffset:function(e){i.backAnimStarted[e]||(i.speedDecelerationRatio[e]=i.speedDecelerationRatio[e]*(i.slowDownRatio[e]+i.slowDownRatioReverse[e]-i.slowDownRatioReverse[e]*i.timeDiff/10),i.speedDecelerationRatioAbs[e]=Math.abs(i.lastFlickSpeed[e]*i.speedDecelerationRatio[e]),i.distanceOffset[e]=i.lastFlickSpeed[e]*i.speedDecelerationRatio[e]*i.timeDiff,v[e]+=i.distanceOffset[e])},panAnimLoop:function(){A.zoomPan&&(A.zoomPan.raf=ie(i.panAnimLoop),i.now=D(),i.timeDiff=i.now-i.lastNow,i.lastNow=i.now,i.calculateAnimOffset("x"),i.calculateAnimOffset("y"),M(),i.calculateOverBoundsAnimOffset("x"),i.calculateOverBoundsAnimOffset("y"),i.speedDecelerationRatioAbs.x<.05&&i.speedDecelerationRatioAbs.y<.05&&(v.x=Math.round(v.x),v.y=Math.round(v.y),M(),it("zoomPan")))}};return i},Wt=function(e){if(e.calculateSwipeSpeed("y"),d=x.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05)return e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0;ot("zoomPan"),e.lastNow=D(),e.panAnimLoop()},Bt=function(e,t){h||(yt=p),"swipe"===e&&(e=k.x-dt.x,a=t.lastFlickDist.x<10,st<e&&(a||20<t.lastFlickOffset.x)?i=-1:e<-st&&(a||t.lastFlickOffset.x<-20)&&(i=1)),i&&((p+=i)<0?(p=g.loop?P()-1:0,o=!0):p>=P()&&(p=g.loop?0:P()-1,o=!0),o&&!g.loop||(I+=i,Re-=i,n=!0));var n,i,o,e=b.x*Re,a=Math.abs(e-R.x),r=n||e>R.x==0<t.lastFlickSpeed.x?(r=0<Math.abs(t.lastFlickSpeed.x)?a/Math.abs(t.lastFlickSpeed.x):333,r=Math.min(r,400),Math.max(r,250)):333;return yt===p&&(n=!1),h=!0,C("mainScrollAnimStart"),rt("mainScroll",R.x,e,r,y.easing.cubic.out,Be,function(){at(),h=!1,yt=-1,!n&&yt===p||x.updateCurrItem(),C("mainScrollAnimComplete")}),n&&x.updateCurrItem(!0),n},Gt=function(e){return 1/be*e*G},Xt=function(){var e,t=f,n=$e(),i=je(),o=(f<n?t=n:i<f&&(t=i),Me);return Te&&!pe&&!Se&&f<n?x.close():(Te&&(e=function(e){T((1-o)*e+o)}),x.zoomTo(t,0,200,y.easing.cubic.out,e)),!0};_e("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,i,o){j=e+t,J=e+n,Q=e+i,ee=o?e+o:""};(ne=l.pointerEvent)&&l.touch&&(l.touch=!1),ne?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):l.touch?(e("touch","start","move","end","cancel"),o=!0):e("mouse","down","move","up"),W=J+" "+Q+" "+ee,B=j,ne&&!o&&(o=1<navigator.maxTouchPoints||1<navigator.msMaxTouchPoints),x.likelyTouchDevice=o,i[j]=_t,i[J]=Nt,i[Q]=Ht,ee&&(i[ee]=i[Q]),l.touch&&(B+=" mousedown",W+=" mousemove mouseup",i.mousedown=i[j],i.mousemove=i[J],i.mouseup=i[Q]),o||(g.allowPanToNext=!1)}}});var Vt,Kt,qt,$t,jt,P,Jt=function(a,e,r,t){Vt&&clearTimeout(Vt),qt=$t=!0,a.initialLayout?(l=a.initialLayout,a.initialLayout=null):l=g.getThumbBoundsFn&&g.getThumbBoundsFn(p);var l,s,c,u=r?g.hideAnimationDuration:g.showAnimationDuration,d=function(){it("initialZoom"),r?(x.template.removeAttribute("style"),x.bg.removeAttribute("style")):(T(1),e&&(e.style.display="block"),y.addClass(m,"pswp--animated-in"),C("initialZoom"+(r?"OutEnd":"InEnd"))),t&&t(),$t=!1};u&&l&&l.x!==undefined?(s=U,c=!x.currItem.src||x.currItem.loadError||g.showHideOpacity,a.miniImg&&(a.miniImg.style.webkitBackfaceVisibility="hidden"),r||(f=l.w/a.w,v.x=l.x,v.y=l.y-re,x[c?"template":"bg"].style.opacity=.001,M()),ot("initialZoom"),r&&!s&&y.removeClass(m,"pswp--animated-in"),c&&(r?y[(s?"remove":"add")+"Class"](m,"pswp--animate_opacity"):setTimeout(function(){y.addClass(m,"pswp--animate_opacity")},30)),Vt=setTimeout(function(){var t,n,i,o,e;C("initialZoom"+(r?"Out":"In")),r?(t=l.w/a.w,n={x:v.x,y:v.y},i=f,o=Me,e=function(e){1===e?(f=t,v.x=l.x,v.y=l.y-se):(f=(t-i)*e+i,v.x=(l.x-n.x)*e+n.x,v.y=(l.y-se-n.y)*e+n.y),M(),c?m.style.opacity=1-e:T(o-e*o)},s?rt("initialZoom",0,1,u,y.easing.cubic.out,e,d):(e(1),Vt=setTimeout(d,u+20))):(f=a.initialZoomLevel,S(v,a.initialPosition),M(),T(1),c?m.style.opacity=1:T(1),Vt=setTimeout(d,u+20))},r?25:90)):(C("initialZoom"+(r?"Out":"In")),f=a.initialZoomLevel,S(v,a.initialPosition),M(),m.style.opacity=r?0:1,T(1),u?setTimeout(function(){d()},u):d())},Z={},Qt=[],en={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return Kt.length}},tn=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},nn=function(e,t,n){var i=e.bounds;i.center.x=Math.round((Z.x-t)/2),i.center.y=Math.round((Z.y-n)/2)+e.vGap.top,i.max.x=t>Z.x?Math.round(Z.x-t):i.center.x,i.max.y=n>Z.y?Math.round(Z.y-n)+e.vGap.top:i.center.y,i.min.x=t>Z.x?0:i.center.x,i.min.y=n>Z.y?e.vGap.top:i.center.y},on=function(e,t,n){var i,o;return!e.src||e.loadError?(e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=tn(),e.initialPosition=e.bounds.center,e.bounds):((i=!n)&&(e.vGap||(e.vGap={top:0,bottom:0}),C("parseVerticalMargin",e)),Z.x=t.x,Z.y=t.y-e.vGap.top-e.vGap.bottom,i&&(t=Z.x/e.w,o=Z.y/e.h,e.fitRatio=t<o?t:o,"orig"===(t=g.scaleMode)?n=1:"fit"===t&&(n=e.fitRatio),e.initialZoomLevel=n=1<n?1:n,e.bounds||(e.bounds=tn())),n?(nn(e,e.w*n,e.h*n),i&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds):void 0)},an=function(e,t,n,i,o,a){t.loadError||i&&(t.imageAppended=!0,sn(t,i,t===x.currItem&&ze),n.appendChild(i),a&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},rn=function(e){e.loading=!0,e.loaded=!1;var t=e.img=y.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t.alt=e.alt||"",t},ln=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=g.errorMsg.replace("%url%",e.src),!0},sn=function(e,t,n){var i;e.src&&(t=t||e.container.lastChild,i=n?e.w:Math.round(e.w*e.fitRatio),n=n?e.h:Math.round(e.h*e.fitRatio),e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=n+"px"),t.style.width=i+"px",t.style.height=n+"px")},cn=function(){if(Qt.length){for(var e,t=0;t<Qt.length;t++)(e=Qt[t]).holder.index===e.index&&an(e.index,e.item,e.baseDiv,e.img,!1,e.clearPlaceholder);Qt=[]}};_e("Controller",{publicMethods:{lazyLoadItem:function(e){e=Ne(e);var t=jt(e);t&&(!t.loaded&&!t.loading||q)&&(C("gettingData",e,t),t.src&&rn(t))},initController:function(){y.extend(g,en,!0),x.items=Kt=t,jt=x.getItemAt,P=g.getNumItemsFn,g.loop,P()<3&&(g.loop=!1),a("beforeChange",function(e){for(var t=g.preload,n=null===e||0<=e,i=Math.min(t[0],P()),o=Math.min(t[1],P()),a=1;a<=(n?o:i);a++)x.lazyLoadItem(p+a);for(a=1;a<=(n?i:o);a++)x.lazyLoadItem(p-a)}),a("initialLayout",function(){x.currItem.initialLayout=g.getThumbBoundsFn&&g.getThumbBoundsFn(p)}),a("mainScrollAnimComplete",cn),a("initialZoomInEnd",cn),a("destroy",function(){for(var e,t=0;t<Kt.length;t++)(e=Kt[t]).container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);Qt=null})},getItemAt:function(e){return 0<=e&&(Kt[e]!==undefined&&Kt[e])},allowProgressiveImg:function(){return g.forceProgressiveLoading||!o||g.mouseUsed||1200<screen.width},setContent:function(t,n){g.loop&&(n=Ne(n));var e,i,o,a=x.getItemAt(t.index),a=(a&&(a.container=null),x.getItemAt(n));a?(C("gettingData",n,a),t.index=n,i=(t.item=a).container=y.createEl("pswp__zoom-wrap"),!a.src&&a.html&&(a.html.tagName?i.appendChild(a.html):i.innerHTML=a.html),ln(a),on(a,w),!a.src||a.loadError||a.loaded?a.src&&!a.loadError&&((e=y.createEl("pswp__img","img")).style.opacity=1,e.src=a.src,sn(a,e),an(n,a,i,e,!0)):(a.loadComplete=function(e){if(_){if(t&&t.index===n){if(ln(e,!0))return e.loadComplete=e.img=null,on(e,w),Ye(e),void(t.index===p&&x.updateCurrZoomItem());e.imageAppended?!$t&&e.placeholder&&(e.placeholder.style.display="none",e.placeholder=null):l.transform&&(h||$t)?Qt.push({item:e,baseDiv:i,img:e.img,index:n,holder:t,clearPlaceholder:!0}):an(n,e,i,e.img,h||$t,!0)}e.loadComplete=null,e.img=null,C("imageLoadComplete",n,e)}},y.features.transform&&(o="pswp__img pswp__img--placeholder",o+=a.msrc?"":" pswp__img--placeholder--blank",o=y.createEl(o,a.msrc?"img":""),a.msrc&&(o.src=a.msrc),sn(a,o),i.appendChild(o),a.placeholder=o),a.loading||rn(a),x.allowProgressiveImg()&&(!qt&&l.transform?Qt.push({item:a,baseDiv:i,img:a.img,index:n,holder:t}):an(n,a,i,a.img,!0,!0))),qt||n!==p?Ye(a):(Ie=i.style,Jt(a,e||a.img)),t.el.innerHTML="",t.el.appendChild(i)):t.el.innerHTML=""},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var un,F,dn,mn,pn,fn,hn,yn,n,xn,gn,vn,L,wn,bn={},In=function(e,t,n){var i=document.createEvent("CustomEvent"),t={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,t),e.target.dispatchEvent(i)},Cn=(_e("Tap",{publicMethods:{initTap:function(){a("firstTouchStart",x.onTapStart),a("touchRelease",x.onTapRelease),a("destroy",function(){bn={},un=null})},onTapStart:function(e){1<e.length&&(clearTimeout(un),un=null)},onTapRelease:function(e,t){var n,i,o;!t||ge||ye||nt||!x.container.contains(e.target)||(n=t,un&&(clearTimeout(un),un=null,i=n,o=bn,Math.abs(i.x-o.x)<Ee&&Math.abs(i.y-o.y)<Ee)?C("doubleTap",n):"mouse"===t.type?In(e,t,"mouse"):"BUTTON"===e.target.tagName.toUpperCase()||y.hasClass(e.target,"pswp__single-tap")?In(e,t):(S(bn,n),un=setTimeout(function(){In(e,t),un=null},300)))}}}),_e("DesktopZoom",{publicMethods:{initDesktopZoom:function(){le||(o?a("mouseUsed",function(){x.setupDesktopZoom()}):x.setupDesktopZoom(!0))},setupDesktopZoom:function(e){F={};var t="wheel mousewheel DOMMouseScroll";a("bindEvents",function(){y.bind(m,t,x.handleMouseWheel)}),a("unbindEvents",function(){F&&y.unbind(m,t,x.handleMouseWheel)}),x.mouseZoomedIn=!1;var n,i=function(){x.mouseZoomedIn&&(y.removeClass(m,"pswp--zoomed-in"),x.mouseZoomedIn=!1),f<1?y.addClass(m,"pswp--zoom-allowed"):y.removeClass(m,"pswp--zoom-allowed"),o()},o=function(){n&&(y.removeClass(m,"pswp--dragging"),n=!1)};a("resize",i),a("afterChange",i),a("pointerDown",function(){x.mouseZoomedIn&&(n=!0,y.addClass(m,"pswp--dragging"))}),a("pointerUp",o),e||i()},handleMouseWheel:function(e){if(f<=x.currItem.fitRatio)return g.modal&&(!g.closeOnScroll||nt||s?e.preventDefault():te&&2<Math.abs(e.deltaY)&&(U=!0,x.close())),!0;if(e.stopPropagation(),F.x=0,"deltaX"in e)1===e.deltaMode?(F.x=18*e.deltaX,F.y=18*e.deltaY):(F.x=e.deltaX,F.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(F.x=-.16*e.wheelDeltaX),e.wheelDeltaY?F.y=-.16*e.wheelDeltaY:F.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;F.y=e.detail}qe(f,!0);var t=v.x-F.x,n=v.y-F.y;(g.modal||t<=d.min.x&&t>=d.max.x&&n<=d.min.y&&n>=d.max.y)&&e.preventDefault(),x.panTo(t,n)},toggleDesktopZoom:function(e){e=e||{x:w.x/2+Pe.x,y:w.y/2+Pe.y};var t=g.getDoubleTapZoom(!0,x.currItem),n=f===t;x.mouseZoomedIn=!n,x.zoomTo(n?x.currItem.initialZoomLevel:t,e,333),y[(n?"remove":"add")+"Class"](m,"pswp--zoomed-in")}}}),{history:!0,galleryUID:1}),Dn=function(){return L.hash.substring(1)},Tn=function(){dn&&clearTimeout(dn),pn&&clearTimeout(pn)},Mn=function(){var e=Dn(),t={};if(!(e.length<5)){var n,i=e.split("&");for(a=0;a<i.length;a++)i[a]&&((n=i[a].split("=")).length<2||(t[n[0]]=n[1]));if(g.galleryPIDs){for(var o=t.pid,a=t.pid=0;a<Kt.length;a++)if(Kt[a].pid===o){t.pid=a;break}}else t.pid=parseInt(t.pid,10)-1;t.pid<0&&(t.pid=0)}return t},Sn=function(){var e,t;pn&&clearTimeout(pn),nt||s?pn=setTimeout(Sn,500):(fn?clearTimeout(mn):fn=!0,t=p+1,(e=jt(p)).hasOwnProperty("pid")&&(t=e.pid),e=n+"&gid="+g.galleryUID+"&pid="+t,xn||-1===L.hash.indexOf(e)&&(vn=!0),t=L.href.split("#")[0]+"#"+e,wn?"#"+e!==window.location.hash&&history[xn?"replaceState":"pushState"]("",document.title,t):xn?L.replace(t):L.hash=e,xn=!0,mn=setTimeout(function(){fn=!1},60))};_e("History",{publicMethods:{initHistory:function(){var e,t;y.extend(g,Cn,!0),g.history&&(L=window.location,xn=gn=vn=!1,n=Dn(),wn="pushState"in history,-1<n.indexOf("gid=")&&(n=(n=n.split("&gid=")[0]).split("?gid=")[0]),a("afterChange",x.updateURL),a("unbindEvents",function(){y.unbind(window,"hashchange",x.onHashChange)}),e=function(){yn=!0,gn||(vn?history.back():n?L.hash=n:wn?history.pushState("",document.title,L.pathname+L.search):L.hash=""),Tn()},a("unbindEvents",function(){U&&e()}),a("destroy",function(){yn||e()}),a("firstUpdate",function(){p=Mn().pid}),-1<(t=n.indexOf("pid="))&&"&"===(n=n.substring(0,t)).slice(-1)&&(n=n.slice(0,-1)),setTimeout(function(){_&&y.bind(window,"hashchange",x.onHashChange)},40))},onHashChange:function(){Dn()===n?(gn=!0,x.close()):fn||(hn=!0,x.goTo(Mn().pid),hn=!1)},updateURL:function(){Tn(),hn||(xn?dn=setTimeout(Sn,800):Sn())}}}),y.extend(x,lt)}});
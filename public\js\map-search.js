/**
 * Script pour la recherche d'adresse et la mise à jour de la carte
 */

// Fonction pour rechercher une adresse
function searchMapAddress() {
    const addressInput = document.getElementById('search_address_input');
    const searchButton = document.getElementById('search_address_button');
    
    if (!addressInput || !addressInput.value.trim()) {
        alert('Veuillez entrer une adresse à rechercher');
        return;
    }
    
    const address = addressInput.value.trim();
    console.log('Recherche d\'adresse:', address);
    
    // Afficher un indicateur de chargement
    if (searchButton) {
        searchButton.textContent = 'Recherche en cours...';
        searchButton.disabled = true;
    }
    
    // Construire l'URL de l'API Nominatim
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`;
    
    // Effectuer la requête
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                
                console.log('Coordonnées trouvées:', lat, lng);
                
                // Mettre à jour les champs de formulaire
                const latitudeInput = document.querySelector('input[name="latitude"]');
                const longitudeInput = document.querySelector('input[name="longitude"]');
                
                if (latitudeInput) {
                    latitudeInput.value = lat;
                    latitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('Latitude mise à jour:', lat);
                }
                
                if (longitudeInput) {
                    longitudeInput.value = lng;
                    longitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('Longitude mise à jour:', lng);
                }
                
                // Mettre à jour l'état du composant Map
                updateMapLocation(lat, lng);
            } else {
                console.warn('Aucun résultat trouvé');
                alert('Aucun résultat trouvé pour cette adresse. Veuillez essayer avec une adresse plus précise.');
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche d\'adresse:', error);
            alert('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
        })
        .finally(() => {
            // Restaurer le bouton
            if (searchButton) {
                searchButton.textContent = 'Rechercher';
                searchButton.disabled = false;
            }
        });
}

// Fonction pour mettre à jour la carte
function updateMapLocation(lat, lng) {
    console.log('Mise à jour de la carte avec les coordonnées:', lat, lng);
    
    // Mettre à jour l'état du composant Map
    const locationInputs = [
        document.querySelector('input[name="data.location"]'),
        document.querySelector('input[name="location"]'),
        document.querySelector('[wire\\:model="data.location"]'),
        document.querySelector('[wire\\:model\\.defer="data.location"]')
    ].filter(Boolean);
    
    if (locationInputs.length > 0) {
        const locationState = { lat, lng };
        
        locationInputs.forEach(input => {
            input.value = JSON.stringify(locationState);
            console.log('Champ location mis à jour avec:', JSON.stringify(locationState));
            
            // Déclencher plusieurs événements
            ['input', 'change', 'blur'].forEach(eventType => {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
        });
    }
    
    // Essayer de mettre à jour directement la carte Leaflet
    setTimeout(() => {
        try {
            // Trouver tous les conteneurs de carte
            const mapContainers = document.querySelectorAll('.leaflet-container');
            console.log('Conteneurs de carte trouvés:', mapContainers.length);
            
            mapContainers.forEach((container, index) => {
                // Essayer d'accéder à l'instance de carte de différentes façons
                let map = null;
                
                // Méthode 1: via _leaflet
                if (container._leaflet) {
                    map = container._leaflet;
                }
                
                // Méthode 2: via les données
                if (!map && container.dataset && container.dataset.map) {
                    map = JSON.parse(container.dataset.map);
                }
                
                // Méthode 3: via l'objet global L
                if (!map && window.L) {
                    // Parcourir toutes les propriétés de L pour trouver la carte
                    for (const key in window.L) {
                        if (window.L[key] && typeof window.L[key] === 'object' && window.L[key]._container === container) {
                            map = window.L[key];
                            break;
                        }
                    }
                }
                
                if (map) {
                    console.log(`Carte #${index} trouvée, mise à jour de la vue`);
                    map.setView([lat, lng], 13);
                    
                    // Mettre à jour le marqueur
                    if (map._layers) {
                        let markerFound = false;
                        
                        for (const layerId in map._layers) {
                            const layer = map._layers[layerId];
                            if (layer instanceof L.Marker) {
                                console.log('Marqueur trouvé, mise à jour de sa position');
                                layer.setLatLng([lat, lng]);
                                markerFound = true;
                                break;
                            }
                        }
                        
                        if (!markerFound) {
                            console.log('Aucun marqueur trouvé, création d\'un nouveau');
                            L.marker([lat, lng]).addTo(map);
                        }
                    }
                }
            });
            
            // Si Livewire est disponible, déclencher un événement
            if (window.Livewire) {
                console.log('Livewire trouvé, déclenchement de l\'événement refreshMap');
                window.Livewire.dispatch('refreshMap');
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour directe de la carte:', error);
        }
    }, 100);
}

// Configurer le bouton de recherche lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    function setupButton() {
        const searchButton = document.getElementById('search_address_button');
        
        if (searchButton) {
            console.log('Bouton de recherche trouvé, ajout de l\'écouteur d\'événements');
            
            searchButton.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log('Bouton de recherche cliqué');
                searchMapAddress();
                return false;
            });
        } else {
            console.log('Bouton de recherche non trouvé, nouvelle tentative dans 500ms');
            setTimeout(setupButton, 500);
        }
    }
    
    setupButton();
});

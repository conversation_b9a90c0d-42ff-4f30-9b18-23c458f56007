<?php

namespace App\Filament\Resources;

use Closure;
use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use App\Models\Propriete;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Dotswan\MapPicker\Fields\Map;
use Filament\Support\Enums\ActionSize;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Actions\Action;
use App\Filament\Resources\ProprieteResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ProprieteResource\RelationManagers;
use App\Filament\Resources\ProprieteResource\RelationManagers\ImagesProprietesRelationManager;

class ProprieteResource extends Resource
{
    protected static ?string $model = Propriete::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Propriétés';

    /**
     * Prépare les données avant de remplir le formulaire pour l'édition.
     * Initialise le champ 'location' (pour la carte) à partir des colonnes latitude/longitude.
     */
    public static function mutateFormDataBeforeFill(array $data): array
    {
        if (!empty($data['latitude']) && !empty($data['longitude'])) {
            $data['location'] = [
                'lat' => (float) $data['latitude'],
                'lng' => (float) $data['longitude'],
            ];
        }
        return $data;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Wizard::make([
                    Forms\Components\Wizard\Step::make('Général')
                        ->schema([
                            Forms\Components\TextInput::make('reference')
                                ->label("Réf interne")
                                ->maxLength(255),
                            Forms\Components\Select::make('categorie')
                            ->label("Catégorie")
                                ->options([
                                    'Appartements' => 'Appartements',
                                    'Villas' => 'Villas',
                                    'Bureaux' => 'Bureaux',
                                    'Ateliers' => 'Ateliers',
                                    'Boxes' => 'Boxes',
                                ]),
                            Forms\Components\Select::make('transaction')
                                ->options([
                                    'Vente' => 'Vente',
                                    'Location' => 'Location',
                                ]),
                            Forms\Components\TextInput::make('agent')
                                ->label('Agent/Propriétaire')
                                ->maxLength(255),
                            Forms\Components\Select::make('statut')
                                ->options([
                                    'En attente' => 'En attente',
                                    'Actif' => 'Actif',
                                    'Actif warm' => 'Actif warm',
                                    'Actif réservé' => 'Actif réservé',
                                    'Vendu' => 'Vendu',
                                    'Retiré' => 'Retiré',
                                    'Archivé' => 'Archivé',
                                    'Non-actif' => 'Non-actif',
                                    'Incomplet' => 'Incomplet',
                                ])
                                ->helperText('Non-actif: Propriété en projet, non affichée dans la liste. Incomplet: Propriété manquant d\'informations essentielles.'),
                            Forms\Components\Select::make('disposition')
                                ->options([
                                    'exclusivité' => 'Exclusivité',
                                    'réservé' => 'Réservé',
                                    'vendu' => 'Vendu',
                                    'loué' => 'Loué',
                                ]),
                            Forms\Components\DatePicker::make('disponible_a_partir')
                                ->label('Disponible à partir du'),
                            Forms\Components\Toggle::make('disponible_de_suite')
                                ->label('Disponible de suite')
                                ->default(false),
                            Forms\Components\Toggle::make('disponibilite_a_convenir')
                                ->label('Disponibilité à convenir')
                                ->default(false),
                            Forms\Components\Toggle::make('afficher_accueil')
                                ->label('Afficher sur la page accueil')
                                ->default(false),
                            Forms\Components\Toggle::make('est_projet')
                                ->label('Projet en cours')
                                ->default(false),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Localisation')
                        ->schema([
                            Forms\Components\TextInput::make('adresse')
                                ->label('Adresse complète')
                                ->placeholder('Saisissez l\'adresse affichée sur la fiche de la propriété')
                                ->helperText('Cette adresse sera affichée sur le site.'),
                            Forms\Components\TextInput::make('code_postal')
                                ->label('Code Postal (CP)')
                                ->placeholder('Ex: 75001')
                                ->maxLength(10),
                            Forms\Components\TextInput::make('ville')
                                ->label('Localité')
                                ->placeholder('Ex: Paris')
                                ->maxLength(100),
                            Forms\Components\TextInput::make('rue')
                                ->label('Rue')
                                ->placeholder('Ex: Rue de la Paix')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('numero_rue')
                                ->label('Numéro de rue')
                                ->placeholder('Ex: 1')
                                ->maxLength(10),
                            Forms\Components\TextInput::make('latitude')
                                ->label('Latitude')
                                ->numeric(),
                            Forms\Components\TextInput::make('longitude')
                                ->label('Longitude')
                                ->numeric(),
                            Forms\Components\Toggle::make('afficher_localisation')
                                ->label('Afficher la localisation sur le site public')
                                ->default(true),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Caractéristiques')
                        ->schema([
                            Forms\Components\TextInput::make('piece')
                                ->label('Pièce(s)')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('chambres')
                                ->label('Chambre(s)')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('wc')
                                ->label('WC\'s')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('salles_de_bain')
                                ->label('Salle(s) de bain')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('etage')
                                ->label('Étage')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('nombre_etages_batiment')
                                ->label('Nombre étage(s) bâtiment')
                                ->numeric()
                                ->integer(),
                            Forms\Components\TextInput::make('terrasse')
                                ->label('Terrasse(s)')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('parking')
                                ->label('Parking(s)')
                                ->numeric()
                                ->integer(),
                            Forms\Components\TextInput::make('garages')
                                ->label('Garage(s)')
                                ->numeric()
                                ->integer(),
                            Forms\Components\TextInput::make('place_parc_interieure')
                                ->label('Place(s) de parc intérieure(s)')
                                ->numeric()
                                ->integer(),
                            Forms\Components\TextInput::make('place_parc_couverte')
                                ->label('Place(s) couverte(s)')
                                ->numeric()
                                ->integer(),
                            Forms\Components\TextInput::make('place_parc_exterieure')
                                ->label('Place(s) de parc extérieure(s)')
                                ->numeric()
                                ->integer(),
                            Forms\Components\TextInput::make('type_structure')
                                ->label('Type de structure')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('annee_construction')
                                ->label('Année de construction')
                                ->maxLength(4),
                            Forms\Components\TextInput::make('annee_renovation')
                                ->label('Année de rénovation')
                                ->maxLength(4),
                            Forms\Components\TextInput::make('type_chauffage')
                                ->label('Type de chauffage')
                                ->maxLength(100),
                            Forms\Components\TextInput::make('surface')
                                ->label('Surface')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\TextInput::make('surface_habitable')
                                ->label('Surface habitable')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\TextInput::make('surface_terrasse')
                                ->label('Surface terrasse(s)')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\TextInput::make('surface_terrain')
                                ->label('Surface terrain')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\TextInput::make('volume')
                                ->label('Volume (m³)')
                                ->numeric()
                                ->step(0.01),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Prix')
                        ->schema([
                            Forms\Components\TextInput::make('prix')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\Select::make('devise')
                                ->options([
                                    'CHF' => 'CHF',
                                    'EUR' => 'EUR',
                                ])
                                ->default('CHF'),
                            Forms\Components\Toggle::make('prix_sur_demande')
                                ->label('Prix sur demande')
                                ->default(false),
                            Forms\Components\Toggle::make('prix_negociable')
                                ->label('Prix négociable')
                                ->default(false),
                            Forms\Components\TextInput::make('prix_garage')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\TextInput::make('prix_parking')
                                ->numeric()
                                ->step(0.01),
                            Forms\Components\TextInput::make('charges')
                                ->numeric()
                                ->step(0.01),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Description')
                        ->schema([
                            Forms\Components\TextInput::make('titre')
                                ->maxLength(255)
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('prestation')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description_courte')
                                ->maxLength(500)
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('note_prive')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description_promotion')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description_projet')
                                ->columnSpan(2),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Images')
                        ->schema([
                            Forms\Components\TextInput::make('lien_video')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('lien_tour_virtuel')
                                ->maxLength(255),
                            Forms\Components\Repeater::make('imagesProprietes')
                                ->label("Images de la propriété")
                                ->relationship()
                                ->schema([
                                    Forms\Components\FileUpload::make('url_image')
                                        ->label("Image")
                                        ->image()
                                        ->maxSize(5120) // 5MB max
                                        ->disk('public_uploads')
                                        ->visibility('public'),
                                    Forms\Components\Toggle::make('is_main')
                                        ->label('Image principale')
                                        ->helperText('Cette image sera utilisée comme vignette principale')
                                        ->default(false),
                                    Forms\Components\Select::make('statut')
                                        ->options([
                                            'public' => 'Public',
                                            'prive' => 'Privé',
                                        ])
                                        ->default('public'),
                                ])
                                ->defaultItems(1)
                                ->orderColumn('order_column')
                                ->reorderable()
                                ->columns(2)
                                ->columnSpan('full'),
                        ])->columns(2),
                ])->columnSpan('full')->skippable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No'),
                Tables\Columns\ImageColumn::make('main_image')
                    ->label('Encart Photo')
                    ->getStateUsing(function (Propriete $record): ?string {
                        return $record->getMainImage();
                    })
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->square(),
                Tables\Columns\TextColumn::make('statut'),
                Tables\Columns\TextColumn::make('transaction'),
                Tables\Columns\TextColumn::make('categorie')
                ->label('Catégorie'),
                Tables\Columns\TextColumn::make('reference')
                    ->label('Réf. interne')
                    ->searchable(),
                Tables\Columns\TextColumn::make('prix')
                    ->numeric()
                    ->sortable()
                    ->money('CHF'),
                Tables\Columns\TextColumn::make('piece')
                ->label("Pièce(s)")
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('surface')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('adresse')
                    ->searchable(),
                Tables\Columns\TextColumn::make('agent') // Display Agent Name
                    ->label('Agent/Propriétaire')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                ->label('Créé')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                ->label('Modifié')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('categorie')
                    ->options([
                        'Appartements' => 'Appartements',
                        'Villas' => 'Villas',
                        'Bureaux' => 'Bureaux',
                        'Ateliers' => 'Ateliers',
                        'Boxes' => 'Boxes',
                    ]),
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'En attente' => 'En attente',
                        'Actif' => 'Actif',
                        'Actif warm' => 'Actif warm',
                        'Actif réservé' => 'Actif réservé',
                        'Vendu' => 'Vendu',
                        'Retiré' => 'Retiré',
                        'Perdu' => 'Perdu',
                    ]),
                Tables\Filters\SelectFilter::make('transaction')
                    ->options([
                        'Vente' => 'Vente',
                        'Location' => 'Location',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ReplicateAction::make()
                    ->excludeAttributes(['reference', 'url_image'])
                    ->beforeReplicaSaved(function (Propriete $record): void {
                        $record->titre = ' [NEW] ' . $record->titre;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProprietes::route('/'),
            'create' => Pages\CreatePropriete::route('/create'),
            'edit' => Pages\EditPropriete::route('/{record}/edit'),
        ];
    }
}

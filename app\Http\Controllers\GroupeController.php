<?php

namespace App\Http\Controllers;

use App\Models\Groupe;
use App\Models\Icon;
use App\Models\Section;
use Illuminate\Http\Request;

class GroupeController extends Controller
{
    public function index()
    {
        // Récupérer la page d'accueil en utilisant son slug
        // $page = Page::where('slug', 'home')->firstOrFail();

        // Récupère la section par son nom (par exemple "proprietes_vedettes")
        // $sections = Section::with('contenuSections')->where('page_id', 4)->get();
        $section_titre = Section::with('contenuSections')->where('page_id', 3)->where('ordre', 1)->get()->first();

        $icons = Icon::where('type', 'hero')->get();
        $groupes = Groupe::all();

        // dd($section_titre);

        // Passage des données à la vue index.blade.php
        // return view('web.index', compact('page', 'sections', 'proprietes', 'developpements', 'articles', 'contact'));
        return view('web.renovation', compact('groupes', 'icons', 'section_titre'));
    }
}

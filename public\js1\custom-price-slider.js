// Script pour un slider de prix personnalisé
document.addEventListener('DOMContentLoaded', function() {
    console.log("Initialisation du slider de prix personnalisé...");
    
    // Fonction pour créer le slider de prix personnalisé
    function createCustomPriceSlider() {
        // Récupérer le conteneur du slider
        const sliderContainer = document.getElementById('custom-price-slider-container');
        if (!sliderContainer) {
            console.error("Conteneur du slider non trouvé");
            return;
        }
        
        // Récupérer les valeurs min et max
        const minPrice = parseInt(sliderContainer.getAttribute('data-min')) || 0;
        const maxPrice = parseInt(sliderContainer.getAttribute('data-max')) || 50000000;
        console.log("Valeurs du slider:", minPrice, maxPrice);
        
        // Créer les éléments HTML pour le slider
        sliderContainer.innerHTML = `
            <div class="price-display">
                <span id="min-price-display">CHF ${formatNumber(minPrice)}</span>
                <span id="max-price-display">CHF ${formatNumber(maxPrice)}</span>
            </div>
            <div class="slider-track">
                <div class="slider-track-active"></div>
                <input type="range" id="min-price-slider" min="${minPrice}" max="${maxPrice}" value="${minPrice}" class="slider">
                <input type="range" id="max-price-slider" min="${minPrice}" max="${maxPrice}" value="${maxPrice}" class="slider">
            </div>
        `;
        
        // Récupérer les éléments créés
        const minPriceSlider = document.getElementById('min-price-slider');
        const maxPriceSlider = document.getElementById('max-price-slider');
        const minPriceDisplay = document.getElementById('min-price-display');
        const maxPriceDisplay = document.getElementById('max-price-display');
        const sliderTrackActive = document.querySelector('.slider-track-active');
        
        // Champs cachés pour le formulaire
        const minPriceInput = document.querySelector('input[name="rng_min_price"]');
        const maxPriceInput = document.querySelector('input[name="rng_max_price"]');
        
        // Fonction pour mettre à jour l'affichage
        function updateSlider() {
            const minVal = parseInt(minPriceSlider.value);
            const maxVal = parseInt(maxPriceSlider.value);
            
            // S'assurer que min <= max
            if (minVal > maxVal) {
                minPriceSlider.value = maxVal;
                return;
            }
            
            // Mettre à jour l'affichage
            minPriceDisplay.textContent = `CHF ${formatNumber(minVal)}`;
            maxPriceDisplay.textContent = `CHF ${formatNumber(maxVal)}`;
            
            // Mettre à jour la barre active
            const minPercent = ((minVal - minPrice) / (maxPrice - minPrice)) * 100;
            const maxPercent = ((maxVal - minPrice) / (maxPrice - minPrice)) * 100;
            sliderTrackActive.style.left = `${minPercent}%`;
            sliderTrackActive.style.width = `${maxPercent - minPercent}%`;
            
            // Mettre à jour les champs cachés
            if (minVal === minPrice && maxVal === maxPrice) {
                // Si les valeurs sont les valeurs par défaut, vider les champs
                minPriceInput.value = '';
                maxPriceInput.value = '';
            } else {
                minPriceInput.value = minVal;
                maxPriceInput.value = maxVal;
            }
        }
        
        // Ajouter les écouteurs d'événements
        minPriceSlider.addEventListener('input', updateSlider);
        maxPriceSlider.addEventListener('input', updateSlider);
        
        // Initialiser l'affichage
        updateSlider();
    }
    
    // Fonction pour formater les nombres avec séparateur de milliers
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, "'");
    }
    
    // Créer le slider
    createCustomPriceSlider();
});

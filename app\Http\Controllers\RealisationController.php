<?php

namespace App\Http\Controllers;

use App\Models\Icon;
use App\Models\Realisation;
use App\Models\Section;
use Illuminate\Http\Request;

class RealisationController extends Controller
{
    public function index()
    {

        // Récupérer la page d'accueil en utilisant son slug
        // $page = Page::where('slug', 'home')->firstOrFail();

        // Récupère la section par son nom (par exemple "proprietes_vedettes")
        $sections = Section::with('contenuSections')->where('page_id', 5)->get();
        $realisations =  Realisation::with(['imagesRealisation' => function ($query) {
            $query->limit(4); // Limit the related imagesRealisation to 4
        }])->latest()->get();
        $icons = Icon::where('type', 'hero')->get();
        // dd($section_titre);

        // Passage des données à la vue index.blade.php
        // return view('web.index', compact('page', 'sections', 'proprietes', 'developpements', 'articles', 'contact'));
        return view('web.realisation', compact('sections', 'realisations', 'icons'));
    }
}

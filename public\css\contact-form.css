/* Styles pour le formulaire de contact */
.text-danger {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

#form-messages {
    margin-bottom: 20px;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    animation: fadeIn 0.5s ease-in-out;
    position: relative;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    padding-left: 40px;
}

.alert-info::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    margin-top: -8px;
    width: 16px;
    height: 16px;
    border: 2px solid #0c5460;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s infinite linear;
}

.alert strong {
    display: block;
    margin-bottom: 5px;
    font-size: 16px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Style du bouton d'envoi */
.wpcf7-submit {
    background-color: #8ac473 !important;
    color: white !important;
    border: none !important;
    padding: 10px 20px !important;
    font-size: 16px !important;
    cursor: pointer !important;
    transition: background-color 0.3s !important;
}

.wpcf7-submit:hover {
    background-color: #7ab364 !important;
}

.wpcf7-submit:disabled {
    background-color: #a9d49a !important;
    cursor: wait !important;
}

/* Style pour le formulaire en cours de soumission */
.wpcf7-form.submitting {
    opacity: 0.7;
    pointer-events: none;
}

/* Animation de chargement */
.wpcf7-form.submitting .wpcf7-submit::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s infinite linear;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

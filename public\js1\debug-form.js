// Script de débogage pour le formulaire de contact
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.querySelector('form.wpcf7-form');

    if (contactForm) {
        console.log('Formulaire de contact trouvé:', contactForm);
        console.log('Action du formulaire:', contactForm.action);
        console.log('Méthode du formulaire:', contactForm.method);

        // Ajouter un gestionnaire d'événements pour la soumission du formulaire
        contactForm.addEventListener('submit', function(e) {
            console.log('Formulaire soumis');

            // Vérifier la validation du formulaire
            if (!this.checkValidity()) {
                return; // Laisser le navigateur gérer la validation
            }

            // Créer un objet FormData pour récupérer les données du formulaire
            const formData = new FormData(this);
            const formDataObj = {};

            // Convertir FormData en objet pour l'affichage
            for (let [key, value] of formData.entries()) {
                formDataObj[key] = value;
            }

            console.log('Données du formulaire:', formDataObj);

            // Cacher les messages précédents
            const formMessages = document.getElementById('form-messages');
            if (formMessages) {
                const previousMessages = formMessages.querySelectorAll('.alert');
                previousMessages.forEach(function(msg) {
                    msg.style.display = 'none';
                });

                // Afficher la notification d'envoi en cours
                const loadingNotification = document.getElementById('sending-notification');
                if (loadingNotification) {
                    // Déterminer si nous sommes sur la page d'accueil
                    const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php';

                    // Appliquer un style adapté à la page
                    if (isHomePage) {
                        loadingNotification.className = 'alert alert-info';
                        loadingNotification.style.backgroundColor = '#dff0d8';
                        loadingNotification.style.color = '#3c763d';
                        loadingNotification.style.padding = '10px';
                        loadingNotification.style.borderRadius = '4px';
                        loadingNotification.style.marginBottom = '10px';
                        loadingNotification.style.fontFamily = 'Arial, sans-serif';
                        loadingNotification.style.fontSize = '14px';
                        loadingNotification.style.fontWeight = 'bold';
                        loadingNotification.style.textShadow = '0 0 1px rgba(255, 255, 255, 0.5)';
                    }

                    loadingNotification.style.display = 'block';
                } else {
                    // Déterminer si nous sommes sur la page d'accueil
                    const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php';

                    // Créer une notification d'envoi en cours si elle n'existe pas
                    const newLoadingNotification = document.createElement('div');
                    newLoadingNotification.id = 'sending-notification';

                    // Appliquer un style adapté à la page
                    if (isHomePage) {
                        newLoadingNotification.className = 'alert alert-info';
                        newLoadingNotification.style.backgroundColor = '#dff0d8';
                        newLoadingNotification.style.color = '#3c763d';
                        newLoadingNotification.style.padding = '10px';
                        newLoadingNotification.style.borderRadius = '4px';
                        newLoadingNotification.style.marginBottom = '10px';
                        newLoadingNotification.style.fontFamily = 'Arial, sans-serif';
                        newLoadingNotification.style.fontSize = '14px';
                        newLoadingNotification.style.fontWeight = 'bold';
                        newLoadingNotification.style.textShadow = '0 0 1px rgba(255, 255, 255, 0.5)';
                    } else {
                        newLoadingNotification.className = 'alert alert-info';
                    }

                    newLoadingNotification.innerHTML = '<strong>Envoi en cours...</strong> Veuillez patienter pendant que nous envoyons votre message.';
                    formMessages.appendChild(newLoadingNotification);
                }
            }

            // Afficher un message de chargement sur le bouton
            const submitButton = this.querySelector('input[type="submit"]');
            if (submitButton) {
                submitButton.value = 'Envoi en cours...';
                submitButton.disabled = true;
            }

            // Ajouter une classe pour indiquer que le formulaire est en cours d'envoi
            this.classList.add('submitting');

            // Envoyer les données via fetch pour déboguer
            fetch('/contact/submit', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                }
            })
            .then(response => {
                console.log('Réponse reçue:', response);

                // Si la réponse est OK (statut 200-299), c'est un succès
                if (response.ok) {
                    // Traiter la réponse JSON
                    return response.json().then(data => {
                        console.log('Réponse JSON:', data);

                        // Afficher un message de succès
                        const formMessages = document.getElementById('form-messages');
                        if (formMessages) {
                            // Cacher la notification d'envoi en cours
                            const loadingNotification = document.getElementById('sending-notification');
                            if (loadingNotification) {
                                loadingNotification.style.display = 'none';
                            }

                            // Déterminer si nous sommes sur la page d'accueil
                            const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php';

                            // Afficher le message de succès avec un style adapté à la page
                            if (isHomePage) {
                                formMessages.innerHTML = '<div class="alert alert-success" id="success-message" style="background-color: #dff0d8; color: #3c763d; padding: 10px; border-radius: 4px; margin-bottom: 10px; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);"><strong>Message envoyé !</strong> ' + (data.message || 'Votre message a été envoyé avec succès. Nous vous contacterons bientôt.') + '</div>';
                            } else {
                                formMessages.innerHTML = '<div class="alert alert-success" id="success-message"><strong>Message envoyé !</strong> ' + (data.message || 'Votre message a été envoyé avec succès. Nous vous contacterons bientôt.') + '</div>';
                            }
                            formMessages.style.display = 'block';
                            formMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }

                        // Réinitialiser le formulaire
                        contactForm.reset();

                        // Réactiver le bouton d'envoi
                        const submitButton = contactForm.querySelector('input[type="submit"]');
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.value = 'Envoyer';
                        }

                        // Supprimer la classe de soumission
                        contactForm.classList.remove('submitting');
                    }).catch(error => {
                        // En cas d'erreur de parsing JSON, afficher quand même un message de succès
                        console.error('Erreur de parsing JSON:', error);

                        // Afficher un message de succès
                        const formMessages = document.getElementById('form-messages');
                        if (formMessages) {
                            // Cacher la notification d'envoi en cours
                            const loadingNotification = document.getElementById('sending-notification');
                            if (loadingNotification) {
                                loadingNotification.style.display = 'none';
                            }

                            // Déterminer si nous sommes sur la page d'accueil
                            const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php';

                            // Afficher le message de succès avec un style adapté à la page
                            if (isHomePage) {
                                formMessages.innerHTML = '<div class="alert alert-success" id="success-message" style="background-color: #dff0d8; color: #3c763d; padding: 10px; border-radius: 4px; margin-bottom: 10px; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);"><strong>Message envoyé !</strong> Votre message a été envoyé avec succès. Nous vous contacterons bientôt.</div>';
                            } else {
                                formMessages.innerHTML = '<div class="alert alert-success" id="success-message"><strong>Message envoyé !</strong> Votre message a été envoyé avec succès. Nous vous contacterons bientôt.</div>';
                            }
                            formMessages.style.display = 'block';
                            formMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }

                        // Réinitialiser le formulaire
                        contactForm.reset();

                        // Réactiver le bouton d'envoi
                        const submitButton = contactForm.querySelector('input[type="submit"]');
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.value = 'Envoyer';
                        }

                        // Supprimer la classe de soumission
                        contactForm.classList.remove('submitting');
                    });
                }

                if (response.redirected) {
                    // Si la réponse est une redirection, suivre la redirection
                    window.location.href = response.url;
                    return;
                }

                return response.text();
            })
            .then(data => {
                if (data) {
                    console.log('Données reçues:', data);
                }
            })
            .catch(error => {
                console.error('Erreur:', error);

                // Afficher un message d'erreur
                const formMessages = document.getElementById('form-messages');
                if (formMessages) {
                    // Cacher la notification d'envoi en cours
                    const loadingNotification = document.getElementById('sending-notification');
                    if (loadingNotification) {
                        loadingNotification.style.display = 'none';
                    }

                    // Déterminer si nous sommes sur la page d'accueil
                    const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php';

                    // Afficher le message d'erreur avec un style adapté à la page
                    if (isHomePage) {
                        formMessages.innerHTML = '<div class="alert alert-danger" id="error-message" style="background-color: #f2dede; color: #a94442; padding: 10px; border-radius: 4px; margin-bottom: 10px; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-shadow: 0 0 1px rgba(255, 255, 255, 0.5);"><strong>Erreur !</strong> Une erreur est survenue lors de l\'envoi de votre message. Veuillez réessayer plus tard.</div>';
                    } else {
                        formMessages.innerHTML = '<div class="alert alert-danger" id="error-message"><strong>Erreur !</strong> Une erreur est survenue lors de l\'envoi de votre message. Veuillez réessayer plus tard.</div>';
                    }
                    formMessages.style.display = 'block';
                    formMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // Réactiver le bouton d'envoi
                const submitButton = contactForm.querySelector('input[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.value = 'Envoyer';
                }

                // Supprimer la classe de soumission
                contactForm.classList.remove('submitting');
            });

            // Empêcher la soumission normale du formulaire pour éviter les conflits avec Contact Form 7
            e.preventDefault();
        });
    } else {
        console.error('Formulaire de contact non trouvé');
    }
});

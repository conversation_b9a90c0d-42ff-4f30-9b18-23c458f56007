<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProjetEnCours extends Model
{
    // Explicitly define the table name (optional if your model name follows <PERSON><PERSON>'s naming convention)
    protected $table = 'projet_en_cours';

    // List the attributes that are mass assignable (adjust as needed)
    protected $fillable = [
        'titre_principale',
        'sous_titre_principale',
        'img_principale',
        'titre_info_projet',
        'img_info_1',
        'img_info_2',
        'texte_info_1',
        'texte_info_2',
        'description_information',
        'titre_mot_architecte',
        'texte_mot_architecte',
        'img_architecte',
        'mot_promoteur',
        'description_promoteur',
        'titre_prestation',
        'sous_titre_prestation',
        'description_prestation',
        'titre_projet_img',
        'sous_titre_projet_img',
        'titre_localisation',
        'sous_titre_localisation',
        'texte_plaquette',
        'etiquette_bouton_plaquette',
        'titre_bien_projet',
        'sous_titre_bien_projet',
        'titre_banniere',
        'img_banniere',
        'etiquette_bouton_banniere',
        'lien_bouton_banniere',
        'lien_bouton_plaquette',
        'titre_contactez_nous',
        'description_contactez_nous',
        'lien_bouton_contactez_nous',
        'etiquette_bouton_contactez_nous',
        'img_info',
        'img_localisation',
        'image_fond_contact',
        'image_fond_prestation',
    ];

    // Define the relationship: one ProjetEnCours has many ImagesProjet
    public function images()
    {
        return $this->hasMany(ImagesProjet::class, 'projet_en_cour_id');
    }
}

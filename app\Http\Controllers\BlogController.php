<?php

namespace App\Http\Controllers;

use App\Models\ArticlesBlog;
use App\Models\BlogCategory;
use App\Models\BlogTag;
use App\Models\Icon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BlogController extends Controller
{
    /**
     * Affiche la liste des articles du blog.
     */
    public function index(Request $request)
    {
        $query = ArticlesBlog::with(['category', 'tags'])
            ->published()
            ->latest('date_publication');

        // Filtrer par catégorie
        if ($request->has('category') && $request->category) {
            $category = BlogCategory::where('slug', $request->category)->firstOrFail();
            $query->where('category_id', $category->id);
        }

        // Filtrer par tag
        if ($request->has('tag') && $request->tag) {
            $tag = BlogTag::where('slug', $request->tag)->firstOrFail();
            $query->whereHas('tags', function ($q) use ($tag) {
                $q->where('blog_tags.id', $tag->id);
            });
        }

        $articles = $query->paginate(9);
        $categories = BlogCategory::withCount('articles')->get();
        $tags = BlogTag::withCount('articles')->get();
        $icons = Icon::where('type', 'hero')->get();

        return view('web.blog.index', compact('articles', 'categories', 'tags', 'icons'));
    }

    /**
     * Affiche un article spécifique.
     */
    public function show($slug)
    {
        // Log pour debug
        Log::info('BlogController show method called with slug/id: ' . $slug);
        
        // Vérifier si le paramètre est un ID numérique ou un slug
        if (is_numeric($slug)) {
            $article = ArticlesBlog::with(['category', 'tags'])
                ->where('id', $slug)
                // ->published() // Temporairement désactivé pour test
                ->firstOrFail();
        } else {
            $article = ArticlesBlog::with(['category', 'tags'])
                ->where('slug', $slug)
                // ->published() // Temporairement désactivé pour test
                ->firstOrFail();
        }

        // Articles connexes (même catégorie)
        $relatedArticles = ArticlesBlog::with(['category'])
            ->where('id', '!=', $article->id)
            ->where('category_id', $article->category_id ? $article->category_id : 0)
            // ->published() // Temporairement désactivé pour test
            ->latest('date_publication')
            ->limit(3)
            ->get();

        $icons = Icon::where('type', 'hero')->get();

        return view('web.blog.show', compact('article', 'relatedArticles', 'icons'));
    }

    /**
     * Affiche un article spécifique par son ID.
     */
    public function showById($id)
    {
        // Log pour debug
        Log::info('BlogController showById method called with id: ' . $id);
        
        $article = ArticlesBlog::with(['category', 'tags'])
            ->where('id', $id)
            ->firstOrFail();

        // Articles connexes (même catégorie)
        $relatedArticles = ArticlesBlog::with(['category'])
            ->where('id', '!=', $article->id)
            ->where('category_id', $article->category_id ? $article->category_id : 0)
            ->latest('date_publication')
            ->published()
            ->limit(3)
            ->get();

        $icons = Icon::where('type', 'hero')->get();

        return view('web.blog.show', compact('article', 'relatedArticles', 'icons'));
    }
}

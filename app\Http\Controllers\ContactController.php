<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Icon;
use App\Models\Section;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    public function index()
    {


        $sections = Section::with('contenuSections')->where('page_id', 6)->get();
        $icons = Icon::where('type', 'hero')->get();
        $contact = Contact::get()->first();

        // Passage des données à la vue index.blade.php
        // return view('web.index', compact('page', 'sections', 'proprietes', 'developpements', 'articles', 'contact'));
        return view('web.contact', compact('icons', 'sections', 'contact'));
    }
}

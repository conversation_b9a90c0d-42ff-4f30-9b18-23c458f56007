<?php

namespace App\Filament\Resources\SectionResource\RelationManagers;

use App\Filament\Forms\Components\IconSelect;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContenuSectionsRelationManager extends RelationManager
{
    protected static string $relationship = 'contenuSections';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('titre')
                    ->nullable()
                    ->maxLength(255),
                IconSelect::make('icon_id')
                    ->label('Icône')
                    ->helperText('Sélectionnez une icône pour ce contenu de section')
                    ->nullable(),
                Forms\Components\RichEditor::make('description')
                    ->nullable()
                    ->columnSpanFull(),
                Forms\Components\FileUpload::make('image')
                    ->nullable()
                    ->image(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('titre')
            ->columns([
                Tables\Columns\TextColumn::make('titre')
                    ->searchable(),
                Tables\Columns\ViewColumn::make('icon_id')
                    ->label('Icône')
                    ->view('filament.tables.columns.icon-column'),
                Tables\Columns\ImageColumn::make('image'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}

// Script pour corriger le problème du slider de prix
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM chargé, vérification du slider de prix...");
    
    // Fonction pour initialiser le slider
    function initPriceSlider() {
        console.log("Tentative d'initialisation du slider de prix...");
        
        var priceSlider = $("#prdctfltr_rng_price");
        
        if (priceSlider.length === 0) {
            console.error("Élément #prdctfltr_rng_price non trouvé dans le DOM");
            return;
        }
        
        console.log("Élément slider trouvé:", priceSlider);
        
        // Vérifier si jQuery et ionRangeSlider sont disponibles
        if (typeof $ === 'undefined' || typeof $.fn.ionRangeSlider === 'undefined') {
            console.error("jQuery ou ionRangeSlider non disponible");
            
            // Charger ionRangeSlider si nécessaire
            if (typeof $ !== 'undefined' && typeof $.fn.ionRangeSlider === 'undefined') {
                console.log("Chargement manuel de ionRangeSlider...");
                
                // Créer un élément script pour charger ionRangeSlider
                var script = document.createElement('script');
                script.src = '/wp-content/plugins/prdctfltr/includes/js/ion.rangeSlider.min.js';
                script.onload = function() {
                    console.log("ionRangeSlider chargé, initialisation du slider...");
                    initSlider();
                };
                document.head.appendChild(script);
            }
            return;
        }
        
        initSlider();
    }
    
    function initSlider() {
        var priceSlider = $("#prdctfltr_rng_price");
        var minPrice = parseInt(priceSlider.data('min')) || 0;
        var maxPrice = parseInt(priceSlider.data('max')) || 50000000;
        
        console.log("Initialisation du slider avec min:", minPrice, "max:", maxPrice);
        
        // Calculer un pas approprié en fonction de la plage de prix
        var range = maxPrice - minPrice;
        var step = 1000; // Pas par défaut
        
        if (range > 10000000) {
            step = 500000; // Pas de 500k pour les grandes plages
        } else if (range > 5000000) {
            step = 250000; // Pas de 250k pour les plages moyennes-grandes
        } else if (range > 1000000) {
            step = 100000; // Pas de 100k pour les plages moyennes
        } else if (range > 500000) {
            step = 50000; // Pas de 50k pour les plages petites-moyennes
        } else if (range > 100000) {
            step = 10000; // Pas de 10k pour les petites plages
        }
        
        try {
            priceSlider.ionRangeSlider({
                type: "double",
                grid: true,
                min: minPrice,
                max: maxPrice,
                from: minPrice,
                to: maxPrice,
                prefix: "CHF ",
                step: step,
                prettify_separator: "'",
                prettify: function(num) {
                    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, "'");
                },
                onChange: function(data) {
                    $('input[name="rng_min_price"]').val(data.from);
                    $('input[name="rng_max_price"]').val(data.to);
                },
                onFinish: function(data) {
                    if (data.from === minPrice && data.to === maxPrice) {
                        $('input[name="rng_min_price"]').val('');
                        $('input[name="rng_max_price"]').val('');
                    } else {
                        $('input[name="rng_min_price"]').val(data.from);
                        $('input[name="rng_max_price"]').val(data.to);
                    }
                }
            });
            console.log("Slider initialisé avec succès");
        } catch (error) {
            console.error("Erreur lors de l'initialisation du slider:", error);
        }
    }
    
    // Essayer d'initialiser immédiatement
    initPriceSlider();
    
    // Si ça ne fonctionne pas, réessayer après un court délai
    setTimeout(initPriceSlider, 1000);
    
    // Et encore une fois après un délai plus long au cas où
    setTimeout(initPriceSlider, 3000);
});

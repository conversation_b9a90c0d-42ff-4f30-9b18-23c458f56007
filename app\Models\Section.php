<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Section extends Model
{
    use HasFactory;

    protected $fillable = [
        'nom',
        'type',
        'ordre',
        'page_id',
        'sous_titre',
        'titre',
        'description',
        'image_fond',
        'etiquette_bouton',
        'lien_bouton',
        'icon_id',
    ];



    public function page()
    {
        return $this->belongsTo(Page::class);
    }
    public function contenuSections()
    {
        return $this->hasMany(ContenusSection::class);
    }

    /**
     * Relation avec l'icône associée à cette section.
     */
    public function icon()
    {
        return $this->belongsTo(Icon::class);
    }
}

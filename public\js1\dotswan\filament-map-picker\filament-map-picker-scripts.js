var zo=Object.create;var $a=Object.defineProperty;var No=Object.getOwnPropertyDescriptor;var Go=Object.getOwnPropertyNames;var Zo=Object.getPrototypeOf,jo=Object.prototype.hasOwnProperty;var Uo=(j,$,U)=>$ in j?$a(j,$,{enumerable:!0,configurable:!0,writable:!0,value:U}):j[$]=U;var Vo=(j,$)=>()=>($||j(($={exports:{}}).exports,$),$.exports);var Ho=(j,$,U,ue)=>{if($&&typeof $=="object"||typeof $=="function")for(let W of Go($))!jo.call(j,W)&&W!==U&&$a(j,W,{get:()=>$[W],enumerable:!(ue=No($,W))||ue.enumerable});return j};var Ko=(j,$,U)=>(U=j!=null?zo(Zo(j)):{},Ho($||!j||!j.__esModule?$a(U,"default",{value:j,enumerable:!0}):U,j));var et=(j,$,U)=>(Uo(j,typeof $!="symbol"?$+"":$,U),U);var lo=Vo((sa,uo)=>{(function(j,$){typeof sa=="object"&&typeof uo<"u"?$(sa):typeof define=="function"&&define.amd?define(["exports"],$):(j=typeof globalThis<"u"?globalThis:j||self,$(j.leaflet={}))})(sa,function(j){"use strict";var $="1.9.4";function U(t){var n,o,u,c;for(o=1,u=arguments.length;o<u;o++){c=arguments[o];for(n in c)t[n]=c[n]}return t}var ue=Object.create||function(){function t(){}return function(n){return t.prototype=n,new t}}();function W(t,n){var o=Array.prototype.slice;if(t.bind)return t.bind.apply(t,o.call(arguments,1));var u=o.call(arguments,2);return function(){return t.apply(n,u.length?u.concat(o.call(arguments)):arguments)}}var ct=0;function F(t){return"_leaflet_id"in t||(t._leaflet_id=++ct),t._leaflet_id}function Wt(t,n,o){var u,c,m,M;return M=function(){u=!1,c&&(m.apply(o,c),c=!1)},m=function(){u?c=arguments:(t.apply(o,arguments),setTimeout(M,n),u=!0)},m}function rt(t,n,o){var u=n[1],c=n[0],m=u-c;return t===u&&o?t:((t-c)%m+m)%m+c}function st(){return!1}function Ft(t,n){if(n===!1)return t;var o=Math.pow(10,n===void 0?6:n);return Math.round(t*o)/o}function Xe(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function Ze(t){return Xe(t).split(/\s+/)}function wt(t,n){Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?ue(t.options):{});for(var o in n)t.options[o]=n[o];return t.options}function Yn(t,n,o){var u=[];for(var c in t)u.push(encodeURIComponent(o?c.toUpperCase():c)+"="+encodeURIComponent(t[c]));return(!n||n.indexOf("?")===-1?"?":"&")+u.join("&")}var la=/\{ *([\w_ -]+) *\}/g;function li(t,n){return t.replace(la,function(o,u){var c=n[u];if(c===void 0)throw new Error("No value provided for variable "+o);return typeof c=="function"&&(c=c(n)),c})}var le=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};function un(t,n){for(var o=0;o<t.length;o++)if(t[o]===n)return o;return-1}var Pi="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function ln(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var hn=0;function je(t){var n=+new Date,o=Math.max(0,16-(n-hn));return hn=n+o,window.setTimeout(t,o)}var hi=window.requestAnimationFrame||ln("RequestAnimationFrame")||je,Jn=window.cancelAnimationFrame||ln("CancelAnimationFrame")||ln("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function Ut(t,n,o){if(o&&hi===je)t.call(n);else return hi.call(window,W(t,n))}function Nt(t){t&&Jn.call(window,t)}var Ue={__proto__:null,extend:U,create:ue,bind:W,get lastId(){return ct},stamp:F,throttle:Wt,wrapNum:rt,falseFn:st,formatNum:Ft,trim:Xe,splitWords:Ze,setOptions:wt,getParamString:Yn,template:li,isArray:le,indexOf:un,emptyImageUrl:Pi,requestFn:hi,cancelFn:Jn,requestAnimFrame:Ut,cancelAnimFrame:Nt};function he(){}he.extend=function(t){var n=function(){wt(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},o=n.__super__=this.prototype,u=ue(o);u.constructor=n,n.prototype=u;for(var c in this)Object.prototype.hasOwnProperty.call(this,c)&&c!=="prototype"&&c!=="__super__"&&(n[c]=this[c]);return t.statics&&U(n,t.statics),t.includes&&(ha(t.includes),U.apply(null,[u].concat(t.includes))),U(u,t),delete u.statics,delete u.includes,u.options&&(u.options=o.options?ue(o.options):{},U(u.options,t.options)),u._initHooks=[],u.callInitHooks=function(){if(!this._initHooksCalled){o.callInitHooks&&o.callInitHooks.call(this),this._initHooksCalled=!0;for(var m=0,M=u._initHooks.length;m<M;m++)u._initHooks[m].call(this)}},n},he.include=function(t){var n=this.prototype.options;return U(this.prototype,t),t.options&&(this.prototype.options=n,this.mergeOptions(t.options)),this},he.mergeOptions=function(t){return U(this.prototype.options,t),this},he.addInitHook=function(t){var n=Array.prototype.slice.call(arguments,1),o=typeof t=="function"?t:function(){this[t].apply(this,n)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(o),this};function ha(t){if(!(typeof L>"u"||!L||!L.Mixin)){t=le(t)?t:[t];for(var n=0;n<t.length;n++)t[n]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var Yt={on:function(t,n,o){if(typeof t=="object")for(var u in t)this._on(u,t[u],n);else{t=Ze(t);for(var c=0,m=t.length;c<m;c++)this._on(t[c],n,o)}return this},off:function(t,n,o){if(!arguments.length)delete this._events;else if(typeof t=="object")for(var u in t)this._off(u,t[u],n);else{t=Ze(t);for(var c=arguments.length===1,m=0,M=t.length;m<M;m++)c?this._off(t[m]):this._off(t[m],n,o)}return this},_on:function(t,n,o,u){if(typeof n!="function"){console.warn("wrong listener type: "+typeof n);return}if(this._listens(t,n,o)===!1){o===this&&(o=void 0);var c={fn:n,ctx:o};u&&(c.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(c)}},_off:function(t,n,o){var u,c,m;if(this._events&&(u=this._events[t],!!u)){if(arguments.length===1){if(this._firingCount)for(c=0,m=u.length;c<m;c++)u[c].fn=st;delete this._events[t];return}if(typeof n!="function"){console.warn("wrong listener type: "+typeof n);return}var M=this._listens(t,n,o);if(M!==!1){var O=u[M];this._firingCount&&(O.fn=st,this._events[t]=u=u.slice()),u.splice(M,1)}}},fire:function(t,n,o){if(!this.listens(t,o))return this;var u=U({},n,{type:t,target:this,sourceTarget:n&&n.sourceTarget||this});if(this._events){var c=this._events[t];if(c){this._firingCount=this._firingCount+1||1;for(var m=0,M=c.length;m<M;m++){var O=c[m],Z=O.fn;O.once&&this.off(t,Z,O.ctx),Z.call(O.ctx||this,u)}this._firingCount--}}return o&&this._propagateEvent(u),this},listens:function(t,n,o,u){typeof t!="string"&&console.warn('"string" type argument expected');var c=n;typeof n!="function"&&(u=!!n,c=void 0,o=void 0);var m=this._events&&this._events[t];if(m&&m.length&&this._listens(t,c,o)!==!1)return!0;if(u){for(var M in this._eventParents)if(this._eventParents[M].listens(t,n,o,u))return!0}return!1},_listens:function(t,n,o){if(!this._events)return!1;var u=this._events[t]||[];if(!n)return!!u.length;o===this&&(o=void 0);for(var c=0,m=u.length;c<m;c++)if(u[c].fn===n&&u[c].ctx===o)return c;return!1},once:function(t,n,o){if(typeof t=="object")for(var u in t)this._on(u,t[u],n,!0);else{t=Ze(t);for(var c=0,m=t.length;c<m;c++)this._on(t[c],n,o,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[F(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[F(t)],this},_propagateEvent:function(t){for(var n in this._eventParents)this._eventParents[n].fire(t.type,U({layer:t.target,propagatedFrom:t.target},t),!0)}};Yt.addEventListener=Yt.on,Yt.removeEventListener=Yt.clearAllEventListeners=Yt.off,Yt.addOneTimeEventListener=Yt.once,Yt.fireEvent=Yt.fire,Yt.hasEventListeners=Yt.listens;var ci=he.extend(Yt);function ut(t,n,o){this.x=o?Math.round(t):t,this.y=o?Math.round(n):n}var Xn=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};ut.prototype={clone:function(){return new ut(this.x,this.y)},add:function(t){return this.clone()._add(at(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(at(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new ut(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new ut(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Xn(this.x),this.y=Xn(this.y),this},distanceTo:function(t){t=at(t);var n=t.x-this.x,o=t.y-this.y;return Math.sqrt(n*n+o*o)},equals:function(t){return t=at(t),t.x===this.x&&t.y===this.y},contains:function(t){return t=at(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+Ft(this.x)+", "+Ft(this.y)+")"}};function at(t,n,o){return t instanceof ut?t:le(t)?new ut(t[0],t[1]):t==null?t:typeof t=="object"&&"x"in t&&"y"in t?new ut(t.x,t.y):new ut(t,n,o)}function Et(t,n){if(t)for(var o=n?[t,n]:t,u=0,c=o.length;u<c;u++)this.extend(o[u])}Et.prototype={extend:function(t){var n,o;if(!t)return this;if(t instanceof ut||typeof t[0]=="number"||"x"in t)n=o=at(t);else if(t=It(t),n=t.min,o=t.max,!n||!o)return this;return!this.min&&!this.max?(this.min=n.clone(),this.max=o.clone()):(this.min.x=Math.min(n.x,this.min.x),this.max.x=Math.max(o.x,this.max.x),this.min.y=Math.min(n.y,this.min.y),this.max.y=Math.max(o.y,this.max.y)),this},getCenter:function(t){return at((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return at(this.min.x,this.max.y)},getTopRight:function(){return at(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var n,o;return typeof t[0]=="number"||t instanceof ut?t=at(t):t=It(t),t instanceof Et?(n=t.min,o=t.max):n=o=t,n.x>=this.min.x&&o.x<=this.max.x&&n.y>=this.min.y&&o.y<=this.max.y},intersects:function(t){t=It(t);var n=this.min,o=this.max,u=t.min,c=t.max,m=c.x>=n.x&&u.x<=o.x,M=c.y>=n.y&&u.y<=o.y;return m&&M},overlaps:function(t){t=It(t);var n=this.min,o=this.max,u=t.min,c=t.max,m=c.x>n.x&&u.x<o.x,M=c.y>n.y&&u.y<o.y;return m&&M},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var n=this.min,o=this.max,u=Math.abs(n.x-o.x)*t,c=Math.abs(n.y-o.y)*t;return It(at(n.x-u,n.y-c),at(o.x+u,o.y+c))},equals:function(t){return t?(t=It(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight())):!1}};function It(t,n){return!t||t instanceof Et?t:new Et(t,n)}function Vt(t,n){if(t)for(var o=n?[t,n]:t,u=0,c=o.length;u<c;u++)this.extend(o[u])}Vt.prototype={extend:function(t){var n=this._southWest,o=this._northEast,u,c;if(t instanceof vt)u=t,c=t;else if(t instanceof Vt){if(u=t._southWest,c=t._northEast,!u||!c)return this}else return t?this.extend(ft(t)||Pt(t)):this;return!n&&!o?(this._southWest=new vt(u.lat,u.lng),this._northEast=new vt(c.lat,c.lng)):(n.lat=Math.min(u.lat,n.lat),n.lng=Math.min(u.lng,n.lng),o.lat=Math.max(c.lat,o.lat),o.lng=Math.max(c.lng,o.lng)),this},pad:function(t){var n=this._southWest,o=this._northEast,u=Math.abs(n.lat-o.lat)*t,c=Math.abs(n.lng-o.lng)*t;return new Vt(new vt(n.lat-u,n.lng-c),new vt(o.lat+u,o.lng+c))},getCenter:function(){return new vt((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new vt(this.getNorth(),this.getWest())},getSouthEast:function(){return new vt(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){typeof t[0]=="number"||t instanceof vt||"lat"in t?t=ft(t):t=Pt(t);var n=this._southWest,o=this._northEast,u,c;return t instanceof Vt?(u=t.getSouthWest(),c=t.getNorthEast()):u=c=t,u.lat>=n.lat&&c.lat<=o.lat&&u.lng>=n.lng&&c.lng<=o.lng},intersects:function(t){t=Pt(t);var n=this._southWest,o=this._northEast,u=t.getSouthWest(),c=t.getNorthEast(),m=c.lat>=n.lat&&u.lat<=o.lat,M=c.lng>=n.lng&&u.lng<=o.lng;return m&&M},overlaps:function(t){t=Pt(t);var n=this._southWest,o=this._northEast,u=t.getSouthWest(),c=t.getNorthEast(),m=c.lat>n.lat&&u.lat<o.lat,M=c.lng>n.lng&&u.lng<o.lng;return m&&M},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,n){return t?(t=Pt(t),this._southWest.equals(t.getSouthWest(),n)&&this._northEast.equals(t.getNorthEast(),n)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function Pt(t,n){return t instanceof Vt?t:new Vt(t,n)}function vt(t,n,o){if(isNaN(t)||isNaN(n))throw new Error("Invalid LatLng object: ("+t+", "+n+")");this.lat=+t,this.lng=+n,o!==void 0&&(this.alt=+o)}vt.prototype={equals:function(t,n){if(!t)return!1;t=ft(t);var o=Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng));return o<=(n===void 0?1e-9:n)},toString:function(t){return"LatLng("+Ft(this.lat,t)+", "+Ft(this.lng,t)+")"},distanceTo:function(t){return Pe.distance(this,ft(t))},wrap:function(){return Pe.wrapLatLng(this)},toBounds:function(t){var n=180*t/40075017,o=n/Math.cos(Math.PI/180*this.lat);return Pt([this.lat-n,this.lng-o],[this.lat+n,this.lng+o])},clone:function(){return new vt(this.lat,this.lng,this.alt)}};function ft(t,n,o){return t instanceof vt?t:le(t)&&typeof t[0]!="object"?t.length===3?new vt(t[0],t[1],t[2]):t.length===2?new vt(t[0],t[1]):null:t==null?t:typeof t=="object"&&"lat"in t?new vt(t.lat,"lng"in t?t.lng:t.lon,t.alt):n===void 0?null:new vt(t,n,o)}var Ce={latLngToPoint:function(t,n){var o=this.projection.project(t),u=this.scale(n);return this.transformation._transform(o,u)},pointToLatLng:function(t,n){var o=this.scale(n),u=this.transformation.untransform(t,o);return this.projection.unproject(u)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var n=this.projection.bounds,o=this.scale(t),u=this.transformation.transform(n.min,o),c=this.transformation.transform(n.max,o);return new Et(u,c)},infinite:!1,wrapLatLng:function(t){var n=this.wrapLng?rt(t.lng,this.wrapLng,!0):t.lng,o=this.wrapLat?rt(t.lat,this.wrapLat,!0):t.lat,u=t.alt;return new vt(o,n,u)},wrapLatLngBounds:function(t){var n=t.getCenter(),o=this.wrapLatLng(n),u=n.lat-o.lat,c=n.lng-o.lng;if(u===0&&c===0)return t;var m=t.getSouthWest(),M=t.getNorthEast(),O=new vt(m.lat-u,m.lng-c),Z=new vt(M.lat-u,M.lng-c);return new Vt(O,Z)}},Pe=U({},Ce,{wrapLng:[-180,180],R:6371e3,distance:function(t,n){var o=Math.PI/180,u=t.lat*o,c=n.lat*o,m=Math.sin((n.lat-t.lat)*o/2),M=Math.sin((n.lng-t.lng)*o/2),O=m*m+Math.cos(u)*Math.cos(c)*M*M,Z=2*Math.atan2(Math.sqrt(O),Math.sqrt(1-O));return this.R*Z}}),$n=6378137,cn={R:$n,MAX_LATITUDE:85.0511287798,project:function(t){var n=Math.PI/180,o=this.MAX_LATITUDE,u=Math.max(Math.min(o,t.lat),-o),c=Math.sin(u*n);return new ut(this.R*t.lng*n,this.R*Math.log((1+c)/(1-c))/2)},unproject:function(t){var n=180/Math.PI;return new vt((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*n,t.x*n/this.R)},bounds:function(){var t=$n*Math.PI;return new Et([-t,-t],[t,t])}()};function $e(t,n,o,u){if(le(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=n,this._c=o,this._d=u}$e.prototype={transform:function(t,n){return this._transform(t.clone(),n)},_transform:function(t,n){return n=n||1,t.x=n*(this._a*t.x+this._b),t.y=n*(this._c*t.y+this._d),t},untransform:function(t,n){return n=n||1,new ut((t.x/n-this._b)/this._a,(t.y/n-this._d)/this._c)}};function di(t,n,o,u){return new $e(t,n,o,u)}var dn=U({},Pe,{code:"EPSG:3857",projection:cn,transformation:function(){var t=.5/(Math.PI*cn.R);return di(t,.5,-t,.5)}()}),ca=U({},dn,{code:"EPSG:900913"});function Qn(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function pn(t,n){var o="",u,c,m,M,O,Z;for(u=0,m=t.length;u<m;u++){for(O=t[u],c=0,M=O.length;c<M;c++)Z=O[c],o+=(c?"L":"M")+Z.x+" "+Z.y;o+=n?tt.svg?"z":"x":""}return o||"M0 0"}var fn=document.documentElement.style,Ti="ActiveXObject"in window,tr=Ti&&!document.addEventListener,Di="msLaunchUri"in navigator&&!("documentMode"in document),Si=ge("webkit"),er=ge("android"),ir=ge("android 2")||ge("android 3"),da=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),pa=er&&ge("Google")&&da<537&&!("AudioNode"in window),_n=!!window.opera,nr=!Di&&ge("chrome"),rr=ge("gecko")&&!Si&&!_n&&!Ti,fa=!nr&&ge("safari"),ar=ge("phantom"),mn="OTransition"in fn,or=navigator.platform.indexOf("Win")===0,sr=Ti&&"transition"in fn,Te="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!ir,ur="MozPerspective"in fn,lr=!window.L_DISABLE_3D&&(sr||Te||ur)&&!mn&&!ar,xe=typeof orientation<"u"||ge("mobile"),hr=xe&&Si,gn=xe&&Te,cr=!window.PointerEvent&&window.MSPointerEvent,dr=!!(window.PointerEvent||cr),yn="ontouchstart"in window||!!window.TouchEvent,_a=!window.L_NO_TOUCH&&(yn||dr),ma=xe&&_n,ga=xe&&rr,ya=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,pr=function(){var t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",st,n),window.removeEventListener("testPassiveEventSupport",st,n)}catch{}return t}(),fr=function(){return!!document.createElement("canvas").getContext}(),vn=!!(document.createElementNS&&Qn("svg").createSVGRect),va=!!vn&&function(){var t=document.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),La=!vn&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var n=t.firstChild;return n.style.behavior="url(#default#VML)",n&&typeof n.adj=="object"}catch{return!1}}(),_r=navigator.platform.indexOf("Mac")===0,ba=navigator.platform.indexOf("Linux")===0;function ge(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var tt={ie:Ti,ielt9:tr,edge:Di,webkit:Si,android:er,android23:ir,androidStock:pa,opera:_n,chrome:nr,gecko:rr,safari:fa,phantom:ar,opera12:mn,win:or,ie3d:sr,webkit3d:Te,gecko3d:ur,any3d:lr,mobile:xe,mobileWebkit:hr,mobileWebkit3d:gn,msPointer:cr,pointer:dr,touch:_a,touchNative:yn,mobileOpera:ma,mobileGecko:ga,retina:ya,passiveEvents:pr,canvas:fr,svg:vn,vml:La,inlineSvg:va,mac:_r,linux:ba},Ln=tt.msPointer?"MSPointerDown":"pointerdown",mr=tt.msPointer?"MSPointerMove":"pointermove",gr=tt.msPointer?"MSPointerUp":"pointerup",yr=tt.msPointer?"MSPointerCancel":"pointercancel",Ai={touchstart:Ln,touchmove:mr,touchend:gr,touchcancel:yr},vr={touchstart:bn,touchmove:ti,touchend:ti,touchcancel:ti},Qe={},Lr=!1;function Ca(t,n,o){return n==="touchstart"&&wa(),vr[n]?(o=vr[n].bind(this,o),t.addEventListener(Ai[n],o,!1),o):(console.warn("wrong event specified:",n),st)}function xa(t,n,o){if(!Ai[n]){console.warn("wrong event specified:",n);return}t.removeEventListener(Ai[n],o,!1)}function ka(t){Qe[t.pointerId]=t}function Ma(t){Qe[t.pointerId]&&(Qe[t.pointerId]=t)}function br(t){delete Qe[t.pointerId]}function wa(){Lr||(document.addEventListener(Ln,ka,!0),document.addEventListener(mr,Ma,!0),document.addEventListener(gr,br,!0),document.addEventListener(yr,br,!0),Lr=!0)}function ti(t,n){if(n.pointerType!==(n.MSPOINTER_TYPE_MOUSE||"mouse")){n.touches=[];for(var o in Qe)n.touches.push(Qe[o]);n.changedTouches=[n],t(n)}}function bn(t,n){n.MSPOINTER_TYPE_TOUCH&&n.pointerType===n.MSPOINTER_TYPE_TOUCH&&zt(n),ti(t,n)}function Ea(t){var n={},o,u;for(u in t)o=t[u],n[u]=o&&o.bind?o.bind(t):o;return t=n,n.type="dblclick",n.detail=2,n.isTrusted=!1,n._simulated=!0,n}var Ba=200;function Pa(t,n){t.addEventListener("dblclick",n);var o=0,u;function c(m){if(m.detail!==1){u=m.detail;return}if(!(m.pointerType==="mouse"||m.sourceCapabilities&&!m.sourceCapabilities.firesTouchEvents)){var M=wr(m);if(!(M.some(function(Z){return Z instanceof HTMLLabelElement&&Z.attributes.for})&&!M.some(function(Z){return Z instanceof HTMLInputElement||Z instanceof HTMLSelectElement}))){var O=Date.now();O-o<=Ba?(u++,u===2&&n(Ea(m))):u=1,o=O}}}return t.addEventListener("click",c),{dblclick:n,simDblclick:c}}function Ta(t,n){t.removeEventListener("dblclick",n.dblclick),t.removeEventListener("click",n.simDblclick)}var Cn=Ii(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),pi=Ii(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Cr=pi==="webkitTransition"||pi==="OTransition"?pi+"End":"transitionend";function xr(t){return typeof t=="string"?document.getElementById(t):t}function fi(t,n){var o=t.style[n]||t.currentStyle&&t.currentStyle[n];if((!o||o==="auto")&&document.defaultView){var u=document.defaultView.getComputedStyle(t,null);o=u?u[n]:null}return o==="auto"?null:o}function gt(t,n,o){var u=document.createElement(t);return u.className=n||"",o&&o.appendChild(u),u}function bt(t){var n=t.parentNode;n&&n.removeChild(t)}function Oi(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Ve(t){var n=t.parentNode;n&&n.lastChild!==t&&n.appendChild(t)}function He(t){var n=t.parentNode;n&&n.firstChild!==t&&n.insertBefore(t,n.firstChild)}function xn(t,n){if(t.classList!==void 0)return t.classList.contains(n);var o=Ri(t);return o.length>0&&new RegExp("(^|\\s)"+n+"(\\s|$)").test(o)}function ht(t,n){if(t.classList!==void 0)for(var o=Ze(n),u=0,c=o.length;u<c;u++)t.classList.add(o[u]);else if(!xn(t,n)){var m=Ri(t);Fi(t,(m?m+" ":"")+n)}}function Bt(t,n){t.classList!==void 0?t.classList.remove(n):Fi(t,Xe((" "+Ri(t)+" ").replace(" "+n+" "," ")))}function Fi(t,n){t.className.baseVal===void 0?t.className=n:t.className.baseVal=n}function Ri(t){return t.correspondingElement&&(t=t.correspondingElement),t.className.baseVal===void 0?t.className:t.className.baseVal}function $t(t,n){"opacity"in t.style?t.style.opacity=n:"filter"in t.style&&Da(t,n)}function Da(t,n){var o=!1,u="DXImageTransform.Microsoft.Alpha";try{o=t.filters.item(u)}catch{if(n===1)return}n=Math.round(n*100),o?(o.Enabled=n!==100,o.Opacity=n):t.style.filter+=" progid:"+u+"(opacity="+n+")"}function Ii(t){for(var n=document.documentElement.style,o=0;o<t.length;o++)if(t[o]in n)return t[o];return!1}function Ke(t,n,o){var u=n||new ut(0,0);t.style[Cn]=(tt.ie3d?"translate("+u.x+"px,"+u.y+"px)":"translate3d("+u.x+"px,"+u.y+"px,0)")+(o?" scale("+o+")":"")}function Tt(t,n){t._leaflet_pos=n,tt.any3d?Ke(t,n):(t.style.left=n.x+"px",t.style.top=n.y+"px")}function qe(t){return t._leaflet_pos||new ut(0,0)}var _i,mi,kn;if("onselectstart"in document)_i=function(){lt(window,"selectstart",zt)},mi=function(){Ct(window,"selectstart",zt)};else{var gi=Ii(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);_i=function(){if(gi){var t=document.documentElement.style;kn=t[gi],t[gi]="none"}},mi=function(){gi&&(document.documentElement.style[gi]=kn,kn=void 0)}}function Mn(){lt(window,"dragstart",zt)}function wn(){Ct(window,"dragstart",zt)}var zi,En;function Bn(t){for(;t.tabIndex===-1;)t=t.parentNode;t.style&&(Ni(),zi=t,En=t.style.outlineStyle,t.style.outlineStyle="none",lt(window,"keydown",Ni))}function Ni(){zi&&(zi.style.outlineStyle=En,zi=void 0,En=void 0,Ct(window,"keydown",Ni))}function kr(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function Pn(t){var n=t.getBoundingClientRect();return{x:n.width/t.offsetWidth||1,y:n.height/t.offsetHeight||1,boundingClientRect:n}}var Sa={__proto__:null,TRANSFORM:Cn,TRANSITION:pi,TRANSITION_END:Cr,get:xr,getStyle:fi,create:gt,remove:bt,empty:Oi,toFront:Ve,toBack:He,hasClass:xn,addClass:ht,removeClass:Bt,setClass:Fi,getClass:Ri,setOpacity:$t,testProp:Ii,setTransform:Ke,setPosition:Tt,getPosition:qe,get disableTextSelection(){return _i},get enableTextSelection(){return mi},disableImageDrag:Mn,enableImageDrag:wn,preventOutline:Bn,restoreOutline:Ni,getSizedParentNode:kr,getScale:Pn};function lt(t,n,o,u){if(n&&typeof n=="object")for(var c in n)Dn(t,c,n[c],o);else{n=Ze(n);for(var m=0,M=n.length;m<M;m++)Dn(t,n[m],o,u)}return this}var ye="_leaflet_events";function Ct(t,n,o,u){if(arguments.length===1)Mr(t),delete t[ye];else if(n&&typeof n=="object")for(var c in n)Sn(t,c,n[c],o);else if(n=Ze(n),arguments.length===2)Mr(t,function(O){return un(n,O)!==-1});else for(var m=0,M=n.length;m<M;m++)Sn(t,n[m],o,u);return this}function Mr(t,n){for(var o in t[ye]){var u=o.split(/\d/)[0];(!n||n(u))&&Sn(t,u,null,null,o)}}var Tn={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function Dn(t,n,o,u){var c=n+F(o)+(u?"_"+F(u):"");if(t[ye]&&t[ye][c])return this;var m=function(O){return o.call(u||t,O||window.event)},M=m;!tt.touchNative&&tt.pointer&&n.indexOf("touch")===0?m=Ca(t,n,m):tt.touch&&n==="dblclick"?m=Pa(t,m):"addEventListener"in t?n==="touchstart"||n==="touchmove"||n==="wheel"||n==="mousewheel"?t.addEventListener(Tn[n]||n,m,tt.passiveEvents?{passive:!1}:!1):n==="mouseenter"||n==="mouseleave"?(m=function(O){O=O||window.event,On(t,O)&&M(O)},t.addEventListener(Tn[n],m,!1)):t.addEventListener(n,M,!1):t.attachEvent("on"+n,m),t[ye]=t[ye]||{},t[ye][c]=m}function Sn(t,n,o,u,c){c=c||n+F(o)+(u?"_"+F(u):"");var m=t[ye]&&t[ye][c];if(!m)return this;!tt.touchNative&&tt.pointer&&n.indexOf("touch")===0?xa(t,n,m):tt.touch&&n==="dblclick"?Ta(t,m):"removeEventListener"in t?t.removeEventListener(Tn[n]||n,m,!1):t.detachEvent("on"+n,m),t[ye][c]=null}function ce(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function An(t){return Dn(t,"wheel",ce),this}function yi(t){return lt(t,"mousedown touchstart dblclick contextmenu",ce),t._leaflet_disable_click=!0,this}function zt(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function We(t){return zt(t),ce(t),this}function wr(t){if(t.composedPath)return t.composedPath();for(var n=[],o=t.target;o;)n.push(o),o=o.parentNode;return n}function Er(t,n){if(!n)return new ut(t.clientX,t.clientY);var o=Pn(n),u=o.boundingClientRect;return new ut((t.clientX-u.left)/o.x-n.clientLeft,(t.clientY-u.top)/o.y-n.clientTop)}var Aa=tt.linux&&tt.chrome?window.devicePixelRatio:tt.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Br(t){return tt.edge?t.wheelDeltaY/2:t.deltaY&&t.deltaMode===0?-t.deltaY/Aa:t.deltaY&&t.deltaMode===1?-t.deltaY*20:t.deltaY&&t.deltaMode===2?-t.deltaY*60:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?-t.detail*20:t.detail?t.detail/-32765*60:0}function On(t,n){var o=n.relatedTarget;if(!o)return!0;try{for(;o&&o!==t;)o=o.parentNode}catch{return!1}return o!==t}var Oa={__proto__:null,on:lt,off:Ct,stopPropagation:ce,disableScrollPropagation:An,disableClickPropagation:yi,preventDefault:zt,stop:We,getPropagationPath:wr,getMousePosition:Er,getWheelDelta:Br,isExternalTarget:On,addListener:lt,removeListener:Ct},vi=ci.extend({run:function(t,n,o,u){this.stop(),this._el=t,this._inProgress=!0,this._duration=o||.25,this._easeOutPower=1/Math.max(u||.5,.2),this._startPos=qe(t),this._offset=n.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=Ut(this._animate,this),this._step()},_step:function(t){var n=+new Date-this._startTime,o=this._duration*1e3;n<o?this._runFrame(this._easeOut(n/o),t):(this._runFrame(1),this._complete())},_runFrame:function(t,n){var o=this._startPos.add(this._offset.multiplyBy(t));n&&o._round(),Tt(this._el,o),this.fire("step")},_complete:function(){Nt(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),_t=ci.extend({options:{crs:dn,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,n){n=wt(this,n),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=W(this._onResize,this),this._initEvents(),n.maxBounds&&this.setMaxBounds(n.maxBounds),n.zoom!==void 0&&(this._zoom=this._limitZoom(n.zoom)),n.center&&n.zoom!==void 0&&this.setView(ft(n.center),n.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=pi&&tt.any3d&&!tt.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),lt(this._proxy,Cr,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,n,o){if(n=n===void 0?this._zoom:this._limitZoom(n),t=this._limitCenter(ft(t),n,this.options.maxBounds),o=o||{},this._stop(),this._loaded&&!o.reset&&o!==!0){o.animate!==void 0&&(o.zoom=U({animate:o.animate},o.zoom),o.pan=U({animate:o.animate,duration:o.duration},o.pan));var u=this._zoom!==n?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,n,o.zoom):this._tryAnimatedPan(t,o.pan);if(u)return clearTimeout(this._sizeTimer),this}return this._resetView(t,n,o.pan&&o.pan.noMoveStart),this},setZoom:function(t,n){return this._loaded?this.setView(this.getCenter(),t,{zoom:n}):(this._zoom=t,this)},zoomIn:function(t,n){return t=t||(tt.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,n)},zoomOut:function(t,n){return t=t||(tt.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,n)},setZoomAround:function(t,n,o){var u=this.getZoomScale(n),c=this.getSize().divideBy(2),m=t instanceof ut?t:this.latLngToContainerPoint(t),M=m.subtract(c).multiplyBy(1-1/u),O=this.containerPointToLatLng(c.add(M));return this.setView(O,n,{zoom:o})},_getBoundsCenterZoom:function(t,n){n=n||{},t=t.getBounds?t.getBounds():Pt(t);var o=at(n.paddingTopLeft||n.padding||[0,0]),u=at(n.paddingBottomRight||n.padding||[0,0]),c=this.getBoundsZoom(t,!1,o.add(u));if(c=typeof n.maxZoom=="number"?Math.min(n.maxZoom,c):c,c===1/0)return{center:t.getCenter(),zoom:c};var m=u.subtract(o).divideBy(2),M=this.project(t.getSouthWest(),c),O=this.project(t.getNorthEast(),c),Z=this.unproject(M.add(O).divideBy(2).add(m),c);return{center:Z,zoom:c}},fitBounds:function(t,n){if(t=Pt(t),!t.isValid())throw new Error("Bounds are not valid.");var o=this._getBoundsCenterZoom(t,n);return this.setView(o.center,o.zoom,n)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,n){return this.setView(t,this._zoom,{pan:n})},panBy:function(t,n){if(t=at(t).round(),n=n||{},!t.x&&!t.y)return this.fire("moveend");if(n.animate!==!0&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new vi,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),n.noMoveStart||this.fire("movestart"),n.animate!==!1){ht(this._mapPane,"leaflet-pan-anim");var o=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,o,n.duration||.25,n.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,n,o){if(o=o||{},o.animate===!1||!tt.any3d)return this.setView(t,n,o);this._stop();var u=this.project(this.getCenter()),c=this.project(t),m=this.getSize(),M=this._zoom;t=ft(t),n=n===void 0?M:n;var O=Math.max(m.x,m.y),Z=O*this.getZoomScale(M,n),K=c.distanceTo(u)||1,Y=1.42,nt=Y*Y;function dt(Dt){var an=Dt?-1:1,Qr=Dt?Z:O,ta=Z*Z-O*O+an*nt*nt*K*K,Wa=2*Qr*nt*K,se=ta/Wa,ea=Math.sqrt(se*se+1)-se,on=ea<1e-9?-18:Math.log(ea);return on}function jt(Dt){return(Math.exp(Dt)-Math.exp(-Dt))/2}function Rt(Dt){return(Math.exp(Dt)+Math.exp(-Dt))/2}function oe(Dt){return jt(Dt)/Rt(Dt)}var Kt=dt(0);function Ne(Dt){return O*(Rt(Kt)/Rt(Kt+Y*Dt))}function Be(Dt){return O*(Rt(Kt)*oe(Kt+Y*Dt)-jt(Kt))/nt}function wi(Dt){return 1-Math.pow(1-Dt,1.5)}var Kn=Date.now(),Ei=(dt(1)-Kt)/Y,$r=o.duration?1e3*o.duration:1e3*Ei*.8;function Bi(){var Dt=(Date.now()-Kn)/$r,an=wi(Dt)*Ei;Dt<=1?(this._flyToFrame=Ut(Bi,this),this._move(this.unproject(u.add(c.subtract(u).multiplyBy(Be(an)/K)),M),this.getScaleZoom(O/Ne(an),M),{flyTo:!0})):this._move(t,n)._moveEnd(!0)}return this._moveStart(!0,o.noMoveStart),Bi.call(this),this},flyToBounds:function(t,n){var o=this._getBoundsCenterZoom(t,n);return this.flyTo(o.center,o.zoom,n)},setMaxBounds:function(t){return t=Pt(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var n=this.options.minZoom;return this.options.minZoom=t,this._loaded&&n!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var n=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&n!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,n){this._enforcingBounds=!0;var o=this.getCenter(),u=this._limitCenter(o,this._zoom,Pt(t));return o.equals(u)||this.panTo(u,n),this._enforcingBounds=!1,this},panInside:function(t,n){n=n||{};var o=at(n.paddingTopLeft||n.padding||[0,0]),u=at(n.paddingBottomRight||n.padding||[0,0]),c=this.project(this.getCenter()),m=this.project(t),M=this.getPixelBounds(),O=It([M.min.add(o),M.max.subtract(u)]),Z=O.getSize();if(!O.contains(m)){this._enforcingBounds=!0;var K=m.subtract(O.getCenter()),Y=O.extend(m).getSize().subtract(Z);c.x+=K.x<0?-Y.x:Y.x,c.y+=K.y<0?-Y.y:Y.y,this.panTo(this.unproject(c),n),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=U({animate:!1,pan:!0},t===!0?{animate:!0}:t);var n=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var o=this.getSize(),u=n.divideBy(2).round(),c=o.divideBy(2).round(),m=u.subtract(c);return!m.x&&!m.y?this:(t.animate&&t.pan?this.panBy(m):(t.pan&&this._rawPanBy(m),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(W(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:n,newSize:o}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=U({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var n=W(this._handleGeolocationResponse,this),o=W(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(n,o,t):navigator.geolocation.getCurrentPosition(n,o,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var n=t.code,o=t.message||(n===1?"permission denied":n===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:n,message:"Geolocation error: "+o+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var n=t.coords.latitude,o=t.coords.longitude,u=new vt(n,o),c=u.toBounds(t.coords.accuracy*2),m=this._locateOptions;if(m.setView){var M=this.getBoundsZoom(c);this.setView(u,m.maxZoom?Math.min(M,m.maxZoom):M)}var O={latlng:u,bounds:c,timestamp:t.timestamp};for(var Z in t.coords)typeof t.coords[Z]=="number"&&(O[Z]=t.coords[Z]);this.fire("locationfound",O)}},addHandler:function(t,n){if(!n)return this;var o=this[t]=new n(this);return this._handlers.push(o),this.options[t]&&o.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),bt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(Nt(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var t;for(t in this._layers)this._layers[t].remove();for(t in this._panes)bt(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,n){var o="leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),u=gt("div",o,n||this._mapPane);return t&&(this._panes[t]=u),u},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds(),n=this.unproject(t.getBottomLeft()),o=this.unproject(t.getTopRight());return new Vt(n,o)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,n,o){t=Pt(t),o=at(o||[0,0]);var u=this.getZoom()||0,c=this.getMinZoom(),m=this.getMaxZoom(),M=t.getNorthWest(),O=t.getSouthEast(),Z=this.getSize().subtract(o),K=It(this.project(O,u),this.project(M,u)).getSize(),Y=tt.any3d?this.options.zoomSnap:1,nt=Z.x/K.x,dt=Z.y/K.y,jt=n?Math.max(nt,dt):Math.min(nt,dt);return u=this.getScaleZoom(jt,u),Y&&(u=Math.round(u/(Y/100))*(Y/100),u=n?Math.ceil(u/Y)*Y:Math.floor(u/Y)*Y),Math.max(c,Math.min(m,u))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new ut(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,n){var o=this._getTopLeftPoint(t,n);return new Et(o,o.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(t===void 0?this.getZoom():t)},getPane:function(t){return typeof t=="string"?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,n){var o=this.options.crs;return n=n===void 0?this._zoom:n,o.scale(t)/o.scale(n)},getScaleZoom:function(t,n){var o=this.options.crs;n=n===void 0?this._zoom:n;var u=o.zoom(t*o.scale(n));return isNaN(u)?1/0:u},project:function(t,n){return n=n===void 0?this._zoom:n,this.options.crs.latLngToPoint(ft(t),n)},unproject:function(t,n){return n=n===void 0?this._zoom:n,this.options.crs.pointToLatLng(at(t),n)},layerPointToLatLng:function(t){var n=at(t).add(this.getPixelOrigin());return this.unproject(n)},latLngToLayerPoint:function(t){var n=this.project(ft(t))._round();return n._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(ft(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(Pt(t))},distance:function(t,n){return this.options.crs.distance(ft(t),ft(n))},containerPointToLayerPoint:function(t){return at(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return at(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var n=this.containerPointToLayerPoint(at(t));return this.layerPointToLatLng(n)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(ft(t)))},mouseEventToContainerPoint:function(t){return Er(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var n=this._container=xr(t);if(n){if(n._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");lt(n,"scroll",this._onScroll,this),this._containerId=F(n)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&tt.any3d,ht(t,"leaflet-container"+(tt.touch?" leaflet-touch":"")+(tt.retina?" leaflet-retina":"")+(tt.ielt9?" leaflet-oldie":"")+(tt.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var n=fi(t,"position");n!=="absolute"&&n!=="relative"&&n!=="fixed"&&n!=="sticky"&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Tt(this._mapPane,new ut(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(ht(t.markerPane,"leaflet-zoom-hide"),ht(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,n,o){Tt(this._mapPane,new ut(0,0));var u=!this._loaded;this._loaded=!0,n=this._limitZoom(n),this.fire("viewprereset");var c=this._zoom!==n;this._moveStart(c,o)._move(t,n)._moveEnd(c),this.fire("viewreset"),u&&this.fire("load")},_moveStart:function(t,n){return t&&this.fire("zoomstart"),n||this.fire("movestart"),this},_move:function(t,n,o,u){n===void 0&&(n=this._zoom);var c=this._zoom!==n;return this._zoom=n,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),u?o&&o.pinch&&this.fire("zoom",o):((c||o&&o.pinch)&&this.fire("zoom",o),this.fire("move",o)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return Nt(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){Tt(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[F(this._container)]=this;var n=t?Ct:lt;n(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&n(window,"resize",this._onResize,this),tt.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){Nt(this._resizeRequest),this._resizeRequest=Ut(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,n){for(var o=[],u,c=n==="mouseout"||n==="mouseover",m=t.target||t.srcElement,M=!1;m;){if(u=this._targets[F(m)],u&&(n==="click"||n==="preclick")&&this._draggableMoved(u)){M=!0;break}if(u&&u.listens(n,!0)&&(c&&!On(m,t)||(o.push(u),c))||m===this._container)break;m=m.parentNode}return!o.length&&!M&&!c&&this.listens(n,!0)&&(o=[this]),o},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var n=t.target||t.srcElement;if(!(!this._loaded||n._leaflet_disable_events||t.type==="click"&&this._isClickDisabled(n))){var o=t.type;o==="mousedown"&&Bn(n),this._fireDOMEvent(t,o)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,n,o){if(t.type==="click"){var u=U({},t);u.type="preclick",this._fireDOMEvent(u,u.type,o)}var c=this._findEventTargets(t,n);if(o){for(var m=[],M=0;M<o.length;M++)o[M].listens(n,!0)&&m.push(o[M]);c=m.concat(c)}if(c.length){n==="contextmenu"&&zt(t);var O=c[0],Z={originalEvent:t};if(t.type!=="keypress"&&t.type!=="keydown"&&t.type!=="keyup"){var K=O.getLatLng&&(!O._radius||O._radius<=10);Z.containerPoint=K?this.latLngToContainerPoint(O.getLatLng()):this.mouseEventToContainerPoint(t),Z.layerPoint=this.containerPointToLayerPoint(Z.containerPoint),Z.latlng=K?O.getLatLng():this.layerPointToLatLng(Z.layerPoint)}for(M=0;M<c.length;M++)if(c[M].fire(n,Z,!0),Z.originalEvent._stopped||c[M].options.bubblingMouseEvents===!1&&un(this._mouseEvents,n)!==-1)return}},_draggableMoved:function(t){return t=t.dragging&&t.dragging.enabled()?t:this,t.dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,n=this._handlers.length;t<n;t++)this._handlers[t].disable()},whenReady:function(t,n){return this._loaded?t.call(n||this,{target:this}):this.on("load",t,n),this},_getMapPanePos:function(){return qe(this._mapPane)||new ut(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,n){var o=t&&n!==void 0?this._getNewPixelOrigin(t,n):this.getPixelOrigin();return o.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,n){var o=this.getSize()._divideBy(2);return this.project(t,n)._subtract(o)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,n,o){var u=this._getNewPixelOrigin(o,n);return this.project(t,n)._subtract(u)},_latLngBoundsToNewLayerBounds:function(t,n,o){var u=this._getNewPixelOrigin(o,n);return It([this.project(t.getSouthWest(),n)._subtract(u),this.project(t.getNorthWest(),n)._subtract(u),this.project(t.getSouthEast(),n)._subtract(u),this.project(t.getNorthEast(),n)._subtract(u)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,n,o){if(!o)return t;var u=this.project(t,n),c=this.getSize().divideBy(2),m=new Et(u.subtract(c),u.add(c)),M=this._getBoundsOffset(m,o,n);return Math.abs(M.x)<=1&&Math.abs(M.y)<=1?t:this.unproject(u.add(M),n)},_limitOffset:function(t,n){if(!n)return t;var o=this.getPixelBounds(),u=new Et(o.min.add(t),o.max.add(t));return t.add(this._getBoundsOffset(u,n))},_getBoundsOffset:function(t,n,o){var u=It(this.project(n.getNorthEast(),o),this.project(n.getSouthWest(),o)),c=u.min.subtract(t.min),m=u.max.subtract(t.max),M=this._rebound(c.x,-m.x),O=this._rebound(c.y,-m.y);return new ut(M,O)},_rebound:function(t,n){return t+n>0?Math.round(t-n)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(n))},_limitZoom:function(t){var n=this.getMinZoom(),o=this.getMaxZoom(),u=tt.any3d?this.options.zoomSnap:1;return u&&(t=Math.round(t/u)*u),Math.max(n,Math.min(o,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){Bt(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,n){var o=this._getCenterOffset(t)._trunc();return(n&&n.animate)!==!0&&!this.getSize().contains(o)?!1:(this.panBy(o,n),!0)},_createAnimProxy:function(){var t=this._proxy=gt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(n){var o=Cn,u=this._proxy.style[o];Ke(this._proxy,this.project(n.center,n.zoom),this.getZoomScale(n.zoom,1)),u===this._proxy.style[o]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){bt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),n=this.getZoom();Ke(this._proxy,this.project(t,n),this.getZoomScale(n,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,n,o){if(this._animatingZoom)return!0;if(o=o||{},!this._zoomAnimated||o.animate===!1||this._nothingToAnimate()||Math.abs(n-this._zoom)>this.options.zoomAnimationThreshold)return!1;var u=this.getZoomScale(n),c=this._getCenterOffset(t)._divideBy(1-1/u);return o.animate!==!0&&!this.getSize().contains(c)?!1:(Ut(function(){this._moveStart(!0,o.noMoveStart||!1)._animateZoom(t,n,!0)},this),!0)},_animateZoom:function(t,n,o,u){this._mapPane&&(o&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=n,ht(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:n,noUpdate:u}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(W(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&Bt(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Fa(t,n){return new _t(t,n)}var Qt=he.extend({options:{position:"topright"},initialize:function(t){wt(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var n=this._map;return n&&n.removeControl(this),this.options.position=t,n&&n.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var n=this._container=this.onAdd(t),o=this.getPosition(),u=t._controlCorners[o];return ht(n,"leaflet-control"),o.indexOf("bottom")!==-1?u.insertBefore(n,u.firstChild):u.appendChild(n),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(bt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),pt=function(t){return new Qt(t)};_t.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},n="leaflet-",o=this._controlContainer=gt("div",n+"control-container",this._container);function u(c,m){var M=n+c+" "+n+m;t[c+m]=gt("div",M,o)}u("top","left"),u("top","right"),u("bottom","left"),u("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)bt(this._controlCorners[t]);bt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var Gi=Qt.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,n,o,u){return o<u?-1:u<o?1:0}},initialize:function(t,n,o){wt(this,o),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var u in t)this._addLayer(t[u],u);for(u in n)this._addLayer(n[u],u,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var n=0;n<this._layers.length;n++)this._layers[n].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return Qt.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,n){return this._addLayer(t,n),this._map?this._update():this},addOverlay:function(t,n){return this._addLayer(t,n,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var n=this._getLayer(F(t));return n&&this._layers.splice(this._layers.indexOf(n),1),this._map?this._update():this},expand:function(){ht(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(ht(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):Bt(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return Bt(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",n=this._container=gt("div",t),o=this.options.collapsed;n.setAttribute("aria-haspopup",!0),yi(n),An(n);var u=this._section=gt("section",t+"-list");o&&(this._map.on("click",this.collapse,this),lt(n,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var c=this._layersLink=gt("a",t+"-toggle",n);c.href="#",c.title="Layers",c.setAttribute("role","button"),lt(c,{keydown:function(m){m.keyCode===13&&this._expandSafely()},click:function(m){zt(m),this._expandSafely()}},this),o||this.expand(),this._baseLayersList=gt("div",t+"-base",u),this._separator=gt("div",t+"-separator",u),this._overlaysList=gt("div",t+"-overlays",u),n.appendChild(u)},_getLayer:function(t){for(var n=0;n<this._layers.length;n++)if(this._layers[n]&&F(this._layers[n].layer)===t)return this._layers[n]},_addLayer:function(t,n,o){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:n,overlay:o}),this.options.sortLayers&&this._layers.sort(W(function(u,c){return this.options.sortFunction(u.layer,c.layer,u.name,c.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;Oi(this._baseLayersList),Oi(this._overlaysList),this._layerControlInputs=[];var t,n,o,u,c=0;for(o=0;o<this._layers.length;o++)u=this._layers[o],this._addItem(u),n=n||u.overlay,t=t||!u.overlay,c+=u.overlay?0:1;return this.options.hideSingleBase&&(t=t&&c>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=n&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var n=this._getLayer(F(t.target)),o=n.overlay?t.type==="add"?"overlayadd":"overlayremove":t.type==="add"?"baselayerchange":null;o&&this._map.fire(o,n)},_createRadioElement:function(t,n){var o='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(n?' checked="checked"':"")+"/>",u=document.createElement("div");return u.innerHTML=o,u.firstChild},_addItem:function(t){var n=document.createElement("label"),o=this._map.hasLayer(t.layer),u;t.overlay?(u=document.createElement("input"),u.type="checkbox",u.className="leaflet-control-layers-selector",u.defaultChecked=o):u=this._createRadioElement("leaflet-base-layers_"+F(this),o),this._layerControlInputs.push(u),u.layerId=F(t.layer),lt(u,"click",this._onInputClick,this);var c=document.createElement("span");c.innerHTML=" "+t.name;var m=document.createElement("span");n.appendChild(m),m.appendChild(u),m.appendChild(c);var M=t.overlay?this._overlaysList:this._baseLayersList;return M.appendChild(n),this._checkDisabledLayers(),n},_onInputClick:function(){if(!this._preventClick){var t=this._layerControlInputs,n,o,u=[],c=[];this._handlingClick=!0;for(var m=t.length-1;m>=0;m--)n=t[m],o=this._getLayer(n.layerId).layer,n.checked?u.push(o):n.checked||c.push(o);for(m=0;m<c.length;m++)this._map.hasLayer(c[m])&&this._map.removeLayer(c[m]);for(m=0;m<u.length;m++)this._map.hasLayer(u[m])||this._map.addLayer(u[m]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t=this._layerControlInputs,n,o,u=this._map.getZoom(),c=t.length-1;c>=0;c--)n=t[c],o=this._getLayer(n.layerId).layer,n.disabled=o.options.minZoom!==void 0&&u<o.options.minZoom||o.options.maxZoom!==void 0&&u>o.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,lt(t,"click",zt),this.expand();var n=this;setTimeout(function(){Ct(t,"click",zt),n._preventClick=!1})}}),Zi=function(t,n,o){return new Gi(t,n,o)},Fn=Qt.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var n="leaflet-control-zoom",o=gt("div",n+" leaflet-bar"),u=this.options;return this._zoomInButton=this._createButton(u.zoomInText,u.zoomInTitle,n+"-in",o,this._zoomIn),this._zoomOutButton=this._createButton(u.zoomOutText,u.zoomOutTitle,n+"-out",o,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),o},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,n,o,u,c){var m=gt("a",o,u);return m.innerHTML=t,m.href="#",m.title=n,m.setAttribute("role","button"),m.setAttribute("aria-label",n),yi(m),lt(m,"click",We),lt(m,"click",c,this),lt(m,"click",this._refocusOnMap,this),m},_updateDisabled:function(){var t=this._map,n="leaflet-disabled";Bt(this._zoomInButton,n),Bt(this._zoomOutButton,n),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(ht(this._zoomOutButton,n),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(ht(this._zoomInButton,n),this._zoomInButton.setAttribute("aria-disabled","true"))}});_t.mergeOptions({zoomControl:!0}),_t.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Fn,this.addControl(this.zoomControl))});var Pr=function(t){return new Fn(t)},Tr=Qt.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var n="leaflet-control-scale",o=gt("div",n),u=this.options;return this._addScales(u,n+"-line",o),t.on(u.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),o},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,n,o){t.metric&&(this._mScale=gt("div",n,o)),t.imperial&&(this._iScale=gt("div",n,o))},_update:function(){var t=this._map,n=t.getSize().y/2,o=t.distance(t.containerPointToLatLng([0,n]),t.containerPointToLatLng([this.options.maxWidth,n]));this._updateScales(o)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var n=this._getRoundNum(t),o=n<1e3?n+" m":n/1e3+" km";this._updateScale(this._mScale,o,n/t)},_updateImperial:function(t){var n=t*3.2808399,o,u,c;n>5280?(o=n/5280,u=this._getRoundNum(o),this._updateScale(this._iScale,u+" mi",u/o)):(c=this._getRoundNum(n),this._updateScale(this._iScale,c+" ft",c/n))},_updateScale:function(t,n,o){t.style.width=Math.round(this.options.maxWidth*o)+"px",t.innerHTML=n},_getRoundNum:function(t){var n=Math.pow(10,(Math.floor(t)+"").length-1),o=t/n;return o=o>=10?10:o>=5?5:o>=3?3:o>=2?2:1,n*o}}),Rn=function(t){return new Tr(t)},ji='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',In=Qt.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(tt.inlineSvg?ji+" ":"")+"Leaflet</a>"},initialize:function(t){wt(this,t),this._attributions={}},onAdd:function(t){t.attributionControl=this,this._container=gt("div","leaflet-control-attribution"),yi(this._container);for(var n in t._layers)t._layers[n].getAttribution&&this.addAttribution(t._layers[n].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var n in this._attributions)this._attributions[n]&&t.push(n);var o=[];this.options.prefix&&o.push(this.options.prefix),t.length&&o.push(t.join(", ")),this._container.innerHTML=o.join(' <span aria-hidden="true">|</span> ')}}});_t.mergeOptions({attributionControl:!0}),_t.addInitHook(function(){this.options.attributionControl&&new In().addTo(this)});var De=function(t){return new In(t)};Qt.Layers=Gi,Qt.Zoom=Fn,Qt.Scale=Tr,Qt.Attribution=In,pt.layers=Zi,pt.zoom=Pr,pt.scale=Rn,pt.attribution=De;var de=he.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});de.addTo=function(t,n){return t.addHandler(n,this),this};var ei={Events:Yt},Dr=tt.touch?"touchstart mousedown":"mousedown",Se=ci.extend({options:{clickTolerance:3},initialize:function(t,n,o,u){wt(this,u),this._element=t,this._dragStartTarget=n||t,this._preventOutline=o},enable:function(){this._enabled||(lt(this._dragStartTarget,Dr,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Se._dragging===this&&this.finishDrag(!0),Ct(this._dragStartTarget,Dr,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(this._enabled&&(this._moved=!1,!xn(this._element,"leaflet-zoom-anim"))){if(t.touches&&t.touches.length!==1){Se._dragging===this&&this.finishDrag();return}if(!(Se._dragging||t.shiftKey||t.which!==1&&t.button!==1&&!t.touches)&&(Se._dragging=this,this._preventOutline&&Bn(this._element),Mn(),_i(),!this._moving)){this.fire("down");var n=t.touches?t.touches[0]:t,o=kr(this._element);this._startPoint=new ut(n.clientX,n.clientY),this._startPos=qe(this._element),this._parentScale=Pn(o);var u=t.type==="mousedown";lt(document,u?"mousemove":"touchmove",this._onMove,this),lt(document,u?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var n=t.touches&&t.touches.length===1?t.touches[0]:t,o=new ut(n.clientX,n.clientY)._subtract(this._startPoint);!o.x&&!o.y||Math.abs(o.x)+Math.abs(o.y)<this.options.clickTolerance||(o.x/=this._parentScale.x,o.y/=this._parentScale.y,zt(t),this._moved||(this.fire("dragstart"),this._moved=!0,ht(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),ht(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(o),this._moving=!0,this._lastEvent=t,this._updatePosition())}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),Tt(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){Bt(document.body,"leaflet-dragging"),this._lastTarget&&(Bt(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),Ct(document,"mousemove touchmove",this._onMove,this),Ct(document,"mouseup touchend touchcancel",this._onUp,this),wn(),mi();var n=this._moved&&this._moving;this._moving=!1,Se._dragging=!1,n&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function Sr(t,n,o){var u,c=[1,4,2,8],m,M,O,Z,K,Y,nt,dt;for(m=0,Y=t.length;m<Y;m++)t[m]._code=te(t[m],n);for(O=0;O<4;O++){for(nt=c[O],u=[],m=0,Y=t.length,M=Y-1;m<Y;M=m++)Z=t[m],K=t[M],Z._code&nt?K._code&nt||(dt=ke(K,Z,nt,n,o),dt._code=te(dt,n),u.push(dt)):(K._code&nt&&(dt=ke(K,Z,nt,n,o),dt._code=te(dt,n),u.push(dt)),u.push(Z));t=u}return t}function Ar(t,n){var o,u,c,m,M,O,Z,K,Y;if(!t||t.length===0)throw new Error("latlngs not passed");ie(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var nt=ft([0,0]),dt=Pt(t),jt=dt.getNorthWest().distanceTo(dt.getSouthWest())*dt.getNorthEast().distanceTo(dt.getNorthWest());jt<1700&&(nt=zn(t));var Rt=t.length,oe=[];for(o=0;o<Rt;o++){var Kt=ft(t[o]);oe.push(n.project(ft([Kt.lat-nt.lat,Kt.lng-nt.lng])))}for(O=Z=K=0,o=0,u=Rt-1;o<Rt;u=o++)c=oe[o],m=oe[u],M=c.y*m.x-m.y*c.x,Z+=(c.x+m.x)*M,K+=(c.y+m.y)*M,O+=M*3;O===0?Y=oe[0]:Y=[Z/O,K/O];var Ne=n.unproject(at(Y));return ft([Ne.lat+nt.lat,Ne.lng+nt.lng])}function zn(t){for(var n=0,o=0,u=0,c=0;c<t.length;c++){var m=ft(t[c]);n+=m.lat,o+=m.lng,u++}return ft([n/u,o/u])}var Ra={__proto__:null,clipPolygon:Sr,polygonCenter:Ar,centroid:zn};function Or(t,n){if(!n||!t.length)return t.slice();var o=n*n;return t=Zt(t,o),t=Ia(t,o),t}function Fr(t,n,o){return Math.sqrt(ee(t,n,o,!0))}function Rr(t,n,o){return ee(t,n,o)}function Ia(t,n){var o=t.length,u=typeof Uint8Array<"u"?Uint8Array:Array,c=new u(o);c[0]=c[o-1]=1,At(t,c,n,0,o-1);var m,M=[];for(m=0;m<o;m++)c[m]&&M.push(t[m]);return M}function At(t,n,o,u,c){var m=0,M,O,Z;for(O=u+1;O<=c-1;O++)Z=ee(t[O],t[u],t[c],!0),Z>m&&(M=O,m=Z);m>o&&(n[M]=1,At(t,n,o,u,M),At(t,n,o,M,c))}function Zt(t,n){for(var o=[t[0]],u=1,c=0,m=t.length;u<m;u++)Li(t[u],t[c])>n&&(o.push(t[u]),c=u);return c<m-1&&o.push(t[m-1]),o}var Nn;function za(t,n,o,u,c){var m=u?Nn:te(t,o),M=te(n,o),O,Z,K;for(Nn=M;;){if(!(m|M))return[t,n];if(m&M)return!1;O=m||M,Z=ke(t,n,O,o,c),K=te(Z,o),O===m?(t=Z,m=K):(n=Z,M=K)}}function ke(t,n,o,u,c){var m=n.x-t.x,M=n.y-t.y,O=u.min,Z=u.max,K,Y;return o&8?(K=t.x+m*(Z.y-t.y)/M,Y=Z.y):o&4?(K=t.x+m*(O.y-t.y)/M,Y=O.y):o&2?(K=Z.x,Y=t.y+M*(Z.x-t.x)/m):o&1&&(K=O.x,Y=t.y+M*(O.x-t.x)/m),new ut(K,Y,c)}function te(t,n){var o=0;return t.x<n.min.x?o|=1:t.x>n.max.x&&(o|=2),t.y<n.min.y?o|=4:t.y>n.max.y&&(o|=8),o}function Li(t,n){var o=n.x-t.x,u=n.y-t.y;return o*o+u*u}function ee(t,n,o,u){var c=n.x,m=n.y,M=o.x-c,O=o.y-m,Z=M*M+O*O,K;return Z>0&&(K=((t.x-c)*M+(t.y-m)*O)/Z,K>1?(c=o.x,m=o.y):K>0&&(c+=M*K,m+=O*K)),M=t.x-c,O=t.y-m,u?M*M+O*O:new ut(c,m)}function ie(t){return!le(t[0])||typeof t[0][0]!="object"&&typeof t[0][0]<"u"}function Ir(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),ie(t)}function Ui(t,n){var o,u,c,m,M,O,Z,K;if(!t||t.length===0)throw new Error("latlngs not passed");ie(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var Y=ft([0,0]),nt=Pt(t),dt=nt.getNorthWest().distanceTo(nt.getSouthWest())*nt.getNorthEast().distanceTo(nt.getNorthWest());dt<1700&&(Y=zn(t));var jt=t.length,Rt=[];for(o=0;o<jt;o++){var oe=ft(t[o]);Rt.push(n.project(ft([oe.lat-Y.lat,oe.lng-Y.lng])))}for(o=0,u=0;o<jt-1;o++)u+=Rt[o].distanceTo(Rt[o+1])/2;if(u===0)K=Rt[0];else for(o=0,m=0;o<jt-1;o++)if(M=Rt[o],O=Rt[o+1],c=M.distanceTo(O),m+=c,m>u){Z=(m-u)/c,K=[O.x-Z*(O.x-M.x),O.y-Z*(O.y-M.y)];break}var Kt=n.unproject(at(K));return ft([Kt.lat+Y.lat,Kt.lng+Y.lng])}var pe={__proto__:null,simplify:Or,pointToSegmentDistance:Fr,closestPointOnSegment:Rr,clipSegment:za,_getEdgeIntersection:ke,_getBitCode:te,_sqClosestPointOnSegment:ee,isFlat:ie,_flat:Ir,polylineCenter:Ui},Vi={project:function(t){return new ut(t.lng,t.lat)},unproject:function(t){return new vt(t.y,t.x)},bounds:new Et([-180,-90],[180,90])},ii={R:6378137,R_MINOR:6356752314245179e-9,bounds:new Et([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(t){var n=Math.PI/180,o=this.R,u=t.lat*n,c=this.R_MINOR/o,m=Math.sqrt(1-c*c),M=m*Math.sin(u),O=Math.tan(Math.PI/4-u/2)/Math.pow((1-M)/(1+M),m/2);return u=-o*Math.log(Math.max(O,1e-10)),new ut(t.lng*n*o,u)},unproject:function(t){for(var n=180/Math.PI,o=this.R,u=this.R_MINOR/o,c=Math.sqrt(1-u*u),m=Math.exp(-t.y/o),M=Math.PI/2-2*Math.atan(m),O=0,Z=.1,K;O<15&&Math.abs(Z)>1e-7;O++)K=c*Math.sin(M),K=Math.pow((1-K)/(1+K),c/2),Z=Math.PI/2-2*Math.atan(m*K)-M,M+=Z;return new vt(M*n,t.x*n/o)}},Na={__proto__:null,LonLat:Vi,Mercator:ii,SphericalMercator:cn},ve=U({},Pe,{code:"EPSG:3395",projection:ii,transformation:function(){var t=.5/(Math.PI*ii.R);return di(t,.5,-t,.5)}()}),fe=U({},Pe,{code:"EPSG:4326",projection:Vi,transformation:di(1/180,1,-1/180,.5)}),Hi=U({},Ce,{projection:Vi,transformation:di(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,n){var o=n.lng-t.lng,u=n.lat-t.lat;return Math.sqrt(o*o+u*u)},infinite:!0});Ce.Earth=Pe,Ce.EPSG3395=ve,Ce.EPSG3857=dn,Ce.EPSG900913=ca,Ce.EPSG4326=fe,Ce.Simple=Hi;var ne=ci.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[F(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[F(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var n=t.target;if(n.hasLayer(this)){if(this._map=n,this._zoomAnimated=n._zoomAnimated,this.getEvents){var o=this.getEvents();n.on(o,this),this.once("remove",function(){n.off(o,this)},this)}this.onAdd(n),this.fire("add"),n.fire("layeradd",{layer:this})}}});_t.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var n=F(t);return this._layers[n]?this:(this._layers[n]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t),this)},removeLayer:function(t){var n=F(t);return this._layers[n]?(this._loaded&&t.onRemove(this),delete this._layers[n],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return F(t)in this._layers},eachLayer:function(t,n){for(var o in this._layers)t.call(n,this._layers[o]);return this},_addLayers:function(t){t=t?le(t)?t:[t]:[];for(var n=0,o=t.length;n<o;n++)this.addLayer(t[n])},_addZoomLimit:function(t){(!isNaN(t.options.maxZoom)||!isNaN(t.options.minZoom))&&(this._zoomBoundLayers[F(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var n=F(t);this._zoomBoundLayers[n]&&(delete this._zoomBoundLayers[n],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,n=-1/0,o=this._getZoomSpan();for(var u in this._zoomBoundLayers){var c=this._zoomBoundLayers[u].options;t=c.minZoom===void 0?t:Math.min(t,c.minZoom),n=c.maxZoom===void 0?n:Math.max(n,c.maxZoom)}this._layersMaxZoom=n===-1/0?void 0:n,this._layersMinZoom=t===1/0?void 0:t,o!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var Ae=ne.extend({initialize:function(t,n){wt(this,n),this._layers={};var o,u;if(t)for(o=0,u=t.length;o<u;o++)this.addLayer(t[o])},addLayer:function(t){var n=this.getLayerId(t);return this._layers[n]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var n=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[n]&&this._map.removeLayer(this._layers[n]),delete this._layers[n],this},hasLayer:function(t){var n=typeof t=="number"?t:this.getLayerId(t);return n in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var n=Array.prototype.slice.call(arguments,1),o,u;for(o in this._layers)u=this._layers[o],u[t]&&u[t].apply(u,n);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,n){for(var o in this._layers)t.call(n,this._layers[o]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return F(t)}}),bi=function(t,n){return new Ae(t,n)},Me=Ae.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),Ae.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),Ae.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new Vt;for(var n in this._layers){var o=this._layers[n];t.extend(o.getBounds?o.getBounds():o.getLatLng())}return t}}),Ga=function(t,n){return new Me(t,n)},Oe=he.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){wt(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,n){var o=this._getIconUrl(t);if(!o){if(t==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var u=this._createImg(o,n&&n.tagName==="IMG"?n:null);return this._setIconStyles(u,t),(this.options.crossOrigin||this.options.crossOrigin==="")&&(u.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),u},_setIconStyles:function(t,n){var o=this.options,u=o[n+"Size"];typeof u=="number"&&(u=[u,u]);var c=at(u),m=at(n==="shadow"&&o.shadowAnchor||o.iconAnchor||c&&c.divideBy(2,!0));t.className="leaflet-marker-"+n+" "+(o.className||""),m&&(t.style.marginLeft=-m.x+"px",t.style.marginTop=-m.y+"px"),c&&(t.style.width=c.x+"px",t.style.height=c.y+"px")},_createImg:function(t,n){return n=n||document.createElement("img"),n.src=t,n},_getIconUrl:function(t){return tt.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function Za(t){return new Oe(t)}var Ci=Oe.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return typeof Ci.imagePath!="string"&&(Ci.imagePath=this._detectIconPath()),(this.options.imagePath||Ci.imagePath)+Oe.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var n=function(o,u,c){var m=u.exec(o);return m&&m[c]};return t=n(t,/^url\((['"])?(.+)\1\)$/,2),t&&n(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=gt("div","leaflet-default-icon-path",document.body),n=fi(t,"background-image")||fi(t,"backgroundImage");if(document.body.removeChild(t),n=this._stripUrl(n),n)return n;var o=document.querySelector('link[href$="leaflet.css"]');return o?o.href.substring(0,o.href.length-11-1):""}}),zr=de.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new Se(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),ht(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&Bt(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var n=this._marker,o=n._map,u=this._marker.options.autoPanSpeed,c=this._marker.options.autoPanPadding,m=qe(n._icon),M=o.getPixelBounds(),O=o.getPixelOrigin(),Z=It(M.min._subtract(O).add(c),M.max._subtract(O).subtract(c));if(!Z.contains(m)){var K=at((Math.max(Z.max.x,m.x)-Z.max.x)/(M.max.x-Z.max.x)-(Math.min(Z.min.x,m.x)-Z.min.x)/(M.min.x-Z.min.x),(Math.max(Z.max.y,m.y)-Z.max.y)/(M.max.y-Z.max.y)-(Math.min(Z.min.y,m.y)-Z.min.y)/(M.min.y-Z.min.y)).multiplyBy(u);o.panBy(K,{animate:!1}),this._draggable._newPos._add(K),this._draggable._startPos._add(K),Tt(n._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=Ut(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(Nt(this._panRequest),this._panRequest=Ut(this._adjustPan.bind(this,t)))},_onDrag:function(t){var n=this._marker,o=n._shadow,u=qe(n._icon),c=n._map.layerPointToLatLng(u);o&&Tt(o,u),n._latlng=c,t.latlng=c,t.oldLatLng=this._oldLatLng,n.fire("move",t).fire("drag",t)},_onDragEnd:function(t){Nt(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),Ki=ne.extend({options:{icon:new Ci,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,n){wt(this,n),this._latlng=ft(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var n=this._latlng;return this._latlng=ft(t),this.update(),this.fire("move",{oldLatLng:n,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,n="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),o=t.icon.createIcon(this._icon),u=!1;o!==this._icon&&(this._icon&&this._removeIcon(),u=!0,t.title&&(o.title=t.title),o.tagName==="IMG"&&(o.alt=t.alt||"")),ht(o,n),t.keyboard&&(o.tabIndex="0",o.setAttribute("role","button")),this._icon=o,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&lt(o,"focus",this._panOnFocus,this);var c=t.icon.createShadow(this._shadow),m=!1;c!==this._shadow&&(this._removeShadow(),m=!0),c&&(ht(c,n),c.alt=""),this._shadow=c,t.opacity<1&&this._updateOpacity(),u&&this.getPane().appendChild(this._icon),this._initInteraction(),c&&m&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&Ct(this._icon,"focus",this._panOnFocus,this),bt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&bt(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&Tt(this._icon,t),this._shadow&&Tt(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var n=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(n)},_initInteraction:function(){if(this.options.interactive&&(ht(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),zr)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new zr(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&$t(this._icon,t),this._shadow&&$t(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var n=this.options.icon.options,o=n.iconSize?at(n.iconSize):at(0,0),u=n.iconAnchor?at(n.iconAnchor):at(0,0);t.panInside(this._latlng,{paddingTopLeft:u,paddingBottomRight:o.subtract(u)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function Gn(t,n){return new Ki(t,n)}var Fe=ne.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return wt(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),qi=Fe.extend({options:{fill:!0,radius:10},initialize:function(t,n){wt(this,n),this._latlng=ft(t),this._radius=this.options.radius},setLatLng:function(t){var n=this._latlng;return this._latlng=ft(t),this.redraw(),this.fire("move",{oldLatLng:n,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var n=t&&t.radius||this._radius;return Fe.prototype.setStyle.call(this,t),this.setRadius(n),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,n=this._radiusY||t,o=this._clickTolerance(),u=[t+o,n+o];this._pxBounds=new Et(this._point.subtract(u),this._point.add(u))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Nr(t,n){return new qi(t,n)}var we=qi.extend({initialize:function(t,n,o){if(typeof n=="number"&&(n=U({},o,{radius:n})),wt(this,n),this._latlng=ft(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new Vt(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:Fe.prototype.setStyle,_project:function(){var t=this._latlng.lng,n=this._latlng.lat,o=this._map,u=o.options.crs;if(u.distance===Pe.distance){var c=Math.PI/180,m=this._mRadius/Pe.R/c,M=o.project([n+m,t]),O=o.project([n-m,t]),Z=M.add(O).divideBy(2),K=o.unproject(Z).lat,Y=Math.acos((Math.cos(m*c)-Math.sin(n*c)*Math.sin(K*c))/(Math.cos(n*c)*Math.cos(K*c)))/c;(isNaN(Y)||Y===0)&&(Y=m/Math.cos(Math.PI/180*n)),this._point=Z.subtract(o.getPixelOrigin()),this._radius=isNaN(Y)?0:Z.x-o.project([K,t-Y]).x,this._radiusY=Z.y-M.y}else{var nt=u.unproject(u.project(this._latlng).subtract([this._mRadius,0]));this._point=o.latLngToLayerPoint(this._latlng),this._radius=this._point.x-o.latLngToLayerPoint(nt).x}this._updateBounds()}});function Gr(t,n,o){return new we(t,n,o)}var Ee=Fe.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,n){wt(this,n),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var n=1/0,o=null,u=ee,c,m,M=0,O=this._parts.length;M<O;M++)for(var Z=this._parts[M],K=1,Y=Z.length;K<Y;K++){c=Z[K-1],m=Z[K];var nt=u(t,c,m,!0);nt<n&&(n=nt,o=u(t,c,m))}return o&&(o.distance=Math.sqrt(n)),o},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Ui(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,n){return n=n||this._defaultShape(),t=ft(t),n.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new Vt,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return ie(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var n=[],o=ie(t),u=0,c=t.length;u<c;u++)o?(n[u]=ft(t[u]),this._bounds.extend(n[u])):n[u]=this._convertLatLngs(t[u]);return n},_project:function(){var t=new Et;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),n=new ut(t,t);this._rawPxBounds&&(this._pxBounds=new Et([this._rawPxBounds.min.subtract(n),this._rawPxBounds.max.add(n)]))},_projectLatlngs:function(t,n,o){var u=t[0]instanceof vt,c=t.length,m,M;if(u){for(M=[],m=0;m<c;m++)M[m]=this._map.latLngToLayerPoint(t[m]),o.extend(M[m]);n.push(M)}else for(m=0;m<c;m++)this._projectLatlngs(t[m],n,o)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}var n=this._parts,o,u,c,m,M,O,Z;for(o=0,c=0,m=this._rings.length;o<m;o++)for(Z=this._rings[o],u=0,M=Z.length;u<M-1;u++)O=za(Z[u],Z[u+1],t,u,!0),O&&(n[c]=n[c]||[],n[c].push(O[0]),(O[1]!==Z[u+1]||u===M-2)&&(n[c].push(O[1]),c++))}},_simplifyPoints:function(){for(var t=this._parts,n=this.options.smoothFactor,o=0,u=t.length;o<u;o++)t[o]=Or(t[o],n)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,n){var o,u,c,m,M,O,Z=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(o=0,m=this._parts.length;o<m;o++)for(O=this._parts[o],u=0,M=O.length,c=M-1;u<M;c=u++)if(!(!n&&u===0)&&Fr(t,O[c],O[u])<=Z)return!0;return!1}});function Ye(t,n){return new Ee(t,n)}Ee._flat=Ir;var ni=Ee.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Ar(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var n=Ee.prototype._convertLatLngs.call(this,t),o=n.length;return o>=2&&n[0]instanceof vt&&n[0].equals(n[o-1])&&n.pop(),n},_setLatLngs:function(t){Ee.prototype._setLatLngs.call(this,t),ie(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return ie(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,n=this.options.weight,o=new ut(n,n);if(t=new Et(t.min.subtract(o),t.max.add(o)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}for(var u=0,c=this._rings.length,m;u<c;u++)m=Sr(this._rings[u],t,!0),m.length&&this._parts.push(m)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var n=!1,o,u,c,m,M,O,Z,K;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(m=0,Z=this._parts.length;m<Z;m++)for(o=this._parts[m],M=0,K=o.length,O=K-1;M<K;O=M++)u=o[M],c=o[O],u.y>t.y!=c.y>t.y&&t.x<(c.x-u.x)*(t.y-u.y)/(c.y-u.y)+u.x&&(n=!n);return n||Ee.prototype._containsPoint.call(this,t,!0)}});function ja(t,n){return new ni(t,n)}var _e=Me.extend({initialize:function(t,n){wt(this,n),this._layers={},t&&this.addData(t)},addData:function(t){var n=le(t)?t:t.features,o,u,c;if(n){for(o=0,u=n.length;o<u;o++)c=n[o],(c.geometries||c.geometry||c.features||c.coordinates)&&this.addData(c);return this}var m=this.options;if(m.filter&&!m.filter(t))return this;var M=Re(t,m);return M?(M.feature=Xi(t),M.defaultOptions=M.options,this.resetStyle(M),m.onEachFeature&&m.onEachFeature(t,M),this.addLayer(M)):this},resetStyle:function(t){return t===void 0?this.eachLayer(this.resetStyle,this):(t.options=U({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(n){this._setLayerStyle(n,t)},this)},_setLayerStyle:function(t,n){t.setStyle&&(typeof n=="function"&&(n=n(t.feature)),t.setStyle(n))}});function Re(t,n){var o=t.type==="Feature"?t.geometry:t,u=o?o.coordinates:null,c=[],m=n&&n.pointToLayer,M=n&&n.coordsToLatLng||Zn,O,Z,K,Y;if(!u&&!o)return null;switch(o.type){case"Point":return O=M(u),Zr(m,t,O,n);case"MultiPoint":for(K=0,Y=u.length;K<Y;K++)O=M(u[K]),c.push(Zr(m,t,O,n));return new Me(c);case"LineString":case"MultiLineString":return Z=Wi(u,o.type==="LineString"?0:1,M),new Ee(Z,n);case"Polygon":case"MultiPolygon":return Z=Wi(u,o.type==="Polygon"?1:2,M),new ni(Z,n);case"GeometryCollection":for(K=0,Y=o.geometries.length;K<Y;K++){var nt=Re({geometry:o.geometries[K],type:"Feature",properties:t.properties},n);nt&&c.push(nt)}return new Me(c);case"FeatureCollection":for(K=0,Y=o.features.length;K<Y;K++){var dt=Re(o.features[K],n);dt&&c.push(dt)}return new Me(c);default:throw new Error("Invalid GeoJSON object.")}}function Zr(t,n,o,u){return t?t(n,o):new Ki(o,u&&u.markersInheritOptions&&u)}function Zn(t){return new vt(t[1],t[0],t[2])}function Wi(t,n,o){for(var u=[],c=0,m=t.length,M;c<m;c++)M=n?Wi(t[c],n-1,o):(o||Zn)(t[c]),u.push(M);return u}function Yi(t,n){return t=ft(t),t.alt!==void 0?[Ft(t.lng,n),Ft(t.lat,n),Ft(t.alt,n)]:[Ft(t.lng,n),Ft(t.lat,n)]}function Ji(t,n,o,u){for(var c=[],m=0,M=t.length;m<M;m++)c.push(n?Ji(t[m],ie(t[m])?0:n-1,o,u):Yi(t[m],u));return!n&&o&&c.length>0&&c.push(c[0].slice()),c}function Je(t,n){return t.feature?U({},t.feature,{geometry:n}):Xi(n)}function Xi(t){return t.type==="Feature"||t.type==="FeatureCollection"?t:{type:"Feature",properties:{},geometry:t}}var jn={toGeoJSON:function(t){return Je(this,{type:"Point",coordinates:Yi(this.getLatLng(),t)})}};Ki.include(jn),we.include(jn),qi.include(jn),Ee.include({toGeoJSON:function(t){var n=!ie(this._latlngs),o=Ji(this._latlngs,n?1:0,!1,t);return Je(this,{type:(n?"Multi":"")+"LineString",coordinates:o})}}),ni.include({toGeoJSON:function(t){var n=!ie(this._latlngs),o=n&&!ie(this._latlngs[0]),u=Ji(this._latlngs,o?2:n?1:0,!0,t);return n||(u=[u]),Je(this,{type:(o?"Multi":"")+"Polygon",coordinates:u})}}),Ae.include({toMultiPoint:function(t){var n=[];return this.eachLayer(function(o){n.push(o.toGeoJSON(t).geometry.coordinates)}),Je(this,{type:"MultiPoint",coordinates:n})},toGeoJSON:function(t){var n=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(n==="MultiPoint")return this.toMultiPoint(t);var o=n==="GeometryCollection",u=[];return this.eachLayer(function(c){if(c.toGeoJSON){var m=c.toGeoJSON(t);if(o)u.push(m.geometry);else{var M=Xi(m);M.type==="FeatureCollection"?u.push.apply(u,M.features):u.push(M)}}}),o?Je(this,{geometries:u,type:"GeometryCollection"}):{type:"FeatureCollection",features:u}}});function jr(t,n){return new _e(t,n)}var Ur=jr,ri=ne.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,n,o){this._url=t,this._bounds=Pt(n),wt(this,o)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(ht(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){bt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&Ve(this._image),this},bringToBack:function(){return this._map&&He(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=Pt(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t=this._url.tagName==="IMG",n=this._image=t?this._url:gt("img");if(ht(n,"leaflet-image-layer"),this._zoomAnimated&&ht(n,"leaflet-zoom-animated"),this.options.className&&ht(n,this.options.className),n.onselectstart=st,n.onmousemove=st,n.onload=W(this.fire,this,"load"),n.onerror=W(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=n.src;return}n.src=this._url,n.alt=this.options.alt},_animateZoom:function(t){var n=this._map.getZoomScale(t.zoom),o=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;Ke(this._image,o,n)},_reset:function(){var t=this._image,n=new Et(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),o=n.getSize();Tt(t,n.min),t.style.width=o.x+"px",t.style.height=o.y+"px"},_updateOpacity:function(){$t(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),Vr=function(t,n,o){return new ri(t,n,o)},$i=ri.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t=this._url.tagName==="VIDEO",n=this._image=t?this._url:gt("video");if(ht(n,"leaflet-image-layer"),this._zoomAnimated&&ht(n,"leaflet-zoom-animated"),this.options.className&&ht(n,this.options.className),n.onselectstart=st,n.onmousemove=st,n.onloadeddata=W(this.fire,this,"load"),t){for(var o=n.getElementsByTagName("source"),u=[],c=0;c<o.length;c++)u.push(o[c].src);this._url=o.length>0?u:[n.src];return}le(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(n.style,"objectFit")&&(n.style.objectFit="fill"),n.autoplay=!!this.options.autoplay,n.loop=!!this.options.loop,n.muted=!!this.options.muted,n.playsInline=!!this.options.playsInline;for(var m=0;m<this._url.length;m++){var M=gt("source");M.src=this._url[m],n.appendChild(M)}}});function Ua(t,n,o){return new $i(t,n,o)}var xi=ri.extend({_initImage:function(){var t=this._image=this._url;ht(t,"leaflet-image-layer"),this._zoomAnimated&&ht(t,"leaflet-zoom-animated"),this.options.className&&ht(t,this.options.className),t.onselectstart=st,t.onmousemove=st}});function Hr(t,n,o){return new xi(t,n,o)}var Le=ne.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,n){t&&(t instanceof vt||le(t))?(this._latlng=ft(t),wt(this,n)):(wt(this,t),this._source=n),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&$t(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&$t(this._container,1),this.bringToFront(),this.options.interactive&&(ht(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?($t(this._container,0),this._removeTimeout=setTimeout(W(bt,void 0,this._container),200)):bt(this._container),this.options.interactive&&(Bt(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=ft(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ve(this._container),this},bringToBack:function(){return this._map&&He(this._container),this},_prepareOpen:function(t){var n=this._source;if(!n._map)return!1;if(n instanceof Me){n=null;var o=this._source._layers;for(var u in o)if(o[u]._map){n=o[u];break}if(!n)return!1;this._source=n}if(!t)if(n.getCenter)t=n.getCenter();else if(n.getLatLng)t=n.getLatLng();else if(n.getBounds)t=n.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,n=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof n=="string")t.innerHTML=n;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(n)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),n=at(this.options.offset),o=this._getAnchor();this._zoomAnimated?Tt(this._container,t.add(o)):n=n.add(t).add(o);var u=this._containerBottom=-n.y,c=this._containerLeft=-Math.round(this._containerWidth/2)+n.x;this._container.style.bottom=u+"px",this._container.style.left=c+"px"}},_getAnchor:function(){return[0,0]}});_t.include({_initOverlay:function(t,n,o,u){var c=n;return c instanceof t||(c=new t(u).setContent(n)),o&&c.setLatLng(o),c}}),ne.include({_initOverlay:function(t,n,o,u){var c=o;return c instanceof t?(wt(c,u),c._source=this):(c=n&&!u?n:new t(u,this),c.setContent(o)),c}});var Qi=Le.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,Le.prototype.openOn.call(this,t)},onAdd:function(t){Le.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Fe||this._source.on("preclick",ce))},onRemove:function(t){Le.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Fe||this._source.off("preclick",ce))},getEvents:function(){var t=Le.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",n=this._container=gt("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),o=this._wrapper=gt("div",t+"-content-wrapper",n);if(this._contentNode=gt("div",t+"-content",o),yi(n),An(this._contentNode),lt(n,"contextmenu",ce),this._tipContainer=gt("div",t+"-tip-container",n),this._tip=gt("div",t+"-tip",this._tipContainer),this.options.closeButton){var u=this._closeButton=gt("a",t+"-close-button",n);u.setAttribute("role","button"),u.setAttribute("aria-label","Close popup"),u.href="#close",u.innerHTML='<span aria-hidden="true">&#215;</span>',lt(u,"click",function(c){zt(c),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,n=t.style;n.width="",n.whiteSpace="nowrap";var o=t.offsetWidth;o=Math.min(o,this.options.maxWidth),o=Math.max(o,this.options.minWidth),n.width=o+1+"px",n.whiteSpace="",n.height="";var u=t.offsetHeight,c=this.options.maxHeight,m="leaflet-popup-scrolled";c&&u>c?(n.height=c+"px",ht(t,m)):Bt(t,m),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var n=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),o=this._getAnchor();Tt(this._container,n.add(o))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,n=parseInt(fi(this._container,"marginBottom"),10)||0,o=this._container.offsetHeight+n,u=this._containerWidth,c=new ut(this._containerLeft,-o-this._containerBottom);c._add(qe(this._container));var m=t.layerPointToContainerPoint(c),M=at(this.options.autoPanPadding),O=at(this.options.autoPanPaddingTopLeft||M),Z=at(this.options.autoPanPaddingBottomRight||M),K=t.getSize(),Y=0,nt=0;m.x+u+Z.x>K.x&&(Y=m.x+u-K.x+Z.x),m.x-Y-O.x<0&&(Y=m.x-O.x),m.y+o+Z.y>K.y&&(nt=m.y+o-K.y+Z.y),m.y-nt-O.y<0&&(nt=m.y-O.y),(Y||nt)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([Y,nt]))}},_getAnchor:function(){return at(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Va=function(t,n){return new Qi(t,n)};_t.mergeOptions({closePopupOnClick:!0}),_t.include({openPopup:function(t,n,o){return this._initOverlay(Qi,t,n,o).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),ne.include({bindPopup:function(t,n){return this._popup=this._initOverlay(Qi,this._popup,t,n),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof Me||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(!(!this._popup||!this._map)){We(t);var n=t.layer||t.target;if(this._popup._source===n&&!(n instanceof Fe)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng);return}this._popup._source=n,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){t.originalEvent.keyCode===13&&this._openPopup(t)}});var Ie=Le.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){Le.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){Le.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=Le.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip",n=t+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=gt("div",n),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+F(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var n,o,u=this._map,c=this._container,m=u.latLngToContainerPoint(u.getCenter()),M=u.layerPointToContainerPoint(t),O=this.options.direction,Z=c.offsetWidth,K=c.offsetHeight,Y=at(this.options.offset),nt=this._getAnchor();O==="top"?(n=Z/2,o=K):O==="bottom"?(n=Z/2,o=0):O==="center"?(n=Z/2,o=K/2):O==="right"?(n=0,o=K/2):O==="left"?(n=Z,o=K/2):M.x<m.x?(O="right",n=0,o=K/2):(O="left",n=Z+(Y.x+nt.x)*2,o=K/2),t=t.subtract(at(n,o,!0)).add(Y).add(nt),Bt(c,"leaflet-tooltip-right"),Bt(c,"leaflet-tooltip-left"),Bt(c,"leaflet-tooltip-top"),Bt(c,"leaflet-tooltip-bottom"),ht(c,"leaflet-tooltip-"+O),Tt(c,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&$t(this._container,t)},_animateZoom:function(t){var n=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(n)},_getAnchor:function(){return at(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),Ha=function(t,n){return new Ie(t,n)};_t.include({openTooltip:function(t,n,o){return this._initOverlay(Ie,t,n,o).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),ne.include({bindTooltip:function(t,n){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Ie,this._tooltip,t,n),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(!(!t&&this._tooltipHandlersAdded)){var n=t?"off":"on",o={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?o.add=this._openTooltip:(o.mouseover=this._openTooltip,o.mouseout=this.closeTooltip,o.click=this._openTooltip,this._map?this._addFocusListeners():o.add=this._addFocusListeners),this._tooltip.options.sticky&&(o.mousemove=this._moveTooltip),this[n](o),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof Me||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var n=typeof t.getElement=="function"&&t.getElement();n&&(lt(n,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),lt(n,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var n=typeof t.getElement=="function"&&t.getElement();n&&n.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var n=this;this._map.once("moveend",function(){n._openOnceFlag=!1,n._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var n=t.latlng,o,u;this._tooltip.options.sticky&&t.originalEvent&&(o=this._map.mouseEventToContainerPoint(t.originalEvent),u=this._map.containerPointToLayerPoint(o),n=this._map.layerPointToLatLng(u)),this._tooltip.setLatLng(n)}});var Kr=Oe.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var n=t&&t.tagName==="DIV"?t:document.createElement("div"),o=this.options;if(o.html instanceof Element?(Oi(n),n.appendChild(o.html)):n.innerHTML=o.html!==!1?o.html:"",o.bgPos){var u=at(o.bgPos);n.style.backgroundPosition=-u.x+"px "+-u.y+"px"}return this._setIconStyles(n,"icon"),n},createShadow:function(){return null}});function Ka(t){return new Kr(t)}Oe.Default=Ci;var ki=ne.extend({options:{tileSize:256,opacity:1,updateWhenIdle:tt.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){wt(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),bt(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ve(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(He(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=Wt(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof ut?t:new ut(t,t)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var n=this.getPane().children,o=-t(-1/0,1/0),u=0,c=n.length,m;u<c;u++)m=n[u].style.zIndex,n[u]!==this._container&&m&&(o=t(o,+m));isFinite(o)&&(this.options.zIndex=o+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!tt.ielt9){$t(this._container,this.options.opacity);var t=+new Date,n=!1,o=!1;for(var u in this._tiles){var c=this._tiles[u];if(!(!c.current||!c.loaded)){var m=Math.min(1,(t-c.loaded)/200);$t(c.el,m),m<1?n=!0:(c.active?o=!0:this._onOpaqueTile(c),c.active=!0)}}o&&!this._noPrune&&this._pruneTiles(),n&&(Nt(this._fadeFrame),this._fadeFrame=Ut(this._updateOpacity,this))}},_onOpaqueTile:st,_initContainer:function(){this._container||(this._container=gt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,n=this.options.maxZoom;if(t!==void 0){for(var o in this._levels)o=Number(o),this._levels[o].el.children.length||o===t?(this._levels[o].el.style.zIndex=n-Math.abs(t-o),this._onUpdateLevel(o)):(bt(this._levels[o].el),this._removeTilesAtZoom(o),this._onRemoveLevel(o),delete this._levels[o]);var u=this._levels[t],c=this._map;return u||(u=this._levels[t]={},u.el=gt("div","leaflet-tile-container leaflet-zoom-animated",this._container),u.el.style.zIndex=n,u.origin=c.project(c.unproject(c.getPixelOrigin()),t).round(),u.zoom=t,this._setZoomTransform(u,c.getCenter(),c.getZoom()),st(u.el.offsetWidth),this._onCreateLevel(u)),this._level=u,u}},_onUpdateLevel:st,_onRemoveLevel:st,_onCreateLevel:st,_pruneTiles:function(){if(this._map){var t,n,o=this._map.getZoom();if(o>this.options.maxZoom||o<this.options.minZoom){this._removeAllTiles();return}for(t in this._tiles)n=this._tiles[t],n.retain=n.current;for(t in this._tiles)if(n=this._tiles[t],n.current&&!n.active){var u=n.coords;this._retainParent(u.x,u.y,u.z,u.z-5)||this._retainChildren(u.x,u.y,u.z,u.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var n in this._tiles)this._tiles[n].coords.z===t&&this._removeTile(n)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)bt(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,n,o,u){var c=Math.floor(t/2),m=Math.floor(n/2),M=o-1,O=new ut(+c,+m);O.z=+M;var Z=this._tileCoordsToKey(O),K=this._tiles[Z];return K&&K.active?(K.retain=!0,!0):(K&&K.loaded&&(K.retain=!0),M>u?this._retainParent(c,m,M,u):!1)},_retainChildren:function(t,n,o,u){for(var c=2*t;c<2*t+2;c++)for(var m=2*n;m<2*n+2;m++){var M=new ut(c,m);M.z=o+1;var O=this._tileCoordsToKey(M),Z=this._tiles[O];if(Z&&Z.active){Z.retain=!0;continue}else Z&&Z.loaded&&(Z.retain=!0);o+1<u&&this._retainChildren(c,m,o+1,u)}},_resetView:function(t){var n=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),n,n)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var n=this.options;return n.minNativeZoom!==void 0&&t<n.minNativeZoom?n.minNativeZoom:n.maxNativeZoom!==void 0&&n.maxNativeZoom<t?n.maxNativeZoom:t},_setView:function(t,n,o,u){var c=Math.round(n);this.options.maxZoom!==void 0&&c>this.options.maxZoom||this.options.minZoom!==void 0&&c<this.options.minZoom?c=void 0:c=this._clampZoom(c);var m=this.options.updateWhenZooming&&c!==this._tileZoom;(!u||m)&&(this._tileZoom=c,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),c!==void 0&&this._update(t),o||this._pruneTiles(),this._noPrune=!!o),this._setZoomTransforms(t,n)},_setZoomTransforms:function(t,n){for(var o in this._levels)this._setZoomTransform(this._levels[o],t,n)},_setZoomTransform:function(t,n,o){var u=this._map.getZoomScale(o,t.zoom),c=t.origin.multiplyBy(u).subtract(this._map._getNewPixelOrigin(n,o)).round();tt.any3d?Ke(t.el,c,u):Tt(t.el,c)},_resetGrid:function(){var t=this._map,n=t.options.crs,o=this._tileSize=this.getTileSize(),u=this._tileZoom,c=this._map.getPixelWorldBounds(this._tileZoom);c&&(this._globalTileRange=this._pxBoundsToTileRange(c)),this._wrapX=n.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,n.wrapLng[0]],u).x/o.x),Math.ceil(t.project([0,n.wrapLng[1]],u).x/o.y)],this._wrapY=n.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([n.wrapLat[0],0],u).y/o.x),Math.ceil(t.project([n.wrapLat[1],0],u).y/o.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(t){var n=this._map,o=n._animatingZoom?Math.max(n._animateToZoom,n.getZoom()):n.getZoom(),u=n.getZoomScale(o,this._tileZoom),c=n.project(t,this._tileZoom).floor(),m=n.getSize().divideBy(u*2);return new Et(c.subtract(m),c.add(m))},_update:function(t){var n=this._map;if(n){var o=this._clampZoom(n.getZoom());if(t===void 0&&(t=n.getCenter()),this._tileZoom!==void 0){var u=this._getTiledPixelBounds(t),c=this._pxBoundsToTileRange(u),m=c.getCenter(),M=[],O=this.options.keepBuffer,Z=new Et(c.getBottomLeft().subtract([O,-O]),c.getTopRight().add([O,-O]));if(!(isFinite(c.min.x)&&isFinite(c.min.y)&&isFinite(c.max.x)&&isFinite(c.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var K in this._tiles){var Y=this._tiles[K].coords;(Y.z!==this._tileZoom||!Z.contains(new ut(Y.x,Y.y)))&&(this._tiles[K].current=!1)}if(Math.abs(o-this._tileZoom)>1){this._setView(t,o);return}for(var nt=c.min.y;nt<=c.max.y;nt++)for(var dt=c.min.x;dt<=c.max.x;dt++){var jt=new ut(dt,nt);if(jt.z=this._tileZoom,!!this._isValidTile(jt)){var Rt=this._tiles[this._tileCoordsToKey(jt)];Rt?Rt.current=!0:M.push(jt)}}if(M.sort(function(Kt,Ne){return Kt.distanceTo(m)-Ne.distanceTo(m)}),M.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var oe=document.createDocumentFragment();for(dt=0;dt<M.length;dt++)this._addTile(M[dt],oe);this._level.el.appendChild(oe)}}}},_isValidTile:function(t){var n=this._map.options.crs;if(!n.infinite){var o=this._globalTileRange;if(!n.wrapLng&&(t.x<o.min.x||t.x>o.max.x)||!n.wrapLat&&(t.y<o.min.y||t.y>o.max.y))return!1}if(!this.options.bounds)return!0;var u=this._tileCoordsToBounds(t);return Pt(this.options.bounds).overlaps(u)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var n=this._map,o=this.getTileSize(),u=t.scaleBy(o),c=u.add(o),m=n.unproject(u,t.z),M=n.unproject(c,t.z);return[m,M]},_tileCoordsToBounds:function(t){var n=this._tileCoordsToNwSe(t),o=new Vt(n[0],n[1]);return this.options.noWrap||(o=this._map.wrapLatLngBounds(o)),o},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var n=t.split(":"),o=new ut(+n[0],+n[1]);return o.z=+n[2],o},_removeTile:function(t){var n=this._tiles[t];n&&(bt(n.el),delete this._tiles[t],this.fire("tileunload",{tile:n.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){ht(t,"leaflet-tile");var n=this.getTileSize();t.style.width=n.x+"px",t.style.height=n.y+"px",t.onselectstart=st,t.onmousemove=st,tt.ielt9&&this.options.opacity<1&&$t(t,this.options.opacity)},_addTile:function(t,n){var o=this._getTilePos(t),u=this._tileCoordsToKey(t),c=this.createTile(this._wrapCoords(t),W(this._tileReady,this,t));this._initTile(c),this.createTile.length<2&&Ut(W(this._tileReady,this,t,null,c)),Tt(c,o),this._tiles[u]={el:c,coords:t,current:!0},n.appendChild(c),this.fire("tileloadstart",{tile:c,coords:t})},_tileReady:function(t,n,o){n&&this.fire("tileerror",{error:n,tile:o,coords:t});var u=this._tileCoordsToKey(t);o=this._tiles[u],o&&(o.loaded=+new Date,this._map._fadeAnimated?($t(o.el,0),Nt(this._fadeFrame),this._fadeFrame=Ut(this._updateOpacity,this)):(o.active=!0,this._pruneTiles()),n||(ht(o.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:o.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),tt.ielt9||!this._map._fadeAnimated?Ut(this._pruneTiles,this):setTimeout(W(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var n=new ut(this._wrapX?rt(t.x,this._wrapX):t.x,this._wrapY?rt(t.y,this._wrapY):t.y);return n.z=t.z,n},_pxBoundsToTileRange:function(t){var n=this.getTileSize();return new Et(t.min.unscaleBy(n).floor(),t.max.unscaleBy(n).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}});function qa(t){return new ki(t)}var ai=ki.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,n){this._url=t,n=wt(this,n),n.detectRetina&&tt.retina&&n.maxZoom>0?(n.tileSize=Math.floor(n.tileSize/2),n.zoomReverse?(n.zoomOffset--,n.minZoom=Math.min(n.maxZoom,n.minZoom+1)):(n.zoomOffset++,n.maxZoom=Math.max(n.minZoom,n.maxZoom-1)),n.minZoom=Math.max(0,n.minZoom)):n.zoomReverse?n.minZoom=Math.min(n.maxZoom,n.minZoom):n.maxZoom=Math.max(n.minZoom,n.maxZoom),typeof n.subdomains=="string"&&(n.subdomains=n.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,n){return this._url===t&&n===void 0&&(n=!0),this._url=t,n||this.redraw(),this},createTile:function(t,n){var o=document.createElement("img");return lt(o,"load",W(this._tileOnLoad,this,n,o)),lt(o,"error",W(this._tileOnError,this,n,o)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(o.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(o.referrerPolicy=this.options.referrerPolicy),o.alt="",o.src=this.getTileUrl(t),o},getTileUrl:function(t){var n={r:tt.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var o=this._globalTileRange.max.y-t.y;this.options.tms&&(n.y=o),n["-y"]=o}return li(this._url,U(n,this.options))},_tileOnLoad:function(t,n){tt.ielt9?setTimeout(W(t,this,null,n),0):t(null,n)},_tileOnError:function(t,n,o){var u=this.options.errorTileUrl;u&&n.getAttribute("src")!==u&&(n.src=u),t(o,n)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,n=this.options.maxZoom,o=this.options.zoomReverse,u=this.options.zoomOffset;return o&&(t=n-t),t+u},_getSubdomain:function(t){var n=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[n]},_abortLoading:function(){var t,n;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&(n=this._tiles[t].el,n.onload=st,n.onerror=st,!n.complete)){n.src=Pi;var o=this._tiles[t].coords;bt(n),delete this._tiles[t],this.fire("tileabort",{tile:n,coords:o})}},_removeTile:function(t){var n=this._tiles[t];if(n)return n.el.setAttribute("src",Pi),ki.prototype._removeTile.call(this,t)},_tileReady:function(t,n,o){if(!(!this._map||o&&o.getAttribute("src")===Pi))return ki.prototype._tileReady.call(this,t,n,o)}});function qr(t,n){return new ai(t,n)}var Wr=ai.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,n){this._url=t;var o=U({},this.defaultWmsParams);for(var u in n)u in this.options||(o[u]=n[u]);n=wt(this,n);var c=n.detectRetina&&tt.retina?2:1,m=this.getTileSize();o.width=m.x*c,o.height=m.y*c,this.wmsParams=o},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var n=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[n]=this._crs.code,ai.prototype.onAdd.call(this,t)},getTileUrl:function(t){var n=this._tileCoordsToNwSe(t),o=this._crs,u=It(o.project(n[0]),o.project(n[1])),c=u.min,m=u.max,M=(this._wmsVersion>=1.3&&this._crs===fe?[c.y,c.x,m.y,m.x]:[c.x,c.y,m.x,m.y]).join(","),O=ai.prototype.getTileUrl.call(this,t);return O+Yn(this.wmsParams,O,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+M},setParams:function(t,n){return U(this.wmsParams,t),n||this.redraw(),this}});function Yr(t,n){return new Wr(t,n)}ai.WMS=Wr,qr.wms=Yr;var me=ne.extend({options:{padding:.1},initialize:function(t){wt(this,t),F(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),ht(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,n){var o=this._map.getZoomScale(n,this._zoom),u=this._map.getSize().multiplyBy(.5+this.options.padding),c=this._map.project(this._center,n),m=u.multiplyBy(-o).add(c).subtract(this._map._getNewPixelOrigin(t,n));tt.any3d?Ke(this._container,m,o):Tt(this._container,m)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var t in this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,n=this._map.getSize(),o=this._map.containerPointToLayerPoint(n.multiplyBy(-t)).round();this._bounds=new Et(o,o.add(n.multiplyBy(1+t*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Jr=me.extend({options:{tolerance:0},getEvents:function(){var t=me.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){me.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");lt(t,"mousemove",this._onMouseMove,this),lt(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),lt(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){Nt(this._redrawRequest),delete this._ctx,bt(this._container),Ct(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var t;this._redrawBounds=null;for(var n in this._layers)t=this._layers[n],t._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){me.prototype._update.call(this);var t=this._bounds,n=this._container,o=t.getSize(),u=tt.retina?2:1;Tt(n,t.min),n.width=u*o.x,n.height=u*o.y,n.style.width=o.x+"px",n.style.height=o.y+"px",tt.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){me.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[F(t)]=t;var n=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=n),this._drawLast=n,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var n=t._order,o=n.next,u=n.prev;o?o.prev=u:this._drawLast=u,u?u.next=o:this._drawFirst=o,delete t._order,delete this._layers[F(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(typeof t.options.dashArray=="string"){var n=t.options.dashArray.split(/[, ]+/),o=[],u,c;for(c=0;c<n.length;c++){if(u=Number(n[c]),isNaN(u))return;o.push(u)}t.options._dashArray=o}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||Ut(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var n=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new Et,this._redrawBounds.extend(t._pxBounds.min.subtract([n,n])),this._redrawBounds.extend(t._pxBounds.max.add([n,n]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var n=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,n.x,n.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,n=this._redrawBounds;if(this._ctx.save(),n){var o=n.getSize();this._ctx.beginPath(),this._ctx.rect(n.min.x,n.min.y,o.x,o.y),this._ctx.clip()}this._drawing=!0;for(var u=this._drawFirst;u;u=u.next)t=u.layer,(!n||t._pxBounds&&t._pxBounds.intersects(n))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,n){if(this._drawing){var o,u,c,m,M=t._parts,O=M.length,Z=this._ctx;if(O){for(Z.beginPath(),o=0;o<O;o++){for(u=0,c=M[o].length;u<c;u++)m=M[o][u],Z[u?"lineTo":"moveTo"](m.x,m.y);n&&Z.closePath()}this._fillStroke(Z,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var n=t._point,o=this._ctx,u=Math.max(Math.round(t._radius),1),c=(Math.max(Math.round(t._radiusY),1)||u)/u;c!==1&&(o.save(),o.scale(1,c)),o.beginPath(),o.arc(n.x,n.y/c,u,0,Math.PI*2,!1),c!==1&&o.restore(),this._fillStroke(o,t)}},_fillStroke:function(t,n){var o=n.options;o.fill&&(t.globalAlpha=o.fillOpacity,t.fillStyle=o.fillColor||o.color,t.fill(o.fillRule||"evenodd")),o.stroke&&o.weight!==0&&(t.setLineDash&&t.setLineDash(n.options&&n.options._dashArray||[]),t.globalAlpha=o.opacity,t.lineWidth=o.weight,t.strokeStyle=o.color,t.lineCap=o.lineCap,t.lineJoin=o.lineJoin,t.stroke())},_onClick:function(t){for(var n=this._map.mouseEventToLayerPoint(t),o,u,c=this._drawFirst;c;c=c.next)o=c.layer,o.options.interactive&&o._containsPoint(n)&&(!(t.type==="click"||t.type==="preclick")||!this._map._draggableMoved(o))&&(u=o);this._fireEvent(u?[u]:!1,t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var n=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,n)}},_handleMouseOut:function(t){var n=this._hoveredLayer;n&&(Bt(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,n){if(!this._mouseHoverThrottled){for(var o,u,c=this._drawFirst;c;c=c.next)o=c.layer,o.options.interactive&&o._containsPoint(n)&&(u=o);u!==this._hoveredLayer&&(this._handleMouseOut(t),u&&(ht(this._container,"leaflet-interactive"),this._fireEvent([u],t,"mouseover"),this._hoveredLayer=u)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,t),this._mouseHoverThrottled=!0,setTimeout(W(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,n,o){this._map._fireDOMEvent(n,o||n.type,t)},_bringToFront:function(t){var n=t._order;if(n){var o=n.next,u=n.prev;if(o)o.prev=u;else return;u?u.next=o:o&&(this._drawFirst=o),n.prev=this._drawLast,this._drawLast.next=n,n.next=null,this._drawLast=n,this._requestRedraw(t)}},_bringToBack:function(t){var n=t._order;if(n){var o=n.next,u=n.prev;if(u)u.next=o;else return;o?o.prev=u:u&&(this._drawLast=u),n.prev=null,n.next=this._drawFirst,this._drawFirst.prev=n,this._drawFirst=n,this._requestRedraw(t)}}});function Xr(t){return tt.canvas?new Jr(t):null}var oi=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch{}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Un={_initContainer:function(){this._container=gt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(me.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var n=t._container=oi("shape");ht(n,"leaflet-vml-shape "+(this.options.className||"")),n.coordsize="1 1",t._path=oi("path"),n.appendChild(t._path),this._updateStyle(t),this._layers[F(t)]=t},_addPath:function(t){var n=t._container;this._container.appendChild(n),t.options.interactive&&t.addInteractiveTarget(n)},_removePath:function(t){var n=t._container;bt(n),t.removeInteractiveTarget(n),delete this._layers[F(t)]},_updateStyle:function(t){var n=t._stroke,o=t._fill,u=t.options,c=t._container;c.stroked=!!u.stroke,c.filled=!!u.fill,u.stroke?(n||(n=t._stroke=oi("stroke")),c.appendChild(n),n.weight=u.weight+"px",n.color=u.color,n.opacity=u.opacity,u.dashArray?n.dashStyle=le(u.dashArray)?u.dashArray.join(" "):u.dashArray.replace(/( *, *)/g," "):n.dashStyle="",n.endcap=u.lineCap.replace("butt","flat"),n.joinstyle=u.lineJoin):n&&(c.removeChild(n),t._stroke=null),u.fill?(o||(o=t._fill=oi("fill")),c.appendChild(o),o.color=u.fillColor||u.color,o.opacity=u.fillOpacity):o&&(c.removeChild(o),t._fill=null)},_updateCircle:function(t){var n=t._point.round(),o=Math.round(t._radius),u=Math.round(t._radiusY||o);this._setPath(t,t._empty()?"M0 0":"AL "+n.x+","+n.y+" "+o+","+u+" 0,"+65535*360)},_setPath:function(t,n){t._path.v=n},_bringToFront:function(t){Ve(t._container)},_bringToBack:function(t){He(t._container)}},tn=tt.vml?oi:Qn,Mi=me.extend({_initContainer:function(){this._container=tn("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=tn("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){bt(this._container),Ct(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){me.prototype._update.call(this);var t=this._bounds,n=t.getSize(),o=this._container;(!this._svgSize||!this._svgSize.equals(n))&&(this._svgSize=n,o.setAttribute("width",n.x),o.setAttribute("height",n.y)),Tt(o,t.min),o.setAttribute("viewBox",[t.min.x,t.min.y,n.x,n.y].join(" ")),this.fire("update")}},_initPath:function(t){var n=t._path=tn("path");t.options.className&&ht(n,t.options.className),t.options.interactive&&ht(n,"leaflet-interactive"),this._updateStyle(t),this._layers[F(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){bt(t._path),t.removeInteractiveTarget(t._path),delete this._layers[F(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var n=t._path,o=t.options;n&&(o.stroke?(n.setAttribute("stroke",o.color),n.setAttribute("stroke-opacity",o.opacity),n.setAttribute("stroke-width",o.weight),n.setAttribute("stroke-linecap",o.lineCap),n.setAttribute("stroke-linejoin",o.lineJoin),o.dashArray?n.setAttribute("stroke-dasharray",o.dashArray):n.removeAttribute("stroke-dasharray"),o.dashOffset?n.setAttribute("stroke-dashoffset",o.dashOffset):n.removeAttribute("stroke-dashoffset")):n.setAttribute("stroke","none"),o.fill?(n.setAttribute("fill",o.fillColor||o.color),n.setAttribute("fill-opacity",o.fillOpacity),n.setAttribute("fill-rule",o.fillRule||"evenodd")):n.setAttribute("fill","none"))},_updatePoly:function(t,n){this._setPath(t,pn(t._parts,n))},_updateCircle:function(t){var n=t._point,o=Math.max(Math.round(t._radius),1),u=Math.max(Math.round(t._radiusY),1)||o,c="a"+o+","+u+" 0 1,0 ",m=t._empty()?"M0 0":"M"+(n.x-o)+","+n.y+c+o*2+",0 "+c+-o*2+",0 ";this._setPath(t,m)},_setPath:function(t,n){t._path.setAttribute("d",n)},_bringToFront:function(t){Ve(t._path)},_bringToBack:function(t){He(t._path)}});tt.vml&&Mi.include(Un);function en(t){return tt.svg||tt.vml?new Mi(t):null}_t.include({getRenderer:function(t){var n=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return n||(n=this._renderer=this._createRenderer()),this.hasLayer(n)||this.addLayer(n),n},_getPaneRenderer:function(t){if(t==="overlayPane"||t===void 0)return!1;var n=this._paneRenderers[t];return n===void 0&&(n=this._createRenderer({pane:t}),this._paneRenderers[t]=n),n},_createRenderer:function(t){return this.options.preferCanvas&&Xr(t)||en(t)}});var Jt=ni.extend({initialize:function(t,n){ni.prototype.initialize.call(this,this._boundsToLatLngs(t),n)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return t=Pt(t),[t.getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function Ht(t,n){return new Jt(t,n)}Mi.create=tn,Mi.pointsToPath=pn,_e.geometryToLayer=Re,_e.coordsToLatLng=Zn,_e.coordsToLatLngs=Wi,_e.latLngToCoords=Yi,_e.latLngsToCoords=Ji,_e.getFeature=Je,_e.asFeature=Xi,_t.mergeOptions({boxZoom:!0});var Vn=de.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){lt(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){Ct(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){bt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||t.which!==1&&t.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),_i(),Mn(),this._startPoint=this._map.mouseEventToContainerPoint(t),lt(document,{contextmenu:We,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=gt("div","leaflet-zoom-box",this._container),ht(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var n=new Et(this._point,this._startPoint),o=n.getSize();Tt(this._box,n.min),this._box.style.width=o.x+"px",this._box.style.height=o.y+"px"},_finish:function(){this._moved&&(bt(this._box),Bt(this._container,"leaflet-crosshair")),mi(),wn(),Ct(document,{contextmenu:We,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if(!(t.which!==1&&t.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(W(this._resetState,this),0);var n=new Vt(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(n).fire("boxzoomend",{boxZoomBounds:n})}},_onKeyDown:function(t){t.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});_t.addInitHook("addHandler","boxZoom",Vn),_t.mergeOptions({doubleClickZoom:!0});var re=de.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var n=this._map,o=n.getZoom(),u=n.options.zoomDelta,c=t.originalEvent.shiftKey?o-u:o+u;n.options.doubleClickZoom==="center"?n.setZoom(c):n.setZoomAround(t.containerPoint,c)}});_t.addInitHook("addHandler","doubleClickZoom",re),_t.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var ot=de.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new Se(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}ht(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){Bt(this._map._container,"leaflet-grab"),Bt(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var n=Pt(this._map.options.maxBounds);this._offsetLimit=It(this._map.latLngToContainerPoint(n.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(n.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var n=this._lastTime=+new Date,o=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(o),this._times.push(n),this._prunePositions(n)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),n=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=n.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,n){return t-(t-n)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var t=this._draggable._newPos.subtract(this._draggable._startPos),n=this._offsetLimit;t.x<n.min.x&&(t.x=this._viscousLimit(t.x,n.min.x)),t.y<n.min.y&&(t.y=this._viscousLimit(t.y,n.min.y)),t.x>n.max.x&&(t.x=this._viscousLimit(t.x,n.max.x)),t.y>n.max.y&&(t.y=this._viscousLimit(t.y,n.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,n=Math.round(t/2),o=this._initialWorldOffset,u=this._draggable._newPos.x,c=(u-n+o)%t+n-o,m=(u+n+o)%t-n-o,M=Math.abs(c+o)<Math.abs(m+o)?c:m;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=M},_onDragEnd:function(t){var n=this._map,o=n.options,u=!o.inertia||t.noInertia||this._times.length<2;if(n.fire("dragend",t),u)n.fire("moveend");else{this._prunePositions(+new Date);var c=this._lastPos.subtract(this._positions[0]),m=(this._lastTime-this._times[0])/1e3,M=o.easeLinearity,O=c.multiplyBy(M/m),Z=O.distanceTo([0,0]),K=Math.min(o.inertiaMaxSpeed,Z),Y=O.multiplyBy(K/Z),nt=K/(o.inertiaDeceleration*M),dt=Y.multiplyBy(-nt/2).round();!dt.x&&!dt.y?n.fire("moveend"):(dt=n._limitOffset(dt,n.options.maxBounds),Ut(function(){n.panBy(dt,{duration:nt,easeLinearity:M,noMoveStart:!0,animate:!0})}))}}});_t.addInitHook("addHandler","dragging",ot),_t.mergeOptions({keyboard:!0,keyboardPanDelta:80});var nn=de.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),lt(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),Ct(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,n=document.documentElement,o=t.scrollTop||n.scrollTop,u=t.scrollLeft||n.scrollLeft;this._map._container.focus(),window.scrollTo(u,o)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var n=this._panKeys={},o=this.keyCodes,u,c;for(u=0,c=o.left.length;u<c;u++)n[o.left[u]]=[-1*t,0];for(u=0,c=o.right.length;u<c;u++)n[o.right[u]]=[t,0];for(u=0,c=o.down.length;u<c;u++)n[o.down[u]]=[0,t];for(u=0,c=o.up.length;u<c;u++)n[o.up[u]]=[0,-1*t]},_setZoomDelta:function(t){var n=this._zoomKeys={},o=this.keyCodes,u,c;for(u=0,c=o.zoomIn.length;u<c;u++)n[o.zoomIn[u]]=t;for(u=0,c=o.zoomOut.length;u<c;u++)n[o.zoomOut[u]]=-t},_addHooks:function(){lt(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){Ct(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var n=t.keyCode,o=this._map,u;if(n in this._panKeys){if(!o._panAnim||!o._panAnim._inProgress)if(u=this._panKeys[n],t.shiftKey&&(u=at(u).multiplyBy(3)),o.options.maxBounds&&(u=o._limitOffset(at(u),o.options.maxBounds)),o.options.worldCopyJump){var c=o.wrapLatLng(o.unproject(o.project(o.getCenter()).add(u)));o.panTo(c)}else o.panBy(u)}else if(n in this._zoomKeys)o.setZoom(o.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[n]);else if(n===27&&o._popup&&o._popup.options.closeOnEscapeKey)o.closePopup();else return;We(t)}}});_t.addInitHook("addHandler","keyboard",nn),_t.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var rn=de.extend({addHooks:function(){lt(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){Ct(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var n=Br(t),o=this._map.options.wheelDebounceTime;this._delta+=n,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var u=Math.max(o-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(W(this._performZoom,this),u),We(t)},_performZoom:function(){var t=this._map,n=t.getZoom(),o=this._map.options.zoomSnap||0;t._stop();var u=this._delta/(this._map.options.wheelPxPerZoomLevel*4),c=4*Math.log(2/(1+Math.exp(-Math.abs(u))))/Math.LN2,m=o?Math.ceil(c/o)*o:c,M=t._limitZoom(n+(this._delta>0?m:-m))-n;this._delta=0,this._startTime=null,M&&(t.options.scrollWheelZoom==="center"?t.setZoom(n+M):t.setZoomAround(this._lastMousePos,n+M))}});_t.addInitHook("addHandler","scrollWheelZoom",rn);var ze=600;_t.mergeOptions({tapHold:tt.touchNative&&tt.safari&&tt.mobile,tapTolerance:15});var Ot=de.extend({addHooks:function(){lt(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){Ct(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),t.touches.length===1){var n=t.touches[0];this._startPos=this._newPos=new ut(n.clientX,n.clientY),this._holdTimeout=setTimeout(W(function(){this._cancel(),this._isTapValid()&&(lt(document,"touchend",zt),lt(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",n))},this),ze),lt(document,"touchend touchcancel contextmenu",this._cancel,this),lt(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){Ct(document,"touchend",zt),Ct(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),Ct(document,"touchend touchcancel contextmenu",this._cancel,this),Ct(document,"touchmove",this._onMove,this)},_onMove:function(t){var n=t.touches[0];this._newPos=new ut(n.clientX,n.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,n){var o=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:n.screenX,screenY:n.screenY,clientX:n.clientX,clientY:n.clientY});o._simulated=!0,n.target.dispatchEvent(o)}});_t.addInitHook("addHandler","tapHold",Ot),_t.mergeOptions({touchZoom:tt.touch,bounceAtZoomLimits:!0});var Hn=de.extend({addHooks:function(){ht(this._map._container,"leaflet-touch-zoom"),lt(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){Bt(this._map._container,"leaflet-touch-zoom"),Ct(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var n=this._map;if(!(!t.touches||t.touches.length!==2||n._animatingZoom||this._zooming)){var o=n.mouseEventToContainerPoint(t.touches[0]),u=n.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=n.getSize()._divideBy(2),this._startLatLng=n.containerPointToLatLng(this._centerPoint),n.options.touchZoom!=="center"&&(this._pinchStartLatLng=n.containerPointToLatLng(o.add(u)._divideBy(2))),this._startDist=o.distanceTo(u),this._startZoom=n.getZoom(),this._moved=!1,this._zooming=!0,n._stop(),lt(document,"touchmove",this._onTouchMove,this),lt(document,"touchend touchcancel",this._onTouchEnd,this),zt(t)}},_onTouchMove:function(t){if(!(!t.touches||t.touches.length!==2||!this._zooming)){var n=this._map,o=n.mouseEventToContainerPoint(t.touches[0]),u=n.mouseEventToContainerPoint(t.touches[1]),c=o.distanceTo(u)/this._startDist;if(this._zoom=n.getScaleZoom(c,this._startZoom),!n.options.bounceAtZoomLimits&&(this._zoom<n.getMinZoom()&&c<1||this._zoom>n.getMaxZoom()&&c>1)&&(this._zoom=n._limitZoom(this._zoom)),n.options.touchZoom==="center"){if(this._center=this._startLatLng,c===1)return}else{var m=o._add(u)._divideBy(2)._subtract(this._centerPoint);if(c===1&&m.x===0&&m.y===0)return;this._center=n.unproject(n.project(this._pinchStartLatLng,this._zoom).subtract(m),this._zoom)}this._moved||(n._moveStart(!0,!1),this._moved=!0),Nt(this._animRequest);var M=W(n._move,n,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=Ut(M,this,!0),zt(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,Nt(this._animRequest),Ct(document,"touchmove",this._onTouchMove,this),Ct(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});_t.addInitHook("addHandler","touchZoom",Hn),_t.BoxZoom=Vn,_t.DoubleClickZoom=re,_t.Drag=ot,_t.Keyboard=nn,_t.ScrollWheelZoom=rn,_t.TapHold=Ot,_t.TouchZoom=Hn,j.Bounds=Et,j.Browser=tt,j.CRS=Ce,j.Canvas=Jr,j.Circle=we,j.CircleMarker=qi,j.Class=he,j.Control=Qt,j.DivIcon=Kr,j.DivOverlay=Le,j.DomEvent=Oa,j.DomUtil=Sa,j.Draggable=Se,j.Evented=ci,j.FeatureGroup=Me,j.GeoJSON=_e,j.GridLayer=ki,j.Handler=de,j.Icon=Oe,j.ImageOverlay=ri,j.LatLng=vt,j.LatLngBounds=Vt,j.Layer=ne,j.LayerGroup=Ae,j.LineUtil=pe,j.Map=_t,j.Marker=Ki,j.Mixin=ei,j.Path=Fe,j.Point=ut,j.PolyUtil=Ra,j.Polygon=ni,j.Polyline=Ee,j.Popup=Qi,j.PosAnimation=vi,j.Projection=Na,j.Rectangle=Jt,j.Renderer=me,j.SVG=Mi,j.SVGOverlay=xi,j.TileLayer=ai,j.Tooltip=Ie,j.Transformation=$e,j.Util=Ue,j.VideoOverlay=$i,j.bind=W,j.bounds=It,j.canvas=Xr,j.circle=Gr,j.circleMarker=Nr,j.control=pt,j.divIcon=Ka,j.extend=U,j.featureGroup=Ga,j.geoJSON=jr,j.geoJson=Ur,j.gridLayer=qa,j.icon=Za,j.imageOverlay=Vr,j.latLng=ft,j.latLngBounds=Pt,j.layerGroup=bi,j.map=Fa,j.marker=Gn,j.point=at,j.polygon=ja,j.polyline=Ye,j.popup=Va,j.rectangle=Ht,j.setOptions=wt,j.stamp=F,j.svg=en,j.svgOverlay=Hr,j.tileLayer=qr,j.tooltip=Ha,j.transformation=di,j.version=$,j.videoOverlay=Ua;var ae=window.L;j.noConflict=function(){return window.L=ae,this},window.L=j})});var Mt=Ko(lo(),1);L.Control.Fullscreen=L.Control.extend({options:{position:"topleft",title:{false:"View Fullscreen",true:"Exit Fullscreen"}},onAdd:function(j){var $=L.DomUtil.create("div","leaflet-control-fullscreen leaflet-bar leaflet-control");return this.link=L.DomUtil.create("a","leaflet-control-fullscreen-button leaflet-bar-part",$),this.link.href="#",this._map=j,this._map.on("fullscreenchange",this._toggleTitle,this),this._toggleTitle(),L.DomEvent.on(this.link,"click",this._click,this),$},_click:function(j){L.DomEvent.stopPropagation(j),L.DomEvent.preventDefault(j),this._map.toggleFullscreen(this.options)},_toggleTitle:function(){this.link.title=this.options.title[this._map.isFullscreen()]}});L.Map.include({isFullscreen:function(){return this._isFullscreen||!1},toggleFullscreen:function(j){var $=this.getContainer();this.isFullscreen()?j&&j.pseudoFullscreen?this._disablePseudoFullscreen($):document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen?document.msExitFullscreen():this._disablePseudoFullscreen($):j&&j.pseudoFullscreen?this._enablePseudoFullscreen($):$.requestFullscreen?$.requestFullscreen():$.mozRequestFullScreen?$.mozRequestFullScreen():$.webkitRequestFullscreen?$.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):$.msRequestFullscreen?$.msRequestFullscreen():this._enablePseudoFullscreen($)},_enablePseudoFullscreen:function(j){L.DomUtil.addClass(j,"leaflet-pseudo-fullscreen"),this._setFullscreen(!0),this.fire("fullscreenchange")},_disablePseudoFullscreen:function(j){L.DomUtil.removeClass(j,"leaflet-pseudo-fullscreen"),this._setFullscreen(!1),this.fire("fullscreenchange")},_setFullscreen:function(j){this._isFullscreen=j;var $=this.getContainer();j?L.DomUtil.addClass($,"leaflet-fullscreen-on"):L.DomUtil.removeClass($,"leaflet-fullscreen-on"),this.invalidateSize()},_onFullscreenChange:function(j){var $=document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement;$===this.getContainer()&&!this._isFullscreen?(this._setFullscreen(!0),this.fire("fullscreenchange")):$!==this.getContainer()&&this._isFullscreen&&(this._setFullscreen(!1),this.fire("fullscreenchange"))}});L.Map.mergeOptions({fullscreenControl:!1});L.Map.addInitHook(function(){this.options.fullscreenControl&&(this.fullscreenControl=new L.Control.Fullscreen(this.options.fullscreenControl),this.addControl(this.fullscreenControl));var j;if("onfullscreenchange"in document?j="fullscreenchange":"onmozfullscreenchange"in document?j="mozfullscreenchange":"onwebkitfullscreenchange"in document?j="webkitfullscreenchange":"onmsfullscreenchange"in document&&(j="MSFullscreenChange"),j){var $=L.bind(this._onFullscreenChange,this);this.whenReady(function(){L.DomEvent.on(document,j,$)}),this.on("unload",function(){L.DomEvent.off(document,j,$)})}});L.control.fullscreen=function(j){return new L.Control.Fullscreen(j)};(()=>{var Io,ui;var j=Object.create,$=Object.defineProperty,U=Object.getOwnPropertyDescriptor,ue=Object.getOwnPropertyNames,W=Object.getPrototypeOf,ct=Object.prototype.hasOwnProperty,F=(e,i)=>()=>(i||e((i={exports:{}}).exports,i),i.exports),Wt=(e,i,r,a)=>{if(i&&typeof i=="object"||typeof i=="function")for(let s of ue(i))!ct.call(e,s)&&s!==r&&$(e,s,{get:()=>i[s],enumerable:!(a=U(i,s))||a.enumerable});return e},rt=(e,i,r)=>(r=e!=null?j(W(e)):{},Wt(i||!e||!e.__esModule?$(r,"default",{value:e,enumerable:!0}):r,e)),st=F((e,i)=>{function r(){this.__data__=[],this.size=0}i.exports=r}),Ft=F((e,i)=>{function r(a,s){return a===s||a!==a&&s!==s}i.exports=r}),Xe=F((e,i)=>{var r=Ft();function a(s,l){for(var h=s.length;h--;)if(r(s[h][0],l))return h;return-1}i.exports=a}),Ze=F((e,i)=>{var r=Xe(),a=Array.prototype,s=a.splice;function l(h){var d=this.__data__,p=r(d,h);if(p<0)return!1;var _=d.length-1;return p==_?d.pop():s.call(d,p,1),--this.size,!0}i.exports=l}),wt=F((e,i)=>{var r=Xe();function a(s){var l=this.__data__,h=r(l,s);return h<0?void 0:l[h][1]}i.exports=a}),Yn=F((e,i)=>{var r=Xe();function a(s){return r(this.__data__,s)>-1}i.exports=a}),la=F((e,i)=>{var r=Xe();function a(s,l){var h=this.__data__,d=r(h,s);return d<0?(++this.size,h.push([s,l])):h[d][1]=l,this}i.exports=a}),li=F((e,i)=>{var r=st(),a=Ze(),s=wt(),l=Yn(),h=la();function d(p){var _=-1,k=p==null?0:p.length;for(this.clear();++_<k;){var w=p[_];this.set(w[0],w[1])}}d.prototype.clear=r,d.prototype.delete=a,d.prototype.get=s,d.prototype.has=l,d.prototype.set=h,i.exports=d}),le=F((e,i)=>{var r=li();function a(){this.__data__=new r,this.size=0}i.exports=a}),un=F((e,i)=>{function r(a){var s=this.__data__,l=s.delete(a);return this.size=s.size,l}i.exports=r}),Pi=F((e,i)=>{function r(a){return this.__data__.get(a)}i.exports=r}),ln=F((e,i)=>{function r(a){return this.__data__.has(a)}i.exports=r}),hn=F((e,i)=>{var r=typeof global=="object"&&global&&global.Object===Object&&global;i.exports=r}),je=F((e,i)=>{var r=hn(),a=typeof self=="object"&&self&&self.Object===Object&&self,s=r||a||Function("return this")();i.exports=s}),hi=F((e,i)=>{var r=je(),a=r.Symbol;i.exports=a}),Jn=F((e,i)=>{var r=hi(),a=Object.prototype,s=a.hasOwnProperty,l=a.toString,h=r?r.toStringTag:void 0;function d(p){var _=s.call(p,h),k=p[h];try{p[h]=void 0;var w=!0}catch{}var z=l.call(p);return w&&(_?p[h]=k:delete p[h]),z}i.exports=d}),Ut=F((e,i)=>{var r=Object.prototype,a=r.toString;function s(l){return a.call(l)}i.exports=s}),Nt=F((e,i)=>{var r=hi(),a=Jn(),s=Ut(),l="[object Null]",h="[object Undefined]",d=r?r.toStringTag:void 0;function p(_){return _==null?_===void 0?h:l:d&&d in Object(_)?a(_):s(_)}i.exports=p}),Ue=F((e,i)=>{function r(a){var s=typeof a;return a!=null&&(s=="object"||s=="function")}i.exports=r}),he=F((e,i)=>{var r=Nt(),a=Ue(),s="[object AsyncFunction]",l="[object Function]",h="[object GeneratorFunction]",d="[object Proxy]";function p(_){if(!a(_))return!1;var k=r(_);return k==l||k==h||k==s||k==d}i.exports=p}),ha=F((e,i)=>{var r=je(),a=r["__core-js_shared__"];i.exports=a}),Yt=F((e,i)=>{var r=ha(),a=function(){var l=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}();function s(l){return!!a&&a in l}i.exports=s}),ci=F((e,i)=>{var r=Function.prototype,a=r.toString;function s(l){if(l!=null){try{return a.call(l)}catch{}try{return l+""}catch{}}return""}i.exports=s}),ut=F((e,i)=>{var r=he(),a=Yt(),s=Ue(),l=ci(),h=/[\\^$.*+?()[\]{}|]/g,d=/^\[object .+?Constructor\]$/,p=Function.prototype,_=Object.prototype,k=p.toString,w=_.hasOwnProperty,z=RegExp("^"+k.call(w).replace(h,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function A(V){if(!s(V)||a(V))return!1;var q=r(V)?z:d;return q.test(l(V))}i.exports=A}),Xn=F((e,i)=>{function r(a,s){return a?.[s]}i.exports=r}),at=F((e,i)=>{var r=ut(),a=Xn();function s(l,h){var d=a(l,h);return r(d)?d:void 0}i.exports=s}),Et=F((e,i)=>{var r=at(),a=je(),s=r(a,"Map");i.exports=s}),It=F((e,i)=>{var r=at(),a=r(Object,"create");i.exports=a}),Vt=F((e,i)=>{var r=It();function a(){this.__data__=r?r(null):{},this.size=0}i.exports=a}),Pt=F((e,i)=>{function r(a){var s=this.has(a)&&delete this.__data__[a];return this.size-=s?1:0,s}i.exports=r}),vt=F((e,i)=>{var r=It(),a="__lodash_hash_undefined__",s=Object.prototype,l=s.hasOwnProperty;function h(d){var p=this.__data__;if(r){var _=p[d];return _===a?void 0:_}return l.call(p,d)?p[d]:void 0}i.exports=h}),ft=F((e,i)=>{var r=It(),a=Object.prototype,s=a.hasOwnProperty;function l(h){var d=this.__data__;return r?d[h]!==void 0:s.call(d,h)}i.exports=l}),Ce=F((e,i)=>{var r=It(),a="__lodash_hash_undefined__";function s(l,h){var d=this.__data__;return this.size+=this.has(l)?0:1,d[l]=r&&h===void 0?a:h,this}i.exports=s}),Pe=F((e,i)=>{var r=Vt(),a=Pt(),s=vt(),l=ft(),h=Ce();function d(p){var _=-1,k=p==null?0:p.length;for(this.clear();++_<k;){var w=p[_];this.set(w[0],w[1])}}d.prototype.clear=r,d.prototype.delete=a,d.prototype.get=s,d.prototype.has=l,d.prototype.set=h,i.exports=d}),$n=F((e,i)=>{var r=Pe(),a=li(),s=Et();function l(){this.size=0,this.__data__={hash:new r,map:new(s||a),string:new r}}i.exports=l}),cn=F((e,i)=>{function r(a){var s=typeof a;return s=="string"||s=="number"||s=="symbol"||s=="boolean"?a!=="__proto__":a===null}i.exports=r}),$e=F((e,i)=>{var r=cn();function a(s,l){var h=s.__data__;return r(l)?h[typeof l=="string"?"string":"hash"]:h.map}i.exports=a}),di=F((e,i)=>{var r=$e();function a(s){var l=r(this,s).delete(s);return this.size-=l?1:0,l}i.exports=a}),dn=F((e,i)=>{var r=$e();function a(s){return r(this,s).get(s)}i.exports=a}),ca=F((e,i)=>{var r=$e();function a(s){return r(this,s).has(s)}i.exports=a}),Qn=F((e,i)=>{var r=$e();function a(s,l){var h=r(this,s),d=h.size;return h.set(s,l),this.size+=h.size==d?0:1,this}i.exports=a}),pn=F((e,i)=>{var r=$n(),a=di(),s=dn(),l=ca(),h=Qn();function d(p){var _=-1,k=p==null?0:p.length;for(this.clear();++_<k;){var w=p[_];this.set(w[0],w[1])}}d.prototype.clear=r,d.prototype.delete=a,d.prototype.get=s,d.prototype.has=l,d.prototype.set=h,i.exports=d}),fn=F((e,i)=>{var r=li(),a=Et(),s=pn(),l=200;function h(d,p){var _=this.__data__;if(_ instanceof r){var k=_.__data__;if(!a||k.length<l-1)return k.push([d,p]),this.size=++_.size,this;_=this.__data__=new s(k)}return _.set(d,p),this.size=_.size,this}i.exports=h}),Ti=F((e,i)=>{var r=li(),a=le(),s=un(),l=Pi(),h=ln(),d=fn();function p(_){var k=this.__data__=new r(_);this.size=k.size}p.prototype.clear=a,p.prototype.delete=s,p.prototype.get=l,p.prototype.has=h,p.prototype.set=d,i.exports=p}),tr=F((e,i)=>{var r=at(),a=function(){try{var s=r(Object,"defineProperty");return s({},"",{}),s}catch{}}();i.exports=a}),Di=F((e,i)=>{var r=tr();function a(s,l,h){l=="__proto__"&&r?r(s,l,{configurable:!0,enumerable:!0,value:h,writable:!0}):s[l]=h}i.exports=a}),Si=F((e,i)=>{var r=Di(),a=Ft();function s(l,h,d){(d!==void 0&&!a(l[h],d)||d===void 0&&!(h in l))&&r(l,h,d)}i.exports=s}),er=F((e,i)=>{function r(a){return function(s,l,h){for(var d=-1,p=Object(s),_=h(s),k=_.length;k--;){var w=_[a?k:++d];if(l(p[w],w,p)===!1)break}return s}}i.exports=r}),ir=F((e,i)=>{var r=er(),a=r();i.exports=a}),da=F((e,i)=>{var r=je(),a=typeof e=="object"&&e&&!e.nodeType&&e,s=a&&typeof i=="object"&&i&&!i.nodeType&&i,l=s&&s.exports===a,h=l?r.Buffer:void 0,d=h?h.allocUnsafe:void 0;function p(_,k){if(k)return _.slice();var w=_.length,z=d?d(w):new _.constructor(w);return _.copy(z),z}i.exports=p}),pa=F((e,i)=>{var r=je(),a=r.Uint8Array;i.exports=a}),_n=F((e,i)=>{var r=pa();function a(s){var l=new s.constructor(s.byteLength);return new r(l).set(new r(s)),l}i.exports=a}),nr=F((e,i)=>{var r=_n();function a(s,l){var h=l?r(s.buffer):s.buffer;return new s.constructor(h,s.byteOffset,s.length)}i.exports=a}),rr=F((e,i)=>{function r(a,s){var l=-1,h=a.length;for(s||(s=Array(h));++l<h;)s[l]=a[l];return s}i.exports=r}),fa=F((e,i)=>{var r=Ue(),a=Object.create,s=function(){function l(){}return function(h){if(!r(h))return{};if(a)return a(h);l.prototype=h;var d=new l;return l.prototype=void 0,d}}();i.exports=s}),ar=F((e,i)=>{function r(a,s){return function(l){return a(s(l))}}i.exports=r}),mn=F((e,i)=>{var r=ar(),a=r(Object.getPrototypeOf,Object);i.exports=a}),or=F((e,i)=>{var r=Object.prototype;function a(s){var l=s&&s.constructor,h=typeof l=="function"&&l.prototype||r;return s===h}i.exports=a}),sr=F((e,i)=>{var r=fa(),a=mn(),s=or();function l(h){return typeof h.constructor=="function"&&!s(h)?r(a(h)):{}}i.exports=l}),Te=F((e,i)=>{function r(a){return a!=null&&typeof a=="object"}i.exports=r}),ur=F((e,i)=>{var r=Nt(),a=Te(),s="[object Arguments]";function l(h){return a(h)&&r(h)==s}i.exports=l}),lr=F((e,i)=>{var r=ur(),a=Te(),s=Object.prototype,l=s.hasOwnProperty,h=s.propertyIsEnumerable,d=r(function(){return arguments}())?r:function(p){return a(p)&&l.call(p,"callee")&&!h.call(p,"callee")};i.exports=d}),xe=F((e,i)=>{var r=Array.isArray;i.exports=r}),hr=F((e,i)=>{var r=9007199254740991;function a(s){return typeof s=="number"&&s>-1&&s%1==0&&s<=r}i.exports=a}),gn=F((e,i)=>{var r=he(),a=hr();function s(l){return l!=null&&a(l.length)&&!r(l)}i.exports=s}),cr=F((e,i)=>{var r=gn(),a=Te();function s(l){return a(l)&&r(l)}i.exports=s}),dr=F((e,i)=>{function r(){return!1}i.exports=r}),yn=F((e,i)=>{var r=je(),a=dr(),s=typeof e=="object"&&e&&!e.nodeType&&e,l=s&&typeof i=="object"&&i&&!i.nodeType&&i,h=l&&l.exports===s,d=h?r.Buffer:void 0,p=d?d.isBuffer:void 0,_=p||a;i.exports=_}),_a=F((e,i)=>{var r=Nt(),a=mn(),s=Te(),l="[object Object]",h=Function.prototype,d=Object.prototype,p=h.toString,_=d.hasOwnProperty,k=p.call(Object);function w(z){if(!s(z)||r(z)!=l)return!1;var A=a(z);if(A===null)return!0;var V=_.call(A,"constructor")&&A.constructor;return typeof V=="function"&&V instanceof V&&p.call(V)==k}i.exports=w}),ma=F((e,i)=>{var r=Nt(),a=hr(),s=Te(),l="[object Arguments]",h="[object Array]",d="[object Boolean]",p="[object Date]",_="[object Error]",k="[object Function]",w="[object Map]",z="[object Number]",A="[object Object]",V="[object RegExp]",q="[object Set]",X="[object String]",mt="[object WeakMap]",x="[object ArrayBuffer]",E="[object DataView]",D="[object Float32Array]",G="[object Float64Array]",S="[object Int8Array]",N="[object Int16Array]",f="[object Int32Array]",g="[object Uint8Array]",v="[object Uint8ClampedArray]",y="[object Uint16Array]",b="[object Uint32Array]",C={};C[D]=C[G]=C[S]=C[N]=C[f]=C[g]=C[v]=C[y]=C[b]=!0,C[l]=C[h]=C[x]=C[d]=C[E]=C[p]=C[_]=C[k]=C[w]=C[z]=C[A]=C[V]=C[q]=C[X]=C[mt]=!1;function P(B){return s(B)&&a(B.length)&&!!C[r(B)]}i.exports=P}),ga=F((e,i)=>{function r(a){return function(s){return a(s)}}i.exports=r}),ya=F((e,i)=>{var r=hn(),a=typeof e=="object"&&e&&!e.nodeType&&e,s=a&&typeof i=="object"&&i&&!i.nodeType&&i,l=s&&s.exports===a,h=l&&r.process,d=function(){try{var p=s&&s.require&&s.require("util").types;return p||h&&h.binding&&h.binding("util")}catch{}}();i.exports=d}),pr=F((e,i)=>{var r=ma(),a=ga(),s=ya(),l=s&&s.isTypedArray,h=l?a(l):r;i.exports=h}),fr=F((e,i)=>{function r(a,s){if(!(s==="constructor"&&typeof a[s]=="function")&&s!="__proto__")return a[s]}i.exports=r}),vn=F((e,i)=>{var r=Di(),a=Ft(),s=Object.prototype,l=s.hasOwnProperty;function h(d,p,_){var k=d[p];(!(l.call(d,p)&&a(k,_))||_===void 0&&!(p in d))&&r(d,p,_)}i.exports=h}),va=F((e,i)=>{var r=vn(),a=Di();function s(l,h,d,p){var _=!d;d||(d={});for(var k=-1,w=h.length;++k<w;){var z=h[k],A=p?p(d[z],l[z],z,d,l):void 0;A===void 0&&(A=l[z]),_?a(d,z,A):r(d,z,A)}return d}i.exports=s}),La=F((e,i)=>{function r(a,s){for(var l=-1,h=Array(a);++l<a;)h[l]=s(l);return h}i.exports=r}),_r=F((e,i)=>{var r=9007199254740991,a=/^(?:0|[1-9]\d*)$/;function s(l,h){var d=typeof l;return h=h??r,!!h&&(d=="number"||d!="symbol"&&a.test(l))&&l>-1&&l%1==0&&l<h}i.exports=s}),ba=F((e,i)=>{var r=La(),a=lr(),s=xe(),l=yn(),h=_r(),d=pr(),p=Object.prototype,_=p.hasOwnProperty;function k(w,z){var A=s(w),V=!A&&a(w),q=!A&&!V&&l(w),X=!A&&!V&&!q&&d(w),mt=A||V||q||X,x=mt?r(w.length,String):[],E=x.length;for(var D in w)(z||_.call(w,D))&&!(mt&&(D=="length"||q&&(D=="offset"||D=="parent")||X&&(D=="buffer"||D=="byteLength"||D=="byteOffset")||h(D,E)))&&x.push(D);return x}i.exports=k}),ge=F((e,i)=>{function r(a){var s=[];if(a!=null)for(var l in Object(a))s.push(l);return s}i.exports=r}),tt=F((e,i)=>{var r=Ue(),a=or(),s=ge(),l=Object.prototype,h=l.hasOwnProperty;function d(p){if(!r(p))return s(p);var _=a(p),k=[];for(var w in p)w=="constructor"&&(_||!h.call(p,w))||k.push(w);return k}i.exports=d}),Ln=F((e,i)=>{var r=ba(),a=tt(),s=gn();function l(h){return s(h)?r(h,!0):a(h)}i.exports=l}),mr=F((e,i)=>{var r=va(),a=Ln();function s(l){return r(l,a(l))}i.exports=s}),gr=F((e,i)=>{var r=Si(),a=da(),s=nr(),l=rr(),h=sr(),d=lr(),p=xe(),_=cr(),k=yn(),w=he(),z=Ue(),A=_a(),V=pr(),q=fr(),X=mr();function mt(x,E,D,G,S,N,f){var g=q(x,D),v=q(E,D),y=f.get(v);if(y){r(x,D,y);return}var b=N?N(g,v,D+"",x,E,f):void 0,C=b===void 0;if(C){var P=p(v),B=!P&&k(v),T=!P&&!B&&V(v);b=v,P||B||T?p(g)?b=g:_(g)?b=l(g):B?(C=!1,b=a(v,!0)):T?(C=!1,b=s(v,!0)):b=[]:A(v)||d(v)?(b=g,d(g)?b=X(g):(!z(g)||w(g))&&(b=h(v))):C=!1}C&&(f.set(v,b),S(b,v,G,N,f),f.delete(v)),r(x,D,b)}i.exports=mt}),yr=F((e,i)=>{var r=Ti(),a=Si(),s=ir(),l=gr(),h=Ue(),d=Ln(),p=fr();function _(k,w,z,A,V){k!==w&&s(w,function(q,X){if(V||(V=new r),h(q))l(k,w,X,z,_,A,V);else{var mt=A?A(p(k,X),q,X+"",k,w,V):void 0;mt===void 0&&(mt=q),a(k,X,mt)}},d)}i.exports=_}),Ai=F((e,i)=>{function r(a){return a}i.exports=r}),vr=F((e,i)=>{function r(a,s,l){switch(l.length){case 0:return a.call(s);case 1:return a.call(s,l[0]);case 2:return a.call(s,l[0],l[1]);case 3:return a.call(s,l[0],l[1],l[2])}return a.apply(s,l)}i.exports=r}),Qe=F((e,i)=>{var r=vr(),a=Math.max;function s(l,h,d){return h=a(h===void 0?l.length-1:h,0),function(){for(var p=arguments,_=-1,k=a(p.length-h,0),w=Array(k);++_<k;)w[_]=p[h+_];_=-1;for(var z=Array(h+1);++_<h;)z[_]=p[_];return z[h]=d(w),r(l,this,z)}}i.exports=s}),Lr=F((e,i)=>{function r(a){return function(){return a}}i.exports=r}),Ca=F((e,i)=>{var r=Lr(),a=tr(),s=Ai(),l=a?function(h,d){return a(h,"toString",{configurable:!0,enumerable:!1,value:r(d),writable:!0})}:s;i.exports=l}),xa=F((e,i)=>{var r=800,a=16,s=Date.now;function l(h){var d=0,p=0;return function(){var _=s(),k=a-(_-p);if(p=_,k>0){if(++d>=r)return arguments[0]}else d=0;return h.apply(void 0,arguments)}}i.exports=l}),ka=F((e,i)=>{var r=Ca(),a=xa(),s=a(r);i.exports=s}),Ma=F((e,i)=>{var r=Ai(),a=Qe(),s=ka();function l(h,d){return s(a(h,d,r),h+"")}i.exports=l}),br=F((e,i)=>{var r=Ft(),a=gn(),s=_r(),l=Ue();function h(d,p,_){if(!l(_))return!1;var k=typeof p;return(k=="number"?a(_)&&s(p,_.length):k=="string"&&p in _)?r(_[p],d):!1}i.exports=h}),wa=F((e,i)=>{var r=Ma(),a=br();function s(l){return r(function(h,d){var p=-1,_=d.length,k=_>1?d[_-1]:void 0,w=_>2?d[2]:void 0;for(k=l.length>3&&typeof k=="function"?(_--,k):void 0,w&&a(d[0],d[1],w)&&(k=_<3?void 0:k,_=1),h=Object(h);++p<_;){var z=d[p];z&&l(h,z,p,k)}return h})}i.exports=s}),ti=F((e,i)=>{var r=yr(),a=wa(),s=a(function(l,h,d){r(l,h,d)});i.exports=s}),bn=F((e,i)=>{var r=Nt(),a=Te(),s="[object Symbol]";function l(h){return typeof h=="symbol"||a(h)&&r(h)==s}i.exports=l}),Ea=F((e,i)=>{var r=xe(),a=bn(),s=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,l=/^\w*$/;function h(d,p){if(r(d))return!1;var _=typeof d;return _=="number"||_=="symbol"||_=="boolean"||d==null||a(d)?!0:l.test(d)||!s.test(d)||p!=null&&d in Object(p)}i.exports=h}),Ba=F((e,i)=>{var r=pn(),a="Expected a function";function s(l,h){if(typeof l!="function"||h!=null&&typeof h!="function")throw new TypeError(a);var d=function(){var p=arguments,_=h?h.apply(this,p):p[0],k=d.cache;if(k.has(_))return k.get(_);var w=l.apply(this,p);return d.cache=k.set(_,w)||k,w};return d.cache=new(s.Cache||r),d}s.Cache=r,i.exports=s}),Pa=F((e,i)=>{var r=Ba(),a=500;function s(l){var h=r(l,function(p){return d.size===a&&d.clear(),p}),d=h.cache;return h}i.exports=s}),Ta=F((e,i)=>{var r=Pa(),a=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,s=/\\(\\)?/g,l=r(function(h){var d=[];return h.charCodeAt(0)===46&&d.push(""),h.replace(a,function(p,_,k,w){d.push(k?w.replace(s,"$1"):_||p)}),d});i.exports=l}),Cn=F((e,i)=>{function r(a,s){for(var l=-1,h=a==null?0:a.length,d=Array(h);++l<h;)d[l]=s(a[l],l,a);return d}i.exports=r}),pi=F((e,i)=>{var r=hi(),a=Cn(),s=xe(),l=bn(),h=1/0,d=r?r.prototype:void 0,p=d?d.toString:void 0;function _(k){if(typeof k=="string")return k;if(s(k))return a(k,_)+"";if(l(k))return p?p.call(k):"";var w=k+"";return w=="0"&&1/k==-h?"-0":w}i.exports=_}),Cr=F((e,i)=>{var r=pi();function a(s){return s==null?"":r(s)}i.exports=a}),xr=F((e,i)=>{var r=xe(),a=Ea(),s=Ta(),l=Cr();function h(d,p){return r(d)?d:a(d,p)?[d]:s(l(d))}i.exports=h}),fi=F((e,i)=>{var r=bn(),a=1/0;function s(l){if(typeof l=="string"||r(l))return l;var h=l+"";return h=="0"&&1/l==-a?"-0":h}i.exports=s}),gt=F((e,i)=>{var r=xr(),a=fi();function s(l,h){h=r(h,l);for(var d=0,p=h.length;l!=null&&d<p;)l=l[a(h[d++])];return d&&d==p?l:void 0}i.exports=s}),bt=F((e,i)=>{var r=gt();function a(s,l,h){var d=s==null?void 0:r(s,l);return d===void 0?h:d}i.exports=a}),Oi=F((e,i)=>{(function(r,a){typeof e=="object"&&typeof i<"u"?i.exports=a():typeof define=="function"&&define.amd?define(a):(r=r||self).RBush=a()})(e,function(){"use strict";function r(x,E,D,G,S){(function N(f,g,v,y,b){for(;y>v;){if(y-v>600){var C=y-v+1,P=g-v+1,B=Math.log(C),T=.5*Math.exp(2*B/3),I=.5*Math.sqrt(B*T*(C-T)/C)*(P-C/2<0?-1:1),R=Math.max(v,Math.floor(g-P*T/C+I)),H=Math.min(y,Math.floor(g+(C-P)*T/C+I));N(f,g,R,H,b)}var J=f[g],Q=v,it=y;for(a(f,v,g),b(f[y],J)>0&&a(f,v,y);Q<it;){for(a(f,Q,it),Q++,it--;b(f[Q],J)<0;)Q++;for(;b(f[it],J)>0;)it--}b(f[v],J)===0?a(f,v,it):a(f,++it,y),it<=g&&(v=it+1),g<=it&&(y=it-1)}})(x,E,D||0,G||x.length-1,S||s)}function a(x,E,D){var G=x[E];x[E]=x[D],x[D]=G}function s(x,E){return x<E?-1:x>E?1:0}var l=function(x){x===void 0&&(x=9),this._maxEntries=Math.max(4,x),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function h(x,E,D){if(!D)return E.indexOf(x);for(var G=0;G<E.length;G++)if(D(x,E[G]))return G;return-1}function d(x,E){p(x,0,x.children.length,E,x)}function p(x,E,D,G,S){S||(S=X(null)),S.minX=1/0,S.minY=1/0,S.maxX=-1/0,S.maxY=-1/0;for(var N=E;N<D;N++){var f=x.children[N];_(S,x.leaf?G(f):f)}return S}function _(x,E){return x.minX=Math.min(x.minX,E.minX),x.minY=Math.min(x.minY,E.minY),x.maxX=Math.max(x.maxX,E.maxX),x.maxY=Math.max(x.maxY,E.maxY),x}function k(x,E){return x.minX-E.minX}function w(x,E){return x.minY-E.minY}function z(x){return(x.maxX-x.minX)*(x.maxY-x.minY)}function A(x){return x.maxX-x.minX+(x.maxY-x.minY)}function V(x,E){return x.minX<=E.minX&&x.minY<=E.minY&&E.maxX<=x.maxX&&E.maxY<=x.maxY}function q(x,E){return E.minX<=x.maxX&&E.minY<=x.maxY&&E.maxX>=x.minX&&E.maxY>=x.minY}function X(x){return{children:x,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function mt(x,E,D,G,S){for(var N=[E,D];N.length;)if(!((D=N.pop())-(E=N.pop())<=G)){var f=E+Math.ceil((D-E)/G/2)*G;r(x,f,E,D,S),N.push(E,f,f,D)}}return l.prototype.all=function(){return this._all(this.data,[])},l.prototype.search=function(x){var E=this.data,D=[];if(!q(x,E))return D;for(var G=this.toBBox,S=[];E;){for(var N=0;N<E.children.length;N++){var f=E.children[N],g=E.leaf?G(f):f;q(x,g)&&(E.leaf?D.push(f):V(x,g)?this._all(f,D):S.push(f))}E=S.pop()}return D},l.prototype.collides=function(x){var E=this.data;if(!q(x,E))return!1;for(var D=[];E;){for(var G=0;G<E.children.length;G++){var S=E.children[G],N=E.leaf?this.toBBox(S):S;if(q(x,N)){if(E.leaf||V(x,N))return!0;D.push(S)}}E=D.pop()}return!1},l.prototype.load=function(x){if(!x||!x.length)return this;if(x.length<this._minEntries){for(var E=0;E<x.length;E++)this.insert(x[E]);return this}var D=this._build(x.slice(),0,x.length-1,0);if(this.data.children.length)if(this.data.height===D.height)this._splitRoot(this.data,D);else{if(this.data.height<D.height){var G=this.data;this.data=D,D=G}this._insert(D,this.data.height-D.height-1,!0)}else this.data=D;return this},l.prototype.insert=function(x){return x&&this._insert(x,this.data.height-1),this},l.prototype.clear=function(){return this.data=X([]),this},l.prototype.remove=function(x,E){if(!x)return this;for(var D,G,S,N=this.data,f=this.toBBox(x),g=[],v=[];N||g.length;){if(N||(N=g.pop(),G=g[g.length-1],D=v.pop(),S=!0),N.leaf){var y=h(x,N.children,E);if(y!==-1)return N.children.splice(y,1),g.push(N),this._condense(g),this}S||N.leaf||!V(N,f)?G?(D++,N=G.children[D],S=!1):N=null:(g.push(N),v.push(D),D=0,G=N,N=N.children[0])}return this},l.prototype.toBBox=function(x){return x},l.prototype.compareMinX=function(x,E){return x.minX-E.minX},l.prototype.compareMinY=function(x,E){return x.minY-E.minY},l.prototype.toJSON=function(){return this.data},l.prototype.fromJSON=function(x){return this.data=x,this},l.prototype._all=function(x,E){for(var D=[];x;)x.leaf?E.push.apply(E,x.children):D.push.apply(D,x.children),x=D.pop();return E},l.prototype._build=function(x,E,D,G){var S,N=D-E+1,f=this._maxEntries;if(N<=f)return d(S=X(x.slice(E,D+1)),this.toBBox),S;G||(G=Math.ceil(Math.log(N)/Math.log(f)),f=Math.ceil(N/Math.pow(f,G-1))),(S=X([])).leaf=!1,S.height=G;var g=Math.ceil(N/f),v=g*Math.ceil(Math.sqrt(f));mt(x,E,D,v,this.compareMinX);for(var y=E;y<=D;y+=v){var b=Math.min(y+v-1,D);mt(x,y,b,g,this.compareMinY);for(var C=y;C<=b;C+=g){var P=Math.min(C+g-1,b);S.children.push(this._build(x,C,P,G-1))}}return d(S,this.toBBox),S},l.prototype._chooseSubtree=function(x,E,D,G){for(;G.push(E),!E.leaf&&G.length-1!==D;){for(var S=1/0,N=1/0,f=void 0,g=0;g<E.children.length;g++){var v=E.children[g],y=z(v),b=(C=x,P=v,(Math.max(P.maxX,C.maxX)-Math.min(P.minX,C.minX))*(Math.max(P.maxY,C.maxY)-Math.min(P.minY,C.minY))-y);b<N?(N=b,S=y<S?y:S,f=v):b===N&&y<S&&(S=y,f=v)}E=f||E.children[0]}var C,P;return E},l.prototype._insert=function(x,E,D){var G=D?x:this.toBBox(x),S=[],N=this._chooseSubtree(G,this.data,E,S);for(N.children.push(x),_(N,G);E>=0&&S[E].children.length>this._maxEntries;)this._split(S,E),E--;this._adjustParentBBoxes(G,S,E)},l.prototype._split=function(x,E){var D=x[E],G=D.children.length,S=this._minEntries;this._chooseSplitAxis(D,S,G);var N=this._chooseSplitIndex(D,S,G),f=X(D.children.splice(N,D.children.length-N));f.height=D.height,f.leaf=D.leaf,d(D,this.toBBox),d(f,this.toBBox),E?x[E-1].children.push(f):this._splitRoot(D,f)},l.prototype._splitRoot=function(x,E){this.data=X([x,E]),this.data.height=x.height+1,this.data.leaf=!1,d(this.data,this.toBBox)},l.prototype._chooseSplitIndex=function(x,E,D){for(var G,S,N,f,g,v,y,b=1/0,C=1/0,P=E;P<=D-E;P++){var B=p(x,0,P,this.toBBox),T=p(x,P,D,this.toBBox),I=(S=B,N=T,f=void 0,g=void 0,v=void 0,y=void 0,f=Math.max(S.minX,N.minX),g=Math.max(S.minY,N.minY),v=Math.min(S.maxX,N.maxX),y=Math.min(S.maxY,N.maxY),Math.max(0,v-f)*Math.max(0,y-g)),R=z(B)+z(T);I<b?(b=I,G=P,C=R<C?R:C):I===b&&R<C&&(C=R,G=P)}return G||D-E},l.prototype._chooseSplitAxis=function(x,E,D){var G=x.leaf?this.compareMinX:k,S=x.leaf?this.compareMinY:w;this._allDistMargin(x,E,D,G)<this._allDistMargin(x,E,D,S)&&x.children.sort(G)},l.prototype._allDistMargin=function(x,E,D,G){x.children.sort(G);for(var S=this.toBBox,N=p(x,0,E,S),f=p(x,D-E,D,S),g=A(N)+A(f),v=E;v<D-E;v++){var y=x.children[v];_(N,x.leaf?S(y):y),g+=A(N)}for(var b=D-E-1;b>=E;b--){var C=x.children[b];_(f,x.leaf?S(C):C),g+=A(f)}return g},l.prototype._adjustParentBBoxes=function(x,E,D){for(var G=D;G>=0;G--)_(E[G],x)},l.prototype._condense=function(x){for(var E=x.length-1,D=void 0;E>=0;E--)x[E].children.length===0?E>0?(D=x[E-1].children).splice(D.indexOf(x[E]),1):this.clear():d(x[E],this.toBBox)},l})}),Ve=F(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.earthRadius=63710088e-1,e.factors={centimeters:e.earthRadius*100,centimetres:e.earthRadius*100,degrees:e.earthRadius/111325,feet:e.earthRadius*3.28084,inches:e.earthRadius*39.37,kilometers:e.earthRadius/1e3,kilometres:e.earthRadius/1e3,meters:e.earthRadius,metres:e.earthRadius,miles:e.earthRadius/1609.344,millimeters:e.earthRadius*1e3,millimetres:e.earthRadius*1e3,nauticalmiles:e.earthRadius/1852,radians:1,yards:e.earthRadius*1.0936},e.unitsFactors={centimeters:100,centimetres:100,degrees:1/111325,feet:3.28084,inches:39.37,kilometers:1/1e3,kilometres:1/1e3,meters:1,metres:1,miles:1/1609.344,millimeters:1e3,millimetres:1e3,nauticalmiles:1/1852,radians:1/e.earthRadius,yards:1.0936133},e.areaFactors={acres:247105e-9,centimeters:1e4,centimetres:1e4,feet:10.763910417,hectares:1e-4,inches:1550.003100006,kilometers:1e-6,kilometres:1e-6,meters:1,metres:1,miles:386e-9,millimeters:1e6,millimetres:1e6,yards:1.195990046};function i(y,b,C){C===void 0&&(C={});var P={type:"Feature"};return(C.id===0||C.id)&&(P.id=C.id),C.bbox&&(P.bbox=C.bbox),P.properties=b||{},P.geometry=y,P}e.feature=i;function r(y,b,C){switch(C===void 0&&(C={}),y){case"Point":return a(b).geometry;case"LineString":return d(b).geometry;case"Polygon":return l(b).geometry;case"MultiPoint":return w(b).geometry;case"MultiLineString":return k(b).geometry;case"MultiPolygon":return z(b).geometry;default:throw new Error(y+" is invalid")}}e.geometry=r;function a(y,b,C){if(C===void 0&&(C={}),!y)throw new Error("coordinates is required");if(!Array.isArray(y))throw new Error("coordinates must be an Array");if(y.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!N(y[0])||!N(y[1]))throw new Error("coordinates must contain numbers");var P={type:"Point",coordinates:y};return i(P,b,C)}e.point=a;function s(y,b,C){return C===void 0&&(C={}),_(y.map(function(P){return a(P,b)}),C)}e.points=s;function l(y,b,C){C===void 0&&(C={});for(var P=0,B=y;P<B.length;P++){var T=B[P];if(T.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var I=0;I<T[T.length-1].length;I++)if(T[T.length-1][I]!==T[0][I])throw new Error("First and last Position are not equivalent.")}var R={type:"Polygon",coordinates:y};return i(R,b,C)}e.polygon=l;function h(y,b,C){return C===void 0&&(C={}),_(y.map(function(P){return l(P,b)}),C)}e.polygons=h;function d(y,b,C){if(C===void 0&&(C={}),y.length<2)throw new Error("coordinates must be an array of two or more positions");var P={type:"LineString",coordinates:y};return i(P,b,C)}e.lineString=d;function p(y,b,C){return C===void 0&&(C={}),_(y.map(function(P){return d(P,b)}),C)}e.lineStrings=p;function _(y,b){b===void 0&&(b={});var C={type:"FeatureCollection"};return b.id&&(C.id=b.id),b.bbox&&(C.bbox=b.bbox),C.features=y,C}e.featureCollection=_;function k(y,b,C){C===void 0&&(C={});var P={type:"MultiLineString",coordinates:y};return i(P,b,C)}e.multiLineString=k;function w(y,b,C){C===void 0&&(C={});var P={type:"MultiPoint",coordinates:y};return i(P,b,C)}e.multiPoint=w;function z(y,b,C){C===void 0&&(C={});var P={type:"MultiPolygon",coordinates:y};return i(P,b,C)}e.multiPolygon=z;function A(y,b,C){C===void 0&&(C={});var P={type:"GeometryCollection",geometries:y};return i(P,b,C)}e.geometryCollection=A;function V(y,b){if(b===void 0&&(b=0),b&&!(b>=0))throw new Error("precision must be a positive number");var C=Math.pow(10,b||0);return Math.round(y*C)/C}e.round=V;function q(y,b){b===void 0&&(b="kilometers");var C=e.factors[b];if(!C)throw new Error(b+" units is invalid");return y*C}e.radiansToLength=q;function X(y,b){b===void 0&&(b="kilometers");var C=e.factors[b];if(!C)throw new Error(b+" units is invalid");return y/C}e.lengthToRadians=X;function mt(y,b){return E(X(y,b))}e.lengthToDegrees=mt;function x(y){var b=y%360;return b<0&&(b+=360),b}e.bearingToAzimuth=x;function E(y){var b=y%(2*Math.PI);return b*180/Math.PI}e.radiansToDegrees=E;function D(y){var b=y%360;return b*Math.PI/180}e.degreesToRadians=D;function G(y,b,C){if(b===void 0&&(b="kilometers"),C===void 0&&(C="kilometers"),!(y>=0))throw new Error("length must be a positive number");return q(X(y,b),C)}e.convertLength=G;function S(y,b,C){if(b===void 0&&(b="meters"),C===void 0&&(C="kilometers"),!(y>=0))throw new Error("area must be a positive number");var P=e.areaFactors[b];if(!P)throw new Error("invalid original units");var B=e.areaFactors[C];if(!B)throw new Error("invalid final units");return y/P*B}e.convertArea=S;function N(y){return!isNaN(y)&&y!==null&&!Array.isArray(y)}e.isNumber=N;function f(y){return!!y&&y.constructor===Object}e.isObject=f;function g(y){if(!y)throw new Error("bbox is required");if(!Array.isArray(y))throw new Error("bbox must be an Array");if(y.length!==4&&y.length!==6)throw new Error("bbox must be an Array of 4 or 6 numbers");y.forEach(function(b){if(!N(b))throw new Error("bbox must only contain numbers")})}e.validateBBox=g;function v(y){if(!y)throw new Error("id is required");if(["string","number"].indexOf(typeof y)===-1)throw new Error("id must be a number or a string")}e.validateId=v}),He=F(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=Ve();function r(E,D,G){if(E!==null)for(var S,N,f,g,v,y,b,C=0,P=0,B,T=E.type,I=T==="FeatureCollection",R=T==="Feature",H=I?E.features.length:1,J=0;J<H;J++){b=I?E.features[J].geometry:R?E.geometry:E,B=b?b.type==="GeometryCollection":!1,v=B?b.geometries.length:1;for(var Q=0;Q<v;Q++){var it=0,Lt=0;if(g=B?b.geometries[Q]:b,g!==null){y=g.coordinates;var xt=g.type;switch(C=G&&(xt==="Polygon"||xt==="MultiPolygon")?1:0,xt){case null:break;case"Point":if(D(y,P,J,it,Lt)===!1)return!1;P++,it++;break;case"LineString":case"MultiPoint":for(S=0;S<y.length;S++){if(D(y[S],P,J,it,Lt)===!1)return!1;P++,xt==="MultiPoint"&&it++}xt==="LineString"&&it++;break;case"Polygon":case"MultiLineString":for(S=0;S<y.length;S++){for(N=0;N<y[S].length-C;N++){if(D(y[S][N],P,J,it,Lt)===!1)return!1;P++}xt==="MultiLineString"&&it++,xt==="Polygon"&&Lt++}xt==="Polygon"&&it++;break;case"MultiPolygon":for(S=0;S<y.length;S++){for(Lt=0,N=0;N<y[S].length;N++){for(f=0;f<y[S][N].length-C;f++){if(D(y[S][N][f],P,J,it,Lt)===!1)return!1;P++}Lt++}it++}break;case"GeometryCollection":for(S=0;S<g.geometries.length;S++)if(r(g.geometries[S],D,G)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function a(E,D,G,S){var N=G;return r(E,function(f,g,v,y,b){g===0&&G===void 0?N=f:N=D(N,f,g,v,y,b)},S),N}function s(E,D){var G;switch(E.type){case"FeatureCollection":for(G=0;G<E.features.length&&D(E.features[G].properties,G)!==!1;G++);break;case"Feature":D(E.properties,0);break}}function l(E,D,G){var S=G;return s(E,function(N,f){f===0&&G===void 0?S=N:S=D(S,N,f)}),S}function h(E,D){if(E.type==="Feature")D(E,0);else if(E.type==="FeatureCollection")for(var G=0;G<E.features.length&&D(E.features[G],G)!==!1;G++);}function d(E,D,G){var S=G;return h(E,function(N,f){f===0&&G===void 0?S=N:S=D(S,N,f)}),S}function p(E){var D=[];return r(E,function(G){D.push(G)}),D}function _(E,D){var G,S,N,f,g,v,y,b,C,P,B=0,T=E.type==="FeatureCollection",I=E.type==="Feature",R=T?E.features.length:1;for(G=0;G<R;G++){for(v=T?E.features[G].geometry:I?E.geometry:E,b=T?E.features[G].properties:I?E.properties:{},C=T?E.features[G].bbox:I?E.bbox:void 0,P=T?E.features[G].id:I?E.id:void 0,y=v?v.type==="GeometryCollection":!1,g=y?v.geometries.length:1,N=0;N<g;N++){if(f=y?v.geometries[N]:v,f===null){if(D(null,B,b,C,P)===!1)return!1;continue}switch(f.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":{if(D(f,B,b,C,P)===!1)return!1;break}case"GeometryCollection":{for(S=0;S<f.geometries.length;S++)if(D(f.geometries[S],B,b,C,P)===!1)return!1;break}default:throw new Error("Unknown Geometry Type")}}B++}}function k(E,D,G){var S=G;return _(E,function(N,f,g,v,y){f===0&&G===void 0?S=N:S=D(S,N,f,g,v,y)}),S}function w(E,D){_(E,function(G,S,N,f,g){var v=G===null?null:G.type;switch(v){case null:case"Point":case"LineString":case"Polygon":return D(i.feature(G,N,{bbox:f,id:g}),S,0)===!1?!1:void 0}var y;switch(v){case"MultiPoint":y="Point";break;case"MultiLineString":y="LineString";break;case"MultiPolygon":y="Polygon";break}for(var b=0;b<G.coordinates.length;b++){var C=G.coordinates[b],P={type:y,coordinates:C};if(D(i.feature(P,N),S,b)===!1)return!1}})}function z(E,D,G){var S=G;return w(E,function(N,f,g){f===0&&g===0&&G===void 0?S=N:S=D(S,N,f,g)}),S}function A(E,D){w(E,function(G,S,N){var f=0;if(G.geometry){var g=G.geometry.type;if(!(g==="Point"||g==="MultiPoint")){var v,y=0,b=0,C=0;if(r(G,function(P,B,T,I,R){if(v===void 0||S>y||I>b||R>C){v=P,y=S,b=I,C=R,f=0;return}var H=i.lineString([v,P],G.properties);if(D(H,S,N,R,f)===!1)return!1;f++,v=P})===!1)return!1}}})}function V(E,D,G){var S=G,N=!1;return A(E,function(f,g,v,y,b){N===!1&&G===void 0?S=f:S=D(S,f,g,v,y,b),N=!0}),S}function q(E,D){if(!E)throw new Error("geojson is required");w(E,function(G,S,N){if(G.geometry!==null){var f=G.geometry.type,g=G.geometry.coordinates;switch(f){case"LineString":if(D(G,S,N,0,0)===!1)return!1;break;case"Polygon":for(var v=0;v<g.length;v++)if(D(i.lineString(g[v],G.properties),S,N,v)===!1)return!1;break}}})}function X(E,D,G){var S=G;return q(E,function(N,f,g,v){f===0&&G===void 0?S=N:S=D(S,N,f,g,v)}),S}function mt(E,D){if(D=D||{},!i.isObject(D))throw new Error("options is invalid");var G=D.featureIndex||0,S=D.multiFeatureIndex||0,N=D.geometryIndex||0,f=D.segmentIndex||0,g=D.properties,v;switch(E.type){case"FeatureCollection":G<0&&(G=E.features.length+G),g=g||E.features[G].properties,v=E.features[G].geometry;break;case"Feature":g=g||E.properties,v=E.geometry;break;case"Point":case"MultiPoint":return null;case"LineString":case"Polygon":case"MultiLineString":case"MultiPolygon":v=E;break;default:throw new Error("geojson is invalid")}if(v===null)return null;var y=v.coordinates;switch(v.type){case"Point":case"MultiPoint":return null;case"LineString":return f<0&&(f=y.length+f-1),i.lineString([y[f],y[f+1]],g,D);case"Polygon":return N<0&&(N=y.length+N),f<0&&(f=y[N].length+f-1),i.lineString([y[N][f],y[N][f+1]],g,D);case"MultiLineString":return S<0&&(S=y.length+S),f<0&&(f=y[S].length+f-1),i.lineString([y[S][f],y[S][f+1]],g,D);case"MultiPolygon":return S<0&&(S=y.length+S),N<0&&(N=y[S].length+N),f<0&&(f=y[S][N].length-f-1),i.lineString([y[S][N][f],y[S][N][f+1]],g,D)}throw new Error("geojson is invalid")}function x(E,D){if(D=D||{},!i.isObject(D))throw new Error("options is invalid");var G=D.featureIndex||0,S=D.multiFeatureIndex||0,N=D.geometryIndex||0,f=D.coordIndex||0,g=D.properties,v;switch(E.type){case"FeatureCollection":G<0&&(G=E.features.length+G),g=g||E.features[G].properties,v=E.features[G].geometry;break;case"Feature":g=g||E.properties,v=E.geometry;break;case"Point":case"MultiPoint":return null;case"LineString":case"Polygon":case"MultiLineString":case"MultiPolygon":v=E;break;default:throw new Error("geojson is invalid")}if(v===null)return null;var y=v.coordinates;switch(v.type){case"Point":return i.point(y,g,D);case"MultiPoint":return S<0&&(S=y.length+S),i.point(y[S],g,D);case"LineString":return f<0&&(f=y.length+f),i.point(y[f],g,D);case"Polygon":return N<0&&(N=y.length+N),f<0&&(f=y[N].length+f),i.point(y[N][f],g,D);case"MultiLineString":return S<0&&(S=y.length+S),f<0&&(f=y[S].length+f),i.point(y[S][f],g,D);case"MultiPolygon":return S<0&&(S=y.length+S),N<0&&(N=y[S].length+N),f<0&&(f=y[S][N].length-f),i.point(y[S][N][f],g,D)}throw new Error("geojson is invalid")}e.coordAll=p,e.coordEach=r,e.coordReduce=a,e.featureEach=h,e.featureReduce=d,e.findPoint=x,e.findSegment=mt,e.flattenEach=w,e.flattenReduce=z,e.geomEach=_,e.geomReduce=k,e.lineEach=q,e.lineReduce=X,e.propEach=s,e.propReduce=l,e.segmentEach=A,e.segmentReduce=V}),xn=F(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=He();function r(a){var s=[1/0,1/0,-1/0,-1/0];return i.coordEach(a,function(l){s[0]>l[0]&&(s[0]=l[0]),s[1]>l[1]&&(s[1]=l[1]),s[2]<l[0]&&(s[2]=l[0]),s[3]<l[1]&&(s[3]=l[1])}),s}r.default=r,e.default=r}),ht=F((e,i)=>{var r=Oi(),a=Ve(),s=He(),l=xn().default,h=s.featureEach,d=s.coordEach,p=a.polygon,_=a.featureCollection;function k(w){var z=new r(w);return z.insert=function(A){if(A.type!=="Feature")throw new Error("invalid feature");return A.bbox=A.bbox?A.bbox:l(A),r.prototype.insert.call(this,A)},z.load=function(A){var V=[];return Array.isArray(A)?A.forEach(function(q){if(q.type!=="Feature")throw new Error("invalid features");q.bbox=q.bbox?q.bbox:l(q),V.push(q)}):h(A,function(q){if(q.type!=="Feature")throw new Error("invalid features");q.bbox=q.bbox?q.bbox:l(q),V.push(q)}),r.prototype.load.call(this,V)},z.remove=function(A,V){if(A.type!=="Feature")throw new Error("invalid feature");return A.bbox=A.bbox?A.bbox:l(A),r.prototype.remove.call(this,A,V)},z.clear=function(){return r.prototype.clear.call(this)},z.search=function(A){var V=r.prototype.search.call(this,this.toBBox(A));return _(V)},z.collides=function(A){return r.prototype.collides.call(this,this.toBBox(A))},z.all=function(){var A=r.prototype.all.call(this);return _(A)},z.toJSON=function(){return r.prototype.toJSON.call(this)},z.fromJSON=function(A){return r.prototype.fromJSON.call(this,A)},z.toBBox=function(A){var V;if(A.bbox)V=A.bbox;else if(Array.isArray(A)&&A.length===4)V=A;else if(Array.isArray(A)&&A.length===6)V=[A[0],A[1],A[3],A[4]];else if(A.type==="Feature")V=l(A);else if(A.type==="FeatureCollection")V=l(A);else throw new Error("invalid geojson");return{minX:V[0],minY:V[1],maxX:V[2],maxY:V[3]}},z}i.exports=k,i.exports.default=k});Array.prototype.findIndex=Array.prototype.findIndex||function(e){if(this===null)throw new TypeError("Array.prototype.findIndex called on null or undefined");if(typeof e!="function")throw new TypeError("callback must be a function");for(var i=Object(this),r=i.length>>>0,a=arguments[1],s=0;s<r;s++)if(e.call(a,i[s],s,i))return s;return-1},Array.prototype.find=Array.prototype.find||function(e){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof e!="function")throw new TypeError("callback must be a function");for(var i=Object(this),r=i.length>>>0,a=arguments[1],s=0;s<r;s++){var l=i[s];if(e.call(a,l,s,i))return l}},typeof Object.assign!="function"&&(Object.assign=function(e){"use strict";if(e==null)throw new TypeError("Cannot convert undefined or null to object");e=Object(e);for(var i=1;i<arguments.length;i++){var r=arguments[i];if(r!=null)for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}),function(e){e.forEach(function(i){i.hasOwnProperty("remove")||Object.defineProperty(i,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})})}([Element.prototype,CharacterData.prototype,DocumentType.prototype]),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(e,i){if(this==null)throw new TypeError('"this" is null or not defined');var r=Object(this),a=r.length>>>0;if(a===0)return!1;var s=i|0,l=Math.max(s>=0?s:a-Math.abs(s),0);function h(d,p){return d===p||typeof d=="number"&&typeof p=="number"&&isNaN(d)&&isNaN(p)}for(;l<a;){if(h(r[l],e))return!0;l++}return!1}});var Bt={name:"@geoman-io/leaflet-geoman-free",version:"2.17.0",description:"A Leaflet Plugin For Editing Geometry Layers in Leaflet 1.0",keywords:["leaflet","geoman","polygon management","geometry editing","map data","map overlay","polygon","geojson","leaflet-draw","data-field-geojson","ui-leaflet-draw"],files:["dist"],main:"dist/leaflet-geoman.js",types:"dist/leaflet-geoman.d.ts",dependencies:{"@turf/boolean-contains":"^6.5.0","@turf/kinks":"^6.5.0","@turf/line-intersect":"^6.5.0","@turf/line-split":"^6.5.0",lodash:"4.17.21","polyclip-ts":"^0.16.5"},devDependencies:{"@types/leaflet":"^1.7.9","cross-env":"^7.0.3",cypress:"6.9.1","cypress-wait-until":"1.7.1",esbuild:"^0.20.0",eslint:"8.56.0","eslint-config-airbnb-base":"15.0.0","eslint-config-prettier":"9.1.0","eslint-plugin-cypress":"2.15.1","eslint-plugin-import":"2.29.1",husky:"^9.0.7",leaflet:"1.9.3","lint-staged":"^15.2.1",prettier:"3.2.4","prosthetic-hand":"1.3.1","ts-node":"^10.9.2"},peerDependencies:{leaflet:"^1.2.0"},scripts:{start:"pnpm run dev",dev:"cross-env DEV=true ts-node bundle.mjs",build:"ts-node bundle.mjs",test:"cypress run",cypress:"cypress open",prepare:"pnpm run build && husky","eslint-check":"eslint --print-config . | eslint-config-prettier-check",eslint:'eslint "{src,cypress,demo}/**/*.js" --fix ',prettier:'prettier --write "{src,cypress,demo}/**/*.{js,css}" --log-level=warn',lint:"pnpm run eslint && pnpm run prettier"},repository:{type:"git",url:"git://github.com/geoman-io/leaflet-geoman.git"},author:{name:"Geoman.io",email:"<EMAIL>",url:"http://geoman.io"},license:"MIT",bugs:{url:"https://github.com/geoman-io/leaflet-geoman/issues"},homepage:"https://geoman.io",prettier:{trailingComma:"es5",tabWidth:2,semi:!0,singleQuote:!0},"lint-staged":{"*.js":'eslint "{src,cypress,demo}/**/*.js" --fix',"*.{js,css,md}":'prettier --write "{src,cypress,demo}/**/*.{js,css}"'}},Fi=rt(ti()),Ri={tooltips:{placeMarker:"Click to place marker",firstVertex:"Click to place first vertex",continueLine:"Click to continue drawing",finishLine:"Click any existing marker to finish",finishPoly:"Click first marker to finish",finishRect:"Click to finish",startCircle:"Click to place circle center",finishCircle:"Click to finish circle",placeCircleMarker:"Click to place circle marker",placeText:"Click to place text",selectFirstLayerFor:"Select first layer for {action}",selectSecondLayerFor:"Select second layer for {action}"},actions:{finish:"Finish",cancel:"Cancel",removeLastVertex:"Remove Last Vertex"},buttonTitles:{drawMarkerButton:"Draw Marker",drawPolyButton:"Draw Polygons",drawLineButton:"Draw Polyline",drawCircleButton:"Draw Circle",drawRectButton:"Draw Rectangle",editButton:"Edit Layers",dragButton:"Drag Layers",cutButton:"Cut Layers",deleteButton:"Remove Layers",drawCircleMarkerButton:"Draw Circle Marker",snappingButton:"Snap dragged marker to other layers and vertices",pinningButton:"Pin shared vertices together",rotateButton:"Rotate Layers",drawTextButton:"Draw Text",scaleButton:"Scale Layers",autoTracingButton:"Auto trace Line",snapGuidesButton:"Show SnapGuides",unionButton:"Union layers",differenceButton:"Subtract layers"},measurements:{totalLength:"Length",segmentLength:"Segment length",area:"Area",radius:"Radius",perimeter:"Perimeter",height:"Height",width:"Width",coordinates:"Position",coordinatesMarker:"Position Marker"}},$t={tooltips:{placeMarker:"Platziere den Marker mit Klick",firstVertex:"Platziere den ersten Marker mit Klick",continueLine:"Klicke, um weiter zu zeichnen",finishLine:"Beende mit Klick auf existierenden Marker",finishPoly:"Beende mit Klick auf ersten Marker",finishRect:"Beende mit Klick",startCircle:"Platziere das Kreiszentrum mit Klick",finishCircle:"Beende den Kreis mit Klick",placeCircleMarker:"Platziere den Kreismarker mit Klick",placeText:"Platziere den Text mit Klick"},actions:{finish:"Beenden",cancel:"Abbrechen",removeLastVertex:"Letzten Vertex l\xF6schen"},buttonTitles:{drawMarkerButton:"Marker zeichnen",drawPolyButton:"Polygon zeichnen",drawLineButton:"Polyline zeichnen",drawCircleButton:"Kreis zeichnen",drawRectButton:"Rechteck zeichnen",editButton:"Layer editieren",dragButton:"Layer bewegen",cutButton:"Layer schneiden",deleteButton:"Layer l\xF6schen",drawCircleMarkerButton:"Kreismarker zeichnen",snappingButton:"Bewegter Layer an andere Layer oder Vertexe einhacken",pinningButton:"Vertexe an der gleichen Position verkn\xFCpfen",rotateButton:"Layer drehen",drawTextButton:"Text zeichnen",scaleButton:"Layer skalieren",autoTracingButton:"Linie automatisch nachzeichen"},measurements:{totalLength:"L\xE4nge",segmentLength:"Segment L\xE4nge",area:"Fl\xE4che",radius:"Radius",perimeter:"Umfang",height:"H\xF6he",width:"Breite",coordinates:"Position",coordinatesMarker:"Position Marker"}},Da={tooltips:{placeMarker:"Clicca per posizionare un Marker",firstVertex:"Clicca per posizionare il primo vertice",continueLine:"Clicca per continuare a disegnare",finishLine:"Clicca qualsiasi marker esistente per terminare",finishPoly:"Clicca il primo marker per terminare",finishRect:"Clicca per terminare",startCircle:"Clicca per posizionare il punto centrale del cerchio",finishCircle:"Clicca per terminare il cerchio",placeCircleMarker:"Clicca per posizionare un Marker del cherchio"},actions:{finish:"Termina",cancel:"Annulla",removeLastVertex:"Rimuovi l'ultimo vertice"},buttonTitles:{drawMarkerButton:"Disegna Marker",drawPolyButton:"Disegna Poligoni",drawLineButton:"Disegna Polilinea",drawCircleButton:"Disegna Cerchio",drawRectButton:"Disegna Rettangolo",editButton:"Modifica Livelli",dragButton:"Sposta Livelli",cutButton:"Ritaglia Livelli",deleteButton:"Elimina Livelli",drawCircleMarkerButton:"Disegna Marker del Cerchio",snappingButton:"Snap ha trascinato il pennarello su altri strati e vertici",pinningButton:"Pin condiviso vertici insieme",rotateButton:"Ruota livello"}},Ii={tooltips:{placeMarker:"Klik untuk menempatkan marker",firstVertex:"Klik untuk menempatkan vertex pertama",continueLine:"Klik untuk meneruskan digitasi",finishLine:"Klik pada sembarang marker yang ada untuk mengakhiri",finishPoly:"Klik marker pertama untuk mengakhiri",finishRect:"Klik untuk mengakhiri",startCircle:"Klik untuk menempatkan titik pusat lingkaran",finishCircle:"Klik untuk mengakhiri lingkaran",placeCircleMarker:"Klik untuk menempatkan penanda lingkarann"},actions:{finish:"Selesai",cancel:"Batal",removeLastVertex:"Hilangkan Vertex Terakhir"},buttonTitles:{drawMarkerButton:"Digitasi Marker",drawPolyButton:"Digitasi Polygon",drawLineButton:"Digitasi Polyline",drawCircleButton:"Digitasi Lingkaran",drawRectButton:"Digitasi Segi Empat",editButton:"Edit Layer",dragButton:"Geser Layer",cutButton:"Potong Layer",deleteButton:"Hilangkan Layer",drawCircleMarkerButton:"Digitasi Penanda Lingkaran",snappingButton:"Jepretkan penanda yang ditarik ke lapisan dan simpul lain",pinningButton:"Sematkan simpul bersama bersama",rotateButton:"Putar lapisan"}},Ke={tooltips:{placeMarker:"Adaug\u0103 un punct",firstVertex:"Apas\u0103 aici pentru a ad\u0103uga primul Vertex",continueLine:"Apas\u0103 aici pentru a continua desenul",finishLine:"Apas\u0103 pe orice obiect pentru a finisa desenul",finishPoly:"Apas\u0103 pe primul obiect pentru a finisa",finishRect:"Apas\u0103 pentru a finisa",startCircle:"Apas\u0103 pentru a desena un cerc",finishCircle:"Apas\u0103 pentru a finisa un cerc",placeCircleMarker:"Adaug\u0103 un punct"},actions:{finish:"Termin\u0103",cancel:"Anuleaz\u0103",removeLastVertex:"\u0218terge ultimul Vertex"},buttonTitles:{drawMarkerButton:"Adaug\u0103 o bulin\u0103",drawPolyButton:"Deseneaz\u0103 un poligon",drawLineButton:"Deseneaz\u0103 o linie",drawCircleButton:"Deseneaz\u0103 un cerc",drawRectButton:"Deseneaz\u0103 un dreptunghi",editButton:"Editeaz\u0103 straturile",dragButton:"Mut\u0103 straturile",cutButton:"Taie straturile",deleteButton:"\u0218terge straturile",drawCircleMarkerButton:"Deseneaz\u0103 marcatorul cercului",snappingButton:"Fixa\u021Bi marcatorul glisat pe alte straturi \u0219i v\xE2rfuri",pinningButton:"Fixa\u021Bi v\xE2rfurile partajate \xEEmpreun\u0103",rotateButton:"Roti\u021Bi stratul"}},Tt={tooltips:{placeMarker:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043C\u0430\u0440\u043A\u0435\u0440",firstVertex:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043F\u0435\u0440\u0432\u044B\u0439 \u043E\u0431\u044A\u0435\u043A\u0442",continueLine:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043F\u0440\u043E\u0434\u043E\u043B\u0436\u0438\u0442\u044C \u0440\u0438\u0441\u043E\u0432\u0430\u043D\u0438\u0435",finishLine:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435 \u043B\u044E\u0431\u043E\u0439 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u0434\u043B\u044F \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u0438\u044F",finishPoly:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u0435\u0440\u0432\u0443\u044E \u0442\u043E\u0447\u043A\u0443, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u043A\u043E\u043D\u0447\u0438\u0442\u044C",finishRect:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u043A\u043E\u043D\u0447\u0438\u0442\u044C",startCircle:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0446\u0435\u043D\u0442\u0440 \u043A\u0440\u0443\u0433\u0430",finishCircle:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u0434\u0430\u0442\u044C \u0440\u0430\u0434\u0438\u0443\u0441",placeCircleMarker:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043A\u0440\u0443\u0433\u043E\u0432\u043E\u0439 \u043C\u0430\u0440\u043A\u0435\u0440"},actions:{finish:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",removeLastVertex:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C \u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0435\u0435 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435"},buttonTitles:{drawMarkerButton:"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043C\u0430\u0440\u043A\u0435\u0440",drawPolyButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043F\u043E\u043B\u0438\u0433\u043E\u043D",drawLineButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043A\u0440\u0438\u0432\u0443\u044E",drawCircleButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043A\u0440\u0443\u0433",drawRectButton:"\u0420\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u043F\u0440\u044F\u043C\u043E\u0443\u0433\u043E\u043B\u044C\u043D\u0438\u043A",editButton:"\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0441\u043B\u043E\u0439",dragButton:"\u041F\u0435\u0440\u0435\u043D\u0435\u0441\u0442\u0438 \u0441\u043B\u043E\u0439",cutButton:"\u0412\u044B\u0440\u0435\u0437\u0430\u0442\u044C \u0441\u043B\u043E\u0439",deleteButton:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0441\u043B\u043E\u0439",drawCircleMarkerButton:"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u0440\u0443\u0433\u043E\u0432\u043E\u0439 \u043C\u0430\u0440\u043A\u0435\u0440",snappingButton:"\u041F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C \u043F\u0435\u0440\u0435\u0442\u0430\u0441\u043A\u0438\u0432\u0430\u0435\u043C\u044B\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u043A \u0434\u0440\u0443\u0433\u0438\u043C \u0441\u043B\u043E\u044F\u043C \u0438 \u0432\u0435\u0440\u0448\u0438\u043D\u0430\u043C",pinningButton:"\u0421\u0432\u044F\u0437\u0430\u0442\u044C \u043E\u0431\u0449\u0438\u0435 \u0442\u043E\u0447\u043A\u0438 \u0432\u043C\u0435\u0441\u0442\u0435",rotateButton:"\u041F\u043E\u0432\u043E\u0440\u043E\u0442 \u0441\u043B\u043E\u044F"}},qe={tooltips:{placeMarker:"Presiona para colocar un marcador",firstVertex:"Presiona para colocar el primer v\xE9rtice",continueLine:"Presiona para continuar dibujando",finishLine:"Presiona cualquier marcador existente para finalizar",finishPoly:"Presiona el primer marcador para finalizar",finishRect:"Presiona para finalizar",startCircle:"Presiona para colocar el centro del c\xEDrculo",finishCircle:"Presiona para finalizar el c\xEDrculo",placeCircleMarker:"Presiona para colocar un marcador de c\xEDrculo"},actions:{finish:"Finalizar",cancel:"Cancelar",removeLastVertex:"Eliminar \xFAltimo v\xE9rtice"},buttonTitles:{drawMarkerButton:"Dibujar Marcador",drawPolyButton:"Dibujar Pol\xEDgono",drawLineButton:"Dibujar L\xEDnea",drawCircleButton:"Dibujar C\xEDrculo",drawRectButton:"Dibujar Rect\xE1ngulo",editButton:"Editar Capas",dragButton:"Arrastrar Capas",cutButton:"Cortar Capas",deleteButton:"Eliminar Capas",drawCircleMarkerButton:"Dibujar Marcador de C\xEDrculo",snappingButton:"El marcador de Snap arrastrado a otras capas y v\xE9rtices",pinningButton:"Fijar juntos los v\xE9rtices compartidos",rotateButton:"Rotar capa"}},_i={tooltips:{placeMarker:"Klik om een marker te plaatsen",firstVertex:"Klik om het eerste punt te plaatsen",continueLine:"Klik om te blijven tekenen",finishLine:"Klik op een bestaand punt om te be\xEBindigen",finishPoly:"Klik op het eerst punt om te be\xEBindigen",finishRect:"Klik om te be\xEBindigen",startCircle:"Klik om het middelpunt te plaatsen",finishCircle:"Klik om de cirkel te be\xEBindigen",placeCircleMarker:"Klik om een marker te plaatsen"},actions:{finish:"Bewaar",cancel:"Annuleer",removeLastVertex:"Verwijder laatste punt"},buttonTitles:{drawMarkerButton:"Plaats Marker",drawPolyButton:"Teken een vlak",drawLineButton:"Teken een lijn",drawCircleButton:"Teken een cirkel",drawRectButton:"Teken een vierkant",editButton:"Bewerk",dragButton:"Verplaats",cutButton:"Knip",deleteButton:"Verwijder",drawCircleMarkerButton:"Plaats Marker",snappingButton:"Snap gesleepte marker naar andere lagen en hoekpunten",pinningButton:"Speld gedeelde hoekpunten samen",rotateButton:"Laag roteren"}},mi={tooltips:{placeMarker:"Cliquez pour placer un marqueur",firstVertex:"Cliquez pour placer le premier sommet",continueLine:"Cliquez pour continuer \xE0 dessiner",finishLine:"Cliquez sur n'importe quel marqueur pour terminer",finishPoly:"Cliquez sur le premier marqueur pour terminer",finishRect:"Cliquez pour terminer",startCircle:"Cliquez pour placer le centre du cercle",finishCircle:"Cliquez pour finir le cercle",placeCircleMarker:"Cliquez pour placer le marqueur circulaire"},actions:{finish:"Terminer",cancel:"Annuler",removeLastVertex:"Retirer le dernier sommet"},buttonTitles:{drawMarkerButton:"Placer des marqueurs",drawPolyButton:"Dessiner des polygones",drawLineButton:"Dessiner des polylignes",drawCircleButton:"Dessiner un cercle",drawRectButton:"Dessiner un rectangle",editButton:"\xC9diter des calques",dragButton:"D\xE9placer des calques",cutButton:"Couper des calques",deleteButton:"Supprimer des calques",drawCircleMarkerButton:"Dessiner un marqueur circulaire",snappingButton:"Glisser le marqueur vers d'autres couches et sommets",pinningButton:"\xC9pingler ensemble les sommets partag\xE9s",rotateButton:"Tourner des calques"}},kn={tooltips:{placeMarker:"\u5355\u51FB\u653E\u7F6E\u6807\u8BB0",firstVertex:"\u5355\u51FB\u653E\u7F6E\u9996\u4E2A\u9876\u70B9",continueLine:"\u5355\u51FB\u7EE7\u7EED\u7ED8\u5236",finishLine:"\u5355\u51FB\u4EFB\u4F55\u5B58\u5728\u7684\u6807\u8BB0\u4EE5\u5B8C\u6210",finishPoly:"\u5355\u51FB\u7B2C\u4E00\u4E2A\u6807\u8BB0\u4EE5\u5B8C\u6210",finishRect:"\u5355\u51FB\u5B8C\u6210",startCircle:"\u5355\u51FB\u653E\u7F6E\u5706\u5FC3",finishCircle:"\u5355\u51FB\u5B8C\u6210\u5706\u5F62",placeCircleMarker:"\u70B9\u51FB\u653E\u7F6E\u5706\u5F62\u6807\u8BB0"},actions:{finish:"\u5B8C\u6210",cancel:"\u53D6\u6D88",removeLastVertex:"\u79FB\u9664\u6700\u540E\u7684\u9876\u70B9"},buttonTitles:{drawMarkerButton:"\u7ED8\u5236\u6807\u8BB0",drawPolyButton:"\u7ED8\u5236\u591A\u8FB9\u5F62",drawLineButton:"\u7ED8\u5236\u7EBF\u6BB5",drawCircleButton:"\u7ED8\u5236\u5706\u5F62",drawRectButton:"\u7ED8\u5236\u957F\u65B9\u5F62",editButton:"\u7F16\u8F91\u56FE\u5C42",dragButton:"\u62D6\u62FD\u56FE\u5C42",cutButton:"\u526A\u5207\u56FE\u5C42",deleteButton:"\u5220\u9664\u56FE\u5C42",drawCircleMarkerButton:"\u753B\u5706\u5708\u6807\u8BB0",snappingButton:"\u5C06\u62D6\u52A8\u7684\u6807\u8BB0\u6355\u6349\u5230\u5176\u4ED6\u56FE\u5C42\u548C\u9876\u70B9",pinningButton:"\u5C06\u5171\u4EAB\u9876\u70B9\u56FA\u5B9A\u5728\u4E00\u8D77",rotateButton:"\u65CB\u8F6C\u56FE\u5C42"}},gi={tooltips:{placeMarker:"\u55AE\u64CA\u653E\u7F6E\u6A19\u8A18",firstVertex:"\u55AE\u64CA\u653E\u7F6E\u7B2C\u4E00\u500B\u9802\u9EDE",continueLine:"\u55AE\u64CA\u7E7C\u7E8C\u7E6A\u88FD",finishLine:"\u55AE\u64CA\u4EFB\u4F55\u5B58\u5728\u7684\u6A19\u8A18\u4EE5\u5B8C\u6210",finishPoly:"\u55AE\u64CA\u7B2C\u4E00\u500B\u6A19\u8A18\u4EE5\u5B8C\u6210",finishRect:"\u55AE\u64CA\u5B8C\u6210",startCircle:"\u55AE\u64CA\u653E\u7F6E\u5713\u5FC3",finishCircle:"\u55AE\u64CA\u5B8C\u6210\u5713\u5F62",placeCircleMarker:"\u9EDE\u64CA\u653E\u7F6E\u5713\u5F62\u6A19\u8A18"},actions:{finish:"\u5B8C\u6210",cancel:"\u53D6\u6D88",removeLastVertex:"\u79FB\u9664\u6700\u5F8C\u4E00\u500B\u9802\u9EDE"},buttonTitles:{drawMarkerButton:"\u653E\u7F6E\u6A19\u8A18",drawPolyButton:"\u7E6A\u88FD\u591A\u908A\u5F62",drawLineButton:"\u7E6A\u88FD\u7DDA\u6BB5",drawCircleButton:"\u7E6A\u88FD\u5713\u5F62",drawRectButton:"\u7E6A\u88FD\u65B9\u5F62",editButton:"\u7DE8\u8F2F\u5716\u5F62",dragButton:"\u79FB\u52D5\u5716\u5F62",cutButton:"\u88C1\u5207\u5716\u5F62",deleteButton:"\u522A\u9664\u5716\u5F62",drawCircleMarkerButton:"\u756B\u5713\u5708\u6A19\u8A18",snappingButton:"\u5C07\u62D6\u52D5\u7684\u6A19\u8A18\u5C0D\u9F4A\u5230\u5176\u4ED6\u5716\u5C64\u548C\u9802\u9EDE",pinningButton:"\u5C07\u5171\u4EAB\u9802\u9EDE\u56FA\u5B9A\u5728\u4E00\u8D77",rotateButton:"\u65CB\u8F49\u5716\u5F62"}},Mn={tooltips:{placeMarker:"Clique para posicionar o marcador",firstVertex:"Clique para posicionar o primeiro v\xE9rtice",continueLine:"Clique para continuar desenhando",finishLine:"Clique em qualquer marcador existente para finalizar",finishPoly:"Clique no primeiro marcador para finalizar",finishRect:"Clique para finalizar",startCircle:"Clique para posicionar o centro do c\xEDrculo",finishCircle:"Clique para finalizar o c\xEDrculo",placeCircleMarker:"Clique para posicionar o marcador circular",placeText:"Clique para inserir texto"},actions:{finish:"Finalizar",cancel:"Cancelar",removeLastVertex:"Remover \xFAltimo v\xE9rtice"},buttonTitles:{drawMarkerButton:"Desenhar Marcador",drawPolyButton:"Desenhar Pol\xEDgonos",drawLineButton:"Desenhar Linha Poligonal",drawCircleButton:"Desenhar C\xEDrculo",drawRectButton:"Desenhar Ret\xE2ngulo",editButton:"Editar Camadas",dragButton:"Arrastar Camadas",cutButton:"Recortar Camadas",deleteButton:"Remover Camadas",drawCircleMarkerButton:"Desenhar Marcador de C\xEDrculo",snappingButton:"Ajustar marcador arrastado a outras camadas e v\xE9rtices",pinningButton:"Unir v\xE9rtices compartilhados",rotateButton:"Rotacionar Camadas",drawTextButton:"Desenhar Texto",scaleButton:"Redimensionar Camadas",autoTracingButton:"Tra\xE7ado Autom\xE1tico de Linha"},measurements:{totalLength:"Comprimento",segmentLength:"Comprimento do Segmento",area:"\xC1rea",radius:"Raio",perimeter:"Per\xEDmetro",height:"Altura",width:"Largura",coordinates:"Posi\xE7\xE3o",coordinatesMarker:"Marcador de Posi\xE7\xE3o"}},wn={tooltips:{placeMarker:"Clique para colocar marcador",firstVertex:"Clique para colocar primeiro v\xE9rtice",continueLine:"Clique para continuar a desenhar",finishLine:"Clique num marcador existente para terminar",finishPoly:"Clique no primeiro marcador para terminar",finishRect:"Clique para terminar",startCircle:"Clique para colocar o centro do c\xEDrculo",finishCircle:"Clique para terminar o c\xEDrculo",placeCircleMarker:"Clique para colocar marcador de c\xEDrculo",placeText:"Clique para colocar texto"},actions:{finish:"Terminar",cancel:"Cancelar",removeLastVertex:"Remover \xDAltimo V\xE9rtice"},buttonTitles:{drawMarkerButton:"Desenhar Marcador",drawPolyButton:"Desenhar Pol\xEDgonos",drawLineButton:"Desenhar Polilinha",drawCircleButton:"Desenhar C\xEDrculo",drawRectButton:"Desenhar Ret\xE2ngulo",editButton:"Editar Camadas",dragButton:"Arrastar Camadas",cutButton:"Cortar Camadas",deleteButton:"Remover Camadas",drawCircleMarkerButton:"Desenhar Marcador de C\xEDrculo",snappingButton:"Ajustar marcador arrastado a outras camadas e v\xE9rtices",pinningButton:"Unir v\xE9rtices partilhados",rotateButton:"Rodar Camadas",drawTextButton:"Desenhar Texto",scaleButton:"Escalar Camadas",autoTracingButton:"Tra\xE7ado Autom\xE1tico de Linha"},measurements:{totalLength:"Comprimento",segmentLength:"Comprimento do Segmento",area:"\xC1rea",radius:"Raio",perimeter:"Per\xEDmetro",height:"Altura",width:"Largura",coordinates:"Posi\xE7\xE3o",coordinatesMarker:"Marcador de Posi\xE7\xE3o"}},zi={tooltips:{placeMarker:"Kliknij, aby umie\u015Bci\u0107 znacznik",firstVertex:"Kliknij, aby umie\u015Bci\u0107 pierwszy wierzcho\u0142ek",continueLine:"Kliknij, aby kontynuowa\u0107 rysowanie",finishLine:"Kliknij dowolny istniej\u0105cy znacznik, aby zako\u0144czy\u0107",finishPoly:"Kliknij pierwszy znacznik, aby zako\u0144czy\u0107",finishRect:"Kliknij, aby zako\u0144czy\u0107",startCircle:"Kliknij, aby umie\u015Bci\u0107 \u015Brodek okr\u0119gu",finishCircle:"Kliknij, aby zako\u0144czy\u0107 okr\u0105g",placeCircleMarker:"Kliknij, aby umie\u015Bci\u0107 znacznik okr\u0119gu",placeText:"Kliknij, aby umie\u015Bci\u0107 tekst"},actions:{finish:"Zako\u0144cz",cancel:"Anuluj",removeLastVertex:"Usu\u0144 ostatni wierzcho\u0142ek"},buttonTitles:{drawMarkerButton:"Rysuj znacznik",drawPolyButton:"Rysuj wielok\u0105t",drawLineButton:"Rysuj lini\u0119",drawCircleButton:"Rysuj okr\u0105g",drawRectButton:"Rysuj prostok\u0105t",editButton:"Edytuj warstwy",dragButton:"Przeci\u0105gnij warstwy",cutButton:"Wytnij warstwy",deleteButton:"Usu\u0144 warstwy",drawCircleMarkerButton:"Rysuj znacznik okr\u0105g\u0142y",snappingButton:"Przyci\u0105gnij przenoszony znacznik do innych warstw i wierzcho\u0142k\xF3w",pinningButton:"Przypnij wsp\xF3lne wierzcho\u0142ki razem",rotateButton:"Obr\xF3\u0107 warstwy",drawTextButton:"Rysuj tekst",scaleButton:"Skaluj warstwy",autoTracingButton:"Automatyczne \u015Bledzenie linii"},measurements:{totalLength:"D\u0142ugo\u015B\u0107",segmentLength:"D\u0142ugo\u015B\u0107 odcinka",area:"Obszar",radius:"Promie\u0144",perimeter:"Obw\xF3d",height:"Wysoko\u015B\u0107",width:"Szeroko\u015B\u0107",coordinates:"Pozycja",coordinatesMarker:"Znacznik pozycji"}},En={tooltips:{placeMarker:"Klicka f\xF6r att placera mark\xF6r",firstVertex:"Klicka f\xF6r att placera f\xF6rsta h\xF6rnet",continueLine:"Klicka f\xF6r att forts\xE4tta rita",finishLine:"Klicka p\xE5 en existerande punkt f\xF6r att slutf\xF6ra",finishPoly:"Klicka p\xE5 den f\xF6rsta punkten f\xF6r att slutf\xF6ra",finishRect:"Klicka f\xF6r att slutf\xF6ra",startCircle:"Klicka f\xF6r att placera cirkelns centrum",finishCircle:"Klicka f\xF6r att slutf\xF6ra cirkeln",placeCircleMarker:"Klicka f\xF6r att placera cirkelmark\xF6r"},actions:{finish:"Slutf\xF6r",cancel:"Avbryt",removeLastVertex:"Ta bort sista h\xF6rnet"},buttonTitles:{drawMarkerButton:"Rita Mark\xF6r",drawPolyButton:"Rita Polygoner",drawLineButton:"Rita Linje",drawCircleButton:"Rita Cirkel",drawRectButton:"Rita Rektangel",editButton:"Redigera Lager",dragButton:"Dra Lager",cutButton:"Klipp i Lager",deleteButton:"Ta bort Lager",drawCircleMarkerButton:"Rita Cirkelmark\xF6r",snappingButton:"Sn\xE4pp dra mark\xF6ren till andra lager och h\xF6rn",pinningButton:"F\xE4st delade h\xF6rn tillsammans",rotateButton:"Rotera lagret"}},Bn={tooltips:{placeMarker:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u0394\u03B5\u03AF\u03BA\u03C4\u03B7",firstVertex:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF \u03C0\u03C1\u03CE\u03C4\u03BF \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF",continueLine:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C3\u03C5\u03BD\u03B5\u03C7\u03AF\u03C3\u03B5\u03C4\u03B5 \u03BD\u03B1 \u03C3\u03C7\u03B5\u03B4\u03B9\u03AC\u03B6\u03B5\u03C4\u03B5",finishLine:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03C3\u03B5 \u03BF\u03C0\u03BF\u03B9\u03BF\u03BD\u03B4\u03AE\u03C0\u03BF\u03C4\u03B5 \u03C5\u03C0\u03AC\u03C1\u03C7\u03BF\u03BD \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BF\u03BB\u03BF\u03BA\u03BB\u03B7\u03C1\u03C9\u03B8\u03B5\u03AF",finishPoly:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03C3\u03C4\u03BF \u03C0\u03C1\u03CE\u03C4\u03BF \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03B5\u03BB\u03B5\u03B9\u03CE\u03C3\u03B5\u03C4\u03B5",finishRect:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03B5\u03BB\u03B5\u03B9\u03CE\u03C3\u03B5\u03C4\u03B5",startCircle:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u03BA\u03AD\u03BD\u03C4\u03C1\u03BF \u039A\u03CD\u03BA\u03BB\u03BF\u03C5",finishCircle:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03BF\u03BB\u03BF\u03BA\u03BB\u03B7\u03C1\u03CE\u03C3\u03B5\u03C4\u03B5 \u03C4\u03BF\u03BD \u039A\u03CD\u03BA\u03BB\u03BF",placeCircleMarker:"\u039A\u03AC\u03BD\u03C4\u03B5 \u03BA\u03BB\u03B9\u03BA \u03B3\u03B9\u03B1 \u03BD\u03B1 \u03C4\u03BF\u03C0\u03BF\u03B8\u03B5\u03C4\u03AE\u03C3\u03B5\u03C4\u03B5 \u039A\u03C5\u03BA\u03BB\u03B9\u03BA\u03CC \u0394\u03B5\u03AF\u03BA\u03C4\u03B7"},actions:{finish:"\u03A4\u03AD\u03BB\u03BF\u03C2",cancel:"\u0391\u03BA\u03CD\u03C1\u03C9\u03C3\u03B7",removeLastVertex:"\u039A\u03B1\u03C4\u03AC\u03C1\u03B3\u03B7\u03C3\u03B7 \u03C4\u03B5\u03BB\u03B5\u03C5\u03C4\u03B1\u03AF\u03BF\u03C5 \u03C3\u03B7\u03BC\u03B5\u03AF\u03BF\u03C5"},buttonTitles:{drawMarkerButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u0394\u03B5\u03AF\u03BA\u03C4\u03B7",drawPolyButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u03A0\u03BF\u03BB\u03C5\u03B3\u03CE\u03BD\u03BF\u03C5",drawLineButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u0393\u03C1\u03B1\u03BC\u03BC\u03AE\u03C2",drawCircleButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u039A\u03CD\u03BA\u03BB\u03BF\u03C5",drawRectButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u039F\u03C1\u03B8\u03BF\u03B3\u03C9\u03BD\u03AF\u03BF\u03C5",editButton:"\u0395\u03C0\u03B5\u03BE\u03B5\u03C1\u03B3\u03B1\u03C3\u03AF\u03B1 \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",dragButton:"\u039C\u03B5\u03C4\u03B1\u03C6\u03BF\u03C1\u03AC \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",cutButton:"\u0391\u03C0\u03BF\u03BA\u03BF\u03C0\u03AE \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",deleteButton:"\u039A\u03B1\u03C4\u03AC\u03C1\u03B3\u03B7\u03C3\u03B7 \u0395\u03C0\u03B9\u03C0\u03AD\u03B4\u03C9\u03BD",drawCircleMarkerButton:"\u03A3\u03C7\u03B5\u03B4\u03AF\u03B1\u03C3\u03B7 \u039A\u03C5\u03BA\u03BB\u03B9\u03BA\u03BF\u03CD \u0394\u03B5\u03AF\u03BA\u03C4\u03B7",snappingButton:"\u03A0\u03C1\u03BF\u03C3\u03BA\u03CC\u03BB\u03BB\u03B7\u03C3\u03B7 \u03C4\u03BF\u03C5 \u0394\u03B5\u03AF\u03BA\u03C4\u03B7 \u03BC\u03B5\u03C4\u03B1\u03C6\u03BF\u03C1\u03AC\u03C2 \u03C3\u03B5 \u03AC\u03BB\u03BB\u03B1 \u0395\u03C0\u03AF\u03C0\u03B5\u03B4\u03B1 \u03BA\u03B1\u03B9 \u039A\u03BF\u03C1\u03C5\u03C6\u03AD\u03C2",pinningButton:"\u03A0\u03B5\u03C1\u03B9\u03BA\u03BF\u03C0\u03AE \u03BA\u03BF\u03B9\u03BD\u03CE\u03BD \u03BA\u03BF\u03C1\u03C5\u03C6\u03CE\u03BD \u03BC\u03B1\u03B6\u03AF",rotateButton:"\u03A0\u03B5\u03C1\u03B9\u03C3\u03C4\u03C1\u03AD\u03C8\u03C4\u03B5 \u03C4\u03BF \u03C3\u03C4\u03C1\u03CE\u03BC\u03B1"}},Ni={tooltips:{placeMarker:"Kattintson a jel\xF6l\u0151 elhelyez\xE9s\xE9hez",firstVertex:"Kattintson az els\u0151 pont elhelyez\xE9s\xE9hez",continueLine:"Kattintson a k\xF6vetkez\u0151 pont elhelyez\xE9s\xE9hez",finishLine:"A befejez\xE9shez kattintson egy megl\xE9v\u0151 pontra",finishPoly:"A befejez\xE9shez kattintson az els\u0151 pontra",finishRect:"Kattintson a befejez\xE9shez",startCircle:"Kattintson a k\xF6r k\xF6z\xE9ppontj\xE1nak elhelyez\xE9s\xE9hez",finishCircle:"Kattintson a k\xF6r befejez\xE9s\xE9hez",placeCircleMarker:"Kattintson a k\xF6rjel\xF6l\u0151 elhelyez\xE9s\xE9hez"},actions:{finish:"Befejez\xE9s",cancel:"M\xE9gse",removeLastVertex:"Utols\xF3 pont elt\xE1vol\xEDt\xE1sa"},buttonTitles:{drawMarkerButton:"Jel\xF6l\u0151 rajzol\xE1sa",drawPolyButton:"Poligon rajzol\xE1sa",drawLineButton:"Vonal rajzol\xE1sa",drawCircleButton:"K\xF6r rajzol\xE1sa",drawRectButton:"N\xE9gyzet rajzol\xE1sa",editButton:"Elemek szerkeszt\xE9se",dragButton:"Elemek mozgat\xE1sa",cutButton:"Elemek v\xE1g\xE1sa",deleteButton:"Elemek t\xF6rl\xE9se",drawCircleMarkerButton:"K\xF6r jel\xF6l\u0151 rajzol\xE1sa",snappingButton:"Kapcsolja a jel\xF6lt\u0151t m\xE1sik elemhez vagy ponthoz",pinningButton:"K\xF6z\xF6s pontok \xF6sszek\xF6t\xE9se",rotateButton:"F\xF3lia elforgat\xE1sa"}},kr={tooltips:{placeMarker:"Tryk for at placere en mark\xF8r",firstVertex:"Tryk for at placere det f\xF8rste punkt",continueLine:"Tryk for at forts\xE6tte linjen",finishLine:"Tryk p\xE5 et eksisterende punkt for at afslutte",finishPoly:"Tryk p\xE5 det f\xF8rste punkt for at afslutte",finishRect:"Tryk for at afslutte",startCircle:"Tryk for at placere cirklens center",finishCircle:"Tryk for at afslutte cirklen",placeCircleMarker:"Tryk for at placere en cirkelmark\xF8r"},actions:{finish:"Afslut",cancel:"Afbryd",removeLastVertex:"Fjern sidste punkt"},buttonTitles:{drawMarkerButton:"Placer mark\xF8r",drawPolyButton:"Tegn polygon",drawLineButton:"Tegn linje",drawCircleButton:"Tegn cirkel",drawRectButton:"Tegn firkant",editButton:"Rediger",dragButton:"Tr\xE6k",cutButton:"Klip",deleteButton:"Fjern",drawCircleMarkerButton:"Tegn cirkelmark\xF8r",snappingButton:"Fastg\xF8r trukket mark\xF8r til andre elementer",pinningButton:"Sammenl\xE6g delte elementer",rotateButton:"Roter laget"}},Pn={tooltips:{placeMarker:"Klikk for \xE5 plassere punkt",firstVertex:"Klikk for \xE5 plassere f\xF8rste punkt",continueLine:"Klikk for \xE5 tegne videre",finishLine:"Klikk p\xE5 et eksisterende punkt for \xE5 fullf\xF8re",finishPoly:"Klikk f\xF8rste punkt for \xE5 fullf\xF8re",finishRect:"Klikk for \xE5 fullf\xF8re",startCircle:"Klikk for \xE5 sette sirkel midtpunkt",finishCircle:"Klikk for \xE5 fullf\xF8re sirkel",placeCircleMarker:"Klikk for \xE5 plassere sirkel",placeText:"Klikk for \xE5 plassere tekst"},actions:{finish:"Fullf\xF8r",cancel:"Kanseller",removeLastVertex:"Fjern forrige punkt"},buttonTitles:{drawMarkerButton:"Tegn punkt",drawPolyButton:"Tegn flate",drawLineButton:"Tegn linje",drawCircleButton:"Tegn sirkel",drawRectButton:"Tegn rektangel",editButton:"Rediger objekter",dragButton:"Dra objekter",cutButton:"Kutt objekter",deleteButton:"Fjern objekter",drawCircleMarkerButton:"Tegn sirkel-punkt",snappingButton:"Fest dratt punkt til andre objekter og punkt",pinningButton:"Pin delte punkter sammen",rotateButton:"Rot\xE9r objekter",drawTextButton:"Tegn tekst",scaleButton:"Skal\xE9r objekter",autoTracingButton:"Automatisk sporing av linje"},measurements:{totalLength:"Lengde",segmentLength:"Segmentlengde",area:"Omr\xE5de",radius:"Radius",perimeter:"Omriss",height:"H\xF8yde",width:"Bredde",coordinates:"Posisjon",coordinatesMarker:"Posisjonsmark\xF8r"}},Sa={tooltips:{placeMarker:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u062C\u0627\u0646\u0645\u0627\u06CC\u06CC \u0646\u0634\u0627\u0646",firstVertex:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0631\u0633\u0645 \u0627\u0648\u0644\u06CC\u0646 \u0631\u0623\u0633",continueLine:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0627\u062F\u0627\u0645\u0647 \u0631\u0633\u0645",finishLine:"\u06A9\u0644\u06CC\u06A9 \u0631\u0648\u06CC \u0647\u0631 \u0646\u0634\u0627\u0646 \u0645\u0648\u062C\u0648\u062F \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646",finishPoly:"\u06A9\u0644\u06CC\u06A9 \u0631\u0648\u06CC \u0627\u0648\u0644\u06CC\u0646 \u0646\u0634\u0627\u0646 \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646",finishRect:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646",startCircle:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0631\u0633\u0645 \u0645\u0631\u06A9\u0632 \u062F\u0627\u06CC\u0631\u0647",finishCircle:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u067E\u0627\u06CC\u0627\u0646 \u0631\u0633\u0645 \u062F\u0627\u06CC\u0631\u0647",placeCircleMarker:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0631\u0633\u0645 \u0646\u0634\u0627\u0646 \u062F\u0627\u06CC\u0631\u0647",placeText:"\u06A9\u0644\u06CC\u06A9 \u0628\u0631\u0627\u06CC \u0646\u0648\u0634\u062A\u0646 \u0645\u062A\u0646"},actions:{finish:"\u067E\u0627\u06CC\u0627\u0646",cancel:"\u0644\u0641\u0648",removeLastVertex:"\u062D\u0630\u0641 \u0622\u062E\u0631\u06CC\u0646 \u0631\u0623\u0633"},buttonTitles:{drawMarkerButton:"\u062F\u0631\u062C \u0646\u0634\u0627\u0646",drawPolyButton:"\u0631\u0633\u0645 \u0686\u0646\u062F\u0636\u0644\u0639\u06CC",drawLineButton:"\u0631\u0633\u0645 \u062E\u0637",drawCircleButton:"\u0631\u0633\u0645 \u062F\u0627\u06CC\u0631\u0647",drawRectButton:"\u0631\u0633\u0645 \u0686\u0647\u0627\u0631\u0636\u0644\u0639\u06CC",editButton:"\u0648\u06CC\u0631\u0627\u06CC\u0634 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",dragButton:"\u062C\u0627\u0628\u062C\u0627\u06CC\u06CC \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",cutButton:"\u0628\u0631\u0634 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",deleteButton:"\u062D\u0630\u0641 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627",drawCircleMarkerButton:"\u0631\u0633\u0645 \u0646\u0634\u0627\u0646 \u062F\u0627\u06CC\u0631\u0647",snappingButton:"\u0646\u0634\u0627\u0646\u06AF\u0631 \u0631\u0627 \u0628\u0647 \u0644\u0627\u06CC\u0647\u200C\u0647\u0627 \u0648 \u0631\u0626\u0648\u0633 \u062F\u06CC\u06AF\u0631 \u0628\u06A9\u0634\u06CC\u062F",pinningButton:"\u0631\u0626\u0648\u0633 \u0645\u0634\u062A\u0631\u06A9 \u0631\u0627 \u0628\u0627 \u0647\u0645 \u067E\u06CC\u0646 \u06A9\u0646\u06CC\u062F",rotateButton:"\u0686\u0631\u062E\u0634 \u0644\u0627\u06CC\u0647",drawTextButton:"\u0631\u0633\u0645 \u0645\u062A\u0646",scaleButton:"\u0645\u0642\u06CC\u0627\u0633\u200C\u06AF\u0630\u0627\u0631\u06CC",autoTracingButton:"\u0631\u062F\u06CC\u0627\u0628 \u062E\u0648\u062F\u06A9\u0627\u0631"},measurements:{totalLength:"\u0637\u0648\u0644",segmentLength:"\u0637\u0648\u0644 \u0628\u062E\u0634",area:"\u0646\u0627\u062D\u06CC\u0647",radius:"\u0634\u0639\u0627\u0639",perimeter:"\u0645\u062D\u06CC\u0637",height:"\u0627\u0631\u062A\u0641\u0627\u0639",width:"\u0639\u0631\u0636",coordinates:"\u0645\u0648\u0642\u0639\u06CC\u062A",coordinatesMarker:"\u0645\u0648\u0642\u0639\u06CC\u062A \u0646\u0634\u0627\u0646"}},lt={tooltips:{placeMarker:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043C\u0430\u0440\u043A\u0435\u0440",firstVertex:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043F\u0435\u0440\u0448\u0443 \u0432\u0435\u0440\u0448\u0438\u043D\u0443",continueLine:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438 \u043C\u0430\u043B\u044E\u0432\u0430\u0442\u0438",finishLine:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u0431\u0443\u0434\u044C-\u044F\u043A\u0438\u0439 \u0456\u0441\u043D\u0443\u044E\u0447\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u0434\u043B\u044F \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u043D\u044F",finishPoly:"\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u043F\u0435\u0440\u0448\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440, \u0449\u043E\u0431 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",finishRect:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",startCircle:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u0434\u043E\u0434\u0430\u0442\u0438 \u0446\u0435\u043D\u0442\u0440 \u043A\u043E\u043B\u0430",finishCircle:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u0437\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438 \u043A\u043E\u043B\u043E",placeCircleMarker:"\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C, \u0449\u043E\u0431 \u043D\u0430\u043D\u0435\u0441\u0442\u0438 \u043A\u0440\u0443\u0433\u043E\u0432\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440"},actions:{finish:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",cancel:"\u0412\u0456\u0434\u043C\u0456\u043D\u0438\u0442\u0438",removeLastVertex:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u044E \u0432\u0435\u0440\u0448\u0438\u043D\u0443"},buttonTitles:{drawMarkerButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043C\u0430\u0440\u043A\u0435\u0440",drawPolyButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043F\u043E\u043B\u0456\u0433\u043E\u043D",drawLineButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043A\u0440\u0438\u0432\u0443",drawCircleButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043A\u043E\u043B\u043E",drawRectButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043F\u0440\u044F\u043C\u043E\u043A\u0443\u0442\u043D\u0438\u043A",editButton:"\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0448\u0430\u0440\u0438",dragButton:"\u041F\u0435\u0440\u0435\u043D\u0435\u0441\u0442\u0438 \u0448\u0430\u0440\u0438",cutButton:"\u0412\u0438\u0440\u0456\u0437\u0430\u0442\u0438 \u0448\u0430\u0440\u0438",deleteButton:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0448\u0430\u0440\u0438",drawCircleMarkerButton:"\u041C\u0430\u043B\u044E\u0432\u0430\u0442\u0438 \u043A\u0440\u0443\u0433\u043E\u0432\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440",snappingButton:"\u041F\u0440\u0438\u0432\u2019\u044F\u0437\u0430\u0442\u0438 \u043F\u0435\u0440\u0435\u0442\u044F\u0433\u043D\u0443\u0442\u0438\u0439 \u043C\u0430\u0440\u043A\u0435\u0440 \u0434\u043E \u0456\u043D\u0448\u0438\u0445 \u0448\u0430\u0440\u0456\u0432 \u0442\u0430 \u0432\u0435\u0440\u0448\u0438\u043D",pinningButton:"\u0417\u0432'\u044F\u0437\u0430\u0442\u0438 \u0441\u043F\u0456\u043B\u044C\u043D\u0456 \u0432\u0435\u0440\u0448\u0438\u043D\u0438 \u0440\u0430\u0437\u043E\u043C",rotateButton:"\u041F\u043E\u0432\u0435\u0440\u043D\u0443\u0442\u0438 \u0448\u0430\u0440"}},ye={tooltips:{placeMarker:"\u0130\u015Faret\xE7i yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n",firstVertex:"\u0130lk tepe noktas\u0131n\u0131 yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n",continueLine:"\xC7izime devam etmek i\xE7in t\u0131klay\u0131n",finishLine:"Bitirmek i\xE7in mevcut herhangi bir i\u015Faret\xE7iyi t\u0131klay\u0131n",finishPoly:"Bitirmek i\xE7in ilk i\u015Faret\xE7iyi t\u0131klay\u0131n",finishRect:"Bitirmek i\xE7in t\u0131klay\u0131n",startCircle:"Daire merkezine yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n",finishCircle:"Daireyi bitirmek i\xE7in t\u0131klay\u0131n",placeCircleMarker:"Daire i\u015Faret\xE7isi yerle\u015Ftirmek i\xE7in t\u0131klay\u0131n"},actions:{finish:"Bitir",cancel:"\u0130ptal",removeLastVertex:"Son k\xF6\u015Feyi kald\u0131r"},buttonTitles:{drawMarkerButton:"\xC7izim \u0130\u015Faret\xE7isi",drawPolyButton:"\xC7okgenler \xE7iz",drawLineButton:"\xC7oklu \xE7izgi \xE7iz",drawCircleButton:"\xC7ember \xE7iz",drawRectButton:"Dikd\xF6rtgen \xE7iz",editButton:"Katmanlar\u0131 d\xFCzenle",dragButton:"Katmanlar\u0131 s\xFCr\xFCkle",cutButton:"Katmanlar\u0131 kes",deleteButton:"Katmanlar\u0131 kald\u0131r",drawCircleMarkerButton:"Daire i\u015Faret\xE7isi \xE7iz",snappingButton:"S\xFCr\xFCklenen i\u015Faret\xE7iyi di\u011Fer katmanlara ve k\xF6\u015Felere yap\u0131\u015Ft\u0131r",pinningButton:"Payla\u015F\u0131lan k\xF6\u015Feleri birbirine sabitle",rotateButton:"Katman\u0131 d\xF6nd\xFCr"}},Ct={tooltips:{placeMarker:"Kliknut\xEDm vytvo\u0159\xEDte zna\u010Dku",firstVertex:"Kliknut\xEDm vytvo\u0159\xEDte prvn\xED objekt",continueLine:"Kliknut\xEDm pokra\u010Dujte v kreslen\xED",finishLine:"Kliknut\xED na libovolnou existuj\xEDc\xED zna\u010Dku pro dokon\u010Den\xED",finishPoly:"Vyberte prvn\xED bod pro dokon\u010Den\xED",finishRect:"Klikn\u011Bte pro dokon\u010Den\xED",startCircle:"Kliknut\xEDm p\u0159idejte st\u0159ed kruhu",finishCircle:"\u041D\u0430\u0436\u043C\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0437\u0430\u0434\u0430\u0442\u044C \u0440\u0430\u0434\u0438\u0443\u0441",placeCircleMarker:"Kliknut\xEDm nastavte polom\u011Br"},actions:{finish:"Dokon\u010Dit",cancel:"Zru\u0161it",removeLastVertex:"Zru\u0161it posledn\xED akci"},buttonTitles:{drawMarkerButton:"P\u0159idat zna\u010Dku",drawPolyButton:"Nakreslit polygon",drawLineButton:"Nakreslit k\u0159ivku",drawCircleButton:"Nakreslit kruh",drawRectButton:"Nakreslit obd\xE9ln\xEDk",editButton:"Upravit vrstvu",dragButton:"P\u0159eneste vrstvu",cutButton:"Vyjmout vrstvu",deleteButton:"Smazat vrstvu",drawCircleMarkerButton:"P\u0159idat kruhovou zna\u010Dku",snappingButton:"Nav\xE1zat ta\u017Enou zna\u010Dku k dal\u0161\xEDm vrstv\xE1m a vrchol\u016Fm",pinningButton:"Spojit spole\u010Dn\xE9 body dohromady",rotateButton:"Oto\u010Dte vrstvu"}},Mr={tooltips:{placeMarker:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u30DE\u30FC\u30AB\u30FC\u3092\u914D\u7F6E",firstVertex:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u6700\u521D\u306E\u9802\u70B9\u3092\u914D\u7F6E",continueLine:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u63CF\u753B\u3092\u7D9A\u3051\u308B",finishLine:"\u4EFB\u610F\u306E\u30DE\u30FC\u30AB\u30FC\u3092\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7D42\u4E86",finishPoly:"\u6700\u521D\u306E\u30DE\u30FC\u30AB\u30FC\u3092\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7D42\u4E86",finishRect:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u7D42\u4E86",startCircle:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u5186\u306E\u4E2D\u5FC3\u3092\u914D\u7F6E",finishCircle:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u5186\u306E\u63CF\u753B\u3092\u7D42\u4E86",placeCircleMarker:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u5186\u30DE\u30FC\u30AB\u30FC\u3092\u914D\u7F6E",placeText:"\u30AF\u30EA\u30C3\u30AF\u3057\u3066\u30C6\u30AD\u30B9\u30C8\u3092\u914D\u7F6E"},actions:{finish:"\u7D42\u4E86",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",removeLastVertex:"\u6700\u5F8C\u306E\u9802\u70B9\u3092\u524A\u9664"},buttonTitles:{drawMarkerButton:"\u30DE\u30FC\u30AB\u30FC\u3092\u63CF\u753B",drawPolyButton:"\u30DD\u30EA\u30B4\u30F3\u3092\u63CF\u753B",drawLineButton:"\u6298\u308C\u7DDA\u3092\u63CF\u753B",drawCircleButton:"\u5186\u3092\u63CF\u753B",drawRectButton:"\u77E9\u5F62\u3092\u63CF\u753B",editButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u7DE8\u96C6",dragButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u30C9\u30E9\u30C3\u30B0",cutButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u5207\u308A\u53D6\u308A",deleteButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u524A\u9664",drawCircleMarkerButton:"\u5186\u30DE\u30FC\u30AB\u30FC\u3092\u63CF\u753B",snappingButton:"\u30C9\u30E9\u30C3\u30B0\u3057\u305F\u30DE\u30FC\u30AB\u30FC\u3092\u4ED6\u306E\u30EC\u30A4\u30E4\u30FC\u3084\u9802\u70B9\u306B\u30B9\u30CA\u30C3\u30D7\u3059\u308B",pinningButton:"\u5171\u6709\u3059\u308B\u9802\u70B9\u3092\u540C\u6642\u306B\u52D5\u304B\u3059",rotateButton:"\u30EC\u30A4\u30E4\u30FC\u3092\u56DE\u8EE2",drawTextButton:"\u30C6\u30AD\u30B9\u30C8\u3092\u63CF\u753B"}},Tn={tooltips:{placeMarker:"Klikkaa asettaaksesi merkin",firstVertex:"Klikkaa asettaakseni ensimm\xE4isen osuuden",continueLine:"Klikkaa jatkaaksesi piirt\xE4mist\xE4",finishLine:"Klikkaa olemassa olevaa merkki\xE4 lopettaaksesi",finishPoly:"Klikkaa ensimm\xE4ist\xE4 merkki\xE4 lopettaaksesi",finishRect:"Klikkaa lopettaaksesi",startCircle:"Klikkaa asettaaksesi ympyr\xE4n keskipisteen",finishCircle:"Klikkaa lopettaaksesi ympyr\xE4n",placeCircleMarker:"Klikkaa asettaaksesi ympyr\xE4merkin",placeText:"Klikkaa asettaaksesi tekstin"},actions:{finish:"Valmis",cancel:"Peruuta",removeLastVertex:"Poista viimeinen osuus"},buttonTitles:{drawMarkerButton:"Piirr\xE4 merkkej\xE4",drawPolyButton:"Piirr\xE4 monikulmioita",drawLineButton:"Piirr\xE4 viivoja",drawCircleButton:"Piirr\xE4 ympyr\xE4",drawRectButton:"Piirr\xE4 neliskulmioita",editButton:"Muokkaa",dragButton:"Siirr\xE4",cutButton:"Leikkaa",deleteButton:"Poista",drawCircleMarkerButton:"Piirr\xE4 ympyr\xE4merkki",snappingButton:"Kiinnit\xE4 siirrett\xE4v\xE4 merkki toisiin muotoihin",pinningButton:"Kiinnit\xE4 jaetut muodot yhteen",rotateButton:"K\xE4\xE4nn\xE4",drawTextButton:"Piirr\xE4 teksti\xE4"}},Dn={tooltips:{placeMarker:"\uB9C8\uCEE4 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694",firstVertex:"\uCCAB\uBC88\uC9F8 \uAF2D\uC9C0\uC810 \uC704\uCE58\uC744 \uD074\uB9AD\uD558\uC138\uC694",continueLine:"\uACC4\uC18D \uADF8\uB9AC\uB824\uBA74 \uD074\uB9AD\uD558\uC138\uC694",finishLine:"\uB05D\uB0B4\uB824\uBA74 \uAE30\uC874 \uB9C8\uCEE4\uB97C \uD074\uB9AD\uD558\uC138\uC694",finishPoly:"\uB05D\uB0B4\uB824\uBA74 \uCC98\uC74C \uB9C8\uCEE4\uB97C \uD074\uB9AD\uD558\uC138\uC694",finishRect:"\uB05D\uB0B4\uB824\uBA74 \uD074\uB9AD\uD558\uC138\uC694",startCircle:"\uC6D0\uC758 \uC911\uC2EC\uC774 \uB420 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694",finishCircle:"\uC6D0\uC744 \uB05D\uB0B4\uB824\uBA74 \uD074\uB9AD\uD558\uC138\uC694",placeCircleMarker:"\uC6D0 \uB9C8\uCEE4 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694",placeText:"\uD14D\uC2A4\uD2B8 \uC704\uCE58\uB97C \uD074\uB9AD\uD558\uC138\uC694"},actions:{finish:"\uB05D\uB0B4\uAE30",cancel:"\uCDE8\uC18C",removeLastVertex:"\uB9C8\uC9C0\uB9C9 \uAF2D\uC9C0\uC810 \uC81C\uAC70"},buttonTitles:{drawMarkerButton:"\uB9C8\uCEE4 \uADF8\uB9AC\uAE30",drawPolyButton:"\uB2E4\uAC01\uD615 \uADF8\uB9AC\uAE30",drawLineButton:"\uB2E4\uAC01\uC120 \uADF8\uB9AC\uAE30",drawCircleButton:"\uC6D0 \uADF8\uB9AC\uAE30",drawRectButton:"\uC9C1\uC0AC\uAC01\uD615 \uADF8\uB9AC\uAE30",editButton:"\uB808\uC774\uC5B4 \uD3B8\uC9D1\uD558\uAE30",dragButton:"\uB808\uC774\uC5B4 \uB04C\uAE30",cutButton:"\uB808\uC774\uC5B4 \uC790\uB974\uAE30",deleteButton:"\uB808\uC774\uC5B4 \uC81C\uAC70\uD558\uAE30",drawCircleMarkerButton:"\uC6D0 \uB9C8\uCEE4 \uADF8\uB9AC\uAE30",snappingButton:"\uC7A1\uC544\uB048 \uB9C8\uCEE4\uB97C \uB2E4\uB978 \uB808\uC774\uC5B4 \uBC0F \uAF2D\uC9C0\uC810\uC5D0 \uB4E4\uB7EC\uBD99\uAC8C \uD558\uAE30",pinningButton:"\uACF5\uC720 \uAF2D\uC9C0\uC810\uC744 \uD568\uAED8 \uCC0D\uAE30",rotateButton:"\uB808\uC774\uC5B4 \uD68C\uC804\uD558\uAE30",drawTextButton:"\uD14D\uC2A4\uD2B8 \uADF8\uB9AC\uAE30"}},Sn={tooltips:{placeMarker:"\u041C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",firstVertex:"\u0411\u0438\u0440\u0438\u043D\u0447\u0438 \u0447\u043E\u043A\u0443\u043D\u0443 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443\u043D\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",continueLine:"\u0421\u04AF\u0440\u04E9\u0442 \u0442\u0430\u0440\u0442\u0443\u0443\u043D\u0443 \u0443\u043B\u0430\u043D\u0442\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishLine:"\u0410\u044F\u043A\u0442\u043E\u043E \u04AF\u0447\u04AF\u043D \u0443\u0447\u0443\u0440\u0434\u0430\u0433\u044B \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishPoly:"\u0411\u04AF\u0442\u04AF\u0440\u04AF\u04AF \u04AF\u0447\u04AF\u043D \u0431\u0438\u0440\u0438\u043D\u0447\u0438 \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishRect:"\u0411\u04AF\u0442\u04AF\u0440\u04AF\u04AF \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",startCircle:"\u0410\u0439\u043B\u0430\u043D\u0430\u043D\u044B\u043D \u0431\u043E\u0440\u0431\u043E\u0440\u0443\u043D \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443\u043D\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",finishCircle:"\u0410\u0439\u043B\u0430\u043D\u0430\u043D\u044B \u0431\u04AF\u0442\u04AF\u0440\u04AF\u04AF \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",placeCircleMarker:"\u0422\u0435\u0433\u0435\u0440\u0435\u043A \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437",placeText:"\u0422\u0435\u043A\u0441\u0442\u0442\u0438 \u0436\u0430\u0439\u0433\u0430\u0448\u0442\u044B\u0440\u0443\u0443 \u04AF\u0447\u04AF\u043D \u0431\u0430\u0441\u044B\u04A3\u044B\u0437"},actions:{finish:"\u0410\u044F\u0433\u044B",cancel:"\u0416\u043E\u043A \u043A\u044B\u043B\u0443\u0443",removeLastVertex:"\u0410\u043A\u044B\u0440\u043A\u044B \u0447\u043E\u043A\u0443\u043D\u0443 \u04E9\u0447\u04AF\u0440\u04AF\u04AF"},buttonTitles:{drawMarkerButton:"\u041C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0447\u0438\u0437\u0443\u0443",drawPolyButton:"\u041F\u043E\u043B\u0438\u0433\u043E\u043D \u0447\u0438\u0437\u0443\u0443",drawLineButton:"\u041F\u043E\u043B\u0438\u043B\u0438\u043D\u0438\u044F \u0447\u0438\u0437\u0443\u0443",drawCircleButton:"\u0414\u0430\u0439\u044B\u043D\u0434\u044B \u0447\u0438\u0437\u0443\u0443",drawRectButton:"\u041F\u0440\u044F\u043C\u043E\u0443\u0433\u043E\u043B\u044C\u043D\u0438\u043A \u0447\u0438\u0437\u0443\u0443",editButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443 \u0442\u04AF\u0437\u04E9\u0442\u04AF\u04AF",dragButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443 \u043A\u0430\u0440\u0430\u043F \u0441\u04AF\u0439\u043B\u04E9\u04AF",cutButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u0431\u0430\u0448\u044B\u043D \u043A\u0435\u0441\u04AF\u04AF",deleteButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u04E9\u0447\u04AF\u0440\u04AF\u04AF",drawCircleMarkerButton:"\u0414\u0430\u0439\u044B\u043D\u0434\u044B \u043C\u0430\u0440\u043A\u0435\u0440\u0434\u0438 \u0447\u0438\u0437\u0443\u0443",snappingButton:"\u0411\u0430\u0448\u043A\u0430 \u0441\u043B\u043E\u043E\u043F\u0442\u043E\u0440\u0434\u0443\u043D \u0436\u0430\u043D\u0430 \u0432\u0435\u0440\u0442\u0435\u043A\u0441\u0442\u0435\u0440\u0434\u0438\u043D \u0430\u0440\u0430\u0441\u044B\u043D\u0430 \u0447\u0435\u043A\u0438\u043B\u0434\u04E9\u04E9",pinningButton:"\u0411\u04E9\u043B\u04AF\u0448\u043A\u04E9\u043D \u0432\u0435\u0440\u0442\u0435\u043A\u0441\u0442\u0435\u0440\u0434\u0438 \u0431\u0438\u0440\u0433\u0435 \u0442\u0443\u0442\u0443\u0448\u0442\u0443\u0440\u0443\u0443",rotateButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u04E9\u0437\u0433\u04E9\u0440\u0442\u04AF\u04AF",drawTextButton:"\u0422\u0435\u043A\u0441\u0442 \u0447\u0438\u0437\u0443\u0443",scaleButton:"\u0421\u043B\u043E\u043E\u043F\u0442\u0443\u043D \u04E9\u043B\u0447\u04E9\u043C\u04AF\u043D \u04E9\u0437\u0433\u04E9\u0440\u0442\u04AF\u04AF",autoTracingButton:"\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0442\u044B\u043A \u0442\u0438\u0437\u043C\u0435\u0433\u0438 \u0447\u0438\u0437\u0443\u0443"},measurements:{totalLength:"\u0423\u0437\u0443\u043D\u0434\u0443\u043A",segmentLength:"\u0421\u0435\u0433\u043C\u0435\u043D\u0442 \u0443\u0437\u0443\u043D\u0434\u0443\u0433\u0443",area:"\u0410\u0439\u043C\u0430\u043A",radius:"\u0420\u0430\u0434\u0438\u0443\u0441",perimeter:"\u041F\u0435\u0440\u0438\u043C\u0435\u0442\u0440",height:"\u0414\u0438\u0430\u043C\u0435\u0442\u0440",width:"\u041A\u0435\u043D\u0447\u0438\u043B\u0438\u043A",coordinates:"\u041A\u043E\u043E\u0440\u0434\u0438\u043D\u0430\u0442\u0442\u0430\u0440",coordinatesMarker:"\u041C\u0430\u0440\u043A\u0435\u0440\u0434\u0438\u043D \u043A\u043E\u043E\u0440\u0434\u0438\u043D\u0430\u0442\u0442\u0430\u0440\u044B"}},ce={en:Ri,de:$t,it:Da,id:Ii,ro:Ke,ru:Tt,es:qe,nl:_i,fr:mi,pt_br:Mn,pt_pt:wn,zh:kn,zh_tw:gi,pl:zi,sv:En,el:Bn,hu:Ni,da:kr,no:Pn,fa:Sa,ua:lt,tr:ye,cz:Ct,ja:Mr,fi:Tn,ko:Dn,ky:Sn},An={_globalEditModeEnabled:!1,enableGlobalEditMode(e){let i={...e};this._globalEditModeEnabled=!0,this.Toolbar.toggleButton("editMode",this.globalEditModeEnabled()),L.PM.Utils.findLayers(this.map).forEach(r=>{this._isRelevantForEdit(r)&&r.pm.enable(i)}),this.throttledReInitEdit||(this.throttledReInitEdit=L.Util.throttle(this.handleLayerAdditionInGlobalEditMode,100,this)),this._addedLayersEdit={},this.map.on("layeradd",this._layerAddedEdit,this),this.map.on("layeradd",this.throttledReInitEdit,this),this._fireGlobalEditModeToggled(!0)},disableGlobalEditMode(){this._globalEditModeEnabled=!1,L.PM.Utils.findLayers(this.map).forEach(e=>{e.pm.disable()}),this.map.off("layeradd",this._layerAddedEdit,this),this.map.off("layeradd",this.throttledReInitEdit,this),this.Toolbar.toggleButton("editMode",this.globalEditModeEnabled()),this._fireGlobalEditModeToggled(!1)},globalEditEnabled(){return this.globalEditModeEnabled()},globalEditModeEnabled(){return this._globalEditModeEnabled},toggleGlobalEditMode(e=this.globalOptions){this.globalEditModeEnabled()?this.disableGlobalEditMode():this.enableGlobalEditMode(e)},handleLayerAdditionInGlobalEditMode(){let e=this._addedLayersEdit;if(this._addedLayersEdit={},this.globalEditModeEnabled())for(let i in e){let r=e[i];this._isRelevantForEdit(r)&&r.pm.enable({...this.globalOptions})}},_layerAddedEdit({layer:e}){this._addedLayersEdit[L.stamp(e)]=e},_isRelevantForEdit(e){return e.pm&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.allowEditing}},yi=An,zt={_globalDragModeEnabled:!1,enableGlobalDragMode(){let e=L.PM.Utils.findLayers(this.map);this._globalDragModeEnabled=!0,this._addedLayersDrag={},e.forEach(i=>{this._isRelevantForDrag(i)&&i.pm.enableLayerDrag()}),this.throttledReInitDrag||(this.throttledReInitDrag=L.Util.throttle(this.reinitGlobalDragMode,100,this)),this.map.on("layeradd",this._layerAddedDrag,this),this.map.on("layeradd",this.throttledReInitDrag,this),this.Toolbar.toggleButton("dragMode",this.globalDragModeEnabled()),this._fireGlobalDragModeToggled(!0)},disableGlobalDragMode(){let e=L.PM.Utils.findLayers(this.map);this._globalDragModeEnabled=!1,e.forEach(i=>{i.pm.disableLayerDrag()}),this.map.off("layeradd",this._layerAddedDrag,this),this.map.off("layeradd",this.throttledReInitDrag,this),this.Toolbar.toggleButton("dragMode",this.globalDragModeEnabled()),this._fireGlobalDragModeToggled(!1)},globalDragModeEnabled(){return!!this._globalDragModeEnabled},toggleGlobalDragMode(){this.globalDragModeEnabled()?this.disableGlobalDragMode():this.enableGlobalDragMode()},reinitGlobalDragMode(){let e=this._addedLayersDrag;if(this._addedLayersDrag={},this.globalDragModeEnabled())for(let i in e){let r=e[i];this._isRelevantForDrag(r)&&r.pm.enableLayerDrag()}},_layerAddedDrag({layer:e}){this._addedLayersDrag[L.stamp(e)]=e},_isRelevantForDrag(e){return e.pm&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.draggable}},We=zt,wr={_globalRemovalModeEnabled:!1,enableGlobalRemovalMode(){this._globalRemovalModeEnabled=!0,this.map.eachLayer(e=>{this._isRelevantForRemoval(e)&&(e.pm.enabled()&&e.pm.disable(),e.on("click",this.removeLayer,this))}),this.throttledReInitRemoval||(this.throttledReInitRemoval=L.Util.throttle(this.handleLayerAdditionInGlobalRemovalMode,100,this)),this._addedLayersRemoval={},this.map.on("layeradd",this._layerAddedRemoval,this),this.map.on("layeradd",this.throttledReInitRemoval,this),this.Toolbar.toggleButton("removalMode",this.globalRemovalModeEnabled()),this._fireGlobalRemovalModeToggled(!0)},disableGlobalRemovalMode(){this._globalRemovalModeEnabled=!1,this.map.eachLayer(e=>{e.off("click",this.removeLayer,this)}),this.map.off("layeradd",this._layerAddedRemoval,this),this.map.off("layeradd",this.throttledReInitRemoval,this),this.Toolbar.toggleButton("removalMode",this.globalRemovalModeEnabled()),this._fireGlobalRemovalModeToggled(!1)},globalRemovalEnabled(){return this.globalRemovalModeEnabled()},globalRemovalModeEnabled(){return!!this._globalRemovalModeEnabled},toggleGlobalRemovalMode(){this.globalRemovalModeEnabled()?this.disableGlobalRemovalMode():this.enableGlobalRemovalMode()},removeLayer(e){let i=e.target;this._isRelevantForRemoval(i)&&!i.pm.dragging()&&(i.removeFrom(this.map.pm._getContainingLayer()),i.remove(),i instanceof L.LayerGroup?(this._fireRemoveLayerGroup(i),this._fireRemoveLayerGroup(this.map,i)):(i.pm._fireRemove(i),i.pm._fireRemove(this.map,i)))},_isRelevantForRemoval(e){return e.pm&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.allowRemoval},handleLayerAdditionInGlobalRemovalMode(){let e=this._addedLayersRemoval;if(this._addedLayersRemoval={},this.globalRemovalModeEnabled())for(let i in e){let r=e[i];this._isRelevantForRemoval(r)&&(r.pm.enabled()&&r.pm.disable(),r.on("click",this.removeLayer,this))}},_layerAddedRemoval({layer:e}){this._addedLayersRemoval[L.stamp(e)]=e}},Er=wr,Aa={_globalRotateModeEnabled:!1,enableGlobalRotateMode(){this._globalRotateModeEnabled=!0,L.PM.Utils.findLayers(this.map).filter(e=>e instanceof L.Polyline).forEach(e=>{this._isRelevantForRotate(e)&&e.pm.enableRotate()}),this.throttledReInitRotate||(this.throttledReInitRotate=L.Util.throttle(this.handleLayerAdditionInGlobalRotateMode,100,this)),this._addedLayersRotate={},this.map.on("layeradd",this._layerAddedRotate,this),this.map.on("layeradd",this.throttledReInitRotate,this),this.Toolbar.toggleButton("rotateMode",this.globalRotateModeEnabled()),this._fireGlobalRotateModeToggled()},disableGlobalRotateMode(){this._globalRotateModeEnabled=!1,L.PM.Utils.findLayers(this.map).filter(e=>e instanceof L.Polyline).forEach(e=>{e.pm.disableRotate()}),this.map.off("layeradd",this._layerAddedRotate,this),this.map.off("layeradd",this.throttledReInitRotate,this),this.Toolbar.toggleButton("rotateMode",this.globalRotateModeEnabled()),this._fireGlobalRotateModeToggled()},globalRotateModeEnabled(){return!!this._globalRotateModeEnabled},toggleGlobalRotateMode(){this.globalRotateModeEnabled()?this.disableGlobalRotateMode():this.enableGlobalRotateMode()},_isRelevantForRotate(e){return e.pm&&e instanceof L.Polyline&&!(e instanceof L.LayerGroup)&&(!L.PM.optIn&&!e.options.pmIgnore||L.PM.optIn&&e.options.pmIgnore===!1)&&!e._pmTempLayer&&e.pm.options.allowRotation},handleLayerAdditionInGlobalRotateMode(){let e=this._addedLayersRotate;if(this._addedLayersRotate={},this.globalRotateModeEnabled())for(let i in e){let r=e[i];this._isRelevantForRemoval(r)&&r.pm.enableRotate()}},_layerAddedRotate({layer:e}){this._addedLayersRotate[L.stamp(e)]=e}},Br=Aa,On=rt(ti()),Oa={_fireDrawStart(e="Draw",i={}){this.__fire(this._map,"pm:drawstart",{shape:this._shape,workingLayer:this._layer},e,i)},_fireDrawEnd(e="Draw",i={}){this.__fire(this._map,"pm:drawend",{shape:this._shape},e,i)},_fireCreate(e,i="Draw",r={}){this.__fire(this._map,"pm:create",{shape:this._shape,marker:e,layer:e},i,r)},_fireCenterPlaced(e="Draw",i={}){let r=e==="Draw"?this._layer:void 0,a=e!=="Draw"?this._layer:void 0;this.__fire(this._layer,"pm:centerplaced",{shape:this._shape,workingLayer:r,layer:a,latlng:this._layer.getLatLng()},e,i)},_fireCut(e,i,r,a="Draw",s={}){this.__fire(e,"pm:cut",{shape:this._shape,layer:i,originalLayer:r},a,s)},_fireEdit(e=this._layer,i="Edit",r={}){this.__fire(e,"pm:edit",{layer:this._layer,shape:this.getShape()},i,r)},_fireEnable(e="Edit",i={}){this.__fire(this._layer,"pm:enable",{layer:this._layer,shape:this.getShape()},e,i)},_fireDisable(e="Edit",i={}){this.__fire(this._layer,"pm:disable",{layer:this._layer,shape:this.getShape()},e,i)},_fireUpdate(e="Edit",i={}){this.__fire(this._layer,"pm:update",{layer:this._layer,shape:this.getShape()},e,i)},_fireMarkerDragStart(e,i=void 0,r="Edit",a={}){this.__fire(this._layer,"pm:markerdragstart",{layer:this._layer,markerEvent:e,shape:this.getShape(),indexPath:i},r,a)},_fireMarkerDrag(e,i=void 0,r="Edit",a={}){this.__fire(this._layer,"pm:markerdrag",{layer:this._layer,markerEvent:e,shape:this.getShape(),indexPath:i},r,a)},_fireMarkerDragEnd(e,i=void 0,r=void 0,a="Edit",s={}){this.__fire(this._layer,"pm:markerdragend",{layer:this._layer,markerEvent:e,shape:this.getShape(),indexPath:i,intersectionReset:r},a,s)},_fireDragStart(e="Edit",i={}){this.__fire(this._layer,"pm:dragstart",{layer:this._layer,shape:this.getShape()},e,i)},_fireDrag(e,i="Edit",r={}){this.__fire(this._layer,"pm:drag",{...e,shape:this.getShape()},i,r)},_fireDragEnd(e="Edit",i={}){this.__fire(this._layer,"pm:dragend",{layer:this._layer,shape:this.getShape()},e,i)},_fireDragEnable(e="Edit",i={}){this.__fire(this._layer,"pm:dragenable",{layer:this._layer,shape:this.getShape()},e,i)},_fireDragDisable(e="Edit",i={}){this.__fire(this._layer,"pm:dragdisable",{layer:this._layer,shape:this.getShape()},e,i)},_fireRemove(e,i=e,r="Edit",a={}){this.__fire(e,"pm:remove",{layer:i,shape:this.getShape()},r,a)},_fireVertexAdded(e,i,r,a="Edit",s={}){this.__fire(this._layer,"pm:vertexadded",{layer:this._layer,workingLayer:this._layer,marker:e,indexPath:i,latlng:r,shape:this.getShape()},a,s)},_fireVertexRemoved(e,i,r="Edit",a={}){this.__fire(this._layer,"pm:vertexremoved",{layer:this._layer,marker:e,indexPath:i,shape:this.getShape()},r,a)},_fireVertexClick(e,i,r="Edit",a={}){this.__fire(this._layer,"pm:vertexclick",{layer:this._layer,markerEvent:e,indexPath:i,shape:this.getShape()},r,a)},_fireIntersect(e,i=this._layer,r="Edit",a={}){this.__fire(i,"pm:intersect",{layer:this._layer,intersection:e,shape:this.getShape()},r,a)},_fireLayerReset(e,i,r="Edit",a={}){this.__fire(this._layer,"pm:layerreset",{layer:this._layer,markerEvent:e,indexPath:i,shape:this.getShape()},r,a)},_fireChange(e,i="Edit",r={}){this.__fire(this._layer,"pm:change",{layer:this._layer,latlngs:e,shape:this.getShape()},i,r)},_fireTextChange(e,i="Edit",r={}){this.__fire(this._layer,"pm:textchange",{layer:this._layer,text:e,shape:this.getShape()},i,r)},_fireTextFocus(e="Edit",i={}){this.__fire(this._layer,"pm:textfocus",{layer:this._layer,shape:this.getShape()},e,i)},_fireTextBlur(e="Edit",i={}){this.__fire(this._layer,"pm:textblur",{layer:this._layer,shape:this.getShape()},e,i)},_fireSnapDrag(e,i,r="Snapping",a={}){this.__fire(e,"pm:snapdrag",i,r,a)},_fireSnap(e,i,r="Snapping",a={}){this.__fire(e,"pm:snap",i,r,a)},_fireUnsnap(e,i,r="Snapping",a={}){this.__fire(e,"pm:unsnap",i,r,a)},_fireRotationEnable(e,i,r="Rotation",a={}){this.__fire(e,"pm:rotateenable",{layer:this._layer,helpLayer:this._rotatePoly,shape:this.getShape()},r,a)},_fireRotationDisable(e,i="Rotation",r={}){this.__fire(e,"pm:rotatedisable",{layer:this._layer,shape:this.getShape()},i,r)},_fireRotationStart(e,i,r="Rotation",a={}){this.__fire(e,"pm:rotatestart",{layer:this._rotationLayer,helpLayer:this._layer,startAngle:this._startAngle,originLatLngs:i},r,a)},_fireRotation(e,i,r,a=this._rotationLayer,s="Rotation",l={}){this.__fire(e,"pm:rotate",{layer:a,helpLayer:this._layer,startAngle:this._startAngle,angle:a.pm.getAngle(),angleDiff:i,oldLatLngs:r,newLatLngs:a.getLatLngs()},s,l)},_fireRotationEnd(e,i,r,a="Rotation",s={}){this.__fire(e,"pm:rotateend",{layer:this._rotationLayer,helpLayer:this._layer,startAngle:i,angle:this._rotationLayer.pm.getAngle(),originLatLngs:r,newLatLngs:this._rotationLayer.getLatLngs()},a,s)},_fireActionClick(e,i,r,a="Toolbar",s={}){this.__fire(this._map,"pm:actionclick",{text:e.text,action:e,btnName:i,button:r},a,s)},_fireButtonClick(e,i,r="Toolbar",a={}){this.__fire(this._map,"pm:buttonclick",{btnName:e,button:i},r,a)},_fireLangChange(e,i,r,a,s="Global",l={}){this.__fire(this.map,"pm:langchange",{oldLang:e,activeLang:i,fallback:r,translations:a},s,l)},_fireGlobalDragModeToggled(e,i="Global",r={}){this.__fire(this.map,"pm:globaldragmodetoggled",{enabled:e,map:this.map},i,r)},_fireGlobalEditModeToggled(e,i="Global",r={}){this.__fire(this.map,"pm:globaleditmodetoggled",{enabled:e,map:this.map},i,r)},_fireGlobalRemovalModeToggled(e,i="Global",r={}){this.__fire(this.map,"pm:globalremovalmodetoggled",{enabled:e,map:this.map},i,r)},_fireGlobalCutModeToggled(e="Global",i={}){this.__fire(this._map,"pm:globalcutmodetoggled",{enabled:!!this._enabled,map:this._map},e,i)},_fireGlobalDrawModeToggled(e="Global",i={}){this.__fire(this._map,"pm:globaldrawmodetoggled",{enabled:this._enabled,shape:this._shape,map:this._map},e,i)},_fireGlobalRotateModeToggled(e="Global",i={}){this.__fire(this.map,"pm:globalrotatemodetoggled",{enabled:this.globalRotateModeEnabled(),map:this.map},e,i)},_fireRemoveLayerGroup(e,i=e,r="Edit",a={}){this.__fire(e,"pm:remove",{layer:i,shape:void 0},r,a)},_fireKeyeventEvent(e,i,r,a="Global",s={}){this.__fire(this.map,"pm:keyevent",{event:e,eventType:i,focusOn:r},a,s)},__fire(e,i,r,a,s={}){r=(0,On.default)(r,s,{source:a}),L.PM.Utils._fireEvent(e,i,r)}},vi=Oa,_t=()=>({_lastEvents:{keydown:void 0,keyup:void 0,current:void 0},_initKeyListener(e){this.map=e,L.DomEvent.on(document,"keydown keyup",this._onKeyListener,this),L.DomEvent.on(window,"blur",this._onBlur,this),e.once("unload",this._unbindKeyListenerEvents,this)},_unbindKeyListenerEvents(){L.DomEvent.off(document,"keydown keyup",this._onKeyListener,this),L.DomEvent.off(window,"blur",this._onBlur,this)},_onKeyListener(e){let i="document";this.map.getContainer().contains(e.target)&&(i="map");let r={event:e,eventType:e.type,focusOn:i};this._lastEvents[e.type]=r,this._lastEvents.current=r,this.map.pm._fireKeyeventEvent(e,e.type,i)},_onBlur(e){e.altKey=!1;let i={event:e,eventType:e.type,focusOn:"document"};this._lastEvents[e.type]=i,this._lastEvents.current=i},getLastKeyEvent(e="current"){return this._lastEvents[e]},isShiftKeyPressed(){return this._lastEvents.current?.event.shiftKey},isAltKeyPressed(){return this._lastEvents.current?.event.altKey},isCtrlKeyPressed(){return this._lastEvents.current?.event.ctrlKey},isMetaKeyPressed(){return this._lastEvents.current?.event.metaKey},getPressedKey(){return this._lastEvents.current?.event.key}}),Fa=_t,Qt=rt(bt());function pt(e){let i=L.PM.activeLang;return(0,Qt.default)(ce[i],e)||(0,Qt.default)(ce.en,e)||e}function Gi(e){for(let i=0;i<e.length;i+=1){let r=e[i];if(Array.isArray(r)){if(Gi(r))return!0}else if(r!=null&&r!=="")return!0}return!1}function Zi(e){return e.reduce((i,r)=>{if(r.length!==0){let a=Array.isArray(r)?Zi(r):r;Array.isArray(a)?a.length!==0&&i.push(a):i.push(a)}return i},[])}function Fn(e,i,r){let a={a:L.CRS.Earth.R,b:63567523142e-4,f:.0033528106647474805},{a:s,b:l,f:h}=a,d=e.lng,p=e.lat,_=r,k=Math.PI,w=i*k/180,z=Math.sin(w),A=Math.cos(w),V=(1-h)*Math.tan(p*k/180),q=1/Math.sqrt(1+V*V),X=V*q,mt=Math.atan2(V,A),x=q*z,E=1-x*x,D=E*(s*s-l*l)/(l*l),G=1+D/16384*(4096+D*(-768+D*(320-175*D))),S=D/1024*(256+D*(-128+D*(74-47*D))),N=_/(l*G),f=2*Math.PI,g,v,y;for(;Math.abs(N-f)>1e-12;){g=Math.cos(2*mt+N),v=Math.sin(N),y=Math.cos(N);let H=S*v*(g+S/4*(y*(-1+2*g*g)-S/6*g*(-3+4*v*v)*(-3+4*g*g)));f=N,N=_/(l*G)+H}let b=X*v-q*y*A,C=Math.atan2(X*y+q*v*A,(1-h)*Math.sqrt(x*x+b*b)),P=Math.atan2(v*z,q*y-X*v*A),B=h/16*E*(4+h*(4-3*E)),T=P-(1-B)*h*x*(N+B*v*(g+B*y*(-1+2*g*g))),I=d+T*180/k,R=C*180/k;return L.latLng(I,R)}function Pr(e,i,r,a,s=!0){let l,h,d,p=[];for(let _=0;_<r;_+=1){if(s)l=_*360/r+a,h=Fn(e,l,i),d=L.latLng(h.lng,h.lat);else{let k=e.lat+Math.cos(2*_*Math.PI/r)*i,w=e.lng+Math.sin(2*_*Math.PI/r)*i;d=L.latLng(k,w)}p.push(d)}return p}function Tr(e,i,r){i=(i+360)%360;let a=Math.PI/180,s=180/Math.PI,{R:l}=L.CRS.Earth,h=e.lng*a,d=e.lat*a,p=i*a,_=Math.sin(d),k=Math.cos(d),w=Math.cos(r/l),z=Math.sin(r/l),A=Math.asin(_*w+k*z*Math.cos(p)),V=h+Math.atan2(Math.sin(p)*z*k,w-_*Math.sin(A));V*=s;let q=V-360,X=V<-180?V+360:V;return V=V>180?q:X,L.latLng([A*s,V])}function Rn(e,i,r){let a=e.latLngToContainerPoint(i),s=e.latLngToContainerPoint(r),l=Math.atan2(s.y-a.y,s.x-a.x)*180/Math.PI+90;return l+=l<0?360:0,l}function ji(e,i,r,a){let s=Rn(e,i,r);return Tr(i,s,a)}function In(e,i,r="asc"){if(!i||Object.keys(i).length===0)return(p,_)=>p-_;let a=Object.keys(i),s,l=a.length-1,h={};for(;l>=0;)s=a[l],h[s.toLowerCase()]=i[s],l-=1;function d(p){if(p instanceof L.Marker)return"Marker";if(p instanceof L.Circle)return"Circle";if(p instanceof L.CircleMarker)return"CircleMarker";if(p instanceof L.Rectangle)return"Rectangle";if(p instanceof L.Polygon)return"Polygon";if(p instanceof L.Polyline)return"Line"}return(p,_)=>{let k,w;if(e==="instanceofShape"){if(k=d(p.layer).toLowerCase(),w=d(_.layer).toLowerCase(),!k||!w)return 0}else{if(!p.hasOwnProperty(e)||!_.hasOwnProperty(e))return 0;k=p[e].toLowerCase(),w=_[e].toLowerCase()}let z=k in h?h[k]:Number.MAX_SAFE_INTEGER,A=w in h?h[w]:Number.MAX_SAFE_INTEGER,V=0;return z<A?V=-1:z>A&&(V=1),r==="desc"?V*-1:V}}function De(e,i=e.getLatLngs()){return e instanceof L.Polygon?L.polygon(i).getLatLngs():L.polyline(i).getLatLngs()}function de(e,i){if(i.options.crs?.projection?.MAX_LATITUDE){let r=i.options.crs?.projection?.MAX_LATITUDE;e.lat=Math.max(Math.min(r,e.lat),-r)}return e}function ei(e){return e.options.renderer||e._map&&(e._map._getPaneRenderer(e.options.pane)||e._map.options.renderer||e._map._renderer)||e._renderer}var Dr=L.Class.extend({includes:[yi,We,Er,Br,vi],initialize(e){this.map=e,this.Draw=new L.PM.Draw(e),this.Toolbar=new L.PM.Toolbar(e),this.Keyboard=Fa(),this.globalOptions={snappable:!0,layerGroup:void 0,snappingOrder:["Marker","CircleMarker","Circle","Line","Polygon","Rectangle"],panes:{vertexPane:"markerPane",layerPane:"overlayPane",markerPane:"markerPane"},draggable:!0},this.Keyboard._initKeyListener(e)},setLang(e="en",i,r="en"){let a=L.PM.activeLang;i&&(ce[e]=(0,Fi.default)(ce[r],i)),L.PM.activeLang=e,this.map.pm.Toolbar.reinit(),this._fireLangChange(a,e,r,ce[e])},addControls(e){this.Toolbar.addControls(e)},removeControls(){this.Toolbar.removeControls()},toggleControls(){this.Toolbar.toggleControls()},controlsVisible(){return this.Toolbar.isVisible},enableDraw(e="Polygon",i){e==="Poly"&&(e="Polygon"),this.Draw.enable(e,i)},disableDraw(e="Polygon"){e==="Poly"&&(e="Polygon"),this.Draw.disable(e)},setPathOptions(e,i={}){let r=i.ignoreShapes||[],a=i.merge||!1;this.map.pm.Draw.shapes.forEach(s=>{r.indexOf(s)===-1&&this.map.pm.Draw[s].setPathOptions(e,a)})},getGlobalOptions(){return this.globalOptions},setGlobalOptions(e){let i=(0,Fi.default)(this.globalOptions,e);i.editable&&(i.resizeableCircleMarker=i.editable,delete i.editable);let r=!1;this.map.pm.Draw.CircleMarker.enabled()&&!!this.map.pm.Draw.CircleMarker.options.resizeableCircleMarker!=!!i.resizeableCircleMarker&&(this.map.pm.Draw.CircleMarker.disable(),r=!0);let a=!1;this.map.pm.Draw.Circle.enabled()&&!!this.map.pm.Draw.Circle.options.resizableCircle!=!!i.resizableCircle&&(this.map.pm.Draw.Circle.disable(),a=!0),this.map.pm.Draw.shapes.forEach(s=>{this.map.pm.Draw[s].setOptions(i)}),r&&this.map.pm.Draw.CircleMarker.enable(),a&&this.map.pm.Draw.Circle.enable(),L.PM.Utils.findLayers(this.map).forEach(s=>{s.pm.setOptions(i)}),this.map.fire("pm:globaloptionschanged"),this.globalOptions=i,this.applyGlobalOptions()},applyGlobalOptions(){L.PM.Utils.findLayers(this.map).forEach(e=>{e.pm.enabled()&&e.pm.applyOptions()})},globalDrawModeEnabled(){return!!this.Draw.getActiveShape()},globalCutModeEnabled(){return!!this.Draw.Cut.enabled()},enableGlobalCutMode(e){return this.Draw.Cut.enable(e)},toggleGlobalCutMode(e){return this.Draw.Cut.toggle(e)},disableGlobalCutMode(){return this.Draw.Cut.disable()},getGeomanLayers(e=!1){let i=L.PM.Utils.findLayers(this.map);if(!e)return i;let r=L.featureGroup();return r._pmTempLayer=!0,i.forEach(a=>{r.addLayer(a)}),r},getGeomanDrawLayers(e=!1){let i=L.PM.Utils.findLayers(this.map).filter(a=>a._drawnByGeoman===!0);if(!e)return i;let r=L.featureGroup();return r._pmTempLayer=!0,i.forEach(a=>{r.addLayer(a)}),r},_getContainingLayer(){return this.globalOptions.layerGroup&&this.globalOptions.layerGroup instanceof L.LayerGroup?this.globalOptions.layerGroup:this.map},_isCRSSimple(){return this.map.options.crs===L.CRS.Simple},_touchEventCounter:0,_addTouchEvents(e){this._touchEventCounter===0&&(L.DomEvent.on(e,"touchmove",this._canvasTouchMove,this),L.DomEvent.on(e,"touchstart touchend touchcancel",this._canvasTouchClick,this)),this._touchEventCounter+=1},_removeTouchEvents(e){this._touchEventCounter===1&&(L.DomEvent.off(e,"touchmove",this._canvasTouchMove,this),L.DomEvent.off(e,"touchstart touchend touchcancel",this._canvasTouchClick,this)),this._touchEventCounter=this._touchEventCounter<=1?0:this._touchEventCounter-1},_canvasTouchMove(e){ei(this.map)._onMouseMove(this._createMouseEvent("mousemove",e))},_canvasTouchClick(e){let i="";e.type==="touchstart"||e.type==="pointerdown"?i="mousedown":(e.type==="touchend"||e.type==="pointerup"||e.type==="touchcancel"||e.type==="pointercancel")&&(i="mouseup"),i&&ei(this.map)._onClick(this._createMouseEvent(i,e))},_createMouseEvent(e,i){let r,a=i.touches[0]||i.changedTouches[0];try{r=new MouseEvent(e,{bubbles:i.bubbles,cancelable:i.cancelable,view:i.view,detail:a.detail,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY,ctrlKey:i.ctrlKey,altKey:i.altKey,shiftKey:i.shiftKey,metaKey:i.metaKey,button:i.button,relatedTarget:i.relatedTarget})}catch{r=document.createEvent("MouseEvents"),r.initMouseEvent(e,i.bubbles,i.cancelable,i.view,a.detail,a.screenX,a.screenY,a.clientX,a.clientY,i.ctrlKey,i.altKey,i.shiftKey,i.metaKey,i.button,i.relatedTarget)}return r}}),Se=Dr,Sr=L.Control.extend({includes:[vi],options:{position:"topleft",disableByOtherButtons:!0},initialize(e){this._button=L.Util.extend({},this.options,e)},onAdd(e){return this._map=e,this._map.pm.Toolbar.options.oneBlock?this._container=this._map.pm.Toolbar._createContainer(this.options.position):this._button.tool==="edit"?this._container=this._map.pm.Toolbar.editContainer:this._button.tool==="options"?this._container=this._map.pm.Toolbar.optionsContainer:this._button.tool==="custom"?this._container=this._map.pm.Toolbar.customContainer:this._container=this._map.pm.Toolbar.drawContainer,this._renderButton(),this._container},_renderButton(){let e=this.buttonsDomNode;this.buttonsDomNode=this._makeButton(this._button),e?e.replaceWith(this.buttonsDomNode):this._container.appendChild(this.buttonsDomNode)},onRemove(){return this.buttonsDomNode.remove(),this._container},getText(){return this._button.text},getIconUrl(){return this._button.iconUrl},destroy(){this._button={},this._update()},toggle(e){return typeof e=="boolean"?this._button.toggleStatus=e:this._button.toggleStatus=!this._button.toggleStatus,this._applyStyleClasses(),this._button.toggleStatus},toggled(){return this._button.toggleStatus},onCreate(){this.toggle(!1)},disable(){this.toggle(!1),this._button.disabled=!0,this._updateDisabled()},enable(){this._button.disabled=!1,this._updateDisabled()},_triggerClick(e){e&&e.preventDefault(),!this._button.disabled&&(this._button.onClick(e,{button:this,event:e}),this._clicked(e),this._button.afterClick(e,{button:this,event:e}))},_makeButton(e){let i=this.options.position.indexOf("right")>-1?"pos-right":"",r=L.DomUtil.create("div",`button-container  ${i}`,this._container);e.title&&r.setAttribute("title",e.title);let a=L.DomUtil.create("a","leaflet-buttons-control-button",r);a.setAttribute("role","button"),a.setAttribute("tabindex","0"),a.href="#";let s=L.DomUtil.create("div",`leaflet-pm-actions-container ${i}`,r),l=e.actions,h={cancel:{text:pt("actions.cancel"),title:pt("actions.cancel"),onClick(){this._triggerClick()}},finishMode:{text:pt("actions.finish"),title:pt("actions.finish"),onClick(){this._triggerClick()}},removeLastVertex:{text:pt("actions.removeLastVertex"),title:pt("actions.removeLastVertex"),onClick(){this._map.pm.Draw[e.jsClass]._removeLastVertex()}},finish:{text:pt("actions.finish"),title:pt("actions.finish"),onClick(p){this._map.pm.Draw[e.jsClass]._finishShape(p)}}};l.forEach(p=>{let _=typeof p=="string"?p:p.name,k;if(h[_])k=h[_];else if(p.text)k=p;else return;let w=L.DomUtil.create("a",`leaflet-pm-action ${i} action-${_}`,s);if(w.setAttribute("role","button"),w.setAttribute("tabindex","0"),w.href="#",k.title&&(w.title=k.title),w.innerHTML=k.text,L.DomEvent.disableClickPropagation(w),L.DomEvent.on(w,"click",L.DomEvent.stop),!e.disabled&&k.onClick){let z=A=>{A.preventDefault();let V="",{buttons:q}=this._map.pm.Toolbar;for(let X in q)if(q[X]._button===e){V=X;break}this._fireActionClick(k,V,e)};L.DomEvent.addListener(w,"click",z,this),L.DomEvent.addListener(w,"click",k.onClick,this)}}),e.toggleStatus&&L.DomUtil.addClass(r,"active");let d=L.DomUtil.create("div","control-icon",a);return e.iconUrl&&d.setAttribute("src",e.iconUrl),e.className&&L.DomUtil.addClass(d,e.className),L.DomEvent.disableClickPropagation(a),L.DomEvent.on(a,"click",L.DomEvent.stop),e.disabled||(L.DomEvent.addListener(a,"click",this._onBtnClick,this),L.DomEvent.addListener(a,"click",this._triggerClick,this)),e.disabled&&(L.DomUtil.addClass(a,"pm-disabled"),a.setAttribute("aria-disabled","true")),r},_applyStyleClasses(){this._container&&(!this._button.toggleStatus||this._button.cssToggle===!1?(L.DomUtil.removeClass(this.buttonsDomNode,"active"),L.DomUtil.removeClass(this._container,"activeChild")):(L.DomUtil.addClass(this.buttonsDomNode,"active"),L.DomUtil.addClass(this._container,"activeChild")))},_onBtnClick(){if(this._button.disabled)return;this._button.disableOtherButtons&&this._map.pm.Toolbar.triggerClickOnToggledButtons(this);let e="",{buttons:i}=this._map.pm.Toolbar;for(let r in i)if(i[r]._button===this._button){e=r;break}this._fireButtonClick(e,this._button)},_clicked(){this._button.doToggle&&this.toggle()},_updateDisabled(){if(!this._container)return;let e="pm-disabled",i=this.buttonsDomNode.children[0];this._button.disabled?(L.DomUtil.addClass(i,e),i.setAttribute("aria-disabled","true")):(L.DomUtil.removeClass(i,e),i.setAttribute("aria-disabled","false"))}}),Ar=Sr;L.Control.PMButton=Ar;var zn=L.Class.extend({options:{drawMarker:!0,drawRectangle:!0,drawPolyline:!0,drawPolygon:!0,drawCircle:!0,drawCircleMarker:!0,drawText:!0,editMode:!0,dragMode:!0,cutPolygon:!0,removalMode:!0,rotateMode:!0,snappingOption:!0,drawControls:!0,editControls:!0,optionsControls:!0,customControls:!0,oneBlock:!1,position:"topleft",positions:{draw:"",edit:"",options:"",custom:""}},customButtons:[],initialize(e){this.customButtons=[],this.options.positions={draw:"",edit:"",options:"",custom:""},this.init(e)},reinit(){let e=this.isVisible;this.removeControls(),this._defineButtons(),e&&this.addControls()},init(e){this.map=e,this.buttons={},this.isVisible=!1,this.drawContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-draw leaflet-bar leaflet-control"),this.editContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-edit leaflet-bar leaflet-control"),this.optionsContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-options leaflet-bar leaflet-control"),this.customContainer=L.DomUtil.create("div","leaflet-pm-toolbar leaflet-pm-custom leaflet-bar leaflet-control"),this._defineButtons()},_createContainer(e){let i=`${e}Container`;return this[i]||(this[i]=L.DomUtil.create("div",`leaflet-pm-toolbar leaflet-pm-${e} leaflet-bar leaflet-control`)),this[i]},getButtons(){return this.buttons},addControls(e=this.options){typeof e.editPolygon<"u"&&(e.editMode=e.editPolygon),typeof e.deleteLayer<"u"&&(e.removalMode=e.deleteLayer),L.Util.setOptions(this,e),this.applyIconStyle(),this.isVisible=!0,this._showHideButtons()},applyIconStyle(){let e=this.getButtons(),i={geomanIcons:{drawMarker:"control-icon leaflet-pm-icon-marker",drawPolyline:"control-icon leaflet-pm-icon-polyline",drawRectangle:"control-icon leaflet-pm-icon-rectangle",drawPolygon:"control-icon leaflet-pm-icon-polygon",drawCircle:"control-icon leaflet-pm-icon-circle",drawCircleMarker:"control-icon leaflet-pm-icon-circle-marker",editMode:"control-icon leaflet-pm-icon-edit",dragMode:"control-icon leaflet-pm-icon-drag",cutPolygon:"control-icon leaflet-pm-icon-cut",removalMode:"control-icon leaflet-pm-icon-delete",drawText:"control-icon leaflet-pm-icon-text"}};for(let r in e){let a=e[r];L.Util.setOptions(a,{className:i.geomanIcons[r]})}},removeControls(){let e=this.getButtons();for(let i in e)e[i].remove();this.isVisible=!1},toggleControls(e=this.options){this.isVisible?this.removeControls():this.addControls(e)},_addButton(e,i){return this.buttons[e]=i,this.options[e]=!!this.options[e]||!1,this.buttons[e]},triggerClickOnToggledButtons(e){for(let i in this.buttons){let r=this.buttons[i];r._button.disableByOtherButtons&&r!==e&&r.toggled()&&r._triggerClick()}},toggleButton(e,i,r=!0){return e==="editPolygon"&&(e="editMode"),e==="deleteLayer"&&(e="removalMode"),r&&this.triggerClickOnToggledButtons(this.buttons[e]),this.buttons[e]?this.buttons[e].toggle(i):!1},_defineButtons(){let e={className:"control-icon leaflet-pm-icon-marker",title:pt("buttonTitles.drawMarkerButton"),jsClass:"Marker",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},i={title:pt("buttonTitles.drawPolyButton"),className:"control-icon leaflet-pm-icon-polygon",jsClass:"Polygon",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["finish","removeLastVertex","cancel"]},r={className:"control-icon leaflet-pm-icon-polyline",title:pt("buttonTitles.drawLineButton"),jsClass:"Line",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["finish","removeLastVertex","cancel"]},a={title:pt("buttonTitles.drawCircleButton"),className:"control-icon leaflet-pm-icon-circle",jsClass:"Circle",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},s={title:pt("buttonTitles.drawCircleMarkerButton"),className:"control-icon leaflet-pm-icon-circle-marker",jsClass:"CircleMarker",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},l={title:pt("buttonTitles.drawRectButton"),className:"control-icon leaflet-pm-icon-rectangle",jsClass:"Rectangle",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]},h={title:pt("buttonTitles.editButton"),className:"control-icon leaflet-pm-icon-edit",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalEditMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},d={title:pt("buttonTitles.dragButton"),className:"control-icon leaflet-pm-icon-drag",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalDragMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},p={title:pt("buttonTitles.cutButton"),className:"control-icon leaflet-pm-icon-cut",jsClass:"Cut",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle({snappable:!0,cursorMarker:!0,allowSelfIntersection:!1})},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finish","removeLastVertex","cancel"]},_={title:pt("buttonTitles.deleteButton"),className:"control-icon leaflet-pm-icon-delete",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalRemovalMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},k={title:pt("buttonTitles.rotateButton"),className:"control-icon leaflet-pm-icon-rotate",onClick:()=>{},afterClick:()=>{this.map.pm.toggleGlobalRotateMode()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,tool:"edit",actions:["finishMode"]},w={className:"control-icon leaflet-pm-icon-text",title:pt("buttonTitles.drawTextButton"),jsClass:"Text",onClick:()=>{},afterClick:(z,A)=>{this.map.pm.Draw[A.button._button.jsClass].toggle()},doToggle:!0,toggleStatus:!1,disableOtherButtons:!0,position:this.options.position,actions:["cancel"]};this._addButton("drawMarker",new L.Control.PMButton(e)),this._addButton("drawPolyline",new L.Control.PMButton(r)),this._addButton("drawRectangle",new L.Control.PMButton(l)),this._addButton("drawPolygon",new L.Control.PMButton(i)),this._addButton("drawCircle",new L.Control.PMButton(a)),this._addButton("drawCircleMarker",new L.Control.PMButton(s)),this._addButton("drawText",new L.Control.PMButton(w)),this._addButton("editMode",new L.Control.PMButton(h)),this._addButton("dragMode",new L.Control.PMButton(d)),this._addButton("cutPolygon",new L.Control.PMButton(p)),this._addButton("removalMode",new L.Control.PMButton(_)),this._addButton("rotateMode",new L.Control.PMButton(k))},_showHideButtons(){if(!this.isVisible)return;this.removeControls(),this.isVisible=!0;let e=this.getButtons(),i=[];this.options.drawControls===!1&&(i=i.concat(Object.keys(e).filter(r=>!e[r]._button.tool))),this.options.editControls===!1&&(i=i.concat(Object.keys(e).filter(r=>e[r]._button.tool==="edit"))),this.options.optionsControls===!1&&(i=i.concat(Object.keys(e).filter(r=>e[r]._button.tool==="options"))),this.options.customControls===!1&&(i=i.concat(Object.keys(e).filter(r=>e[r]._button.tool==="custom")));for(let r in e)if(this.options[r]&&i.indexOf(r)===-1){let a=e[r]._button.tool;a||(a="draw"),e[r].setPosition(this._getBtnPosition(a)),e[r].addTo(this.map)}},_getBtnPosition(e){return this.options.positions&&this.options.positions[e]?this.options.positions[e]:this.options.position},setBlockPosition(e,i){this.options.positions[e]=i,this._showHideButtons(),this.changeControlOrder()},getBlockPositions(){return this.options.positions},copyDrawControl(e,i){if(i)typeof i!="object"&&(i={name:i});else throw new TypeError("Button has no name");let r=this._btnNameMapping(e);if(!i.name)throw new TypeError("Button has no name");if(this.buttons[i.name])throw new TypeError("Button with this name already exists");let a=this.map.pm.Draw.createNewDrawInstance(i.name,r);i={...this.buttons[r]._button,...i};let s=this.createCustomControl(i);return{drawInstance:a,control:s}},createCustomControl(e){if(!e.name)throw new TypeError("Button has no name");if(this.buttons[e.name])throw new TypeError("Button with this name already exists");e.onClick||(e.onClick=()=>{}),e.afterClick||(e.afterClick=()=>{}),e.toggle!==!1&&(e.toggle=!0),e.block&&(e.block=e.block.toLowerCase()),(!e.block||e.block==="draw")&&(e.block=""),e.className?e.className.indexOf("control-icon")===-1&&(e.className=`control-icon ${e.className}`):e.className="control-icon";let i={tool:e.block,className:e.className,title:e.title||"",jsClass:e.name,onClick:e.onClick,afterClick:e.afterClick,doToggle:e.toggle,toggleStatus:!1,disableOtherButtons:e.disableOtherButtons??!0,disableByOtherButtons:e.disableByOtherButtons??!0,cssToggle:e.toggle,position:this.options.position,actions:e.actions||[],disabled:!!e.disabled};this.options[e.name]!==!1&&(this.options[e.name]=!0);let r=this._addButton(e.name,new L.Control.PMButton(i));return this.changeControlOrder(),r},controlExists(e){return!!this.getButton(e)},getButton(e){return this.getButtons()[e]},getButtonsInBlock(e){let i={};if(e)for(let r in this.getButtons()){let a=this.getButtons()[r];(a._button.tool===e||e==="draw"&&!a._button.tool)&&(i[r]=a)}return i},changeControlOrder(e=[]){let i=this._shapeMapping(),r=[];e.forEach(l=>{i[l]?r.push(i[l]):r.push(l)});let a=this.getButtons(),s={};r.forEach(l=>{a[l]&&(s[l]=a[l])}),Object.keys(a).filter(l=>!a[l]._button.tool).forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).filter(l=>a[l]._button.tool==="edit").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).filter(l=>a[l]._button.tool==="options").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).filter(l=>a[l]._button.tool==="custom").forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),Object.keys(a).forEach(l=>{r.indexOf(l)===-1&&(s[l]=a[l])}),this.map.pm.Toolbar.buttons=s,this._showHideButtons()},getControlOrder(){let e=this.getButtons(),i=[];for(let r in e)i.push(r);return i},changeActionsOfControl(e,i){let r=this._btnNameMapping(e);if(!r)throw new TypeError("No name passed");if(!i)throw new TypeError("No actions passed");if(!this.buttons[r])throw new TypeError("Button with this name not exists");this.buttons[r]._button.actions=i,this.changeControlOrder()},setButtonDisabled(e,i){let r=this._btnNameMapping(e);i?this.buttons[r].disable():this.buttons[r].enable()},_shapeMapping(){return{Marker:"drawMarker",Circle:"drawCircle",Polygon:"drawPolygon",Rectangle:"drawRectangle",Polyline:"drawPolyline",Line:"drawPolyline",CircleMarker:"drawCircleMarker",Edit:"editMode",Drag:"dragMode",Cut:"cutPolygon",Removal:"removalMode",Rotate:"rotateMode",Text:"drawText"}},_btnNameMapping(e){let i=this._shapeMapping();return i[e]?i[e]:e}}),Ra=zn,Or=rt(ti()),Fr={_initSnappableMarkers(){this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,this._assignEvents(this._markers),this._layer.off("pm:dragstart",this._unsnap,this),this._layer.on("pm:dragstart",this._unsnap,this)},_disableSnapping(){this._layer.off("pm:dragstart",this._unsnap,this)},_assignEvents(e){e.forEach(i=>{if(Array.isArray(i)){this._assignEvents(i);return}i.off("drag",this._handleSnapping,this),i.on("drag",this._handleSnapping,this),i.off("dragend",this._cleanupSnapping,this),i.on("dragend",this._cleanupSnapping,this)})},_cleanupSnapping(e){if(e){let i=e.target;i._snapped=!1}delete this._snapList,this.throttledList&&(this._map.off("layeradd",this.throttledList,this),this.throttledList=void 0),this._map.off("layerremove",this._handleSnapLayerRemoval,this),this.debugIndicatorLines&&this.debugIndicatorLines.forEach(i=>{i.remove()})},_handleThrottleSnapping(){this.throttledList&&this._createSnapList()},_handleSnapping(e){let i=e.target;if(i._snapped=!1,this.throttledList||(this.throttledList=L.Util.throttle(this._handleThrottleSnapping,100,this)),e?.originalEvent?.altKey||this._map?.pm?.Keyboard.isAltKeyPressed()||(this._snapList===void 0&&(this._createSnapList(),this._map.off("layeradd",this.throttledList,this),this._map.on("layeradd",this.throttledList,this)),this._snapList.length<=0))return!1;let r=this._calcClosestLayer(i.getLatLng(),this._snapList);if(Object.keys(r).length===0)return!1;let a=r.layer instanceof L.Marker||r.layer instanceof L.CircleMarker||!this.options.snapSegment,s;a?s=r.latlng:s=this._checkPrioritiySnapping(r);let l=this.options.snapDistance,h={marker:i,shape:this._shape,snapLatLng:s,segment:r.segment,layer:this._layer,workingLayer:this._layer,layerInteractedWith:r.layer,distance:r.distance};if(this._fireSnapDrag(h.marker,h),this._fireSnapDrag(this._layer,h),r.distance<l){i._orgLatLng=i.getLatLng(),i.setLatLng(s),i._snapped=!0,i._snapInfo=h;let d=()=>{this._snapLatLng=s,this._fireSnap(i,h),this._fireSnap(this._layer,h)},p=this._snapLatLng||{},_=s||{};(p.lat!==_.lat||p.lng!==_.lng)&&d()}else this._snapLatLng&&(this._unsnap(h),i._snapped=!1,i._snapInfo=void 0,this._fireUnsnap(h.marker,h),this._fireUnsnap(this._layer,h));return!0},_createSnapList(){let e=[],i=[],r=this._map;r.off("layerremove",this._handleSnapLayerRemoval,this),r.on("layerremove",this._handleSnapLayerRemoval,this),r.eachLayer(a=>{if((a instanceof L.Polyline||a instanceof L.Marker||a instanceof L.CircleMarker||a instanceof L.ImageOverlay)&&a.options.snapIgnore!==!0){if(a.options.snapIgnore===void 0&&(!L.PM.optIn&&a.options.pmIgnore===!0||L.PM.optIn&&a.options.pmIgnore!==!1))return;(a instanceof L.Circle||a instanceof L.CircleMarker)&&a.pm&&a.pm._hiddenPolyCircle?e.push(a.pm._hiddenPolyCircle):a instanceof L.ImageOverlay&&(a=L.rectangle(a.getBounds())),e.push(a);let s=L.polyline([],{color:"red",pmIgnore:!0});s._pmTempLayer=!0,i.push(s),(a instanceof L.Circle||a instanceof L.CircleMarker)&&i.push(s)}}),e=e.filter(a=>this._layer!==a),e=e.filter(a=>a._latlng||a._latlngs&&Gi(a._latlngs)),e=e.filter(a=>!a._pmTempLayer),this._otherSnapLayers?(this._otherSnapLayers.forEach(()=>{let a=L.polyline([],{color:"red",pmIgnore:!0});a._pmTempLayer=!0,i.push(a)}),this._snapList=e.concat(this._otherSnapLayers)):this._snapList=e,this.debugIndicatorLines=i},_handleSnapLayerRemoval({layer:e}){if(!e._leaflet_id)return;let i=this._snapList.findIndex(r=>r._leaflet_id===e._leaflet_id);i>-1&&this._snapList.splice(i,1)},_calcClosestLayer(e,i){return this._calcClosestLayers(e,i,1)[0]},_calcClosestLayers(e,i,r=1){let a=[],s={};i.forEach((h,d)=>{if(h._parentCopy&&h._parentCopy===this._layer)return;let p=this._calcLayerDistances(e,h);if(p.distance=Math.floor(p.distance),this.debugIndicatorLines){if(!this.debugIndicatorLines[d]){let _=L.polyline([],{color:"red",pmIgnore:!0});_._pmTempLayer=!0,this.debugIndicatorLines[d]=_}this.debugIndicatorLines[d].setLatLngs([e,p.latlng])}r===1&&(s.distance===void 0||p.distance-5<=s.distance)?(p.distance+5<s.distance&&(a=[]),s=p,s.layer=h,a.push(s)):r!==1&&(s={},s=p,s.layer=h,a.push(s))}),r!==1&&(a=a.sort((h,d)=>h.distance-d.distance)),r===-1&&(r=a.length);let l=this._getClosestLayerByPriority(a,r);return L.Util.isArray(l)?l:[l]},_calcLayerDistances(e,i){let r=this._map,a=i instanceof L.Marker||i instanceof L.CircleMarker,s=i instanceof L.Polygon,l=e;if(a){let h=i.getLatLng();return{latlng:{...h},distance:this._getDistance(r,h,l)}}return this._calcLatLngDistances(l,i.getLatLngs(),r,s)},_calcLatLngDistances(e,i,r,a=!1){let s,l,h,d=p=>{p.forEach((_,k)=>{if(Array.isArray(_)){d(_);return}if(this.options.snapSegment){let w=_,z;a?z=k+1===p.length?0:k+1:z=k+1===p.length?void 0:k+1;let A=p[z];if(A){let V=this._getDistanceToSegment(r,e,w,A);(l===void 0||V<l)&&(l=V,h=[w,A])}}else{let w=this._getDistance(r,e,_);(l===void 0||w<l)&&(l=w,s=_)}})};return d(i),this.options.snapSegment?{latlng:{...this._getClosestPointOnSegment(r,e,h[0],h[1])},segment:h,distance:l}:{latlng:s,distance:l}},_getClosestLayerByPriority(e,i=1){e=e.sort((h,d)=>h._leaflet_id-d._leaflet_id);let r=["Marker","CircleMarker","Circle","Line","Polygon","Rectangle"],a=this._map.pm.globalOptions.snappingOrder||[],s=0,l={};return a.concat(r).forEach(h=>{l[h]||(s+=1,l[h]=s)}),e.sort(In("instanceofShape",l)),i===1?e[0]||{}:e.slice(0,i)},_checkPrioritiySnapping(e){let i=this._map,r=e.segment[0],a=e.segment[1],s=e.latlng,l=this._getDistance(i,r,s),h=this._getDistance(i,a,s),d=l<h?r:a,p=l<h?l:h;if(this.options.snapMiddle){let w=L.PM.Utils.calcMiddleLatLng(i,r,a),z=this._getDistance(i,w,s);z<l&&z<h&&(d=w,p=z)}let _=this.options.snapDistance,k;return p<_?k=d:k=s,{...k}},_unsnap(){delete this._snapLatLng},_getClosestPointOnSegment(e,i,r,a){let s=e.getMaxZoom();s===1/0&&(s=e.getZoom());let l=e.project(i,s),h=e.project(r,s),d=e.project(a,s),p=L.LineUtil.closestPointOnSegment(l,h,d);return e.unproject(p,s)},_getDistanceToSegment(e,i,r,a){let s=e.latLngToLayerPoint(i),l=e.latLngToLayerPoint(r),h=e.latLngToLayerPoint(a);return L.LineUtil.pointToSegmentDistance(s,l,h)},_getDistance(e,i,r){return e.latLngToLayerPoint(i).distanceTo(e.latLngToLayerPoint(r))}},Rr=Fr,Ia=L.Class.extend({includes:[Rr,vi],options:{snappable:!0,snapDistance:20,snapMiddle:!1,allowSelfIntersection:!0,tooltips:!0,templineStyle:{},hintlineStyle:{color:"#3388ff",dashArray:"5,5"},pathOptions:null,cursorMarker:!0,finishOn:null,markerStyle:{draggable:!0,icon:L.icon()},hideMiddleMarkers:!1,minRadiusCircle:null,maxRadiusCircle:null,minRadiusCircleMarker:null,maxRadiusCircleMarker:null,resizeableCircleMarker:!1,resizableCircle:!0,markerEditable:!0,continueDrawing:!1,snapSegment:!0,requireSnapToFinish:!1,rectangleAngle:0},setOptions(e){L.Util.setOptions(this,e),this.setStyle(this.options)},setStyle(){},getOptions(){return this.options},initialize(e){let i=new L.Icon.Default;i.options.tooltipAnchor=[0,0],this.options.markerStyle.icon=i,this._map=e,this.shapes=["Marker","CircleMarker","Line","Polygon","Rectangle","Circle","Cut","Text"],this.shapes.forEach(r=>{this[r]=new L.PM.Draw[r](this._map)}),this.Marker.setOptions({continueDrawing:!0}),this.CircleMarker.setOptions({continueDrawing:!0})},setPathOptions(e,i=!1){i?this.options.pathOptions=(0,Or.default)(this.options.pathOptions,e):this.options.pathOptions=e},getShapes(){return this.shapes},getShape(){return this._shape},enable(e,i){if(!e)throw new Error(`Error: Please pass a shape as a parameter. Possible shapes are: ${this.getShapes().join(",")}`);this.disable(),this[e].enable(i)},disable(){this.shapes.forEach(e=>{this[e].disable()})},addControls(){this.shapes.forEach(e=>{this[e].addButton()})},getActiveShape(){let e;return this.shapes.forEach(i=>{this[i]._enabled&&(e=i)}),e},_setGlobalDrawMode(){this._shape==="Cut"?this._fireGlobalCutModeToggled():this._fireGlobalDrawModeToggled();let e=[];this._map.eachLayer(i=>{(i instanceof L.Polyline||i instanceof L.Marker||i instanceof L.Circle||i instanceof L.CircleMarker||i instanceof L.ImageOverlay)&&(i._pmTempLayer||e.push(i))}),this._enabled?e.forEach(i=>{L.PM.Utils.disablePopup(i)}):e.forEach(i=>{L.PM.Utils.enablePopup(i)})},createNewDrawInstance(e,i){let r=this._getShapeFromBtnName(i);if(this[e])throw new TypeError("Draw Type already exists");if(!L.PM.Draw[r])throw new TypeError(`There is no class L.PM.Draw.${r}`);return this[e]=new L.PM.Draw[r](this._map),this[e].toolbarButtonName=e,this[e]._shape=e,this.shapes.push(e),this[i]&&this[e].setOptions(this[i].options),this[e].setOptions(this[e].options),this[e]},_getShapeFromBtnName(e){let i={drawMarker:"Marker",drawCircle:"Circle",drawPolygon:"Polygon",drawPolyline:"Line",drawRectangle:"Rectangle",drawCircleMarker:"CircleMarker",editMode:"Edit",dragMode:"Drag",cutPolygon:"Cut",removalMode:"Removal",rotateMode:"Rotate",drawText:"Text"};return i[e]?i[e]:this[e]?this[e]._shape:e},_finishLayer(e){e.pm&&(e.pm.setOptions(this.options),e.pm._shape=this._shape,e.pm._map=this._map),this._addDrawnLayerProp(e)},_addDrawnLayerProp(e){e._drawnByGeoman=!0},_setPane(e,i){i==="layerPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.layerPane||"overlayPane":i==="vertexPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.vertexPane||"markerPane":i==="markerPane"&&(e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.markerPane||"markerPane")},_isFirstLayer(){return(this._map||this._layer._map).pm.getGeomanLayers().length===0}}),At=Ia;At.Marker=At.extend({initialize(e){this._map=e,this._shape="Marker",this.toolbarButtonName="drawMarker"},enable(e){L.Util.setOptions(this,e),this._enabled=!0,this._map.getContainer().classList.add("geoman-draw-cursor"),this._map.on("click",this._createMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._hintMarker=L.marker(this._map.getCenter(),this.options.markerStyle),this._setPane(this._hintMarker,"markerPane"),this._hintMarker._pmTempLayer=!0,this._hintMarker.addTo(this._map),this.options.tooltips&&this._hintMarker.bindTooltip(pt("tooltips.placeMarker"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._layer=this._hintMarker,this._map.on("mousemove",this._syncHintMarker,this),this.options.markerEditable&&this._map.eachLayer(i=>{this.isRelevantMarker(i)&&i.pm.enable()}),this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("click",this._createMarker,this),this._hintMarker.remove(),this._map.off("mousemove",this._syncHintMarker,this),this._map.eachLayer(e=>{this.isRelevantMarker(e)&&e.pm.disable()}),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},isRelevantMarker(e){return e instanceof L.Marker&&e.pm&&!e._pmTempLayer&&!e.pm._initTextMarker},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let i=e;i.target=this._hintMarker,this._handleSnapping(i)}this._fireChange(this._hintMarker.getLatLng(),"Draw")},_createMarker(e){if(!e.latlng||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=new L.Marker(i,this.options.markerStyle);this._setPane(r,"markerPane"),this._finishLayer(r),r.pm||(r.options.draggable=!1),r.addTo(this._map.pm._getContainingLayer()),r.pm&&this.options.markerEditable?r.pm.enable():r.dragging&&r.dragging.disable(),this._fireCreate(r),this._cleanupSnapping(),this.options.continueDrawing||this.disable()},setStyle(){this.options.markerStyle?.icon&&this._hintMarker?.setIcon(this.options.markerStyle.icon)}});var Zt=63710088e-1,Nn={centimeters:Zt*100,centimetres:Zt*100,degrees:Zt/111325,feet:Zt*3.28084,inches:Zt*39.37,kilometers:Zt/1e3,kilometres:Zt/1e3,meters:Zt,metres:Zt,miles:Zt/1609.344,millimeters:Zt*1e3,millimetres:Zt*1e3,nauticalmiles:Zt/1852,radians:1,yards:Zt*1.0936},za={centimeters:100,centimetres:100,degrees:1/111325,feet:3.28084,inches:39.37,kilometers:1/1e3,kilometres:1/1e3,meters:1,metres:1,miles:1/1609.344,millimeters:1e3,millimetres:1e3,nauticalmiles:1/1852,radians:1/Zt,yards:1.0936133};function ke(e,i,r){r===void 0&&(r={});var a={type:"Feature"};return(r.id===0||r.id)&&(a.id=r.id),r.bbox&&(a.bbox=r.bbox),a.properties=i||{},a.geometry=e,a}function te(e,i,r){if(r===void 0&&(r={}),!e)throw new Error("coordinates is required");if(!Array.isArray(e))throw new Error("coordinates must be an Array");if(e.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!Vi(e[0])||!Vi(e[1]))throw new Error("coordinates must contain numbers");var a={type:"Point",coordinates:e};return ke(a,i,r)}function Li(e,i,r){if(r===void 0&&(r={}),e.length<2)throw new Error("coordinates must be an array of two or more positions");var a={type:"LineString",coordinates:e};return ke(a,i,r)}function ee(e,i){i===void 0&&(i={});var r={type:"FeatureCollection"};return i.id&&(r.id=i.id),i.bbox&&(r.bbox=i.bbox),r.features=e,r}function ie(e,i){i===void 0&&(i="kilometers");var r=Nn[i];if(!r)throw new Error(i+" units is invalid");return e*r}function Ir(e,i){i===void 0&&(i="kilometers");var r=Nn[i];if(!r)throw new Error(i+" units is invalid");return e/r}function Ui(e){var i=e%(2*Math.PI);return i*180/Math.PI}function pe(e){var i=e%360;return i*Math.PI/180}function Vi(e){return!isNaN(e)&&e!==null&&!Array.isArray(e)}function ii(e){var i,r,a={type:"FeatureCollection",features:[]};if(e.type==="Feature"?r=e.geometry:r=e,r.type==="LineString")i=[r.coordinates];else if(r.type==="MultiLineString")i=r.coordinates;else if(r.type==="MultiPolygon")i=[].concat.apply([],r.coordinates);else if(r.type==="Polygon")i=r.coordinates;else throw new Error("Input must be a LineString, MultiLineString, Polygon, or MultiPolygon Feature or Geometry");return i.forEach(function(s){i.forEach(function(l){for(var h=0;h<s.length-1;h++)for(var d=h;d<l.length-1;d++)if(!(s===l&&(Math.abs(h-d)===1||h===0&&d===s.length-2&&s[h][0]===s[s.length-1][0]&&s[h][1]===s[s.length-1][1]))){var p=Na(s[h][0],s[h][1],s[h+1][0],s[h+1][1],l[d][0],l[d][1],l[d+1][0],l[d+1][1]);p&&a.features.push(te([p[0],p[1]]))}})}),a}function Na(e,i,r,a,s,l,h,d){var p,_,k,w,z,A={x:null,y:null,onLine1:!1,onLine2:!1};return p=(d-l)*(r-e)-(h-s)*(a-i),p===0?A.x!==null&&A.y!==null?A:!1:(_=i-l,k=e-s,w=(h-s)*_-(d-l)*k,z=(r-e)*_-(a-i)*k,_=w/p,k=z/p,A.x=e+_*(r-e),A.y=i+_*(a-i),_>=0&&_<=1&&(A.onLine1=!0),k>=0&&k<=1&&(A.onLine2=!0),A.onLine1&&A.onLine2?[A.x,A.y]:!1)}At.Line=At.extend({initialize(e){this._map=e,this._shape="Line",this.toolbarButtonName="drawPolyline",this._doesSelfIntersect=!1},enable(e){L.Util.setOptions(this,e),this._enabled=!0,this._markers=[],this._layerGroup=new L.FeatureGroup,this._layerGroup._pmTempLayer=!0,this._layerGroup.addTo(this._map),this._layer=L.polyline([],{...this.options.templineStyle,pmIgnore:!1}),this._setPane(this._layer,"layerPane"),this._layer._pmTempLayer=!0,this._layerGroup.addLayer(this._layer),this._hintline=L.polyline([],this.options.hintlineStyle),this._setPane(this._hintline,"layerPane"),this._hintline._pmTempLayer=!0,this._layerGroup.addLayer(this._hintline),this._hintMarker=L.marker(this._map.getCenter(),{interactive:!1,zIndexOffset:100,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._hintMarker),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(pt("tooltips.firstVertex"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._map.getContainer().classList.add("geoman-draw-cursor"),this._map.on("click",this._createVertex,this),this.options.finishOn&&this.options.finishOn!=="snap"&&this._map.on(this.options.finishOn,this._finishShape,this),this.options.finishOn==="dblclick"&&(this.tempMapDoubleClickZoomState=this._map.doubleClickZoom._enabled,this.tempMapDoubleClickZoomState&&this._map.doubleClickZoom.disable()),this._map.on("mousemove",this._syncHintMarker,this),this._hintMarker.on("move",this._syncHintLine,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._otherSnapLayers=[],this.isRed=!1,this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("click",this._createVertex,this),this._map.off("mousemove",this._syncHintMarker,this),this.options.finishOn&&this.options.finishOn!=="snap"&&this._map.off(this.options.finishOn,this._finishShape,this),this.tempMapDoubleClickZoomState&&this._map.doubleClickZoom.enable(),this._map.removeLayer(this._layerGroup),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_syncHintLine(){let e=this._layer.getLatLngs();if(e.length>0){let i=e[e.length-1];this._hintline.setLatLngs([i,this._hintMarker.getLatLng()])}},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let r=e;r.target=this._hintMarker,this._handleSnapping(r)}this.options.allowSelfIntersection||this._handleSelfIntersection(!0,this._hintMarker.getLatLng());let i=this._layer._defaultShape().slice();i.push(this._hintMarker.getLatLng()),this._change(i)},hasSelfIntersection(){return ii(this._layer.toGeoJSON(15)).features.length>0},_handleSelfIntersection(e,i){let r=L.polyline(this._layer.getLatLngs());e&&(i||(i=this._hintMarker.getLatLng()),r.addLatLng(i));let a=ii(r.toGeoJSON(15));this._doesSelfIntersect=a.features.length>0,this._doesSelfIntersect?this.isRed||(this.isRed=!0,this._hintline.setStyle({color:"#f00000ff"}),this._fireIntersect(a,this._map,"Draw")):this._hintline.isEmpty()||(this.isRed=!1,this._hintline.setStyle(this.options.hintlineStyle))},_createVertex(e){if(!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!0,e.latlng),this._doesSelfIntersect))return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=this._layer.getLatLngs(),a=r[r.length-1];if(i.equals(r[0])||r.length>0&&i.equals(a)){this._finishShape();return}this._layer._latlngInfo=this._layer._latlngInfo||[],this._layer._latlngInfo.push({latlng:i,snapInfo:this._hintMarker._snapInfo}),this._layer.addLatLng(i);let s=this._createMarker(i);this._setTooltipText(),this._setHintLineAfterNewVertex(i),this._fireVertexAdded(s,void 0,i,"Draw"),this._change(this._layer.getLatLngs()),this.options.finishOn==="snap"&&this._hintMarker._snapped&&this._finishShape(e)},_setHintLineAfterNewVertex(e){this._hintline.setLatLngs([e,e])},_removeLastVertex(){let e=this._markers;if(e.length<=1){this.disable();return}let i=this._layer.getLatLngs(),r=e[e.length-1],{indexPath:a}=L.PM.Utils.findDeepMarkerIndex(e,r);e.pop(),this._layerGroup.removeLayer(r);let s=e[e.length-1],l=i.indexOf(s.getLatLng());i=i.slice(0,l+1),this._layer.setLatLngs(i),this._layer._latlngInfo.pop(),this._syncHintLine(),this._setTooltipText(),this._fireVertexRemoved(r,a,"Draw"),this._change(this._layer.getLatLngs())},_finishShape(){if(!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!1),this._doesSelfIntersect)||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;let e=this._layer.getLatLngs();if(e.length<=1)return;let i=L.polyline(e,this.options.pathOptions);this._setPane(i,"layerPane"),this._finishLayer(i),i.addTo(this._map.pm._getContainingLayer()),this._fireCreate(i),this.options.snappable&&this._cleanupSnapping(),this.disable(),this.options.continueDrawing&&this.enable()},_createMarker(e){let i=new L.Marker(e,{draggable:!1,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._pmTempLayer=!0,this._layerGroup.addLayer(i),this._markers.push(i),i.on("click",this._finishShape,this),i},_setTooltipText(){let{length:e}=this._layer.getLatLngs().flat(),i="";e<=1?i=pt("tooltips.continueLine"):i=pt("tooltips.finishLine"),this._hintMarker.setTooltipContent(i)},_change(e){this._fireChange(e,"Draw")},setStyle(){this._layer?.setStyle(this.options.templineStyle),this._hintline?.setStyle(this.options.hintlineStyle)}}),At.Polygon=At.Line.extend({initialize(e){this._map=e,this._shape="Polygon",this.toolbarButtonName="drawPolygon"},enable(e){L.PM.Draw.Line.prototype.enable.call(this,e),this._layer.pm._shape="Polygon"},_createMarker(e){let i=new L.Marker(e,{draggable:!1,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._pmTempLayer=!0,this._layerGroup.addLayer(i),this._markers.push(i),this._layer.getLatLngs().flat().length===1?(i.on("click",this._finishShape,this),this._tempSnapLayerIndex=this._otherSnapLayers.push(i)-1,this.options.snappable&&this._cleanupSnapping()):i.on("click",()=>1),i},_setTooltipText(){let{length:e}=this._layer.getLatLngs().flat(),i="";e<=2?i=pt("tooltips.continueLine"):i=pt("tooltips.finishPoly"),this._hintMarker.setTooltipContent(i)},_finishShape(){if(!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!0,this._layer.getLatLngs()[0]),this._doesSelfIntersect)||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;let e=this._layer.getLatLngs();if(e.length<=2)return;let i=L.polygon(e,this.options.pathOptions);this._setPane(i,"layerPane"),this._finishLayer(i),i.addTo(this._map.pm._getContainingLayer()),this._fireCreate(i),this._cleanupSnapping(),this._otherSnapLayers.splice(this._tempSnapLayerIndex,1),delete this._tempSnapLayerIndex,this.disable(),this.options.continueDrawing&&this.enable()}}),At.Rectangle=At.extend({initialize(e){this._map=e,this._shape="Rectangle",this.toolbarButtonName="drawRectangle"},enable(e){if(L.Util.setOptions(this,e),this._enabled=!0,this._layerGroup=new L.FeatureGroup,this._layerGroup._pmTempLayer=!0,this._layerGroup.addTo(this._map),this._layer=L.rectangle([[0,0],[0,0]],this.options.pathOptions),this._setPane(this._layer,"layerPane"),this._layer._pmTempLayer=!0,this._startMarker=L.marker(this._map.getCenter(),{icon:L.divIcon({className:"marker-icon rect-start-marker"}),draggable:!1,zIndexOffset:-100,opacity:this.options.cursorMarker?1:0}),this._setPane(this._startMarker,"vertexPane"),this._startMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._startMarker),this._hintMarker=L.marker(this._map.getCenter(),{zIndexOffset:150,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._hintMarker),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(pt("tooltips.firstVertex"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this.options.cursorMarker){this._styleMarkers=[];for(let i=0;i<2;i+=1){let r=L.marker(this._map.getCenter(),{icon:L.divIcon({className:"marker-icon rect-style-marker"}),draggable:!1,zIndexOffset:100});this._setPane(r,"vertexPane"),r._pmTempLayer=!0,this._layerGroup.addLayer(r),this._styleMarkers.push(r)}}this._map.getContainer().classList.add("geoman-draw-cursor"),this._map.on("click",this._placeStartingMarkers,this),this._map.on("mousemove",this._syncHintMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._otherSnapLayers=[],this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("click",this._finishShape,this),this._map.off("click",this._placeStartingMarkers,this),this._map.off("mousemove",this._syncHintMarker,this),this._map.removeLayer(this._layerGroup),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_placeStartingMarkers(e){this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng();L.DomUtil.addClass(this._startMarker._icon,"visible"),this._startMarker.setLatLng(i),this.options.cursorMarker&&this._styleMarkers&&this._styleMarkers.forEach(r=>{L.DomUtil.addClass(r._icon,"visible"),r.setLatLng(i)}),this._map.off("click",this._placeStartingMarkers,this),this._map.on("click",this._finishShape,this),this._hintMarker.setTooltipContent(pt("tooltips.finishRect")),this._setRectangleOrigin()},_setRectangleOrigin(){let e=this._startMarker.getLatLng();e&&(this._layerGroup.addLayer(this._layer),this._layer.setLatLngs([e,e]),this._hintMarker.on("move",this._syncRectangleSize,this))},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let r=e;r.target=this._hintMarker,this._handleSnapping(r)}let i=this._layerGroup&&this._layerGroup.hasLayer(this._layer)?this._layer.getLatLngs():[this._hintMarker.getLatLng()];this._fireChange(i,"Draw")},_syncRectangleSize(){let e=de(this._startMarker.getLatLng(),this._map),i=de(this._hintMarker.getLatLng(),this._map),r=L.PM.Utils._getRotatedRectangle(e,i,this.options.rectangleAngle||0,this._map);if(this._layer.setLatLngs(r),this.options.cursorMarker&&this._styleMarkers){let a=[];r.forEach(s=>{!s.equals(e,1e-8)&&!s.equals(i,1e-8)&&a.push(s)}),a.forEach((s,l)=>{try{this._styleMarkers[l].setLatLng(s)}catch{}})}},_findCorners(){let e=this._layer.getLatLngs()[0];return L.PM.Utils._getRotatedRectangle(e[0],e[2],this.options.rectangleAngle||0,this._map)},_finishShape(e){this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=this._startMarker.getLatLng();if(this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer()||r.equals(i))return;let a=L.rectangle([r,i],this.options.pathOptions);if(this.options.rectangleAngle){let s=L.PM.Utils._getRotatedRectangle(r,i,this.options.rectangleAngle||0,this._map);a.setLatLngs(s),a.pm&&a.pm._setAngle(this.options.rectangleAngle||0)}this._setPane(a,"layerPane"),this._finishLayer(a),a.addTo(this._map.pm._getContainingLayer()),this._fireCreate(a),this.disable(),this.options.continueDrawing&&this.enable()},setStyle(){this._layer?.setStyle(this.options.pathOptions)}}),At.CircleMarker=At.extend({initialize(e){this._map=e,this._shape="CircleMarker",this.toolbarButtonName="drawCircleMarker",this._layerIsDragging=!1,this._BaseCircleClass=L.CircleMarker,this._minRadiusOption="minRadiusCircleMarker",this._maxRadiusOption="maxRadiusCircleMarker",this._editableOption="resizeableCircleMarker",this._defaultRadius=10},enable(e){if(L.Util.setOptions(this,e),this.options.editable&&(this.options.resizeableCircleMarker=this.options.editable,delete this.options.editable),this._enabled=!0,this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._map.getContainer().classList.add("geoman-draw-cursor"),this.options[this._editableOption]){let i={};L.extend(i,this.options.templineStyle),i.radius=0,this._layerGroup=new L.FeatureGroup,this._layerGroup._pmTempLayer=!0,this._layerGroup.addTo(this._map),this._layer=new this._BaseCircleClass(this._map.getCenter(),i),this._setPane(this._layer,"layerPane"),this._layer._pmTempLayer=!0,this._centerMarker=L.marker(this._map.getCenter(),{icon:L.divIcon({className:"marker-icon"}),draggable:!1,zIndexOffset:100}),this._setPane(this._centerMarker,"vertexPane"),this._centerMarker._pmTempLayer=!0,this._hintMarker=L.marker(this._map.getCenter(),{zIndexOffset:110,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._layerGroup.addLayer(this._hintMarker),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(pt("tooltips.startCircle"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._hintline=L.polyline([],this.options.hintlineStyle),this._setPane(this._hintline,"layerPane"),this._hintline._pmTempLayer=!0,this._layerGroup.addLayer(this._hintline),this._map.on("click",this._placeCenterMarker,this)}else this._map.on("click",this._createMarker,this),this._hintMarker=new this._BaseCircleClass(this._map.getCenter(),{radius:this._defaultRadius,...this.options.templineStyle}),this._setPane(this._hintMarker,"layerPane"),this._hintMarker._pmTempLayer=!0,this._hintMarker.addTo(this._map),this._layer=this._hintMarker,this.options.tooltips&&this._hintMarker.bindTooltip(pt("tooltips.placeCircleMarker"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip();this._map.on("mousemove",this._syncHintMarker,this),this._extendingEnable(),this._otherSnapLayers=[],this._fireDrawStart(),this._setGlobalDrawMode()},_extendingEnable(){!this.options[this._editableOption]&&this.options.markerEditable&&this._map.eachLayer(e=>{this.isRelevantMarker(e)&&e.pm.enable()}),this._layer.bringToBack()},disable(){this._enabled&&(this._enabled=!1,this._map.getContainer().classList.remove("geoman-draw-cursor"),this.options[this._editableOption]?(this._map.off("click",this._finishShape,this),this._map.off("click",this._placeCenterMarker,this),this._map.removeLayer(this._layerGroup)):(this._map.off("click",this._createMarker,this),this._extendingDisable(),this._hintMarker.remove()),this._map.off("mousemove",this._syncHintMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},_extendingDisable(){this._map.eachLayer(e=>{this.isRelevantMarker(e)&&e.pm.disable()})},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_placeCenterMarker(e){this._layerGroup.addLayer(this._layer),this._layerGroup.addLayer(this._centerMarker),this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng();this._layerGroup.addLayer(this._layer),this._centerMarker.setLatLng(i),this._map.off("click",this._placeCenterMarker,this),this._map.on("click",this._finishShape,this),this._placeCircleCenter()},_placeCircleCenter(){let e=this._centerMarker.getLatLng();e&&(this._layer.setLatLng(e),this._hintMarker.on("move",this._syncHintLine,this),this._hintMarker.on("move",this._syncCircleRadius,this),this._hintMarker.setTooltipContent(pt("tooltips.finishCircle")),this._fireCenterPlaced(),this._fireChange(this._layer.getLatLng(),"Draw"))},_syncHintLine(){let e=this._centerMarker.getLatLng(),i=this._getNewDestinationOfHintMarker();this._hintline.setLatLngs([e,i])},_syncCircleRadius(){let e=this._centerMarker.getLatLng(),i=this._hintMarker.getLatLng(),r=this._distanceCalculation(e,i);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._layer.setRadius(this.options[this._minRadiusOption]):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]?this._layer.setRadius(this.options[this._maxRadiusOption]):this._layer.setRadius(r)},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this._hintMarker.setLatLng(this._getNewDestinationOfHintMarker()),this.options.snappable){let r=e;r.target=this._hintMarker,this._handleSnapping(r)}this._handleHintMarkerSnapping();let i=this._layerGroup&&this._layerGroup.hasLayer(this._centerMarker)?this._centerMarker.getLatLng():this._hintMarker.getLatLng();this._fireChange(i,"Draw")},isRelevantMarker(e){return e instanceof L.CircleMarker&&!(e instanceof L.Circle)&&e.pm&&!e._pmTempLayer},_createMarker(e){if(this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer()||!e.latlng||this._layerIsDragging)return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng(),r=new this._BaseCircleClass(i,{radius:this._defaultRadius,...this.options.pathOptions});this._setPane(r,"layerPane"),this._finishLayer(r),r.addTo(this._map.pm._getContainingLayer()),this._extendingCreateMarker(r),this._fireCreate(r),this._cleanupSnapping(),this.options.continueDrawing||this.disable()},_extendingCreateMarker(e){e.pm&&this.options.markerEditable&&e.pm.enable()},_finishShape(e){if(this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._centerMarker.getLatLng(),r=this._defaultRadius;if(this.options[this._editableOption]){let l=this._hintMarker.getLatLng();r=this._distanceCalculation(i,l),this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?r=this.options[this._minRadiusOption]:this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&(r=this.options[this._maxRadiusOption])}let a={...this.options.pathOptions,radius:r},s=new this._BaseCircleClass(i,a);this._setPane(s,"layerPane"),this._finishLayer(s),s.addTo(this._map.pm._getContainingLayer()),s.pm&&s.pm._updateHiddenPolyCircle(),this._fireCreate(s),this.disable(),this.options.continueDrawing&&this.enable()},_getNewDestinationOfHintMarker(){let e=this._hintMarker.getLatLng();if(this.options[this._editableOption]){if(!this._layerGroup.hasLayer(this._centerMarker))return e;let i=this._centerMarker.getLatLng(),r=this._distanceCalculation(i,e);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?e=ji(this._map,i,e,this._getMinDistanceInMeter()):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&(e=ji(this._map,i,e,this._getMaxDistanceInMeter()))}return e},_getMinDistanceInMeter(){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._minRadiusOption],this._map,this._centerMarker.getLatLng())},_getMaxDistanceInMeter(){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._maxRadiusOption],this._map,this._centerMarker.getLatLng())},_handleHintMarkerSnapping(){if(this.options[this._editableOption]){if(this._hintMarker._snapped){let e=this._centerMarker.getLatLng(),i=this._hintMarker.getLatLng(),r=this._distanceCalculation(e,i);this._layerGroup.hasLayer(this._centerMarker)&&(this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._hintMarker.setLatLng(this._hintMarker._orgLatLng):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&this._hintMarker.setLatLng(this._hintMarker._orgLatLng))}this._hintMarker.setLatLng(this._getNewDestinationOfHintMarker())}},setStyle(){let e={};L.extend(e,this.options.templineStyle),this.options[this._editableOption]&&(e.radius=0),this._layer?.setStyle(e),this._hintline?.setStyle(this.options.hintlineStyle)},_distanceCalculation(e,i){return this._map.project(e).distanceTo(this._map.project(i))}}),At.Circle=At.CircleMarker.extend({initialize(e){this._map=e,this._shape="Circle",this.toolbarButtonName="drawCircle",this._BaseCircleClass=L.Circle,this._minRadiusOption="minRadiusCircle",this._maxRadiusOption="maxRadiusCircle",this._editableOption="resizableCircle",this._defaultRadius=100},_extendingEnable(){},_extendingDisable(){},_extendingCreateMarker(){},isRelevantMarker(){},_getMinDistanceInMeter(){return this.options[this._minRadiusOption]},_getMaxDistanceInMeter(){return this.options[this._maxRadiusOption]},_distanceCalculation(e,i){return this._map.distance(e,i)}});function ve(e){if(!e)throw new Error("coord is required");if(!Array.isArray(e)){if(e.type==="Feature"&&e.geometry!==null&&e.geometry.type==="Point")return e.geometry.coordinates;if(e.type==="Point")return e.coordinates}if(Array.isArray(e)&&e.length>=2&&!Array.isArray(e[0])&&!Array.isArray(e[1]))return e;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function fe(e){if(Array.isArray(e))return e;if(e.type==="Feature"){if(e.geometry!==null)return e.geometry.coordinates}else if(e.coordinates)return e.coordinates;throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array")}function Hi(e){return e.type==="Feature"?e.geometry:e}function ne(e,i){return e.type==="FeatureCollection"?"FeatureCollection":e.type==="GeometryCollection"?"GeometryCollection":e.type==="Feature"&&e.geometry!==null?e.geometry.type:e.type}function Ae(e,i,r){if(e!==null)for(var a,s,l,h,d,p,_,k=0,w=0,z,A=e.type,V=A==="FeatureCollection",q=A==="Feature",X=V?e.features.length:1,mt=0;mt<X;mt++){_=V?e.features[mt].geometry:q?e.geometry:e,z=_?_.type==="GeometryCollection":!1,d=z?_.geometries.length:1;for(var x=0;x<d;x++){var E=0,D=0;if(h=z?_.geometries[x]:_,h!==null){p=h.coordinates;var G=h.type;switch(k=r&&(G==="Polygon"||G==="MultiPolygon")?1:0,G){case null:break;case"Point":if(i(p,w,mt,E,D)===!1)return!1;w++,E++;break;case"LineString":case"MultiPoint":for(a=0;a<p.length;a++){if(i(p[a],w,mt,E,D)===!1)return!1;w++,G==="MultiPoint"&&E++}G==="LineString"&&E++;break;case"Polygon":case"MultiLineString":for(a=0;a<p.length;a++){for(s=0;s<p[a].length-k;s++){if(i(p[a][s],w,mt,E,D)===!1)return!1;w++}G==="MultiLineString"&&E++,G==="Polygon"&&D++}G==="Polygon"&&E++;break;case"MultiPolygon":for(a=0;a<p.length;a++){for(D=0,s=0;s<p[a].length;s++){for(l=0;l<p[a][s].length-k;l++){if(i(p[a][s][l],w,mt,E,D)===!1)return!1;w++}D++}E++}break;case"GeometryCollection":for(a=0;a<h.geometries.length;a++)if(Ae(h.geometries[a],i,r)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function bi(e,i){if(e.type==="Feature")i(e,0);else if(e.type==="FeatureCollection")for(var r=0;r<e.features.length&&i(e.features[r],r)!==!1;r++);}function Me(e,i,r){var a=r;return bi(e,function(s,l){l===0&&r===void 0?a=s:a=i(a,s,l)}),a}function Ga(e,i){var r,a,s,l,h,d,p,_,k,w,z=0,A=e.type==="FeatureCollection",V=e.type==="Feature",q=A?e.features.length:1;for(r=0;r<q;r++){for(d=A?e.features[r].geometry:V?e.geometry:e,_=A?e.features[r].properties:V?e.properties:{},k=A?e.features[r].bbox:V?e.bbox:void 0,w=A?e.features[r].id:V?e.id:void 0,p=d?d.type==="GeometryCollection":!1,h=p?d.geometries.length:1,s=0;s<h;s++){if(l=p?d.geometries[s]:d,l===null){if(i(null,z,_,k,w)===!1)return!1;continue}switch(l.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":{if(i(l,z,_,k,w)===!1)return!1;break}case"GeometryCollection":{for(a=0;a<l.geometries.length;a++)if(i(l.geometries[a],z,_,k,w)===!1)return!1;break}default:throw new Error("Unknown Geometry Type")}}z++}}function Oe(e,i){Ga(e,function(r,a,s,l,h){var d=r===null?null:r.type;switch(d){case null:case"Point":case"LineString":case"Polygon":return i(ke(r,s,{bbox:l,id:h}),a,0)===!1?!1:void 0}var p;switch(d){case"MultiPoint":p="Point";break;case"MultiLineString":p="LineString";break;case"MultiPolygon":p="Polygon";break}for(var _=0;_<r.coordinates.length;_++){var k=r.coordinates[_],w={type:p,coordinates:k};if(i(ke(w,s),a,_)===!1)return!1}})}function Za(e){if(!e)throw new Error("geojson is required");var i=[];return Oe(e,function(r){Ci(r,i)}),ee(i)}function Ci(e,i){var r=[],a=e.geometry;if(a!==null){switch(a.type){case"Polygon":r=fe(a);break;case"LineString":r=[fe(a)]}r.forEach(function(s){var l=zr(s,e.properties);l.forEach(function(h){h.id=i.length,i.push(h)})})}}function zr(e,i){var r=[];return e.reduce(function(a,s){var l=Li([a,s],i);return l.bbox=Ki(a,s),r.push(l),s}),r}function Ki(e,i){var r=e[0],a=e[1],s=i[0],l=i[1],h=r<s?r:s,d=a<l?a:l,p=r>s?r:s,_=a>l?a:l;return[h,d,p,_]}var Gn=Za,Fe=rt(ht(),1);function qi(e,i){var r={},a=[];if(e.type==="LineString"&&(e=ke(e)),i.type==="LineString"&&(i=ke(i)),e.type==="Feature"&&i.type==="Feature"&&e.geometry!==null&&i.geometry!==null&&e.geometry.type==="LineString"&&i.geometry.type==="LineString"&&e.geometry.coordinates.length===2&&i.geometry.coordinates.length===2){var s=Nr(e,i);return s&&a.push(s),ee(a)}var l=(0,Fe.default)();return l.load(Gn(i)),bi(Gn(e),function(h){bi(l.search(h),function(d){var p=Nr(h,d);if(p){var _=fe(p).join(",");r[_]||(r[_]=!0,a.push(p))}})}),ee(a)}function Nr(e,i){var r=fe(e),a=fe(i);if(r.length!==2)throw new Error("<intersects> line1 must only contain 2 coordinates");if(a.length!==2)throw new Error("<intersects> line2 must only contain 2 coordinates");var s=r[0][0],l=r[0][1],h=r[1][0],d=r[1][1],p=a[0][0],_=a[0][1],k=a[1][0],w=a[1][1],z=(w-_)*(h-s)-(k-p)*(d-l),A=(k-p)*(l-_)-(w-_)*(s-p),V=(h-s)*(l-_)-(d-l)*(s-p);if(z===0)return null;var q=A/z,X=V/z;if(q>=0&&q<=1&&X>=0&&X<=1){var mt=s+q*(h-s),x=l+q*(d-l);return te([mt,x])}return null}var we=qi,Gr=rt(ht(),1);function Ee(e,i,r){r===void 0&&(r={});var a=ve(e),s=ve(i),l=pe(s[1]-a[1]),h=pe(s[0]-a[0]),d=pe(a[1]),p=pe(s[1]),_=Math.pow(Math.sin(l/2),2)+Math.pow(Math.sin(h/2),2)*Math.cos(d)*Math.cos(p);return ie(2*Math.atan2(Math.sqrt(_),Math.sqrt(1-_)),r.units)}var Ye=Ee;function ni(e){var i=e[0],r=e[1],a=e[2],s=e[3],l=Ye(e.slice(0,2),[a,r]),h=Ye(e.slice(0,2),[i,s]);if(l>=h){var d=(r+s)/2;return[i,d-(a-i)/2,a,d+(a-i)/2]}else{var p=(i+a)/2;return[p-(s-r)/2,r,p+(s-r)/2,s]}}var ja=ni;function _e(e){var i=[1/0,1/0,-1/0,-1/0];return Ae(e,function(r){i[0]>r[0]&&(i[0]=r[0]),i[1]>r[1]&&(i[1]=r[1]),i[2]<r[0]&&(i[2]=r[0]),i[3]<r[1]&&(i[3]=r[1])}),i}_e.default=_e;var Re=_e;function Zr(e,i){i===void 0&&(i={});var r=i.precision,a=i.coordinates,s=i.mutate;if(r=r==null||isNaN(r)?6:r,a=a==null||isNaN(a)?3:a,!e)throw new Error("<geojson> is required");if(typeof r!="number")throw new Error("<precision> must be a number");if(typeof a!="number")throw new Error("<coordinates> must be a number");(s===!1||s===void 0)&&(e=JSON.parse(JSON.stringify(e)));var l=Math.pow(10,r);return Ae(e,function(h){Zn(h,l,a)}),e}function Zn(e,i,r){e.length>r&&e.splice(r,e.length);for(var a=0;a<e.length;a++)e[a]=Math.round(e[a]*i)/i;return e}var Wi=Zr;function Yi(e,i,r){if(r===void 0&&(r={}),r.final===!0)return Ji(e,i);var a=ve(e),s=ve(i),l=pe(a[0]),h=pe(s[0]),d=pe(a[1]),p=pe(s[1]),_=Math.sin(h-l)*Math.cos(p),k=Math.cos(d)*Math.sin(p)-Math.sin(d)*Math.cos(p)*Math.cos(h-l);return Ui(Math.atan2(_,k))}function Ji(e,i){var r=Yi(i,e);return r=(r+180)%360,r}function Je(e,i,r,a){a===void 0&&(a={});var s=ve(e),l=pe(s[0]),h=pe(s[1]),d=pe(r),p=Ir(i,a.units),_=Math.asin(Math.sin(h)*Math.cos(p)+Math.cos(h)*Math.sin(p)*Math.cos(d)),k=l+Math.atan2(Math.sin(d)*Math.sin(p)*Math.cos(h),Math.cos(p)-Math.sin(h)*Math.sin(_)),w=Ui(k),z=Ui(_);return te([w,z],a.properties)}function Xi(e,i,r){r===void 0&&(r={});var a=te([1/0,1/0],{dist:1/0}),s=0;return Oe(e,function(l){for(var h=fe(l),d=0;d<h.length-1;d++){var p=te(h[d]);p.properties.dist=Ye(i,p,r);var _=te(h[d+1]);_.properties.dist=Ye(i,_,r);var k=Ye(p,_,r),w=Math.max(p.properties.dist,_.properties.dist),z=Yi(p,_),A=Je(i,w,z+90,r),V=Je(i,w,z-90,r),q=we(Li([A.geometry.coordinates,V.geometry.coordinates]),Li([p.geometry.coordinates,_.geometry.coordinates])),X=null;q.features.length>0&&(X=q.features[0],X.properties.dist=Ye(i,X,r),X.properties.location=s+Ye(p,X,r)),p.properties.dist<a.properties.dist&&(a=p,a.properties.index=d,a.properties.location=s),_.properties.dist<a.properties.dist&&(a=_,a.properties.index=d+1,a.properties.location=s+k),X&&X.properties.dist<a.properties.dist&&(a=X,a.properties.index=d),s+=k}}),a}var jn=Xi;function jr(e,i){if(!e)throw new Error("line is required");if(!i)throw new Error("splitter is required");var r=ne(e),a=ne(i);if(r!=="LineString")throw new Error("line must be LineString");if(a==="FeatureCollection")throw new Error("splitter cannot be a FeatureCollection");if(a==="GeometryCollection")throw new Error("splitter cannot be a GeometryCollection");var s=Wi(i,{precision:7});switch(a){case"Point":return ri(e,s);case"MultiPoint":return Ur(e,s);case"LineString":case"MultiLineString":case"Polygon":case"MultiPolygon":return Ur(e,we(e,s))}}function Ur(e,i){var r=[],a=(0,Gr.default)();return Oe(i,function(s){if(r.forEach(function(d,p){d.id=p}),!r.length)r=ri(e,s).features,r.forEach(function(d){d.bbox||(d.bbox=ja(Re(d)))}),a.load(ee(r));else{var l=a.search(s);if(l.features.length){var h=Vr(s,l);r=r.filter(function(d){return d.id!==h.id}),a.remove(h),bi(ri(h,s),function(d){r.push(d),a.insert(d)})}}}),ee(r)}function ri(e,i){var r=[],a=fe(e)[0],s=fe(e)[e.geometry.coordinates.length-1];if($i(a,ve(i))||$i(s,ve(i)))return ee([e]);var l=(0,Gr.default)(),h=Gn(e);l.load(h);var d=l.search(i);if(!d.features.length)return ee([e]);var p=Vr(i,d),_=[a],k=Me(h,function(w,z,A){var V=fe(z)[1],q=ve(i);return A===p.id?(w.push(q),r.push(Li(w)),$i(q,V)?[q]:[q,V]):(w.push(V),w)},_);return k.length>1&&r.push(Li(k)),ee(r)}function Vr(e,i){if(!i.features.length)throw new Error("lines must contain features");if(i.features.length===1)return i.features[0];var r,a=1/0;return bi(i,function(s){var l=jn(s,e),h=l.properties.dist;h<a&&(r=s,a=h)}),r}function $i(e,i){return e[0]===i[0]&&e[1]===i[1]}var Ua=jr;function xi(e,i,r){if(r===void 0&&(r={}),!e)throw new Error("point is required");if(!i)throw new Error("polygon is required");var a=ve(e),s=Hi(i),l=s.type,h=i.bbox,d=s.coordinates;if(h&&Le(a,h)===!1)return!1;l==="Polygon"&&(d=[d]);for(var p=!1,_=0;_<d.length&&!p;_++)if(Hr(a,d[_][0],r.ignoreBoundary)){for(var k=!1,w=1;w<d[_].length&&!k;)Hr(a,d[_][w],!r.ignoreBoundary)&&(k=!0),w++;k||(p=!0)}return p}function Hr(e,i,r){var a=!1;i[0][0]===i[i.length-1][0]&&i[0][1]===i[i.length-1][1]&&(i=i.slice(0,i.length-1));for(var s=0,l=i.length-1;s<i.length;l=s++){var h=i[s][0],d=i[s][1],p=i[l][0],_=i[l][1],k=e[1]*(h-p)+d*(p-e[0])+_*(e[0]-h)===0&&(h-e[0])*(p-e[0])<=0&&(d-e[1])*(_-e[1])<=0;if(k)return!r;var w=d>e[1]!=_>e[1]&&e[0]<(p-h)*(e[1]-d)/(_-d)+h;w&&(a=!a)}return a}function Le(e,i){return i[0]<=e[0]&&i[1]<=e[1]&&i[2]>=e[0]&&i[3]>=e[1]}function Qi(e,i,r){r===void 0&&(r={});for(var a=ve(e),s=fe(i),l=0;l<s.length-1;l++){var h=!1;if(r.ignoreEndVertices&&(l===0&&(h="start"),l===s.length-2&&(h="end"),l===0&&l+1===s.length-1&&(h="both")),Va(s[l],s[l+1],a,h,typeof r.epsilon>"u"?null:r.epsilon))return!0}return!1}function Va(e,i,r,a,s){var l=r[0],h=r[1],d=e[0],p=e[1],_=i[0],k=i[1],w=r[0]-d,z=r[1]-p,A=_-d,V=k-p,q=w*V-z*A;if(s!==null){if(Math.abs(q)>s)return!1}else if(q!==0)return!1;if(a){if(a==="start")return Math.abs(A)>=Math.abs(V)?A>0?d<l&&l<=_:_<=l&&l<d:V>0?p<h&&h<=k:k<=h&&h<p;if(a==="end")return Math.abs(A)>=Math.abs(V)?A>0?d<=l&&l<_:_<l&&l<=d:V>0?p<=h&&h<k:k<h&&h<=p;if(a==="both")return Math.abs(A)>=Math.abs(V)?A>0?d<l&&l<_:_<l&&l<d:V>0?p<h&&h<k:k<h&&h<p}else return Math.abs(A)>=Math.abs(V)?A>0?d<=l&&l<=_:_<=l&&l<=d:V>0?p<=h&&h<=k:k<=h&&h<=p;return!1}var Ie=Qi;function Ha(e,i){var r=Hi(e),a=Hi(i),s=r.type,l=a.type,h=r.coordinates,d=a.coordinates;switch(s){case"Point":switch(l){case"Point":return me(h,d);default:throw new Error("feature2 "+l+" geometry not supported")}case"MultiPoint":switch(l){case"Point":return Kr(r,a);case"MultiPoint":return Ka(r,a);default:throw new Error("feature2 "+l+" geometry not supported")}case"LineString":switch(l){case"Point":return Ie(a,r,{ignoreEndVertices:!0});case"LineString":return ai(r,a);case"MultiPoint":return ki(r,a);default:throw new Error("feature2 "+l+" geometry not supported")}case"Polygon":switch(l){case"Point":return xi(a,r,{ignoreBoundary:!0});case"LineString":return qr(r,a);case"Polygon":return Wr(r,a);case"MultiPoint":return qa(r,a);default:throw new Error("feature2 "+l+" geometry not supported")}default:throw new Error("feature1 "+s+" geometry not supported")}}function Kr(e,i){var r,a=!1;for(r=0;r<e.coordinates.length;r++)if(me(e.coordinates[r],i.coordinates)){a=!0;break}return a}function Ka(e,i){for(var r=0,a=i.coordinates;r<a.length;r++){for(var s=a[r],l=!1,h=0,d=e.coordinates;h<d.length;h++){var p=d[h];if(me(s,p)){l=!0;break}}if(!l)return!1}return!0}function ki(e,i){for(var r=!1,a=0,s=i.coordinates;a<s.length;a++){var l=s[a];if(Ie(l,e,{ignoreEndVertices:!0})&&(r=!0),!Ie(l,e))return!1}return!!r}function qa(e,i){for(var r=0,a=i.coordinates;r<a.length;r++){var s=a[r];if(!xi(s,e,{ignoreBoundary:!0}))return!1}return!0}function ai(e,i){for(var r=!1,a=0,s=i.coordinates;a<s.length;a++){var l=s[a];if(Ie({type:"Point",coordinates:l},e,{ignoreEndVertices:!0})&&(r=!0),!Ie({type:"Point",coordinates:l},e,{ignoreEndVertices:!1}))return!1}return r}function qr(e,i){var r=!1,a=0,s=Re(e),l=Re(i);if(!Yr(s,l))return!1;for(a;a<i.coordinates.length-1;a++){var h=Jr(i.coordinates[a],i.coordinates[a+1]);if(xi({type:"Point",coordinates:h},e,{ignoreBoundary:!0})){r=!0;break}}return r}function Wr(e,i){if(e.type==="Feature"&&e.geometry===null||i.type==="Feature"&&i.geometry===null)return!1;var r=Re(e),a=Re(i);if(!Yr(r,a))return!1;for(var s=Hi(i).coordinates,l=0,h=s;l<h.length;l++)for(var d=h[l],p=0,_=d;p<_.length;p++){var k=_[p];if(!xi(k,e))return!1}return!0}function Yr(e,i){return!(e[0]>i[0]||e[2]<i[2]||e[1]>i[1]||e[3]<i[3])}function me(e,i){return e[0]===i[0]&&e[1]===i[1]}function Jr(e,i){return[(e[0]+i[0])/2,(e[1]+i[1])/2]}var Xr=rt(bt()),oi=e=>()=>e,Un=e=>{let i=e?(r,a)=>a.minus(r).abs().isLessThanOrEqualTo(e):oi(!1);return(r,a)=>i(r,a)?0:r.comparedTo(a)};function tn(e){let i=e?(r,a,s,l,h)=>r.exponentiatedBy(2).isLessThanOrEqualTo(l.minus(a).exponentiatedBy(2).plus(h.minus(s).exponentiatedBy(2)).times(e)):oi(!1);return(r,a,s)=>{let l=r.x,h=r.y,d=s.x,p=s.y,_=h.minus(p).times(a.x.minus(d)).minus(l.minus(d).times(a.y.minus(p)));return i(_,l,h,d,p)?0:_.comparedTo(0)}}var Mi=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,en=Math.ceil,Jt=Math.floor,Ht="[BigNumber Error] ",Vn=Ht+"Number primitive has more than 15 significant digits: ",re=1e14,ot=14,nn=9007199254740991,rn=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],ze=1e7,Ot=1e9;function Hn(e){var i,r,a,s=x.prototype={constructor:x,toString:null,valueOf:null},l=new x(1),h=20,d=4,p=-7,_=21,k=-1e7,w=1e7,z=!1,A=1,V=0,q={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:"\xA0",suffix:""},X="0123456789abcdefghijklmnopqrstuvwxyz",mt=!0;function x(f,g){var v,y,b,C,P,B,T,I,R=this;if(!(R instanceof x))return new x(f,g);if(g==null){if(f&&f._isBigNumber===!0){R.s=f.s,!f.c||f.e>w?R.c=R.e=null:f.e<k?R.c=[R.e=0]:(R.e=f.e,R.c=f.c.slice());return}if((B=typeof f=="number")&&f*0==0){if(R.s=1/f<0?(f=-f,-1):1,f===~~f){for(C=0,P=f;P>=10;P/=10,C++);C>w?R.c=R.e=null:(R.e=C,R.c=[f]);return}I=String(f)}else{if(!Mi.test(I=String(f)))return a(R,I,B);R.s=I.charCodeAt(0)==45?(I=I.slice(1),-1):1}(C=I.indexOf("."))>-1&&(I=I.replace(".","")),(P=I.search(/e/i))>0?(C<0&&(C=P),C+=+I.slice(P+1),I=I.substring(0,P)):C<0&&(C=I.length)}else{if(o(g,2,X.length,"Base"),g==10&&mt)return R=new x(f),S(R,h+R.e+1,d);if(I=String(f),B=typeof f=="number"){if(f*0!=0)return a(R,I,B,g);if(R.s=1/f<0?(I=I.slice(1),-1):1,x.DEBUG&&I.replace(/^0\.0*|\./,"").length>15)throw Error(Vn+f)}else R.s=I.charCodeAt(0)===45?(I=I.slice(1),-1):1;for(v=X.slice(0,g),C=P=0,T=I.length;P<T;P++)if(v.indexOf(y=I.charAt(P))<0){if(y=="."){if(P>C){C=T;continue}}else if(!b&&(I==I.toUpperCase()&&(I=I.toLowerCase())||I==I.toLowerCase()&&(I=I.toUpperCase()))){b=!0,P=-1,C=0;continue}return a(R,String(f),B,g)}B=!1,I=r(I,g,10,R.s),(C=I.indexOf("."))>-1?I=I.replace(".",""):C=I.length}for(P=0;I.charCodeAt(P)===48;P++);for(T=I.length;I.charCodeAt(--T)===48;);if(I=I.slice(P,++T)){if(T-=P,B&&x.DEBUG&&T>15&&(f>nn||f!==Jt(f)))throw Error(Vn+R.s*f);if((C=C-P-1)>w)R.c=R.e=null;else if(C<k)R.c=[R.e=0];else{if(R.e=C,R.c=[],P=(C+1)%ot,C<0&&(P+=ot),P<T){for(P&&R.c.push(+I.slice(0,P)),T-=ot;P<T;)R.c.push(+I.slice(P,P+=ot));P=ot-(I=I.slice(P)).length}else P-=T;for(;P--;I+="0");R.c.push(+I)}}else R.c=[R.e=0]}x.clone=Hn,x.ROUND_UP=0,x.ROUND_DOWN=1,x.ROUND_CEIL=2,x.ROUND_FLOOR=3,x.ROUND_HALF_UP=4,x.ROUND_HALF_DOWN=5,x.ROUND_HALF_EVEN=6,x.ROUND_HALF_CEIL=7,x.ROUND_HALF_FLOOR=8,x.EUCLID=9,x.config=x.set=function(f){var g,v;if(f!=null)if(typeof f=="object"){if(f.hasOwnProperty(g="DECIMAL_PLACES")&&(v=f[g],o(v,0,Ot,g),h=v),f.hasOwnProperty(g="ROUNDING_MODE")&&(v=f[g],o(v,0,8,g),d=v),f.hasOwnProperty(g="EXPONENTIAL_AT")&&(v=f[g],v&&v.pop?(o(v[0],-Ot,0,g),o(v[1],0,Ot,g),p=v[0],_=v[1]):(o(v,-Ot,Ot,g),p=-(_=v<0?-v:v))),f.hasOwnProperty(g="RANGE"))if(v=f[g],v&&v.pop)o(v[0],-Ot,-1,g),o(v[1],1,Ot,g),k=v[0],w=v[1];else if(o(v,-Ot,Ot,g),v)k=-(w=v<0?-v:v);else throw Error(Ht+g+" cannot be zero: "+v);if(f.hasOwnProperty(g="CRYPTO"))if(v=f[g],v===!!v)if(v)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))z=v;else throw z=!v,Error(Ht+"crypto unavailable");else z=v;else throw Error(Ht+g+" not true or false: "+v);if(f.hasOwnProperty(g="MODULO_MODE")&&(v=f[g],o(v,0,9,g),A=v),f.hasOwnProperty(g="POW_PRECISION")&&(v=f[g],o(v,0,Ot,g),V=v),f.hasOwnProperty(g="FORMAT"))if(v=f[g],typeof v=="object")q=v;else throw Error(Ht+g+" not an object: "+v);if(f.hasOwnProperty(g="ALPHABET"))if(v=f[g],typeof v=="string"&&!/^.?$|[+\-.\s]|(.).*\1/.test(v))mt=v.slice(0,10)=="0123456789",X=v;else throw Error(Ht+g+" invalid: "+v)}else throw Error(Ht+"Object expected: "+f);return{DECIMAL_PLACES:h,ROUNDING_MODE:d,EXPONENTIAL_AT:[p,_],RANGE:[k,w],CRYPTO:z,MODULO_MODE:A,POW_PRECISION:V,FORMAT:q,ALPHABET:X}},x.isBigNumber=function(f){if(!f||f._isBigNumber!==!0)return!1;if(!x.DEBUG)return!0;var g,v,y=f.c,b=f.e,C=f.s;t:if({}.toString.call(y)=="[object Array]"){if((C===1||C===-1)&&b>=-Ot&&b<=Ot&&b===Jt(b)){if(y[0]===0){if(b===0&&y.length===1)return!0;break t}if(g=(b+1)%ot,g<1&&(g+=ot),String(y[0]).length==g){for(g=0;g<y.length;g++)if(v=y[g],v<0||v>=re||v!==Jt(v))break t;if(v!==0)return!0}}}else if(y===null&&b===null&&(C===null||C===1||C===-1))return!0;throw Error(Ht+"Invalid BigNumber: "+f)},x.maximum=x.max=function(){return D(arguments,-1)},x.minimum=x.min=function(){return D(arguments,1)},x.random=function(){var f=9007199254740992,g=Math.random()*f&2097151?function(){return Jt(Math.random()*f)}:function(){return(Math.random()*1073741824|0)*8388608+(Math.random()*8388608|0)};return function(v){var y,b,C,P,B,T=0,I=[],R=new x(l);if(v==null?v=h:o(v,0,Ot),P=en(v/ot),z)if(crypto.getRandomValues){for(y=crypto.getRandomValues(new Uint32Array(P*=2));T<P;)B=y[T]*131072+(y[T+1]>>>11),B>=9e15?(b=crypto.getRandomValues(new Uint32Array(2)),y[T]=b[0],y[T+1]=b[1]):(I.push(B%1e14),T+=2);T=P/2}else if(crypto.randomBytes){for(y=crypto.randomBytes(P*=7);T<P;)B=(y[T]&31)*281474976710656+y[T+1]*1099511627776+y[T+2]*4294967296+y[T+3]*16777216+(y[T+4]<<16)+(y[T+5]<<8)+y[T+6],B>=9e15?crypto.randomBytes(7).copy(y,T):(I.push(B%1e14),T+=7);T=P/7}else throw z=!1,Error(Ht+"crypto unavailable");if(!z)for(;T<P;)B=g(),B<9e15&&(I[T++]=B%1e14);for(P=I[--T],v%=ot,P&&v&&(B=rn[ot-v],I[T]=Jt(P/B)*B);I[T]===0;I.pop(),T--);if(T<0)I=[C=0];else{for(C=-1;I[0]===0;I.splice(0,1),C-=ot);for(T=1,B=I[0];B>=10;B/=10,T++);T<ot&&(C-=ot-T)}return R.e=C,R.c=I,R}}(),x.sum=function(){for(var f=1,g=arguments,v=new x(g[0]);f<g.length;)v=v.plus(g[f++]);return v},r=function(){var f="0123456789";function g(v,y,b,C){for(var P,B=[0],T,I=0,R=v.length;I<R;){for(T=B.length;T--;B[T]*=y);for(B[0]+=C.indexOf(v.charAt(I++)),P=0;P<B.length;P++)B[P]>b-1&&(B[P+1]==null&&(B[P+1]=0),B[P+1]+=B[P]/b|0,B[P]%=b)}return B.reverse()}return function(v,y,b,C,P){var B,T,I,R,H,J,Q,it,Lt=v.indexOf("."),xt=h,yt=d;for(Lt>=0&&(R=V,V=0,v=v.replace(".",""),it=new x(y),J=it.pow(v.length-Lt),V=R,it.c=g(m(t(J.c),J.e,"0"),10,b,f),it.e=it.c.length),Q=g(v,y,b,P?(B=X,f):(B=f,X)),I=R=Q.length;Q[--R]==0;Q.pop());if(!Q[0])return B.charAt(0);if(Lt<0?--I:(J.c=Q,J.e=I,J.s=C,J=i(J,it,xt,yt,b),Q=J.c,H=J.r,I=J.e),T=I+xt+1,Lt=Q[T],R=b/2,H=H||T<0||Q[T+1]!=null,H=yt<4?(Lt!=null||H)&&(yt==0||yt==(J.s<0?3:2)):Lt>R||Lt==R&&(yt==4||H||yt==6&&Q[T-1]&1||yt==(J.s<0?8:7)),T<1||!Q[0])v=H?m(B.charAt(1),-xt,B.charAt(0)):B.charAt(0);else{if(Q.length=T,H)for(--b;++Q[--T]>b;)Q[T]=0,T||(++I,Q=[1].concat(Q));for(R=Q.length;!Q[--R];);for(Lt=0,v="";Lt<=R;v+=B.charAt(Q[Lt++]));v=m(v,I,B.charAt(0))}return v}}(),i=function(){function f(y,b,C){var P,B,T,I,R=0,H=y.length,J=b%ze,Q=b/ze|0;for(y=y.slice();H--;)T=y[H]%ze,I=y[H]/ze|0,P=Q*T+I*J,B=J*T+P%ze*ze+R,R=(B/C|0)+(P/ze|0)+Q*I,y[H]=B%C;return R&&(y=[R].concat(y)),y}function g(y,b,C,P){var B,T;if(C!=P)T=C>P?1:-1;else for(B=T=0;B<C;B++)if(y[B]!=b[B]){T=y[B]>b[B]?1:-1;break}return T}function v(y,b,C,P){for(var B=0;C--;)y[C]-=B,B=y[C]<b[C]?1:0,y[C]=B*P+y[C]-b[C];for(;!y[0]&&y.length>1;y.splice(0,1));}return function(y,b,C,P,B){var T,I,R,H,J,Q,it,Lt,xt,yt,kt,qt,oa,Ja,Xa,Ge,Wn,be=y.s==b.s?1:-1,Xt=y.c,St=b.c;if(!Xt||!Xt[0]||!St||!St[0])return new x(!y.s||!b.s||(Xt?St&&Xt[0]==St[0]:!St)?NaN:Xt&&Xt[0]==0||!St?be*0:be/0);for(Lt=new x(be),xt=Lt.c=[],I=y.e-b.e,be=C+I+1,B||(B=re,I=ae(y.e/ot)-ae(b.e/ot),be=be/ot|0),R=0;St[R]==(Xt[R]||0);R++);if(St[R]>(Xt[R]||0)&&I--,be<0)xt.push(1),H=!0;else{for(Ja=Xt.length,Ge=St.length,R=0,be+=2,J=Jt(B/(St[0]+1)),J>1&&(St=f(St,J,B),Xt=f(Xt,J,B),Ge=St.length,Ja=Xt.length),oa=Ge,yt=Xt.slice(0,Ge),kt=yt.length;kt<Ge;yt[kt++]=0);Wn=St.slice(),Wn=[0].concat(Wn),Xa=St[0],St[1]>=B/2&&Xa++;do{if(J=0,T=g(St,yt,Ge,kt),T<0){if(qt=yt[0],Ge!=kt&&(qt=qt*B+(yt[1]||0)),J=Jt(qt/Xa),J>1)for(J>=B&&(J=B-1),Q=f(St,J,B),it=Q.length,kt=yt.length;g(Q,yt,it,kt)==1;)J--,v(Q,Ge<it?Wn:St,it,B),it=Q.length,T=1;else J==0&&(T=J=1),Q=St.slice(),it=Q.length;if(it<kt&&(Q=[0].concat(Q)),v(yt,Q,kt,B),kt=yt.length,T==-1)for(;g(St,yt,Ge,kt)<1;)J++,v(yt,Ge<kt?Wn:St,kt,B),kt=yt.length}else T===0&&(J++,yt=[0]);xt[R++]=J,yt[0]?yt[kt++]=Xt[oa]||0:(yt=[Xt[oa]],kt=1)}while((oa++<Ja||yt[0]!=null)&&be--);H=yt[0]!=null,xt[0]||xt.splice(0,1)}if(B==re){for(R=1,be=xt[0];be>=10;be/=10,R++);S(Lt,C+(Lt.e=R+I*ot-1)+1,P,H)}else Lt.e=I,Lt.r=+H;return Lt}}();function E(f,g,v,y){var b,C,P,B,T;if(v==null?v=d:o(v,0,8),!f.c)return f.toString();if(b=f.c[0],P=f.e,g==null)T=t(f.c),T=y==1||y==2&&(P<=p||P>=_)?c(T,P):m(T,P,"0");else if(f=S(new x(f),g,v),C=f.e,T=t(f.c),B=T.length,y==1||y==2&&(g<=C||C<=p)){for(;B<g;T+="0",B++);T=c(T,C)}else if(g-=P,T=m(T,C,"0"),C+1>B){if(--g>0)for(T+=".";g--;T+="0");}else if(g+=C-B,g>0)for(C+1==B&&(T+=".");g--;T+="0");return f.s<0&&b?"-"+T:T}function D(f,g){for(var v,y,b=1,C=new x(f[0]);b<f.length;b++)y=new x(f[b]),(!y.s||(v=n(C,y))===g||v===0&&C.s===g)&&(C=y);return C}function G(f,g,v){for(var y=1,b=g.length;!g[--b];g.pop());for(b=g[0];b>=10;b/=10,y++);return(v=y+v*ot-1)>w?f.c=f.e=null:v<k?f.c=[f.e=0]:(f.e=v,f.c=g),f}a=function(){var f=/^(-?)0([xbo])(?=\w[\w.]*$)/i,g=/^([^.]+)\.$/,v=/^\.([^.]+)$/,y=/^-?(Infinity|NaN)$/,b=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(C,P,B,T){var I,R=B?P:P.replace(b,"");if(y.test(R))C.s=isNaN(R)?null:R<0?-1:1;else{if(!B&&(R=R.replace(f,function(H,J,Q){return I=(Q=Q.toLowerCase())=="x"?16:Q=="b"?2:8,!T||T==I?J:H}),T&&(I=T,R=R.replace(g,"$1").replace(v,"0.$1")),P!=R))return new x(R,I);if(x.DEBUG)throw Error(Ht+"Not a"+(T?" base "+T:"")+" number: "+P);C.s=null}C.c=C.e=null}}();function S(f,g,v,y){var b,C,P,B,T,I,R,H=f.c,J=rn;if(H){t:{for(b=1,B=H[0];B>=10;B/=10,b++);if(C=g-b,C<0)C+=ot,P=g,T=H[I=0],R=Jt(T/J[b-P-1]%10);else if(I=en((C+1)/ot),I>=H.length)if(y){for(;H.length<=I;H.push(0));T=R=0,b=1,C%=ot,P=C-ot+1}else break t;else{for(T=B=H[I],b=1;B>=10;B/=10,b++);C%=ot,P=C-ot+b,R=P<0?0:Jt(T/J[b-P-1]%10)}if(y=y||g<0||H[I+1]!=null||(P<0?T:T%J[b-P-1]),y=v<4?(R||y)&&(v==0||v==(f.s<0?3:2)):R>5||R==5&&(v==4||y||v==6&&(C>0?P>0?T/J[b-P]:0:H[I-1])%10&1||v==(f.s<0?8:7)),g<1||!H[0])return H.length=0,y?(g-=f.e+1,H[0]=J[(ot-g%ot)%ot],f.e=-g||0):H[0]=f.e=0,f;if(C==0?(H.length=I,B=1,I--):(H.length=I+1,B=J[ot-C],H[I]=P>0?Jt(T/J[b-P]%J[P])*B:0),y)for(;;)if(I==0){for(C=1,P=H[0];P>=10;P/=10,C++);for(P=H[0]+=B,B=1;P>=10;P/=10,B++);C!=B&&(f.e++,H[0]==re&&(H[0]=1));break}else{if(H[I]+=B,H[I]!=re)break;H[I--]=0,B=1}for(C=H.length;H[--C]===0;H.pop());}f.e>w?f.c=f.e=null:f.e<k&&(f.c=[f.e=0])}return f}function N(f){var g,v=f.e;return v===null?f.toString():(g=t(f.c),g=v<=p||v>=_?c(g,v):m(g,v,"0"),f.s<0?"-"+g:g)}return s.absoluteValue=s.abs=function(){var f=new x(this);return f.s<0&&(f.s=1),f},s.comparedTo=function(f,g){return n(this,new x(f,g))},s.decimalPlaces=s.dp=function(f,g){var v,y,b,C=this;if(f!=null)return o(f,0,Ot),g==null?g=d:o(g,0,8),S(new x(C),f+C.e+1,g);if(!(v=C.c))return null;if(y=((b=v.length-1)-ae(this.e/ot))*ot,b=v[b])for(;b%10==0;b/=10,y--);return y<0&&(y=0),y},s.dividedBy=s.div=function(f,g){return i(this,new x(f,g),h,d)},s.dividedToIntegerBy=s.idiv=function(f,g){return i(this,new x(f,g),0,1)},s.exponentiatedBy=s.pow=function(f,g){var v,y,b,C,P,B,T,I,R,H=this;if(f=new x(f),f.c&&!f.isInteger())throw Error(Ht+"Exponent not an integer: "+N(f));if(g!=null&&(g=new x(g)),B=f.e>14,!H.c||!H.c[0]||H.c[0]==1&&!H.e&&H.c.length==1||!f.c||!f.c[0])return R=new x(Math.pow(+N(H),B?f.s*(2-u(f)):+N(f))),g?R.mod(g):R;if(T=f.s<0,g){if(g.c?!g.c[0]:!g.s)return new x(NaN);y=!T&&H.isInteger()&&g.isInteger(),y&&(H=H.mod(g))}else{if(f.e>9&&(H.e>0||H.e<-1||(H.e==0?H.c[0]>1||B&&H.c[1]>=24e7:H.c[0]<8e13||B&&H.c[0]<=9999975e7)))return C=H.s<0&&u(f)?-0:0,H.e>-1&&(C=1/C),new x(T?1/C:C);V&&(C=en(V/ot+2))}for(B?(v=new x(.5),T&&(f.s=1),I=u(f)):(b=Math.abs(+N(f)),I=b%2),R=new x(l);;){if(I){if(R=R.times(H),!R.c)break;C?R.c.length>C&&(R.c.length=C):y&&(R=R.mod(g))}if(b){if(b=Jt(b/2),b===0)break;I=b%2}else if(f=f.times(v),S(f,f.e+1,1),f.e>14)I=u(f);else{if(b=+N(f),b===0)break;I=b%2}H=H.times(H),C?H.c&&H.c.length>C&&(H.c.length=C):y&&(H=H.mod(g))}return y?R:(T&&(R=l.div(R)),g?R.mod(g):C?S(R,V,d,P):R)},s.integerValue=function(f){var g=new x(this);return f==null?f=d:o(f,0,8),S(g,g.e+1,f)},s.isEqualTo=s.eq=function(f,g){return n(this,new x(f,g))===0},s.isFinite=function(){return!!this.c},s.isGreaterThan=s.gt=function(f,g){return n(this,new x(f,g))>0},s.isGreaterThanOrEqualTo=s.gte=function(f,g){return(g=n(this,new x(f,g)))===1||g===0},s.isInteger=function(){return!!this.c&&ae(this.e/ot)>this.c.length-2},s.isLessThan=s.lt=function(f,g){return n(this,new x(f,g))<0},s.isLessThanOrEqualTo=s.lte=function(f,g){return(g=n(this,new x(f,g)))===-1||g===0},s.isNaN=function(){return!this.s},s.isNegative=function(){return this.s<0},s.isPositive=function(){return this.s>0},s.isZero=function(){return!!this.c&&this.c[0]==0},s.minus=function(f,g){var v,y,b,C,P=this,B=P.s;if(f=new x(f,g),g=f.s,!B||!g)return new x(NaN);if(B!=g)return f.s=-g,P.plus(f);var T=P.e/ot,I=f.e/ot,R=P.c,H=f.c;if(!T||!I){if(!R||!H)return R?(f.s=-g,f):new x(H?P:NaN);if(!R[0]||!H[0])return H[0]?(f.s=-g,f):new x(R[0]?P:d==3?-0:0)}if(T=ae(T),I=ae(I),R=R.slice(),B=T-I){for((C=B<0)?(B=-B,b=R):(I=T,b=H),b.reverse(),g=B;g--;b.push(0));b.reverse()}else for(y=(C=(B=R.length)<(g=H.length))?B:g,B=g=0;g<y;g++)if(R[g]!=H[g]){C=R[g]<H[g];break}if(C&&(b=R,R=H,H=b,f.s=-f.s),g=(y=H.length)-(v=R.length),g>0)for(;g--;R[v++]=0);for(g=re-1;y>B;){if(R[--y]<H[y]){for(v=y;v&&!R[--v];R[v]=g);--R[v],R[y]+=re}R[y]-=H[y]}for(;R[0]==0;R.splice(0,1),--I);return R[0]?G(f,R,I):(f.s=d==3?-1:1,f.c=[f.e=0],f)},s.modulo=s.mod=function(f,g){var v,y,b=this;return f=new x(f,g),!b.c||!f.s||f.c&&!f.c[0]?new x(NaN):!f.c||b.c&&!b.c[0]?new x(b):(A==9?(y=f.s,f.s=1,v=i(b,f,0,3),f.s=y,v.s*=y):v=i(b,f,0,A),f=b.minus(v.times(f)),!f.c[0]&&A==1&&(f.s=b.s),f)},s.multipliedBy=s.times=function(f,g){var v,y,b,C,P,B,T,I,R,H,J,Q,it,Lt,xt,yt=this,kt=yt.c,qt=(f=new x(f,g)).c;if(!kt||!qt||!kt[0]||!qt[0])return!yt.s||!f.s||kt&&!kt[0]&&!qt||qt&&!qt[0]&&!kt?f.c=f.e=f.s=null:(f.s*=yt.s,!kt||!qt?f.c=f.e=null:(f.c=[0],f.e=0)),f;for(y=ae(yt.e/ot)+ae(f.e/ot),f.s*=yt.s,T=kt.length,H=qt.length,T<H&&(it=kt,kt=qt,qt=it,b=T,T=H,H=b),b=T+H,it=[];b--;it.push(0));for(Lt=re,xt=ze,b=H;--b>=0;){for(v=0,J=qt[b]%xt,Q=qt[b]/xt|0,P=T,C=b+P;C>b;)I=kt[--P]%xt,R=kt[P]/xt|0,B=Q*I+R*J,I=J*I+B%xt*xt+it[C]+v,v=(I/Lt|0)+(B/xt|0)+Q*R,it[C--]=I%Lt;it[C]=v}return v?++y:it.splice(0,1),G(f,it,y)},s.negated=function(){var f=new x(this);return f.s=-f.s||null,f},s.plus=function(f,g){var v,y=this,b=y.s;if(f=new x(f,g),g=f.s,!b||!g)return new x(NaN);if(b!=g)return f.s=-g,y.minus(f);var C=y.e/ot,P=f.e/ot,B=y.c,T=f.c;if(!C||!P){if(!B||!T)return new x(b/0);if(!B[0]||!T[0])return T[0]?f:new x(B[0]?y:b*0)}if(C=ae(C),P=ae(P),B=B.slice(),b=C-P){for(b>0?(P=C,v=T):(b=-b,v=B),v.reverse();b--;v.push(0));v.reverse()}for(b=B.length,g=T.length,b-g<0&&(v=T,T=B,B=v,g=b),b=0;g;)b=(B[--g]=B[g]+T[g]+b)/re|0,B[g]=re===B[g]?0:B[g]%re;return b&&(B=[b].concat(B),++P),G(f,B,P)},s.precision=s.sd=function(f,g){var v,y,b,C=this;if(f!=null&&f!==!!f)return o(f,1,Ot),g==null?g=d:o(g,0,8),S(new x(C),f,g);if(!(v=C.c))return null;if(b=v.length-1,y=b*ot+1,b=v[b]){for(;b%10==0;b/=10,y--);for(b=v[0];b>=10;b/=10,y++);}return f&&C.e+1>y&&(y=C.e+1),y},s.shiftedBy=function(f){return o(f,-nn,nn),this.times("1e"+f)},s.squareRoot=s.sqrt=function(){var f,g,v,y,b,C=this,P=C.c,B=C.s,T=C.e,I=h+4,R=new x("0.5");if(B!==1||!P||!P[0])return new x(!B||B<0&&(!P||P[0])?NaN:P?C:1/0);if(B=Math.sqrt(+N(C)),B==0||B==1/0?(g=t(P),(g.length+T)%2==0&&(g+="0"),B=Math.sqrt(+g),T=ae((T+1)/2)-(T<0||T%2),B==1/0?g="5e"+T:(g=B.toExponential(),g=g.slice(0,g.indexOf("e")+1)+T),v=new x(g)):v=new x(B+""),v.c[0]){for(T=v.e,B=T+I,B<3&&(B=0);;)if(b=v,v=R.times(b.plus(i(C,b,I,1))),t(b.c).slice(0,B)===(g=t(v.c)).slice(0,B))if(v.e<T&&--B,g=g.slice(B-3,B+1),g=="9999"||!y&&g=="4999"){if(!y&&(S(b,b.e+h+2,0),b.times(b).eq(C))){v=b;break}I+=4,B+=4,y=1}else{(!+g||!+g.slice(1)&&g.charAt(0)=="5")&&(S(v,v.e+h+2,1),f=!v.times(v).eq(C));break}}return S(v,v.e+h+1,d,f)},s.toExponential=function(f,g){return f!=null&&(o(f,0,Ot),f++),E(this,f,g,1)},s.toFixed=function(f,g){return f!=null&&(o(f,0,Ot),f=f+this.e+1),E(this,f,g)},s.toFormat=function(f,g,v){var y,b=this;if(v==null)f!=null&&g&&typeof g=="object"?(v=g,g=null):f&&typeof f=="object"?(v=f,f=g=null):v=q;else if(typeof v!="object")throw Error(Ht+"Argument not an object: "+v);if(y=b.toFixed(f,g),b.c){var C,P=y.split("."),B=+v.groupSize,T=+v.secondaryGroupSize,I=v.groupSeparator||"",R=P[0],H=P[1],J=b.s<0,Q=J?R.slice(1):R,it=Q.length;if(T&&(C=B,B=T,T=C,it-=C),B>0&&it>0){for(C=it%B||B,R=Q.substr(0,C);C<it;C+=B)R+=I+Q.substr(C,B);T>0&&(R+=I+Q.slice(C)),J&&(R="-"+R)}y=H?R+(v.decimalSeparator||"")+((T=+v.fractionGroupSize)?H.replace(new RegExp("\\d{"+T+"}\\B","g"),"$&"+(v.fractionGroupSeparator||"")):H):R}return(v.prefix||"")+y+(v.suffix||"")},s.toFraction=function(f){var g,v,y,b,C,P,B,T,I,R,H,J,Q=this,it=Q.c;if(f!=null&&(B=new x(f),!B.isInteger()&&(B.c||B.s!==1)||B.lt(l)))throw Error(Ht+"Argument "+(B.isInteger()?"out of range: ":"not an integer: ")+N(B));if(!it)return new x(Q);for(g=new x(l),I=v=new x(l),y=T=new x(l),J=t(it),C=g.e=J.length-Q.e-1,g.c[0]=rn[(P=C%ot)<0?ot+P:P],f=!f||B.comparedTo(g)>0?C>0?g:I:B,P=w,w=1/0,B=new x(J),T.c[0]=0;R=i(B,g,0,1),b=v.plus(R.times(y)),b.comparedTo(f)!=1;)v=y,y=b,I=T.plus(R.times(b=I)),T=b,g=B.minus(R.times(b=g)),B=b;return b=i(f.minus(v),y,0,1),T=T.plus(b.times(I)),v=v.plus(b.times(y)),T.s=I.s=Q.s,C=C*2,H=i(I,y,C,d).minus(Q).abs().comparedTo(i(T,v,C,d).minus(Q).abs())<1?[I,y]:[T,v],w=P,H},s.toNumber=function(){return+N(this)},s.toPrecision=function(f,g){return f!=null&&o(f,1,Ot),E(this,f,g,2)},s.toString=function(f){var g,v=this,y=v.s,b=v.e;return b===null?y?(g="Infinity",y<0&&(g="-"+g)):g="NaN":(f==null?g=b<=p||b>=_?c(t(v.c),b):m(t(v.c),b,"0"):f===10&&mt?(v=S(new x(v),h+b+1,d),g=m(t(v.c),v.e,"0")):(o(f,2,X.length,"Base"),g=r(m(t(v.c),b,"0"),10,f,y,!0)),y<0&&v.c[0]&&(g="-"+g)),g},s.valueOf=s.toJSON=function(){return N(this)},s._isBigNumber=!0,s[Symbol.toStringTag]="BigNumber",s[Symbol.for("nodejs.util.inspect.custom")]=s.valueOf,e!=null&&x.set(e),x}function ae(e){var i=e|0;return e>0||e===i?i:i-1}function t(e){for(var i,r,a=1,s=e.length,l=e[0]+"";a<s;){for(i=e[a++]+"",r=ot-i.length;r--;i="0"+i);l+=i}for(s=l.length;l.charCodeAt(--s)===48;);return l.slice(0,s+1||1)}function n(e,i){var r,a,s=e.c,l=i.c,h=e.s,d=i.s,p=e.e,_=i.e;if(!h||!d)return null;if(r=s&&!s[0],a=l&&!l[0],r||a)return r?a?0:-d:h;if(h!=d)return h;if(r=h<0,a=p==_,!s||!l)return a?0:!s^r?1:-1;if(!a)return p>_^r?1:-1;for(d=(p=s.length)<(_=l.length)?p:_,h=0;h<d;h++)if(s[h]!=l[h])return s[h]>l[h]^r?1:-1;return p==_?0:p>_^r?1:-1}function o(e,i,r,a){if(e<i||e>r||e!==Jt(e))throw Error(Ht+(a||"Argument")+(typeof e=="number"?e<i||e>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function u(e){var i=e.c.length-1;return ae(e.e/ot)==i&&e.c[i]%2!=0}function c(e,i){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(i<0?"e":"e+")+i}function m(e,i,r){var a,s;if(i<0){for(s=r+".";++i;s+=r);e=s+e}else if(a=e.length,++i>a){for(s=r,i-=a;--i;s+=r);e+=s}else i<a&&(e=e.slice(0,i)+"."+e.slice(i));return e}var M=Hn(),O=M,Z=class{constructor(e){et(this,"key");et(this,"left",null);et(this,"right",null);this.key=e}},K=class extends Z{constructor(e){super(e)}},Y=class{constructor(){et(this,"size",0);et(this,"modificationCount",0);et(this,"splayCount",0)}splay(e){let i=this.root;if(i==null)return this.compare(e,e),-1;let r=null,a=null,s=null,l=null,h=i,d=this.compare,p;for(;;)if(p=d(h.key,e),p>0){let _=h.left;if(_==null||(p=d(_.key,e),p>0&&(h.left=_.right,_.right=h,h=_,_=h.left,_==null)))break;r==null?a=h:r.left=h,r=h,h=_}else if(p<0){let _=h.right;if(_==null||(p=d(_.key,e),p<0&&(h.right=_.left,_.left=h,h=_,_=h.right,_==null)))break;s==null?l=h:s.right=h,s=h,h=_}else break;return s!=null&&(s.right=h.left,h.left=l),r!=null&&(r.left=h.right,h.right=a),this.root!==h&&(this.root=h,this.splayCount++),p}splayMin(e){let i=e,r=i.left;for(;r!=null;){let a=r;i.left=a.right,a.right=i,i=a,r=i.left}return i}splayMax(e){let i=e,r=i.right;for(;r!=null;){let a=r;i.right=a.left,a.left=i,i=a,r=i.right}return i}_delete(e){if(this.root==null||this.splay(e)!=0)return null;let i=this.root,r=i,a=i.left;if(this.size--,a==null)this.root=i.right;else{let s=i.right;i=this.splayMax(a),i.right=s,this.root=i}return this.modificationCount++,r}addNewRoot(e,i){this.size++,this.modificationCount++;let r=this.root;if(r==null){this.root=e;return}i<0?(e.left=r,e.right=r.right,r.right=null):(e.right=r,e.left=r.left,r.left=null),this.root=e}_first(){let e=this.root;return e==null?null:(this.root=this.splayMin(e),this.root)}_last(){let e=this.root;return e==null?null:(this.root=this.splayMax(e),this.root)}clear(){this.root=null,this.size=0,this.modificationCount++}has(e){return this.validKey(e)&&this.splay(e)==0}defaultCompare(){return(e,i)=>e<i?-1:e>i?1:0}wrap(){return{getRoot:()=>this.root,setRoot:e=>{this.root=e},getSize:()=>this.size,getModificationCount:()=>this.modificationCount,getSplayCount:()=>this.splayCount,setSplayCount:e=>{this.splayCount=e},splay:e=>this.splay(e),has:e=>this.has(e)}}},nt=(ui=class extends Y{constructor(r,a){super();et(this,"root",null);et(this,"compare");et(this,"validKey");et(this,Io,"[object Set]");this.compare=r??this.defaultCompare(),this.validKey=a??(s=>s!=null&&s!=null)}delete(r){return this.validKey(r)?this._delete(r)!=null:!1}deleteAll(r){for(let a of r)this.delete(a)}forEach(r){let a=this[Symbol.iterator](),s;for(;s=a.next(),!s.done;)r(s.value,s.value,this)}add(r){let a=this.splay(r);return a!=0&&this.addNewRoot(new K(r),a),this}addAndReturn(r){let a=this.splay(r);return a!=0&&this.addNewRoot(new K(r),a),this.root.key}addAll(r){for(let a of r)this.add(a)}isEmpty(){return this.root==null}isNotEmpty(){return this.root!=null}single(){if(this.size==0)throw"Bad state: No element";if(this.size>1)throw"Bad state: Too many element";return this.root.key}first(){if(this.size==0)throw"Bad state: No element";return this._first().key}last(){if(this.size==0)throw"Bad state: No element";return this._last().key}lastBefore(r){if(r==null)throw"Invalid arguments(s)";if(this.root==null)return null;if(this.splay(r)<0)return this.root.key;let a=this.root.left;if(a==null)return null;let s=a.right;for(;s!=null;)a=s,s=a.right;return a.key}firstAfter(r){if(r==null)throw"Invalid arguments(s)";if(this.root==null)return null;if(this.splay(r)>0)return this.root.key;let a=this.root.right;if(a==null)return null;let s=a.left;for(;s!=null;)a=s,s=a.left;return a.key}retainAll(r){let a=new ui(this.compare,this.validKey),s=this.modificationCount;for(let l of r){if(s!=this.modificationCount)throw"Concurrent modification during iteration.";this.validKey(l)&&this.splay(l)==0&&a.add(this.root.key)}a.size!=this.size&&(this.root=a.root,this.size=a.size,this.modificationCount++)}lookup(r){return!this.validKey(r)||this.splay(r)!=0?null:this.root.key}intersection(r){let a=new ui(this.compare,this.validKey);for(let s of this)r.has(s)&&a.add(s);return a}difference(r){let a=new ui(this.compare,this.validKey);for(let s of this)r.has(s)||a.add(s);return a}union(r){let a=this.clone();return a.addAll(r),a}clone(){let r=new ui(this.compare,this.validKey);return r.size=this.size,r.root=this.copyNode(this.root),r}copyNode(r){if(r==null)return null;function a(l,h){let d,p;do{if(d=l.left,p=l.right,d!=null){let _=new K(d.key);h.left=_,a(d,_)}if(p!=null){let _=new K(p.key);h.right=_,l=p,h=_}}while(p!=null)}let s=new K(r.key);return a(r,s),s}toSet(){return this.clone()}entries(){return new Rt(this.wrap())}keys(){return this[Symbol.iterator]()}values(){return this[Symbol.iterator]()}[Symbol.iterator](){return new jt(this.wrap())}},Io=Symbol.toStringTag,ui),dt=class{constructor(e){et(this,"tree");et(this,"path",new Array);et(this,"modificationCount",null);et(this,"splayCount");this.tree=e,this.splayCount=e.getSplayCount()}[Symbol.iterator](){return this}next(){return this.moveNext()?{done:!1,value:this.current()}:{done:!0,value:null}}current(){if(!this.path.length)return null;let e=this.path[this.path.length-1];return this.getValue(e)}rebuildPath(e){this.path.splice(0,this.path.length),this.tree.splay(e),this.path.push(this.tree.getRoot()),this.splayCount=this.tree.getSplayCount()}findLeftMostDescendent(e){for(;e!=null;)this.path.push(e),e=e.left}moveNext(){if(this.modificationCount!=this.tree.getModificationCount()){if(this.modificationCount==null){this.modificationCount=this.tree.getModificationCount();let r=this.tree.getRoot();for(;r!=null;)this.path.push(r),r=r.left;return this.path.length>0}throw"Concurrent modification during iteration."}if(!this.path.length)return!1;this.splayCount!=this.tree.getSplayCount()&&this.rebuildPath(this.path[this.path.length-1].key);let e=this.path[this.path.length-1],i=e.right;if(i!=null){for(;i!=null;)this.path.push(i),i=i.left;return!0}for(this.path.pop();this.path.length&&this.path[this.path.length-1].right===e;)e=this.path.pop();return this.path.length>0}},jt=class extends dt{getValue(e){return e.key}},Rt=class extends dt{getValue(e){return[e.key,e.key]}},oe=e=>e,Kt=e=>{if(e){let i=new nt(Un(e)),r=new nt(Un(e)),a=(l,h)=>h.addAndReturn(l),s=l=>({x:a(l.x,i),y:a(l.y,r)});return s({x:new O(0),y:new O(0)}),s}return oe},Ne=e=>({set:i=>{Be=Ne(i)},reset:()=>Ne(e),compare:Un(e),snap:Kt(e),orient:tn(e)}),Be=Ne(),wi=(e,i)=>e.ll.x.isLessThanOrEqualTo(i.x)&&i.x.isLessThanOrEqualTo(e.ur.x)&&e.ll.y.isLessThanOrEqualTo(i.y)&&i.y.isLessThanOrEqualTo(e.ur.y),Kn=(e,i)=>{if(i.ur.x.isLessThan(e.ll.x)||e.ur.x.isLessThan(i.ll.x)||i.ur.y.isLessThan(e.ll.y)||e.ur.y.isLessThan(i.ll.y))return null;let r=e.ll.x.isLessThan(i.ll.x)?i.ll.x:e.ll.x,a=e.ur.x.isLessThan(i.ur.x)?e.ur.x:i.ur.x,s=e.ll.y.isLessThan(i.ll.y)?i.ll.y:e.ll.y,l=e.ur.y.isLessThan(i.ur.y)?e.ur.y:i.ur.y;return{ll:{x:r,y:s},ur:{x:a,y:l}}},Ei=(e,i)=>e.x.times(i.y).minus(e.y.times(i.x)),$r=(e,i)=>e.x.times(i.x).plus(e.y.times(i.y)),Bi=e=>$r(e,e).sqrt(),Dt=(e,i,r)=>{let a={x:i.x.minus(e.x),y:i.y.minus(e.y)},s={x:r.x.minus(e.x),y:r.y.minus(e.y)};return Ei(s,a).div(Bi(s)).div(Bi(a))},an=(e,i,r)=>{let a={x:i.x.minus(e.x),y:i.y.minus(e.y)},s={x:r.x.minus(e.x),y:r.y.minus(e.y)};return $r(s,a).div(Bi(s)).div(Bi(a))},Qr=(e,i,r)=>i.y.isZero()?null:{x:e.x.plus(i.x.div(i.y).times(r.minus(e.y))),y:r},ta=(e,i,r)=>i.x.isZero()?null:{x:r,y:e.y.plus(i.y.div(i.x).times(r.minus(e.x)))},Wa=(e,i,r,a)=>{if(i.x.isZero())return ta(r,a,e.x);if(a.x.isZero())return ta(e,i,r.x);if(i.y.isZero())return Qr(r,a,e.y);if(a.y.isZero())return Qr(e,i,r.y);let s=Ei(i,a);if(s.isZero())return null;let l={x:r.x.minus(e.x),y:r.y.minus(e.y)},h=Ei(l,i).div(s),d=Ei(l,a).div(s),p=e.x.plus(d.times(i.x)),_=r.x.plus(h.times(a.x)),k=e.y.plus(d.times(i.y)),w=r.y.plus(h.times(a.y)),z=p.plus(_).div(2),A=k.plus(w).div(2);return{x:z,y:A}},se=class ho{constructor(i,r){et(this,"point");et(this,"isLeft");et(this,"segment");et(this,"otherSE");et(this,"consumedBy");i.events===void 0?i.events=[this]:i.events.push(this),this.point=i,this.isLeft=r}static compare(i,r){let a=ho.comparePoints(i.point,r.point);return a!==0?a:(i.point!==r.point&&i.link(r),i.isLeft!==r.isLeft?i.isLeft?1:-1:on.compare(i.segment,r.segment))}static comparePoints(i,r){return i.x.isLessThan(r.x)?-1:i.x.isGreaterThan(r.x)?1:i.y.isLessThan(r.y)?-1:i.y.isGreaterThan(r.y)?1:0}link(i){if(i.point===this.point)throw new Error("Tried to link already linked events");let r=i.point.events;for(let a=0,s=r.length;a<s;a++){let l=r[a];this.point.events.push(l),l.point=this.point}this.checkForConsuming()}checkForConsuming(){let i=this.point.events.length;for(let r=0;r<i;r++){let a=this.point.events[r];if(a.segment.consumedBy===void 0)for(let s=r+1;s<i;s++){let l=this.point.events[s];l.consumedBy===void 0&&a.otherSE.point.events===l.otherSE.point.events&&a.segment.consume(l.segment)}}}getAvailableLinkedEvents(){let i=[];for(let r=0,a=this.point.events.length;r<a;r++){let s=this.point.events[r];s!==this&&!s.segment.ringOut&&s.segment.isInResult()&&i.push(s)}return i}getLeftmostComparator(i){let r=new Map,a=s=>{let l=s.otherSE;r.set(s,{sine:Dt(this.point,i.point,l.point),cosine:an(this.point,i.point,l.point)})};return(s,l)=>{r.has(s)||a(s),r.has(l)||a(l);let{sine:h,cosine:d}=r.get(s),{sine:p,cosine:_}=r.get(l);return h.isGreaterThanOrEqualTo(0)&&p.isGreaterThanOrEqualTo(0)?d.isLessThan(_)?1:d.isGreaterThan(_)?-1:0:h.isLessThan(0)&&p.isLessThan(0)?d.isLessThan(_)?-1:d.isGreaterThan(_)?1:0:p.isLessThan(h)?-1:p.isGreaterThan(h)?1:0}}},ea=0,on=class ua{constructor(i,r,a,s){et(this,"id");et(this,"leftSE");et(this,"rightSE");et(this,"rings");et(this,"windings");et(this,"ringOut");et(this,"consumedBy");et(this,"prev");et(this,"_prevInResult");et(this,"_beforeState");et(this,"_afterState");et(this,"_isInResult");this.id=++ea,this.leftSE=i,i.segment=this,i.otherSE=r,this.rightSE=r,r.segment=this,r.otherSE=i,this.rings=a,this.windings=s}static compare(i,r){let a=i.leftSE.point.x,s=r.leftSE.point.x,l=i.rightSE.point.x,h=r.rightSE.point.x;if(h.isLessThan(a))return 1;if(l.isLessThan(s))return-1;let d=i.leftSE.point.y,p=r.leftSE.point.y,_=i.rightSE.point.y,k=r.rightSE.point.y;if(a.isLessThan(s)){if(p.isLessThan(d)&&p.isLessThan(_))return 1;if(p.isGreaterThan(d)&&p.isGreaterThan(_))return-1;let w=i.comparePoint(r.leftSE.point);if(w<0)return 1;if(w>0)return-1;let z=r.comparePoint(i.rightSE.point);return z!==0?z:-1}if(a.isGreaterThan(s)){if(d.isLessThan(p)&&d.isLessThan(k))return-1;if(d.isGreaterThan(p)&&d.isGreaterThan(k))return 1;let w=r.comparePoint(i.leftSE.point);if(w!==0)return w;let z=i.comparePoint(r.rightSE.point);return z<0?1:z>0?-1:1}if(d.isLessThan(p))return-1;if(d.isGreaterThan(p))return 1;if(l.isLessThan(h)){let w=r.comparePoint(i.rightSE.point);if(w!==0)return w}if(l.isGreaterThan(h)){let w=i.comparePoint(r.rightSE.point);if(w<0)return 1;if(w>0)return-1}if(!l.eq(h)){let w=_.minus(d),z=l.minus(a),A=k.minus(p),V=h.minus(s);if(w.isGreaterThan(z)&&A.isLessThan(V))return 1;if(w.isLessThan(z)&&A.isGreaterThan(V))return-1}return l.isGreaterThan(h)?1:l.isLessThan(h)||_.isLessThan(k)?-1:_.isGreaterThan(k)?1:i.id<r.id?-1:i.id>r.id?1:0}static fromRing(i,r,a){let s,l,h,d=se.comparePoints(i,r);if(d<0)s=i,l=r,h=1;else if(d>0)s=r,l=i,h=-1;else throw new Error(`Tried to create degenerate segment at [${i.x}, ${i.y}]`);let p=new se(s,!0),_=new se(l,!1);return new ua(p,_,[a],[h])}replaceRightSE(i){this.rightSE=i,this.rightSE.segment=this,this.rightSE.otherSE=this.leftSE,this.leftSE.otherSE=this.rightSE}bbox(){let i=this.leftSE.point.y,r=this.rightSE.point.y;return{ll:{x:this.leftSE.point.x,y:i.isLessThan(r)?i:r},ur:{x:this.rightSE.point.x,y:i.isGreaterThan(r)?i:r}}}vector(){return{x:this.rightSE.point.x.minus(this.leftSE.point.x),y:this.rightSE.point.y.minus(this.leftSE.point.y)}}isAnEndpoint(i){return i.x.eq(this.leftSE.point.x)&&i.y.eq(this.leftSE.point.y)||i.x.eq(this.rightSE.point.x)&&i.y.eq(this.rightSE.point.y)}comparePoint(i){return Be.orient(this.leftSE.point,i,this.rightSE.point)}getIntersection(i){let r=this.bbox(),a=i.bbox(),s=Kn(r,a);if(s===null)return null;let l=this.leftSE.point,h=this.rightSE.point,d=i.leftSE.point,p=i.rightSE.point,_=wi(r,d)&&this.comparePoint(d)===0,k=wi(a,l)&&i.comparePoint(l)===0,w=wi(r,p)&&this.comparePoint(p)===0,z=wi(a,h)&&i.comparePoint(h)===0;if(k&&_)return z&&!w?h:!z&&w?p:null;if(k)return w&&l.x.eq(p.x)&&l.y.eq(p.y)?null:l;if(_)return z&&h.x.eq(d.x)&&h.y.eq(d.y)?null:d;if(z&&w)return null;if(z)return h;if(w)return p;let A=Wa(l,this.vector(),d,i.vector());return A===null||!wi(s,A)?null:Be.snap(A)}split(i){let r=[],a=i.events!==void 0,s=new se(i,!0),l=new se(i,!1),h=this.rightSE;this.replaceRightSE(l),r.push(l),r.push(s);let d=new ua(s,h,this.rings.slice(),this.windings.slice());return se.comparePoints(d.leftSE.point,d.rightSE.point)>0&&d.swapEvents(),se.comparePoints(this.leftSE.point,this.rightSE.point)>0&&this.swapEvents(),a&&(s.checkForConsuming(),l.checkForConsuming()),r}swapEvents(){let i=this.rightSE;this.rightSE=this.leftSE,this.leftSE=i,this.leftSE.isLeft=!0,this.rightSE.isLeft=!1;for(let r=0,a=this.windings.length;r<a;r++)this.windings[r]*=-1}consume(i){let r=this,a=i;for(;r.consumedBy;)r=r.consumedBy;for(;a.consumedBy;)a=a.consumedBy;let s=ua.compare(r,a);if(s!==0){if(s>0){let l=r;r=a,a=l}if(r.prev===a){let l=r;r=a,a=l}for(let l=0,h=a.rings.length;l<h;l++){let d=a.rings[l],p=a.windings[l],_=r.rings.indexOf(d);_===-1?(r.rings.push(d),r.windings.push(p)):r.windings[_]+=p}a.rings=null,a.windings=null,a.consumedBy=r,a.leftSE.consumedBy=r.leftSE,a.rightSE.consumedBy=r.rightSE}}prevInResult(){return this._prevInResult!==void 0?this._prevInResult:(this.prev?this.prev.isInResult()?this._prevInResult=this.prev:this._prevInResult=this.prev.prevInResult():this._prevInResult=null,this._prevInResult)}beforeState(){if(this._beforeState!==void 0)return this._beforeState;if(!this.prev)this._beforeState={rings:[],windings:[],multiPolys:[]};else{let i=this.prev.consumedBy||this.prev;this._beforeState=i.afterState()}return this._beforeState}afterState(){if(this._afterState!==void 0)return this._afterState;let i=this.beforeState();this._afterState={rings:i.rings.slice(0),windings:i.windings.slice(0),multiPolys:[]};let r=this._afterState.rings,a=this._afterState.windings,s=this._afterState.multiPolys;for(let d=0,p=this.rings.length;d<p;d++){let _=this.rings[d],k=this.windings[d],w=r.indexOf(_);w===-1?(r.push(_),a.push(k)):a[w]+=k}let l=[],h=[];for(let d=0,p=r.length;d<p;d++){if(a[d]===0)continue;let _=r[d],k=_.poly;if(h.indexOf(k)===-1)if(_.isExterior)l.push(k);else{h.indexOf(k)===-1&&h.push(k);let w=l.indexOf(_.poly);w!==-1&&l.splice(w,1)}}for(let d=0,p=l.length;d<p;d++){let _=l[d].multiPoly;s.indexOf(_)===-1&&s.push(_)}return this._afterState}isInResult(){if(this.consumedBy)return!1;if(this._isInResult!==void 0)return this._isInResult;let i=this.beforeState().multiPolys,r=this.afterState().multiPolys;switch(ia.type){case"union":{let a=i.length===0,s=r.length===0;this._isInResult=a!==s;break}case"intersection":{let a,s;i.length<r.length?(a=i.length,s=r.length):(a=r.length,s=i.length),this._isInResult=s===ia.numMultiPolys&&a<s;break}case"xor":{let a=Math.abs(i.length-r.length);this._isInResult=a%2===1;break}case"difference":{let a=s=>s.length===1&&s[0].isSubject;this._isInResult=a(i)!==a(r);break}}return this._isInResult}},to=class{constructor(e,i,r){et(this,"poly");et(this,"isExterior");et(this,"segments");et(this,"bbox");if(!Array.isArray(e)||e.length===0)throw new Error("Input geometry is not a valid Polygon or MultiPolygon");if(this.poly=i,this.isExterior=r,this.segments=[],typeof e[0][0]!="number"||typeof e[0][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");let a=Be.snap({x:new O(e[0][0]),y:new O(e[0][1])});this.bbox={ll:{x:a.x,y:a.y},ur:{x:a.x,y:a.y}};let s=a;for(let l=1,h=e.length;l<h;l++){if(typeof e[l][0]!="number"||typeof e[l][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");let d=Be.snap({x:new O(e[l][0]),y:new O(e[l][1])});d.x.eq(s.x)&&d.y.eq(s.y)||(this.segments.push(on.fromRing(s,d,this)),d.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=d.x),d.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=d.y),d.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=d.x),d.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=d.y),s=d)}(!a.x.eq(s.x)||!a.y.eq(s.y))&&this.segments.push(on.fromRing(s,a,this))}getSweepEvents(){let e=[];for(let i=0,r=this.segments.length;i<r;i++){let a=this.segments[i];e.push(a.leftSE),e.push(a.rightSE)}return e}},co=class{constructor(e,i){et(this,"multiPoly");et(this,"exteriorRing");et(this,"interiorRings");et(this,"bbox");if(!Array.isArray(e))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");this.exteriorRing=new to(e[0],this,!0),this.bbox={ll:{x:this.exteriorRing.bbox.ll.x,y:this.exteriorRing.bbox.ll.y},ur:{x:this.exteriorRing.bbox.ur.x,y:this.exteriorRing.bbox.ur.y}},this.interiorRings=[];for(let r=1,a=e.length;r<a;r++){let s=new to(e[r],this,!1);s.bbox.ll.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=s.bbox.ll.x),s.bbox.ll.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=s.bbox.ll.y),s.bbox.ur.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=s.bbox.ur.x),s.bbox.ur.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=s.bbox.ur.y),this.interiorRings.push(s)}this.multiPoly=i}getSweepEvents(){let e=this.exteriorRing.getSweepEvents();for(let i=0,r=this.interiorRings.length;i<r;i++){let a=this.interiorRings[i].getSweepEvents();for(let s=0,l=a.length;s<l;s++)e.push(a[s])}return e}},eo=class{constructor(e,i){et(this,"isSubject");et(this,"polys");et(this,"bbox");if(!Array.isArray(e))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");try{typeof e[0][0][0]=="number"&&(e=[e])}catch{}this.polys=[],this.bbox={ll:{x:new O(Number.POSITIVE_INFINITY),y:new O(Number.POSITIVE_INFINITY)},ur:{x:new O(Number.NEGATIVE_INFINITY),y:new O(Number.NEGATIVE_INFINITY)}};for(let r=0,a=e.length;r<a;r++){let s=new co(e[r],this);s.bbox.ll.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=s.bbox.ll.x),s.bbox.ll.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=s.bbox.ll.y),s.bbox.ur.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=s.bbox.ur.x),s.bbox.ur.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=s.bbox.ur.y),this.polys.push(s)}this.isSubject=i}getSweepEvents(){let e=[];for(let i=0,r=this.polys.length;i<r;i++){let a=this.polys[i].getSweepEvents();for(let s=0,l=a.length;s<l;s++)e.push(a[s])}return e}},po=class Qa{constructor(i){et(this,"events");et(this,"poly");et(this,"_isExteriorRing");et(this,"_enclosingRing");this.events=i;for(let r=0,a=i.length;r<a;r++)i[r].segment.ringOut=this;this.poly=null}static factory(i){let r=[];for(let a=0,s=i.length;a<s;a++){let l=i[a];if(!l.isInResult()||l.ringOut)continue;let h=null,d=l.leftSE,p=l.rightSE,_=[d],k=d.point,w=[];for(;h=d,d=p,_.push(d),d.point!==k;)for(;;){let z=d.getAvailableLinkedEvents();if(z.length===0){let q=_[0].point,X=_[_.length-1].point;throw new Error(`Unable to complete output ring starting at [${q.x}, ${q.y}]. Last matching segment found ends at [${X.x}, ${X.y}].`)}if(z.length===1){p=z[0].otherSE;break}let A=null;for(let q=0,X=w.length;q<X;q++)if(w[q].point===d.point){A=q;break}if(A!==null){let q=w.splice(A)[0],X=_.splice(q.index);X.unshift(X[0].otherSE),r.push(new Qa(X.reverse()));continue}w.push({index:_.length,point:d.point});let V=d.getLeftmostComparator(h);p=z.sort(V)[0].otherSE;break}r.push(new Qa(_))}return r}getGeom(){let i=this.events[0].point,r=[i];for(let _=1,k=this.events.length-1;_<k;_++){let w=this.events[_].point,z=this.events[_+1].point;Be.orient(w,i,z)!==0&&(r.push(w),i=w)}if(r.length===1)return null;let a=r[0],s=r[1];Be.orient(a,i,s)===0&&r.shift(),r.push(r[0]);let l=this.isExteriorRing()?1:-1,h=this.isExteriorRing()?0:r.length-1,d=this.isExteriorRing()?r.length:-1,p=[];for(let _=h;_!=d;_+=l)p.push([r[_].x.toNumber(),r[_].y.toNumber()]);return p}isExteriorRing(){if(this._isExteriorRing===void 0){let i=this.enclosingRing();this._isExteriorRing=i?!i.isExteriorRing():!0}return this._isExteriorRing}enclosingRing(){return this._enclosingRing===void 0&&(this._enclosingRing=this._calcEnclosingRing()),this._enclosingRing}_calcEnclosingRing(){let i=this.events[0];for(let s=1,l=this.events.length;s<l;s++){let h=this.events[s];se.compare(i,h)>0&&(i=h)}let r=i.segment.prevInResult(),a=r?r.prevInResult():null;for(;;){if(!r)return null;if(!a)return r.ringOut;if(a.ringOut!==r.ringOut)return a.ringOut?.enclosingRing()!==r.ringOut?r.ringOut:r.ringOut?.enclosingRing();r=a.prevInResult(),a=r?r.prevInResult():null}}},io=class{constructor(e){et(this,"exteriorRing");et(this,"interiorRings");this.exteriorRing=e,e.poly=this,this.interiorRings=[]}addInterior(e){this.interiorRings.push(e),e.poly=this}getGeom(){let e=this.exteriorRing.getGeom();if(e===null)return null;let i=[e];for(let r=0,a=this.interiorRings.length;r<a;r++){let s=this.interiorRings[r].getGeom();s!==null&&i.push(s)}return i}},fo=class{constructor(e){et(this,"rings");et(this,"polys");this.rings=e,this.polys=this._composePolys(e)}getGeom(){let e=[];for(let i=0,r=this.polys.length;i<r;i++){let a=this.polys[i].getGeom();a!==null&&e.push(a)}return e}_composePolys(e){let i=[];for(let r=0,a=e.length;r<a;r++){let s=e[r];if(!s.poly)if(s.isExteriorRing())i.push(new io(s));else{let l=s.enclosingRing();l?.poly||i.push(new io(l)),l?.poly?.addInterior(s)}}return i}},_o=class{constructor(e,i=on.compare){et(this,"queue");et(this,"tree");et(this,"segments");this.queue=e,this.tree=new nt(i),this.segments=[]}process(e){let i=e.segment,r=[];if(e.consumedBy)return e.isLeft?this.queue.delete(e.otherSE):this.tree.delete(i),r;e.isLeft&&this.tree.add(i);let a=i,s=i;do a=this.tree.lastBefore(a);while(a!=null&&a.consumedBy!=null);do s=this.tree.firstAfter(s);while(s!=null&&s.consumedBy!=null);if(e.isLeft){let l=null;if(a){let d=a.getIntersection(i);if(d!==null&&(i.isAnEndpoint(d)||(l=d),!a.isAnEndpoint(d))){let p=this._splitSafely(a,d);for(let _=0,k=p.length;_<k;_++)r.push(p[_])}}let h=null;if(s){let d=s.getIntersection(i);if(d!==null&&(i.isAnEndpoint(d)||(h=d),!s.isAnEndpoint(d))){let p=this._splitSafely(s,d);for(let _=0,k=p.length;_<k;_++)r.push(p[_])}}if(l!==null||h!==null){let d=null;l===null?d=h:h===null?d=l:d=se.comparePoints(l,h)<=0?l:h,this.queue.delete(i.rightSE),r.push(i.rightSE);let p=i.split(d);for(let _=0,k=p.length;_<k;_++)r.push(p[_])}r.length>0?(this.tree.delete(i),r.push(e)):(this.segments.push(i),i.prev=a)}else{if(a&&s){let l=a.getIntersection(s);if(l!==null){if(!a.isAnEndpoint(l)){let h=this._splitSafely(a,l);for(let d=0,p=h.length;d<p;d++)r.push(h[d])}if(!s.isAnEndpoint(l)){let h=this._splitSafely(s,l);for(let d=0,p=h.length;d<p;d++)r.push(h[d])}}}this.tree.delete(i)}return r}_splitSafely(e,i){this.tree.delete(e);let r=e.rightSE;this.queue.delete(r);let a=e.split(i);return a.push(r),e.consumedBy===void 0&&this.tree.add(e),a}},mo=class{constructor(){et(this,"type");et(this,"numMultiPolys")}run(e,i,r){qn.type=e;let a=[new eo(i,!0)];for(let p=0,_=r.length;p<_;p++)a.push(new eo(r[p],!1));if(qn.numMultiPolys=a.length,qn.type==="difference"){let p=a[0],_=1;for(;_<a.length;)Kn(a[_].bbox,p.bbox)!==null?_++:a.splice(_,1)}if(qn.type==="intersection")for(let p=0,_=a.length;p<_;p++){let k=a[p];for(let w=p+1,z=a.length;w<z;w++)if(Kn(k.bbox,a[w].bbox)===null)return[]}let s=new nt(se.compare);for(let p=0,_=a.length;p<_;p++){let k=a[p].getSweepEvents();for(let w=0,z=k.length;w<z;w++)s.add(k[w])}let l=new _o(s),h=null;for(s.size!=0&&(h=s.first(),s.delete(h));h;){let p=l.process(h);for(let _=0,k=p.length;_<k;_++){let w=p[_];w.consumedBy===void 0&&s.add(w)}s.size!=0?(h=s.first(),s.delete(h)):h=null}Be.reset();let d=po.factory(l.segments);return new fo(d).getGeom()}},qn=new mo,ia=qn,go=(e,...i)=>ia.run("intersection",e,i),yo=(e,...i)=>ia.run("difference",e,i),qo=Be.set;function na(e){let i={type:"Feature"};return i.geometry=e,i}function ra(e){return e.type==="Feature"?e.geometry:e}function no(e){return e&&e.geometry&&e.geometry.coordinates?e.geometry.coordinates:e}function vo(e){return na({type:"LineString",coordinates:e})}function Lo(e){return na({type:"MultiLineString",coordinates:e})}function ro(e){return na({type:"Polygon",coordinates:e})}function ao(e){return na({type:"MultiPolygon",coordinates:e})}function bo(e,i){let r=ra(e),a=ra(i),s=go(r.coordinates,a.coordinates);return s.length===0?null:s.length===1?ro(s[0]):ao(s)}function Co(e,i){let r=ra(e),a=ra(i),s=yo(r.coordinates,a.coordinates);return s.length===0?null:s.length===1?ro(s[0]):ao(s)}function oo(e){return Array.isArray(e)?1+oo(e[0]):-1}function xo(e){e instanceof L.Polyline&&(e=e.toGeoJSON(15));let i=no(e),r=oo(i),a=[];return r>1?i.forEach(s=>{a.push(vo(s))}):a.push(e),a}function ko(e){let i=[];return e.eachLayer(r=>{i.push(no(r.toGeoJSON(15)))}),Lo(i)}At.Cut=At.Polygon.extend({initialize(e){this._map=e,this._shape="Cut",this.toolbarButtonName="cutPolygon"},_finishShape(){if(this._editedLayers=[],!this.options.allowSelfIntersection&&(this._handleSelfIntersection(!0,this._layer.getLatLngs()[0]),this._doesSelfIntersect)||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;let e=this._layer.getLatLngs();if(e.length<=2)return;let i=L.polygon(e,this.options.pathOptions);i._latlngInfos=this._layer._latlngInfo,this.cut(i),this._cleanupSnapping(),this._otherSnapLayers.splice(this._tempSnapLayerIndex,1),delete this._tempSnapLayerIndex,this._editedLayers.forEach(({layer:r,originalLayer:a})=>{this._fireCut(a,r,a),this._fireCut(this._map,r,a),a.pm._fireEdit()}),this._editedLayers=[],this.disable(),this.options.continueDrawing&&this.enable()},cut(e){let i=this._map._layers,r=e._latlngInfos||[];Object.keys(i).map(a=>i[a]).filter(a=>a.pm).filter(a=>!a._pmTempLayer).filter(a=>!L.PM.optIn&&!a.options.pmIgnore||L.PM.optIn&&a.options.pmIgnore===!1).filter(a=>a instanceof L.Polyline).filter(a=>a!==e).filter(a=>a.pm.options.allowCutting).filter(a=>this.options.layersToCut&&L.Util.isArray(this.options.layersToCut)&&this.options.layersToCut.length>0?this.options.layersToCut.indexOf(a)>-1:!0).filter(a=>!this._layerGroup.hasLayer(a)).filter(a=>{try{let s=!!we(e.toGeoJSON(15),a.toGeoJSON(15)).features.length>0;return s||a instanceof L.Polyline&&!(a instanceof L.Polygon)?s:!!bo(e.toGeoJSON(15),a.toGeoJSON(15))}catch{return a instanceof L.Polygon&&console.error("You can't cut polygons with self-intersections"),!1}}).forEach(a=>{let s;if(a instanceof L.Polygon){s=L.polygon(a.getLatLngs());let p=s.getLatLngs();r.forEach(_=>{if(_&&_.snapInfo){let{latlng:k}=_,w=this._calcClosestLayer(k,[s]);if(w&&w.segment&&w.distance<this.options.snapDistance){let{segment:z}=w;if(z&&z.length===2){let{indexPath:A,parentPath:V,newIndex:q}=L.PM.Utils._getIndexFromSegment(p,z);(A.length>1?(0,Xr.default)(p,V):p).splice(q,0,k)}}}})}else s=a;let l=this._cutLayer(e,s),h=L.geoJSON(l,a.options);h.getLayers().length===1&&([h]=h.getLayers()),this._setPane(h,"layerPane");let d=h.addTo(this._map.pm._getContainingLayer());if(d.pm.enable(a.pm.options),d.pm.disable(),a._pmTempLayer=!0,e._pmTempLayer=!0,a.remove(),a.removeFrom(this._map.pm._getContainingLayer()),e.remove(),e.removeFrom(this._map.pm._getContainingLayer()),d.getLayers&&d.getLayers().length===0&&this._map.pm.removeLayer({target:d}),d instanceof L.LayerGroup?(d.eachLayer(p=>{this._addDrawnLayerProp(p)}),this._addDrawnLayerProp(d)):this._addDrawnLayerProp(d),this.options.layersToCut&&L.Util.isArray(this.options.layersToCut)&&this.options.layersToCut.length>0){let p=this.options.layersToCut.indexOf(a);p>-1&&this.options.layersToCut.splice(p,1)}this._editedLayers.push({layer:d,originalLayer:a})})},_cutLayer(e,i){let r=L.geoJSON(),a;if(i instanceof L.Polygon)a=Co(i.toGeoJSON(15),e.toGeoJSON(15));else{let s=xo(i);s.forEach(l=>{let h=Ua(l,e.toGeoJSON(15)),d;h&&h.features.length>0?d=L.geoJSON(h):d=L.geoJSON(l),d.getLayers().forEach(p=>{Ha(e.toGeoJSON(15),p.toGeoJSON(15))||p.addTo(r)})}),s.length>1?a=ko(r):a=r.toGeoJSON(15)}return a},_change:L.Util.falseFn}),At.Text=At.extend({initialize(e){this._map=e,this._shape="Text",this.toolbarButtonName="drawText"},enable(e){L.Util.setOptions(this,e),this._enabled=!0,this._map.on("click",this._createMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!0),this._hintMarker=L.marker(this._map.getCenter(),{interactive:!1,zIndexOffset:100,icon:L.divIcon({className:"marker-icon cursor-marker"})}),this._setPane(this._hintMarker,"vertexPane"),this._hintMarker._pmTempLayer=!0,this._hintMarker.addTo(this._map),this.options.cursorMarker&&L.DomUtil.addClass(this._hintMarker._icon,"visible"),this.options.tooltips&&this._hintMarker.bindTooltip(pt("tooltips.placeText"),{permanent:!0,offset:L.point(0,10),direction:"bottom",opacity:.8}).openTooltip(),this._layer=this._hintMarker,this._map.on("mousemove",this._syncHintMarker,this),this._map.getContainer().classList.add("geoman-draw-cursor"),this._fireDrawStart(),this._setGlobalDrawMode()},disable(){this._enabled&&(this._enabled=!1,this._map.off("click",this._createMarker,this),this._hintMarker?.remove(),this._map.getContainer().classList.remove("geoman-draw-cursor"),this._map.off("mousemove",this._syncHintMarker,this),this._map.pm.Toolbar.toggleButton(this.toolbarButtonName,!1),this.options.snappable&&this._cleanupSnapping(),this._fireDrawEnd(),this._setGlobalDrawMode())},enabled(){return this._enabled},toggle(e){this.enabled()?this.disable():this.enable(e)},_syncHintMarker(e){if(this._hintMarker.setLatLng(e.latlng),this.options.snappable){let i=e;i.target=this._hintMarker,this._handleSnapping(i)}},_createMarker(e){if(!e.latlng||this.options.requireSnapToFinish&&!this._hintMarker._snapped&&!this._isFirstLayer())return;this._hintMarker._snapped||this._hintMarker.setLatLng(e.latlng);let i=this._hintMarker.getLatLng();if(this.textArea=this._createTextArea(),this.options.textOptions?.className){let s=this.options.textOptions.className.split(" ");this.textArea.classList.add(...s)}let r=this._createTextIcon(this.textArea),a=new L.Marker(i,{textMarker:!0,_textMarkerOverPM:!0,icon:r});if(this._setPane(a,"markerPane"),this._finishLayer(a),a.pm||(a.options.draggable=!1),a.addTo(this._map.pm._getContainingLayer()),a.pm){a.pm.textArea=this.textArea,L.setOptions(a.pm,{removeIfEmpty:this.options.textOptions?.removeIfEmpty??!0});let s=this.options.textOptions?.focusAfterDraw??!0;a.pm._createTextMarker(s),this.options.textOptions?.text&&a.pm.setText(this.options.textOptions.text)}this._fireCreate(a),this._cleanupSnapping(),this.disable(),this.options.continueDrawing&&this.enable()},_createTextArea(){let e=document.createElement("textarea");return e.readOnly=!0,e.classList.add("pm-textarea","pm-disabled"),e},_createTextIcon(e){return L.divIcon({className:"pm-text-marker",html:e})}});var Mo={enableLayerDrag(){if(!this.options.draggable||!this._layer._map)return;this.disable(),this._layerDragEnabled=!0,this._map||(this._map=this._layer._map),(this._layer instanceof L.Marker||this._layer instanceof L.ImageOverlay)&&L.DomEvent.on(this._getDOMElem(),"dragstart",this._stopDOMImageDrag),this._layer.dragging&&this._layer.dragging.disable(),this._tempDragCoord=null,ei(this._layer)instanceof L.Canvas?(this._layer.on("mouseout",this.removeDraggingClass,this),this._layer.on("mouseover",this.addDraggingClass,this)):this.addDraggingClass(),this._originalMapDragState=this._layer._map.dragging._enabled,this._safeToCacheDragState=!0;let e=this._getDOMElem();e&&(ei(this._layer)instanceof L.Canvas?(this._layer.on("touchstart mousedown",this._dragMixinOnMouseDown,this),this._map.pm._addTouchEvents(e)):L.DomEvent.on(e,"touchstart mousedown",this._simulateMouseDownEvent,this)),this._fireDragEnable()},disableLayerDrag(){this._layerDragEnabled=!1,ei(this._layer)instanceof L.Canvas?(this._layer.off("mouseout",this.removeDraggingClass,this),this._layer.off("mouseover",this.addDraggingClass,this)):this.removeDraggingClass(),this._originalMapDragState&&this._dragging&&this._map.dragging.enable(),this._safeToCacheDragState=!1,this._layer.dragging&&this._layer.dragging.disable();let e=this._getDOMElem();e&&(ei(this._layer)instanceof L.Canvas?(this._layer.off("touchstart mousedown",this._dragMixinOnMouseDown,this),this._map.pm._removeTouchEvents(e)):L.DomEvent.off(e,"touchstart mousedown",this._simulateMouseDownEvent,this)),this._layerDragged&&this._fireUpdate(),this._layerDragged=!1,this._fireDragDisable()},dragging(){return this._dragging},layerDragEnabled(){return!!this._layerDragEnabled},_simulateMouseDownEvent(e){let i=e.touches?e.touches[0]:e,r={originalEvent:i,target:this._layer};return r.containerPoint=this._map.mouseEventToContainerPoint(i),r.latlng=this._map.containerPointToLatLng(r.containerPoint),this._dragMixinOnMouseDown(r),!1},_simulateMouseMoveEvent(e){let i=e.touches?e.touches[0]:e,r={originalEvent:i,target:this._layer};return r.containerPoint=this._map.mouseEventToContainerPoint(i),r.latlng=this._map.containerPointToLatLng(r.containerPoint),this._dragMixinOnMouseMove(r),!1},_simulateMouseUpEvent(e){let i={originalEvent:e.touches?e.touches[0]:e,target:this._layer};return e.type.indexOf("touch")===-1&&(i.containerPoint=this._map.mouseEventToContainerPoint(e),i.latlng=this._map.containerPointToLatLng(i.containerPoint)),this._dragMixinOnMouseUp(i),!1},_dragMixinOnMouseDown(e){if(e.originalEvent.button>0)return;this._overwriteEventIfItComesFromMarker(e);let i=e._fromLayerSync,r=this._syncLayers("_dragMixinOnMouseDown",e);if(this._layer instanceof L.Marker&&(this.options.snappable&&!i&&!r?this._initSnappableMarkers():this._disableSnapping()),this._layer instanceof L.CircleMarker){let a="resizeableCircleMarker";this._layer instanceof L.Circle&&(a="resizableCircle"),this.options.snappable&&!i&&!r?this._layer.pm.options[a]||this._initSnappableMarkersDrag():this._layer.pm.options[a]?this._layer.pm._disableSnapping():this._layer.pm._disableSnappingDrag()}this._safeToCacheDragState&&(this._originalMapDragState=this._layer._map.dragging._enabled,this._safeToCacheDragState=!1),this._tempDragCoord=e.latlng,L.DomEvent.on(this._map.getContainer(),"touchend mouseup",this._simulateMouseUpEvent,this),L.DomEvent.on(this._map.getContainer(),"touchmove mousemove",this._simulateMouseMoveEvent,this)},_dragMixinOnMouseMove(e){this._overwriteEventIfItComesFromMarker(e);let i=this._getDOMElem();this._syncLayers("_dragMixinOnMouseMove",e),this._dragging||(this._dragging=!0,L.DomUtil.addClass(i,"leaflet-pm-dragging"),this._layer instanceof L.Marker||this._layer.bringToFront(),this._originalMapDragState&&this._map.dragging.disable(),this._fireDragStart()),this._tempDragCoord||(this._tempDragCoord=e.latlng),this._onLayerDrag(e),this._layer instanceof L.CircleMarker&&this._layer.pm._updateHiddenPolyCircle()},_dragMixinOnMouseUp(e){let i=this._getDOMElem();return this._syncLayers("_dragMixinOnMouseUp",e),this._originalMapDragState&&this._map.dragging.enable(),this._safeToCacheDragState=!0,L.DomEvent.off(this._map.getContainer(),"touchmove mousemove",this._simulateMouseMoveEvent,this),L.DomEvent.off(this._map.getContainer(),"touchend mouseup",this._simulateMouseUpEvent,this),this._dragging?(this._layer instanceof L.CircleMarker&&this._layer.pm._updateHiddenPolyCircle(),this._layerDragged=!0,window.setTimeout(()=>{this._dragging=!1,i&&L.DomUtil.removeClass(i,"leaflet-pm-dragging"),this._fireDragEnd(),this._fireEdit(),this._layerEdited=!0},10),!0):!1},_onLayerDrag(e){let{latlng:i}=e,r={lat:i.lat-this._tempDragCoord.lat,lng:i.lng-this._tempDragCoord.lng},a=s=>s.map(l=>{if(Array.isArray(l))return a(l);let h={lat:l.lat+r.lat,lng:l.lng+r.lng};return(l.alt||l.alt===0)&&(h.alt=l.alt),h});if(this._layer instanceof L.Circle&&this._layer.options.resizableCircle||this._layer instanceof L.CircleMarker&&this._layer.options.resizeableCircleMarker){let s=a([this._layer.getLatLng()]);this._layer.setLatLng(s[0]),this._fireChange(this._layer.getLatLng(),"Edit")}else if(this._layer instanceof L.CircleMarker||this._layer instanceof L.Marker){let s=this._layer.getLatLng();this._layer._snapped&&(s=this._layer._orgLatLng);let l=a([s]);this._layer.setLatLng(l[0]),this._fireChange(this._layer.getLatLng(),"Edit")}else if(this._layer instanceof L.ImageOverlay){let s=a([this._layer.getBounds().getNorthWest(),this._layer.getBounds().getSouthEast()]);this._layer.setBounds(s),this._fireChange(this._layer.getBounds(),"Edit")}else{let s=a(this._layer.getLatLngs());this._layer.setLatLngs(s),this._fireChange(this._layer.getLatLngs(),"Edit")}this._tempDragCoord=i,e.layer=this._layer,this._fireDrag(e)},addDraggingClass(){let e=this._getDOMElem();e&&L.DomUtil.addClass(e,"leaflet-pm-draggable")},removeDraggingClass(){let e=this._getDOMElem();e&&L.DomUtil.removeClass(e,"leaflet-pm-draggable")},_getDOMElem(){let e=null;return this._layer._path?e=this._layer._path:this._layer._renderer&&this._layer._renderer._container?e=this._layer._renderer._container:this._layer._image?e=this._layer._image:this._layer._icon&&(e=this._layer._icon),e},_overwriteEventIfItComesFromMarker(e){e.target.getLatLng&&(!e.target._radius||e.target._radius<=10)&&(e.containerPoint=this._map.mouseEventToContainerPoint(e.originalEvent),e.latlng=this._map.containerPointToLatLng(e.containerPoint))},_syncLayers(e,i){if(this.enabled())return!1;if(!i._fromLayerSync&&this._layer===i.target&&this.options.syncLayersOnDrag){i._fromLayerSync=!0;let r=[];if(L.Util.isArray(this.options.syncLayersOnDrag))r=this.options.syncLayersOnDrag,this.options.syncLayersOnDrag.forEach(a=>{a instanceof L.LayerGroup&&(r=r.concat(a.pm.getLayers(!0)))});else if(this.options.syncLayersOnDrag===!0&&this._parentLayerGroup)for(let a in this._parentLayerGroup){let s=this._parentLayerGroup[a];s.pm&&(r=s.pm.getLayers(!0))}return L.Util.isArray(r)&&r.length>0&&(r=r.filter(a=>!!a.pm).filter(a=>!!a.pm.options.draggable),r.forEach(a=>{a!==this._layer&&a.pm[e]&&(a._snapped=!1,a.pm[e](i))})),r.length>0}return!1},_stopDOMImageDrag(e){return e.preventDefault(),!1}},wo=Mo,Eo=rt(bt());function Bo(e,i,r,a){return r.unproject(i.transform(r.project(e,a)),a)}function so(e,i,r){let a=r.getMaxZoom();if(a===1/0&&(a=r.getZoom()),L.Util.isArray(e)){let s=[];return e.forEach(l=>{s.push(so(l,i,r))}),s}return e instanceof L.LatLng?Bo(e,i,r,a):null}function sn(e,i){i instanceof L.Layer&&(i=i.getLatLng());let r=e.getMaxZoom();return r===1/0&&(r=e.getZoom()),e.project(i,r)}function aa(e,i){let r=e.getMaxZoom();return r===1/0&&(r=e.getZoom()),e.unproject(i,r)}var Po={_onRotateStart(e){this._preventRenderingMarkers(!0),this._rotationOriginLatLng=this._getRotationCenter().clone(),this._rotationOriginPoint=sn(this._map,this._rotationOriginLatLng),this._rotationStartPoint=sn(this._map,e.target.getLatLng()),this._initialRotateLatLng=De(this._layer),this._startAngle=this.getAngle();let i=De(this._rotationLayer,this._rotationLayer.pm._rotateOrgLatLng);this._fireRotationStart(this._rotationLayer,i),this._fireRotationStart(this._map,i)},_onRotate(e){let i=sn(this._map,e.target.getLatLng()),r=this._rotationStartPoint,a=this._rotationOriginPoint,s=Math.atan2(i.y-a.y,i.x-a.x)-Math.atan2(r.y-a.y,r.x-a.x);this._layer.setLatLngs(this._rotateLayer(s,this._initialRotateLatLng,this._rotationOriginLatLng,L.PM.Matrix.init(),this._map));let l=this;function h(k,w=[],z=-1){if(z>-1&&w.push(z),L.Util.isArray(k[0]))k.forEach((A,V)=>h(A,w.slice(),V));else{let A=(0,Eo.default)(l._markers,w);k.forEach((V,q)=>{A[q].setLatLng(V)})}}h(this._layer.getLatLngs());let d=De(this._rotationLayer);this._rotationLayer.setLatLngs(this._rotateLayer(s,this._rotationLayer.pm._rotateOrgLatLng,this._rotationOriginLatLng,L.PM.Matrix.init(),this._map));let p=s*180/Math.PI;p=p<0?p+360:p;let _=p+this._startAngle;this._setAngle(_),this._rotationLayer.pm._setAngle(_),this._fireRotation(this._rotationLayer,p,d),this._fireRotation(this._map,p,d),this._rotationLayer.pm._fireChange(this._rotationLayer.getLatLngs(),"Rotation")},_onRotateEnd(){let e=this._startAngle;delete this._rotationOriginLatLng,delete this._rotationOriginPoint,delete this._rotationStartPoint,delete this._initialRotateLatLng,delete this._startAngle;let i=De(this._rotationLayer,this._rotationLayer.pm._rotateOrgLatLng);this._rotationLayer.pm._rotateOrgLatLng=De(this._rotationLayer),this._fireRotationEnd(this._rotationLayer,e,i),this._fireRotationEnd(this._map,e,i),this._rotationLayer.pm._fireEdit(this._rotationLayer,"Rotation"),this._preventRenderingMarkers(!1),this._layerRotated=!0},_rotateLayer(e,i,r,a,s){let l=sn(s,r);return this._matrix=a.clone().rotate(e,l).flip(),so(i,this._matrix,s)},_setAngle(e){e=e<0?e+360:e,this._angle=e%360},_getRotationCenter(){if(this._rotationCenter)return this._rotationCenter;let e=L.polygon(this._layer.getLatLngs(),{stroke:!1,fill:!1,pmIgnore:!0}).addTo(this._layer._map),i=e.getCenter();return e.removeFrom(this._layer._map),i},enableRotate(){if(!this.options.allowRotation){this.disableRotate();return}this.rotateEnabled()&&this.disableRotate(),this._layer instanceof L.Rectangle&&this._angle===void 0&&this.setInitAngle(Rn(this._layer._map,this._layer.getLatLngs()[0][0],this._layer.getLatLngs()[0][1])||0);let e={fill:!1,stroke:!1,pmIgnore:!1,snapIgnore:!0};this._rotatePoly=L.polygon(this._layer.getLatLngs(),e),this._rotatePoly._pmTempLayer=!0,this._rotatePoly.addTo(this._layer._map),this._rotatePoly.pm._setAngle(this.getAngle()),this._rotatePoly.pm.setRotationCenter(this.getRotationCenter()),this._rotatePoly.pm.setOptions(this._layer._map.pm.getGlobalOptions()),this._rotatePoly.pm.setOptions({rotate:!0,snappable:!1,hideMiddleMarkers:!0}),this._rotatePoly.pm._rotationLayer=this._layer,this._rotatePoly.pm.enable(),this._rotateOrgLatLng=De(this._layer),this._rotateEnabled=!0,this._layer.on("remove",this.disableRotate,this),this._fireRotationEnable(this._layer),this._fireRotationEnable(this._layer._map)},disableRotate(){this.rotateEnabled()&&(this._rotatePoly.pm._layerRotated&&this._fireUpdate(),this._rotatePoly.pm._layerRotated=!1,this._rotatePoly.pm.disable(),this._rotatePoly.remove(),this._rotatePoly.pm.setOptions({rotate:!1}),this._rotatePoly=void 0,this._rotateOrgLatLng=void 0,this._layer.off("remove",this.disableRotate,this),this._rotateEnabled=!1,this._fireRotationDisable(this._layer),this._fireRotationDisable(this._layer._map))},rotateEnabled(){return!!this._rotateEnabled},rotateLayer(e){let i=this.getAngle(),r=this._layer.getLatLngs(),a=e*(Math.PI/180);this._layer.setLatLngs(this._rotateLayer(a,this._layer.getLatLngs(),this._getRotationCenter(),L.PM.Matrix.init(),this._layer._map)),this._rotateOrgLatLng=L.polygon(this._layer.getLatLngs()).getLatLngs(),this._setAngle(this.getAngle()+e),this.rotateEnabled()&&this._rotatePoly&&this._rotatePoly.pm.enabled()&&(this._rotatePoly.setLatLngs(this._rotateLayer(a,this._rotatePoly.getLatLngs(),this._getRotationCenter(),L.PM.Matrix.init(),this._rotatePoly._map)),this._rotatePoly.pm._initMarkers());let s=this.getAngle()-i;s=s<0?s+360:s,this._startAngle=i,this._fireRotation(this._layer,s,r,this._layer),this._fireRotation(this._map||this._layer._map,s,r,this._layer),delete this._startAngle,this._fireChange(this._layer.getLatLngs(),"Rotation")},rotateLayerToAngle(e){let i=e-this.getAngle();this.rotateLayer(i)},getAngle(){return this._angle||0},setInitAngle(e){this._setAngle(e)},getRotationCenter(){return this._getRotationCenter()},setRotationCenter(e){this._rotationCenter=e,this._rotatePoly&&this._rotatePoly.pm.setRotationCenter(e)}},To=Po,Do=L.Class.extend({includes:[wo,Rr,To,vi],options:{snappable:!0,snapDistance:20,allowSelfIntersection:!0,allowSelfIntersectionEdit:!1,preventMarkerRemoval:!1,removeLayerBelowMinVertexCount:!0,limitMarkersToCount:-1,hideMiddleMarkers:!1,snapSegment:!0,syncLayersOnDrag:!1,draggable:!0,allowEditing:!0,allowRemoval:!0,allowCutting:!0,allowRotation:!0,addVertexOn:"click",removeVertexOn:"contextmenu",removeVertexValidation:void 0,addVertexValidation:void 0,moveVertexValidation:void 0,resizeableCircleMarker:!1,resizableCircle:!0},setOptions(e){L.Util.setOptions(this,e)},getOptions(){return this.options},applyOptions(){},isPolygon(){return this._layer instanceof L.Polygon},getShape(){return this._shape},_setPane(e,i){i==="layerPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.layerPane||"overlayPane":i==="vertexPane"?e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.vertexPane||"markerPane":i==="markerPane"&&(e.options.pane=this._map.pm.globalOptions.panes&&this._map.pm.globalOptions.panes.markerPane||"markerPane")},remove(){(this._map||this._layer._map).pm.removeLayer({target:this._layer})},_vertexValidation(e,i){let r=i.target,a={layer:this._layer,marker:r,event:i},s="";return e==="move"?s="moveVertexValidation":e==="add"?s="addVertexValidation":e==="remove"&&(s="removeVertexValidation"),this.options[s]&&typeof this.options[s]=="function"&&!this.options[s](a)?(e==="move"&&(r._cancelDragEventChain=r.getLatLng()),!1):(r._cancelDragEventChain=null,!0)},_vertexValidationDrag(e){return e._cancelDragEventChain?(e._latlng=e._cancelDragEventChain,e.update(),!1):!0},_vertexValidationDragEnd(e){return e._cancelDragEventChain?(e._cancelDragEventChain=null,!1):!0}}),Gt=Do;Gt.LayerGroup=L.Class.extend({initialize(e){this._layerGroup=e,this._layers=this.getLayers(),this._getMap(),this._layers.forEach(a=>this._initLayer(a));let i=a=>{if(a.layer._pmTempLayer)return;this._layers=this.getLayers();let s=this._layers.filter(l=>!l.pm._parentLayerGroup||!(this._layerGroup._leaflet_id in l.pm._parentLayerGroup));s.forEach(l=>{this._initLayer(l)}),s.length>0&&this._getMap()&&this._getMap().pm.globalEditModeEnabled()&&this.enabled()&&this.enable(this.getOptions())};this._layerGroup.on("layeradd",L.Util.throttle(i,100,this),this),this._layerGroup.on("layerremove",a=>{this._removeLayerFromGroup(a.target)},this);let r=a=>{a.target._pmTempLayer||(this._layers=this.getLayers())};this._layerGroup.on("layerremove",L.Util.throttle(r,100,this),this)},enable(e,i=[]){i.length===0&&(this._layers=this.getLayers()),this._options=e,this._layers.forEach(r=>{r instanceof L.LayerGroup?i.indexOf(r._leaflet_id)===-1&&(i.push(r._leaflet_id),r.pm.enable(e,i)):r.pm.enable(e)})},disable(e=[]){e.length===0&&(this._layers=this.getLayers()),this._layers.forEach(i=>{i instanceof L.LayerGroup?e.indexOf(i._leaflet_id)===-1&&(e.push(i._leaflet_id),i.pm.disable(e)):i.pm.disable()})},enabled(e=[]){return e.length===0&&(this._layers=this.getLayers()),!!this._layers.find(i=>i instanceof L.LayerGroup?e.indexOf(i._leaflet_id)===-1?(e.push(i._leaflet_id),i.pm.enabled(e)):!1:i.pm.enabled())},toggleEdit(e,i=[]){i.length===0&&(this._layers=this.getLayers()),this._options=e,this._layers.forEach(r=>{r instanceof L.LayerGroup?i.indexOf(r._leaflet_id)===-1&&(i.push(r._leaflet_id),r.pm.toggleEdit(e,i)):r.pm.toggleEdit(e)})},_initLayer(e){let i=L.Util.stamp(this._layerGroup);e.pm._parentLayerGroup||(e.pm._parentLayerGroup={}),e.pm._parentLayerGroup[i]=this._layerGroup},_removeLayerFromGroup(e){if(e.pm&&e.pm._layerGroup){let i=L.Util.stamp(this._layerGroup);delete e.pm._layerGroup[i]}},dragging(){return this._layers=this.getLayers(),this._layers?!!this._layers.find(e=>e.pm.dragging()):!1},getOptions(){return this.options},_getMap(){return this._map||this._layers.find(e=>!!e._map)?._map||null},getLayers(e=!1,i=!0,r=!0,a=[]){let s=[];return e?this._layerGroup.getLayers().forEach(l=>{s.push(l),l instanceof L.LayerGroup&&a.indexOf(l._leaflet_id)===-1&&(a.push(l._leaflet_id),s=s.concat(l.pm.getLayers(!0,!0,!0,a)))}):s=this._layerGroup.getLayers(),r&&(s=s.filter(l=>!(l instanceof L.LayerGroup))),i&&(s=s.filter(l=>!!l.pm),s=s.filter(l=>!l._pmTempLayer),s=s.filter(l=>!L.PM.optIn&&!l.options.pmIgnore||L.PM.optIn&&l.options.pmIgnore===!1)),s},setOptions(e,i=[]){i.length===0&&(this._layers=this.getLayers()),this.options=e,this._layers.forEach(r=>{r.pm&&(r instanceof L.LayerGroup?i.indexOf(r._leaflet_id)===-1&&(i.push(r._leaflet_id),r.pm.setOptions(e,i)):r.pm.setOptions(e))})}}),Gt.Marker=Gt.extend({_shape:"Marker",initialize(e){this._layer=e,this._enabled=!1,this._layer.on("dragend",this._onDragEnd,this)},enable(e={draggable:!0}){if(L.Util.setOptions(this,e),!this.options.allowEditing||!this._layer._map){this.disable();return}this._map=this._layer._map,this.enabled()&&this.disable(),this.applyOptions(),this._layer.on("remove",this.disable,this),this._enabled=!0,this._fireEnable()},disable(){this.enabled()&&(this.disableLayerDrag(),this._layer.off("remove",this.disable,this),this._layer.off("contextmenu",this._removeMarker,this),this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable(),this._enabled=!1)},enabled(){return this._enabled},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping(),this.options.draggable?this.enableLayerDrag():this.disableLayerDrag(),this.options.preventMarkerRemoval||this._layer.on("contextmenu",this._removeMarker,this)},_removeMarker(e){let i=e.target;i.remove(),this._fireRemove(i),this._fireRemove(this._map,i)},_onDragEnd(){this._fireEdit(),this._layerEdited=!0},_initSnappableMarkers(){let e=this._layer;this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,e.off("pm:drag",this._handleSnapping,this),e.on("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.on("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this),e.on("pm:dragstart",this._unsnap,this)},_disableSnapping(){let e=this._layer;e.off("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this)}});var si=rt(bt()),So={filterMarkerGroup(){this.markerCache=[],this.createCache(),this._layer.on("pm:edit",this.createCache,this),this.applyLimitFilters({}),this.throttledApplyLimitFilters||(this.throttledApplyLimitFilters=L.Util.throttle(this.applyLimitFilters,100,this)),this._layer.on("pm:disable",this._removeMarkerLimitEvents,this),this._layer.on("remove",this._removeMarkerLimitEvents,this),this.options.limitMarkersToCount>-1&&(this._layer.on("pm:vertexremoved",this._initMarkers,this),this._map.on("mousemove",this.throttledApplyLimitFilters,this))},_removeMarkerLimitEvents(){this._map.off("mousemove",this.throttledApplyLimitFilters,this),this._layer.off("pm:edit",this.createCache,this),this._layer.off("pm:disable",this._removeMarkerLimitEvents,this),this._layer.off("pm:vertexremoved",this._initMarkers,this)},createCache(){let e=[...this._markerGroup.getLayers(),...this.markerCache];this.markerCache=e.filter((i,r,a)=>a.indexOf(i)===r)},_removeFromCache(e){let i=this.markerCache.indexOf(e);i>-1&&this.markerCache.splice(i,1)},renderLimits(e){this.markerCache.forEach(i=>{e.includes(i)?this._markerGroup.addLayer(i):this._markerGroup.removeLayer(i)})},applyLimitFilters({latlng:e={lat:0,lng:0}}){if(this._preventRenderMarkers)return;let i=[...this._filterClosestMarkers(e)];this.renderLimits(i)},_filterClosestMarkers(e){let i=[...this.markerCache],r=this.options.limitMarkersToCount;return r===-1?i:(i.sort((a,s)=>{let l=a._latlng.distanceTo(e),h=s._latlng.distanceTo(e);return l-h}),i.filter((a,s)=>r>-1?s<r:!0))},_preventRenderMarkers:!1,_preventRenderingMarkers(e){this._preventRenderMarkers=!!e}},Ao=So;Gt.Line=Gt.extend({includes:[Ao],_shape:"Line",initialize(e){this._layer=e,this._enabled=!1},enable(e){if(L.Util.setOptions(this,e),this._map=this._layer._map,!!this._map){if(!this.options.allowEditing){this.disable();return}this.enabled()&&this.disable(),this._enabled=!0,this._initMarkers(),this.applyOptions(),this._layer.on("remove",this.disable,this),this.options.allowSelfIntersection||this._layer.on("pm:vertexremoved",this._handleSelfIntersectionOnVertexRemoval,this),this.options.allowSelfIntersection?this.cachedColor=void 0:(this._layer.options.color!=="#f00000ff"?(this.cachedColor=this._layer.options.color,this.isRed=!1):this.isRed=!0,this._handleLayerStyle()),this._fireEnable()}},disable(){if(!this.enabled()||this._dragging)return;this._enabled=!1,this._markerGroup.clearLayers(),this._markerGroup.removeFrom(this._map),this._layer.off("remove",this.disable,this),this.options.allowSelfIntersection||this._layer.off("pm:vertexremoved",this._handleSelfIntersectionOnVertexRemoval,this);let e=this._layer._path?this._layer._path:this._layer._renderer._container;L.DomUtil.removeClass(e,"leaflet-pm-draggable"),this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable()},enabled(){return this._enabled},toggleEdit(e){return this.enabled()?this.disable():this.enable(e),this.enabled()},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping()},_initMarkers(){let e=this._map,i=this._layer.getLatLngs();this._markerGroup&&this._markerGroup.clearLayers(),this._markerGroup=new L.FeatureGroup,this._markerGroup._pmTempLayer=!0;let r=a=>{if(Array.isArray(a[0]))return a.map(r,this);let s=a.map(this._createMarker,this);return this.options.hideMiddleMarkers!==!0&&a.map((l,h)=>{let d=this.isPolygon()?(h+1)%a.length:h+1;return this._createMiddleMarker(s[h],s[d])}),s};this._markers=r(i),this.filterMarkerGroup(),e.addLayer(this._markerGroup)},_createMarker(e){let i=new L.Marker(e,{draggable:!0,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._pmTempLayer=!0,this.options.rotate?(i.on("dragstart",this._onRotateStart,this),i.on("drag",this._onRotate,this),i.on("dragend",this._onRotateEnd,this)):(i.on("click",this._onVertexClick,this),i.on("dragstart",this._onMarkerDragStart,this),i.on("move",this._onMarkerDrag,this),i.on("dragend",this._onMarkerDragEnd,this),this.options.preventMarkerRemoval||i.on(this.options.removeVertexOn,this._removeMarker,this)),this._markerGroup.addLayer(i),i},_createMiddleMarker(e,i){if(!e||!i)return!1;let r=L.PM.Utils.calcMiddleLatLng(this._map,e.getLatLng(),i.getLatLng()),a=this._createMarker(r),s=L.divIcon({className:"marker-icon marker-icon-middle"});return a.setIcon(s),a.leftM=e,a.rightM=i,e._middleMarkerNext=a,i._middleMarkerPrev=a,a.on(this.options.addVertexOn,this._onMiddleMarkerClick,this),a.on("movestart",this._onMiddleMarkerMoveStart,this),a},_onMiddleMarkerClick(e){let i=e.target;if(!this._vertexValidation("add",e))return;let r=L.divIcon({className:"marker-icon"});i.setIcon(r),this._addMarker(i,i.leftM,i.rightM)},_onMiddleMarkerMoveStart(e){let i=e.target;if(i.on("moveend",this._onMiddleMarkerMoveEnd,this),!this._vertexValidation("add",e)){i.on("move",this._onMiddleMarkerMovePrevent,this);return}i._dragging=!0,this._addMarker(i,i.leftM,i.rightM)},_onMiddleMarkerMovePrevent(e){let i=e.target;this._vertexValidationDrag(i)},_onMiddleMarkerMoveEnd(e){let i=e.target;if(i.off("move",this._onMiddleMarkerMovePrevent,this),i.off("moveend",this._onMiddleMarkerMoveEnd,this),!this._vertexValidationDragEnd(i))return;let r=L.divIcon({className:"marker-icon"});i.setIcon(r),setTimeout(()=>{delete i._dragging},100)},_addMarker(e,i,r){e.off("movestart",this._onMiddleMarkerMoveStart,this),e.off(this.options.addVertexOn,this._onMiddleMarkerClick,this);let a=e.getLatLng(),s=this._layer._latlngs;delete e.leftM,delete e.rightM;let{indexPath:l,index:h,parentPath:d}=L.PM.Utils.findDeepMarkerIndex(this._markers,i),p=l.length>1?(0,si.default)(s,d):s,_=l.length>1?(0,si.default)(this._markers,d):this._markers;p.splice(h+1,0,a),_.splice(h+1,0,e),this._layer.setLatLngs(s),this.options.hideMiddleMarkers!==!0&&(this._createMiddleMarker(i,e),this._createMiddleMarker(e,r)),this._fireEdit(),this._layerEdited=!0,this._fireChange(this._layer.getLatLngs(),"Edit"),this._fireVertexAdded(e,L.PM.Utils.findDeepMarkerIndex(this._markers,e).indexPath,a),this.options.snappable&&this._initSnappableMarkers()},hasSelfIntersection(){return ii(this._layer.toGeoJSON(15)).features.length>0},_handleSelfIntersectionOnVertexRemoval(){this._handleLayerStyle(!0)&&(this._layer.setLatLngs(this._coordsBeforeEdit),this._coordsBeforeEdit=null,this._initMarkers())},_handleLayerStyle(e){let i=this._layer,r,a;if(this.options.allowSelfIntersection?r=!1:(a=ii(this._layer.toGeoJSON(15)),r=a.features.length>0),r){if(!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this._updateDisabledMarkerStyle(this._markers,!0),this.isRed)return r;e?this._flashLayer():(i.setStyle({color:"#f00000ff"}),this.isRed=!0),this._fireIntersect(a)}else i.setStyle({color:this.cachedColor}),this.isRed=!1,!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this._updateDisabledMarkerStyle(this._markers,!1);return r},_flashLayer(){this.cachedColor||(this.cachedColor=this._layer.options.color),this._layer.setStyle({color:"#f00000ff"}),this.isRed=!0,window.setTimeout(()=>{this._layer.setStyle({color:this.cachedColor}),this.isRed=!1},200)},_updateDisabledMarkerStyle(e,i){e.forEach(r=>{Array.isArray(r)?this._updateDisabledMarkerStyle(r,i):r._icon&&(i&&!this._checkMarkerAllowedToDrag(r)?L.DomUtil.addClass(r._icon,"vertexmarker-disabled"):L.DomUtil.removeClass(r._icon,"vertexmarker-disabled"))})},_removeMarker(e){let i=e.target;if(!this._vertexValidation("remove",e))return;this.options.allowSelfIntersection||(this._coordsBeforeEdit=De(this._layer,this._layer.getLatLngs()));let r=this._layer.getLatLngs(),{indexPath:a,index:s,parentPath:l}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);if(!a)return;let h=a.length>1?(0,si.default)(r,l):r,d=a.length>1?(0,si.default)(this._markers,l):this._markers;if(!this.options.removeLayerBelowMinVertexCount&&(h.length<=2||this.isPolygon()&&h.length<=3)){this._flashLayer();return}h.splice(s,1),this._layer.setLatLngs(r),this.isPolygon()&&h.length<=2&&h.splice(0,h.length);let p=!1;if(h.length<=1&&(h.splice(0,h.length),l.length>1&&a.length>1&&(r=Zi(r)),this._layer.setLatLngs(r),this._initMarkers(),p=!0),Gi(r)||this._layer.remove(),r=Zi(r),this._layer.setLatLngs(r),this._markers=Zi(this._markers),!p&&(d=a.length>1?(0,si.default)(this._markers,l):this._markers,i._middleMarkerPrev&&(this._markerGroup.removeLayer(i._middleMarkerPrev),this._removeFromCache(i._middleMarkerPrev)),i._middleMarkerNext&&(this._markerGroup.removeLayer(i._middleMarkerNext),this._removeFromCache(i._middleMarkerNext)),this._markerGroup.removeLayer(i),this._removeFromCache(i),d)){let _,k;if(this.isPolygon()?(_=(s+1)%d.length,k=(s+(d.length-1))%d.length):(k=s-1<0?void 0:s-1,_=s+1>=d.length?void 0:s+1),_!==k){let w=d[k],z=d[_];this.options.hideMiddleMarkers!==!0&&this._createMiddleMarker(w,z)}d.splice(s,1)}this._fireEdit(),this._layerEdited=!0,this._fireVertexRemoved(i,a),this._fireChange(this._layer.getLatLngs(),"Edit")},updatePolygonCoordsFromMarkerDrag(e){let i=this._layer.getLatLngs(),r=e.getLatLng(),{indexPath:a,index:s,parentPath:l}=L.PM.Utils.findDeepMarkerIndex(this._markers,e);(a.length>1?(0,si.default)(i,l):i).splice(s,1,r),this._layer.setLatLngs(i)},_getNeighborMarkers(e){let{indexPath:i,index:r,parentPath:a}=L.PM.Utils.findDeepMarkerIndex(this._markers,e),s=i.length>1?(0,si.default)(this._markers,a):this._markers,l=(r+1)%s.length,h=(r+(s.length-1))%s.length,d=s[h],p=s[l];return{prevMarker:d,nextMarker:p}},_checkMarkerAllowedToDrag(e){let{prevMarker:i,nextMarker:r}=this._getNeighborMarkers(e),a=L.polyline([i.getLatLng(),e.getLatLng()]),s=L.polyline([e.getLatLng(),r.getLatLng()]),l=we(this._layer.toGeoJSON(15),a.toGeoJSON(15)).features.length,h=we(this._layer.toGeoJSON(15),s.toGeoJSON(15)).features.length;return e.getLatLng()===this._markers[0][0].getLatLng()?h+=1:e.getLatLng()===this._markers[0][this._markers[0].length-1].getLatLng()&&(l+=1),!(l<=2&&h<=2)},_onMarkerDragStart(e){let i=e.target;if(this.cachedColor||(this.cachedColor=this._layer.options.color),!this._vertexValidation("move",e))return;let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDragStart(e,r),this.options.allowSelfIntersection||(this._coordsBeforeEdit=De(this._layer,this._layer.getLatLngs())),!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this.hasSelfIntersection()?this._markerAllowedToDrag=this._checkMarkerAllowedToDrag(i):this._markerAllowedToDrag=null},_onMarkerDrag(e){let i=e.target;if(!this._vertexValidationDrag(i))return;let{indexPath:r,index:a,parentPath:s}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);if(!r)return;if(!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this.hasSelfIntersection()&&this._markerAllowedToDrag===!1){this._layer.setLatLngs(this._coordsBeforeEdit),this._initMarkers(),this._handleLayerStyle();return}this.updatePolygonCoordsFromMarkerDrag(i);let l=r.length>1?(0,si.default)(this._markers,s):this._markers,h=(a+1)%l.length,d=(a+(l.length-1))%l.length,p=i.getLatLng(),_=l[d].getLatLng(),k=l[h].getLatLng();if(i._middleMarkerNext){let w=L.PM.Utils.calcMiddleLatLng(this._map,p,k);i._middleMarkerNext.setLatLng(w)}if(i._middleMarkerPrev){let w=L.PM.Utils.calcMiddleLatLng(this._map,p,_);i._middleMarkerPrev.setLatLng(w)}this.options.allowSelfIntersection||this._handleLayerStyle(),this._fireMarkerDrag(e,r),this._fireChange(this._layer.getLatLngs(),"Edit")},_onMarkerDragEnd(e){let i=e.target;if(!this._vertexValidationDragEnd(i))return;let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i),a=this.hasSelfIntersection();a&&this.options.allowSelfIntersectionEdit&&this._markerAllowedToDrag&&(a=!1);let s=!this.options.allowSelfIntersection&&a;if(this._fireMarkerDragEnd(e,r,s),s){this._layer.setLatLngs(this._coordsBeforeEdit),this._coordsBeforeEdit=null,this._initMarkers(),this.options.snappable&&this._initSnappableMarkers(),this._handleLayerStyle(),this._fireLayerReset(e,r);return}!this.options.allowSelfIntersection&&this.options.allowSelfIntersectionEdit&&this._handleLayerStyle(),this._fireEdit(),this._layerEdited=!0,this._fireChange(this._layer.getLatLngs(),"Edit")},_onVertexClick(e){let i=e.target;if(i._dragging)return;let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireVertexClick(e,r)}}),Gt.Polygon=Gt.Line.extend({_shape:"Polygon",_checkMarkerAllowedToDrag(e){let{prevMarker:i,nextMarker:r}=this._getNeighborMarkers(e),a=L.polyline([i.getLatLng(),e.getLatLng()]),s=L.polyline([e.getLatLng(),r.getLatLng()]),l=we(this._layer.toGeoJSON(15),a.toGeoJSON(15)).features.length,h=we(this._layer.toGeoJSON(15),s.toGeoJSON(15)).features.length;return!(l<=2&&h<=2)}}),Gt.Rectangle=Gt.Polygon.extend({_shape:"Rectangle",_initMarkers(){let e=this._map,i=this._findCorners();this._markerGroup&&this._markerGroup.clearLayers(),this._markerGroup=new L.FeatureGroup,this._markerGroup._pmTempLayer=!0,e.addLayer(this._markerGroup),this._markers=[],this._markers[0]=i.map(this._createMarker,this),[this._cornerMarkers]=this._markers,this._layer.getLatLngs()[0].forEach((r,a)=>{let s=this._cornerMarkers.find(l=>l._index===a);s&&s.setLatLng(r)})},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping(),this._addMarkerEvents()},_createMarker(e,i){let r=new L.Marker(e,{draggable:!0,icon:L.divIcon({className:"marker-icon"})});return this._setPane(r,"vertexPane"),r._origLatLng=e,r._index=i,r._pmTempLayer=!0,r.on("click",this._onVertexClick,this),this._markerGroup.addLayer(r),r},_addMarkerEvents(){this._markers[0].forEach(e=>{e.on("dragstart",this._onMarkerDragStart,this),e.on("drag",this._onMarkerDrag,this),e.on("dragend",this._onMarkerDragEnd,this),this.options.preventMarkerRemoval||e.on("contextmenu",this._removeMarker,this)})},_removeMarker(){return null},_onMarkerDragStart(e){if(!this._vertexValidation("move",e))return;let i=e.target,r=this._cornerMarkers;i._oppositeCornerLatLng=r.find(s=>s._index===(i._index+2)%4).getLatLng(),i._snapped=!1;let{indexPath:a}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDragStart(e,a)},_onMarkerDrag(e){let i=e.target;if(!this._vertexValidationDrag(i)||i._index===void 0)return;this._adjustRectangleForMarkerMove(i);let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDrag(e,r),this._fireChange(this._layer.getLatLngs(),"Edit")},_onMarkerDragEnd(e){let i=e.target;if(!this._vertexValidationDragEnd(i))return;this._cornerMarkers.forEach(a=>{delete a._oppositeCornerLatLng});let{indexPath:r}=L.PM.Utils.findDeepMarkerIndex(this._markers,i);this._fireMarkerDragEnd(e,r),this._fireEdit(),this._layerEdited=!0,this._fireChange(this._layer.getLatLngs(),"Edit")},_adjustRectangleForMarkerMove(e){L.extend(e._origLatLng,e._latlng);let i=L.PM.Utils._getRotatedRectangle(e.getLatLng(),e._oppositeCornerLatLng,this.getAngle(),this._map);this._layer.setLatLngs(i),this._adjustAllMarkers(),this._layer.redraw()},_adjustAllMarkers(){let e=this._layer.getLatLngs()[0];e&&e.length!==4&&e.length>0?(e.forEach((i,r)=>{this._cornerMarkers[r].setLatLng(i)}),this._cornerMarkers.slice(e.length).forEach(i=>{i.setLatLng(e[0])})):!e||!e.length?console.error("The layer has no LatLngs"):this._cornerMarkers.forEach(i=>{i.setLatLng(e[i._index])})},_findCorners(){this._angle===void 0&&this.setInitAngle(Rn(this._map,this._layer.getLatLngs()[0][0],this._layer.getLatLngs()[0][1])||0);let e=this._layer.getLatLngs()[0];return L.PM.Utils._getRotatedRectangle(e[0],e[2],this.getAngle(),this._map||this)}}),Gt.CircleMarker=Gt.extend({_shape:"CircleMarker",initialize(e){this._layer=e,this._enabled=!1,this._minRadiusOption="minRadiusCircleMarker",this._maxRadiusOption="maxRadiusCircleMarker",this._editableOption="resizeableCircleMarker",this._updateHiddenPolyCircle()},enable(e={draggable:!0,snappable:!0}){if(L.Util.setOptions(this,e),this.options.editable&&(this.options.resizeableCircleMarker=this.options.editable,delete this.options.editable),!this.options.allowEditing||!this._layer._map){this.disable();return}this._map=this._layer._map,this.enabled()&&this.disable(),this.applyOptions(),this._layer.on("remove",this.disable,this),this._enabled=!0,this._extendingEnable(),this._updateHiddenPolyCircle(),this._fireEnable()},_extendingEnable(){this._layer.on("pm:dragstart",this._onDragStart,this),this._layer.on("pm:drag",this._onMarkerDrag,this),this._layer.on("pm:dragend",this._onMarkerDragEnd,this)},disable(){this.dragging()||(this._map||(this._map=this._layer._map),this._map&&this.enabled()&&(this.layerDragEnabled()&&this.disableLayerDrag(),this.options[this._editableOption]?(this._helperLayers&&this._helperLayers.clearLayers(),this._map.off("move",this._syncMarkers,this),this._outerMarker.off("drag",this._handleOuterMarkerSnapping,this)):this._map.off("move",this._updateHiddenPolyCircle,this),this._extendingDisable(),this._layer.off("remove",this.disable,this),this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable(),this._enabled=!1))},_extendingDisable(){this._layer.off("contextmenu",this._removeMarker,this)},enabled(){return this._enabled},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},applyOptions(){this.options[this._editableOption]?(this._initMarkers(),this._map.on("move",this._syncMarkers,this),this.options.snappable?(this._initSnappableMarkers(),this._outerMarker.on("drag",this._handleOuterMarkerSnapping,this),this._outerMarker.on("move",this._syncHintLine,this),this._outerMarker.on("move",this._syncCircleRadius,this),this._centerMarker.on("move",this._moveCircle,this)):this._disableSnapping()):(this.options.draggable&&this.enableLayerDrag(),this._map.on("move",this._updateHiddenPolyCircle,this),this.options.snappable?this._initSnappableMarkersDrag():this._disableSnappingDrag()),this._extendingApplyOptions()},_extendingApplyOptions(){this.options.preventMarkerRemoval||this._layer.on("contextmenu",this._removeMarker,this)},_initMarkers(){let e=this._map;this._helperLayers&&this._helperLayers.clearLayers(),this._helperLayers=new L.FeatureGroup,this._helperLayers._pmTempLayer=!0,this._helperLayers.addTo(e);let i=this._layer.getLatLng(),r=this._layer._radius,a=this._getLatLngOnCircle(i,r);this._centerMarker=this._createCenterMarker(i),this._outerMarker=this._createOuterMarker(a),this._markers=[this._centerMarker,this._outerMarker],this._createHintLine(this._centerMarker,this._outerMarker)},_getLatLngOnCircle(e,i){let r=this._map.project(e),a=L.point(r.x+i,r.y);return this._map.unproject(a)},_createHintLine(e,i){let r=e.getLatLng(),a=i.getLatLng();this._hintline=L.polyline([r,a],this.options.hintlineStyle),this._setPane(this._hintline,"layerPane"),this._hintline._pmTempLayer=!0,this._helperLayers.addLayer(this._hintline)},_createCenterMarker(e){let i=this._createMarker(e);return this.options.draggable?L.DomUtil.addClass(i._icon,"leaflet-pm-draggable"):i.dragging.disable(),i},_createOuterMarker(e){let i=this._createMarker(e);return i.on("drag",this._resizeCircle,this),i},_createMarker(e){let i=new L.Marker(e,{draggable:!0,icon:L.divIcon({className:"marker-icon"})});return this._setPane(i,"vertexPane"),i._origLatLng=e,i._pmTempLayer=!0,i.on("dragstart",this._onMarkerDragStart,this),i.on("drag",this._onMarkerDrag,this),i.on("dragend",this._onMarkerDragEnd,this),i.on("click",this._onVertexClick,this),this._helperLayers.addLayer(i),i},_moveCircle(e){if(e.target._cancelDragEventChain)return;let i=this._centerMarker.getLatLng();this._layer.setLatLng(i);let r=this._layer._radius,a=this._getLatLngOnCircle(i,r);this._outerMarker._latlng=a,this._outerMarker.update(),this._syncHintLine(),this._updateHiddenPolyCircle(),this._fireCenterPlaced("Edit"),this._fireChange(this._layer.getLatLng(),"Edit")},_syncMarkers(){let e=this._layer.getLatLng(),i=this._layer._radius,r=this._getLatLngOnCircle(e,i);this._outerMarker.setLatLng(r),this._centerMarker.setLatLng(e),this._syncHintLine(),this._updateHiddenPolyCircle()},_resizeCircle(){this._outerMarker.setLatLng(this._getNewDestinationOfOuterMarker()),this._syncHintLine(),this._syncCircleRadius()},_syncCircleRadius(){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng(),r=this._distanceCalculation(e,i);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._layer.setRadius(this.options[this._minRadiusOption]):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]?this._layer.setRadius(this.options[this._maxRadiusOption]):this._layer.setRadius(r),this._updateHiddenPolyCircle(),this._fireChange(this._layer.getLatLng(),"Edit")},_syncHintLine(){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng();this._hintline.setLatLngs([e,i])},_removeMarker(){this.options[this._editableOption]&&this.disable(),this._layer.remove(),this._fireRemove(this._layer),this._fireRemove(this._map,this._layer)},_onDragStart(){this._map.pm.Draw.CircleMarker._layerIsDragging=!0},_onMarkerDragStart(e){this._vertexValidation("move",e)&&this._fireMarkerDragStart(e)},_onMarkerDrag(e){let i=e.target;i instanceof L.Marker&&!this._vertexValidationDrag(i)||this._fireMarkerDrag(e)},_onMarkerDragEnd(e){this._extedingMarkerDragEnd();let i=e.target;this._vertexValidationDragEnd(i)&&(this.options[this._editableOption]&&(this._fireEdit(),this._layerEdited=!0),this._fireMarkerDragEnd(e))},_extedingMarkerDragEnd(){this._map.pm.Draw.CircleMarker._layerIsDragging=!1},_initSnappableMarkersDrag(){let e=this._layer;this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,e.off("pm:drag",this._handleSnapping,this),e.on("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.on("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this),e.on("pm:dragstart",this._unsnap,this)},_disableSnappingDrag(){let e=this._layer;e.off("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this)},_updateHiddenPolyCircle(){let e=this._layer._map||this._map;if(e){let i=L.PM.Utils.pxRadiusToMeterRadius(this._layer.getRadius(),e,this._layer.getLatLng()),r=L.circle(this._layer.getLatLng(),this._layer.options);r.setRadius(i);let a=e&&e.pm._isCRSSimple();this._hiddenPolyCircle?this._hiddenPolyCircle.setLatLngs(L.PM.Utils.circleToPolygon(r,200,!a).getLatLngs()):this._hiddenPolyCircle=L.PM.Utils.circleToPolygon(r,200,!a),this._hiddenPolyCircle._parentCopy||(this._hiddenPolyCircle._parentCopy=this._layer)}},_getNewDestinationOfOuterMarker(){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng(),r=this._distanceCalculation(e,i);return this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?i=ji(this._map,e,i,this._getMinDistanceInMeter(e)):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&(i=ji(this._map,e,i,this._getMaxDistanceInMeter(e))),i},_handleOuterMarkerSnapping(){if(this._outerMarker._snapped){let e=this._centerMarker.getLatLng(),i=this._outerMarker.getLatLng(),r=this._distanceCalculation(e,i);this.options[this._minRadiusOption]&&r<this.options[this._minRadiusOption]?this._outerMarker.setLatLng(this._outerMarker._orgLatLng):this.options[this._maxRadiusOption]&&r>this.options[this._maxRadiusOption]&&this._outerMarker.setLatLng(this._outerMarker._orgLatLng)}this._outerMarker.setLatLng(this._getNewDestinationOfOuterMarker())},_distanceCalculation(e,i){return this._map.project(e).distanceTo(this._map.project(i))},_getMinDistanceInMeter(e){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._minRadiusOption],this._map,e)},_getMaxDistanceInMeter(e){return L.PM.Utils.pxRadiusToMeterRadius(this.options[this._maxRadiusOption],this._map,e)},_onVertexClick(e){e.target._dragging||this._fireVertexClick(e,void 0)}}),Gt.Circle=Gt.CircleMarker.extend({_shape:"Circle",initialize(e){this._layer=e,this._enabled=!1,this._minRadiusOption="minRadiusCircle",this._maxRadiusOption="maxRadiusCircle",this._editableOption="resizableCircle",this._updateHiddenPolyCircle()},enable(e){L.PM.Edit.CircleMarker.prototype.enable.call(this,e||{})},_extendingEnable(){},_extendingDisable(){this._layer.off("remove",this.disable,this);let e=this._layer._path?this._layer._path:this._layer._renderer._container;L.DomUtil.removeClass(e,"leaflet-pm-draggable")},_extendingApplyOptions(){},_syncMarkers(){},_removeMarker(){},_onDragStart(){},_extedingMarkerDragEnd(){},_updateHiddenPolyCircle(){let e=this._map&&this._map.pm._isCRSSimple();this._hiddenPolyCircle?this._hiddenPolyCircle.setLatLngs(L.PM.Utils.circleToPolygon(this._layer,200,!e).getLatLngs()):this._hiddenPolyCircle=L.PM.Utils.circleToPolygon(this._layer,200,!e),this._hiddenPolyCircle._parentCopy||(this._hiddenPolyCircle._parentCopy=this._layer)},_distanceCalculation(e,i){return this._map.distance(e,i)},_getMinDistanceInMeter(){return this.options[this._minRadiusOption]},_getMaxDistanceInMeter(){return this.options[this._maxRadiusOption]},_onVertexClick(e){e.target._dragging||this._fireVertexClick(e,void 0)}}),Gt.ImageOverlay=Gt.extend({_shape:"ImageOverlay",initialize(e){this._layer=e,this._enabled=!1},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},enabled(){return this._enabled},enable(e={draggable:!0,snappable:!0}){if(L.Util.setOptions(this,e),this._map=this._layer._map,!!this._map){if(!this.options.allowEditing){this.disable();return}this.enabled()||this.disable(),this.enableLayerDrag(),this._layer.on("remove",this.disable,this),this._enabled=!0,this._otherSnapLayers=this._findCorners(),this._fireEnable()}},disable(){this._dragging||(this._map||(this._map=this._layer._map),this.disableLayerDrag(),this._layer.off("remove",this.disable,this),this.enabled()||(this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable()),this._enabled=!1)},_findCorners(){let e=this._layer.getBounds(),i=e.getNorthWest(),r=e.getNorthEast(),a=e.getSouthEast(),s=e.getSouthWest();return[i,r,a,s]}}),Gt.Text=Gt.extend({_shape:"Text",initialize(e){this._layer=e,this._enabled=!1},enable(e){if(L.Util.setOptions(this,e),!!this.textArea){if(!this.options.allowEditing||!this._layer._map){this.disable();return}this._map=this._layer._map,this.enabled()&&this.disable(),this.applyOptions(),this._safeToCacheDragState=!0,this._focusChange(),this.textArea.readOnly=!1,this.textArea.classList.remove("pm-disabled"),this._layer.on("remove",this.disable,this),L.DomEvent.on(this.textArea,"input",this._autoResize,this),L.DomEvent.on(this.textArea,"focus",this._focusChange,this),L.DomEvent.on(this.textArea,"blur",this._focusChange,this),this._layer.on("dblclick",L.DomEvent.stop),L.DomEvent.off(this.textArea,"mousedown",this._preventTextSelection),this._enabled=!0,this._fireEnable()}},disable(){if(!this.enabled())return;this._layer.off("remove",this.disable,this),L.DomEvent.off(this.textArea,"input",this._autoResize,this),L.DomEvent.off(this.textArea,"focus",this._focusChange,this),L.DomEvent.off(this.textArea,"blur",this._focusChange,this),L.DomEvent.off(document,"click",this._documentClick,this),this._focusChange(),this.textArea.readOnly=!0,this.textArea.classList.add("pm-disabled");let e=document.activeElement;this.textArea.focus(),this.textArea.selectionStart=0,this.textArea.selectionEnd=0,L.DomEvent.on(this.textArea,"mousedown",this._preventTextSelection),e.focus(),this._disableOnBlurActive=!1,this._layerEdited&&this._fireUpdate(),this._layerEdited=!1,this._fireDisable(),this._enabled=!1},enabled(){return this._enabled},toggleEdit(e){this.enabled()?this.disable():this.enable(e)},applyOptions(){this.options.snappable?this._initSnappableMarkers():this._disableSnapping()},_initSnappableMarkers(){let e=this._layer;this.options.snapDistance=this.options.snapDistance||30,this.options.snapSegment=this.options.snapSegment===void 0?!0:this.options.snapSegment,e.off("pm:drag",this._handleSnapping,this),e.on("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.on("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this),e.on("pm:dragstart",this._unsnap,this)},_disableSnapping(){let e=this._layer;e.off("pm:drag",this._handleSnapping,this),e.off("pm:dragend",this._cleanupSnapping,this),e.off("pm:dragstart",this._unsnap,this)},_autoResize(){this.textArea.style.height="1px",this.textArea.style.width="1px";let e=this.textArea.scrollHeight>21?this.textArea.scrollHeight:21,i=this.textArea.scrollWidth>16?this.textArea.scrollWidth:16;this.textArea.style.height=`${e}px`,this.textArea.style.width=`${i}px`,this._layer.options.text=this.getText(),this._fireTextChange(this.getText())},_disableOnBlur(){this._disableOnBlurActive=!0,setTimeout(()=>{this.enabled()&&L.DomEvent.on(document,"click",this._documentClick,this)},100)},_documentClick(e){e.target!==this.textArea&&(this.disable(),!this.getText()&&this.options.removeIfEmpty&&this.remove())},_focusChange(e={}){let i=this._hasFocus;this._hasFocus=e.type==="focus",!i!=!this._hasFocus&&(this._hasFocus?(this._applyFocus(),this._focusText=this.getText(),this._fireTextFocus()):(this._removeFocus(),this._fireTextBlur(),this._focusText!==this.getText()&&(this._fireEdit(),this._layerEdited=!0)))},_applyFocus(){this.textArea.classList.add("pm-hasfocus"),this._map.dragging&&(this._safeToCacheDragState&&(this._originalMapDragState=this._map.dragging._enabled,this._safeToCacheDragState=!1),this._map.dragging.disable())},_removeFocus(){this._map.dragging&&(this._originalMapDragState&&this._map.dragging.enable(),this._safeToCacheDragState=!0),this.textArea.classList.remove("pm-hasfocus")},focus(){if(!this.enabled())throw new TypeError("Layer is not enabled");this.textArea.focus()},blur(){if(!this.enabled())throw new TypeError("Layer is not enabled");this.textArea.blur(),this._disableOnBlurActive&&this.disable()},hasFocus(){return this._hasFocus},getElement(){return this.textArea},setText(e){this.textArea.value=e,this._autoResize()},getText(){return this.textArea.value},_initTextMarker(){if(this.textArea=L.PM.Draw.Text.prototype._createTextArea.call(this),this.options.className){let i=this.options.className.split(" ");this.textArea.classList.add(...i)}let e=L.PM.Draw.Text.prototype._createTextIcon.call(this,this.textArea);this._layer.setIcon(e),this._layer.once("add",this._createTextMarker,this)},_createTextMarker(e=!1){this._layer.off("add",this._createTextMarker,this),this._layer.getElement().tabIndex=-1,this.textArea.wrap="off",this.textArea.style.overflow="hidden",this.textArea.style.height=L.DomUtil.getStyle(this.textArea,"font-size"),this.textArea.style.width="1px",this._layer.options.text&&this.setText(this._layer.options.text),this._autoResize(),e===!0&&(this.enable(),this.focus(),this._disableOnBlur())},_preventTextSelection(e){e.preventDefault()}});var Ya=function(e,i,r,a,s,l){this._matrix=[e,i,r,a,s,l]};Ya.init=()=>new L.PM.Matrix(1,0,0,1,0,0),Ya.prototype={transform(e){return this._transform(e.clone())},_transform(e){let i=this._matrix,{x:r,y:a}=e;return e.x=i[0]*r+i[1]*a+i[4],e.y=i[2]*r+i[3]*a+i[5],e},untransform(e){let i=this._matrix;return new L.Point((e.x/i[0]-i[4])/i[0],(e.y/i[2]-i[5])/i[2])},clone(){let e=this._matrix;return new L.PM.Matrix(e[0],e[1],e[2],e[3],e[4],e[5])},translate(e){if(e===void 0)return new L.Point(this._matrix[4],this._matrix[5]);let i,r;return typeof e=="number"?(i=e,r=e):(i=e.x,r=e.y),this._add(1,0,0,1,i,r)},scale(e,i){if(e===void 0)return new L.Point(this._matrix[0],this._matrix[3]);let r,a;return i=i||L.point(0,0),typeof e=="number"?(r=e,a=e):(r=e.x,a=e.y),this._add(r,0,0,a,i.x,i.y)._add(1,0,0,1,-i.x,-i.y)},rotate(e,i){let r=Math.cos(e),a=Math.sin(e);return i=i||new L.Point(0,0),this._add(r,a,-a,r,i.x,i.y)._add(1,0,0,1,-i.x,-i.y)},flip(){return this._matrix[1]*=-1,this._matrix[2]*=-1,this},_add(e,i,r,a,s,l){let h=[[],[],[]],d=this._matrix,p=[[d[0],d[2],d[4]],[d[1],d[3],d[5]],[0,0,1]],_=[[e,r,s],[i,a,l],[0,0,1]],k;e&&e instanceof L.PM.Matrix&&(d=e._matrix,_=[[d[0],d[2],d[4]],[d[1],d[3],d[5]],[0,0,1]]);for(let w=0;w<3;w+=1)for(let z=0;z<3;z+=1){k=0;for(let A=0;A<3;A+=1)k+=p[w][A]*_[A][z];h[w][z]=k}return this._matrix=[h[0][0],h[1][0],h[0][1],h[1][1],h[0][2],h[1][2]],this}};var Oo=Ya,Fo={calcMiddleLatLng(e,i,r){let a=e.project(i),s=e.project(r);return e.unproject(a._add(s)._divideBy(2))},findLayers(e){let i=[];return e.eachLayer(r=>{(r instanceof L.Polyline||r instanceof L.Marker||r instanceof L.Circle||r instanceof L.CircleMarker||r instanceof L.ImageOverlay)&&i.push(r)}),i=i.filter(r=>!!r.pm),i=i.filter(r=>!r._pmTempLayer),i=i.filter(r=>!L.PM.optIn&&!r.options.pmIgnore||L.PM.optIn&&r.options.pmIgnore===!1),i},circleToPolygon(e,i=60,r=!0){let a=e.getLatLng(),s=e.getRadius(),l=Pr(a,s,i,0,r),h=[];for(let d=0;d<l.length;d+=1){let p=[l[d].lat,l[d].lng];h.push(p)}return L.polygon(h,e.options)},disablePopup(e){e.getPopup()&&(e._tempPopupCopy=e.getPopup(),e.unbindPopup())},enablePopup(e){e._tempPopupCopy&&(e.bindPopup(e._tempPopupCopy),delete e._tempPopupCopy)},_fireEvent(e,i,r,a=!1){e.fire(i,r,a);let{groups:s}=this.getAllParentGroups(e);s.forEach(l=>{l.fire(i,r,a)})},getAllParentGroups(e){let i=[],r=[],a=s=>{for(let l in s._eventParents)if(i.indexOf(l)===-1){i.push(l);let h=s._eventParents[l];r.push(h),a(h)}};return!e._pmLastGroupFetch||!e._pmLastGroupFetch.time||new Date().getTime()-e._pmLastGroupFetch.time>1e3?(a(e),e._pmLastGroupFetch={time:new Date().getTime(),groups:r,groupIds:i},{groupIds:i,groups:r}):{groups:e._pmLastGroupFetch.groups,groupIds:e._pmLastGroupFetch.groupIds}},createGeodesicPolygon:Pr,getTranslation:pt,findDeepCoordIndex(e,i,r=!0){let a,s=h=>(d,p)=>{let _=h.concat(p);if(r){if(d.lat&&d.lat===i.lat&&d.lng===i.lng)return a=_,!0}else if(d.lat&&L.latLng(d).equals(i))return a=_,!0;return Array.isArray(d)&&d.some(s(_))};e.some(s([]));let l={};return a&&(l={indexPath:a,index:a[a.length-1],parentPath:a.slice(0,a.length-1)}),l},findDeepMarkerIndex(e,i){let r,a=l=>(h,d)=>{let p=l.concat(d);return h._leaflet_id===i._leaflet_id?(r=p,!0):Array.isArray(h)&&h.some(a(p))};e.some(a([]));let s={};return r&&(s={indexPath:r,index:r[r.length-1],parentPath:r.slice(0,r.length-1)}),s},_getIndexFromSegment(e,i){if(i&&i.length===2){let r=this.findDeepCoordIndex(e,i[0]),a=this.findDeepCoordIndex(e,i[1]),s=Math.max(r.index,a.index);return(r.index===0||a.index===0)&&s!==1&&(s+=1),{indexA:r,indexB:a,newIndex:s,indexPath:r.indexPath,parentPath:r.parentPath}}return null},_getRotatedRectangle(e,i,r,a){let s=sn(a,e),l=sn(a,i),h=r*Math.PI/180,d=Math.cos(h),p=Math.sin(h),_=(l.x-s.x)*d+(l.y-s.y)*p,k=(l.y-s.y)*d-(l.x-s.x)*p,w=_*d+s.x,z=_*p+s.y,A=-k*p+s.x,V=k*d+s.y,q=aa(a,s),X=aa(a,{x:w,y:z}),mt=aa(a,l),x=aa(a,{x:A,y:V});return[q,X,mt,x]},pxRadiusToMeterRadius(e,i,r){let a=i.project(r),s=L.point(a.x+e,a.y);return i.distance(i.unproject(s),r)}},Ro=Fo;L.PM=L.PM||{version:Bt.version,Map:Se,Toolbar:Ra,Draw:At,Edit:Gt,Utils:Ro,Matrix:Oo,activeLang:"en",optIn:!1,initialize(e){this.addInitHooks(e)},setOptIn(e){this.optIn=!!e},addInitHooks(){function e(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Map(this)):this.options.pmIgnore||(this.pm=new L.PM.Map(this)),this.pm&&this.pm.setGlobalOptions({})}L.Map.addInitHook(e);function i(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.LayerGroup(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.LayerGroup(this))}L.LayerGroup.addInitHook(i);function r(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.options.textMarker?(this.pm=new L.PM.Edit.Text(this),this.options._textMarkerOverPM||this.pm._initTextMarker(),delete this.options._textMarkerOverPM):this.pm=new L.PM.Edit.Marker(this)):this.options.pmIgnore||(this.options.textMarker?(this.pm=new L.PM.Edit.Text(this),this.options._textMarkerOverPM||this.pm._initTextMarker(),delete this.options._textMarkerOverPM):this.pm=new L.PM.Edit.Marker(this))}L.Marker.addInitHook(r);function a(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.CircleMarker(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.CircleMarker(this))}L.CircleMarker.addInitHook(a);function s(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Line(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Line(this))}L.Polyline.addInitHook(s);function l(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Polygon(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Polygon(this))}L.Polygon.addInitHook(l);function h(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Rectangle(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Rectangle(this))}L.Rectangle.addInitHook(h);function d(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.Circle(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.Circle(this))}L.Circle.addInitHook(d);function p(){this.pm=void 0,L.PM.optIn?this.options.pmIgnore===!1&&(this.pm=new L.PM.Edit.ImageOverlay(this)):this.options.pmIgnore||(this.pm=new L.PM.Edit.ImageOverlay(this))}L.ImageOverlay.addInitHook(p)},reInitLayer(e){e instanceof L.LayerGroup&&e.eachLayer(i=>{this.reInitLayer(i)}),e.pm||L.PM.optIn&&e.options.pmIgnore!==!1||e.options.pmIgnore||(e instanceof L.Map?e.pm=new L.PM.Map(e):e instanceof L.Marker?e.options.textMarker?(e.pm=new L.PM.Edit.Text(e),e.pm._initTextMarker(),e.pm._createTextMarker(!1)):e.pm=new L.PM.Edit.Marker(e):e instanceof L.Circle?e.pm=new L.PM.Edit.Circle(e):e instanceof L.CircleMarker?e.pm=new L.PM.Edit.CircleMarker(e):e instanceof L.Rectangle?e.pm=new L.PM.Edit.Rectangle(e):e instanceof L.Polygon?e.pm=new L.PM.Edit.Polygon(e):e instanceof L.Polyline?e.pm=new L.PM.Edit.Line(e):e instanceof L.LayerGroup?e.pm=new L.PM.Edit.LayerGroup(e):e instanceof L.ImageOverlay&&(e.pm=new L.PM.Edit.ImageOverlay(e)))}},L.version==="1.7.1"&&L.Canvas.include({_onClick(e){let i=this._map.mouseEventToLayerPoint(e),r,a;for(let s=this._drawFirst;s;s=s.next)r=s.layer,r.options.interactive&&r._containsPoint(i)&&(!(e.type==="click"||e.type==="preclick")||!this._map._draggableMoved(r))&&(a=r);a&&(L.DomEvent.fakeStop(e),this._fireEvent([a],e))}}),L.PM.initialize()})();document.addEventListener("DOMContentLoaded",()=>{let j=($,U,ue)=>({map:null,tile:null,marker:null,rangeCircle:null,drawItems:null,rangeSelectField:null,formRestorationHiddenInput:null,debouncedUpdate:null,debounce:function(W,ct){let F;return function(...rt){let st=()=>{clearTimeout(F),W(...rt)};clearTimeout(F),F=setTimeout(st,ct)}},createMap:function(W){let ct=this;if(this.map=Mt.map(W,U.controls),U.bounds){let Wt=Mt.latLng(U.bounds.sw.lat,U.bounds.sw.lng),rt=Mt.latLng(U.bounds.ne.lat,U.bounds.ne.lng),st=Mt.latLngBounds(Wt,rt);this.map.setMaxBounds(st),this.map.fitBounds(st),this.map.on("drag",function(){map.panInsideBounds(st,{animate:!1})})}this.map.on("load",()=>{setTimeout(()=>this.map.invalidateSize(!0),0),U.showMarker&&!U.clickable&&this.marker.setLatLng(this.map.getCenter())}),U.draggable||this.map.dragging.disable(),U.clickable&&this.map.on("click",function(Wt){ct.setCoordinates(Wt.latlng)}),this.tile=Mt.tileLayer(U.tilesUrl,{attribution:U.attribution,minZoom:U.minZoom,maxZoom:U.maxZoom,tileSize:U.tileSize,zoomOffset:U.zoomOffset,detectRetina:U.detectRetina}).addTo(this.map),U.showMarker&&(this.marker=Mt.marker(this.getCoordinates(),{icon:this.createMarkerIcon(),draggable:!1,autoPan:!0}).addTo(this.map),this.setMarkerRange(),U.clickable||this.map.on("move",()=>this.setCoordinates(this.map.getCenter()))),U.clickable||this.map.on("moveend",()=>this.updateLocation()),this.map.on("locationfound",function(){ct.map.setZoom(U.controls.zoom)});let F=this.getCoordinates();if(!F.lat&&!F.lng?this.map.locate({setView:!0,maxZoom:U.controls.maxZoom,enableHighAccuracy:!0,watch:!1}):this.map.setView(new Mt.LatLng(F.lat,F.lng)),U.showMyLocationButton&&this.addLocationButton(),U.liveLocation.send&&U.liveLocation.realtime&&setInterval(()=>{this.fetchCurrentLocation()},U.liveLocation.miliseconds),this.map.on("zoomend",function(Wt){ct.setFormRestorationState(!1,ct.map.getZoom())}),U.geoMan.show){this.map.pm.addControls({snappable:U.geoMan.snappable,snapDistance:U.geoMan.snapDistance,position:U.geoMan.position,drawCircleMarker:U.geoMan.drawCircleMarker,rotateMode:U.geoMan.rotateMode,drawRectangle:U.geoMan.drawRectangle,drawText:U.geoMan.drawText,drawMarker:U.geoMan.drawMarker,drawPolygon:U.geoMan.drawPolygon,drawPolyline:U.geoMan.drawPolyline,drawCircle:U.geoMan.drawCircle,editMode:U.geoMan.editMode,dragMode:U.geoMan.dragMode,cutPolygon:U.geoMan.cutPolygon,editPolygon:U.geoMan.editPolygon,deleteLayer:U.geoMan.deleteLayer}),this.drawItems=new Mt.FeatureGroup().addTo(this.map),this.map.on("pm:create",rt=>{if(rt.layer&&rt.layer.pm){if(rt.layer.pm.enable(),rt.shape==="Circle"){let st=rt.layer.getLatLng(),Ft=rt.layer.getRadius();rt.layer.circleData={center:st,radius:Ft}}this.drawItems.addLayer(rt.layer),this.updateGeoJson()}}),this.map.on("pm:edit",rt=>{rt.layer&&rt.layer.getRadius&&(rt.layer.circleData={center:rt.layer.getLatLng(),radius:rt.layer.getRadius()}),this.updateGeoJson()}),this.map.on("pm:remove",rt=>{try{this.drawItems.removeLayer(rt.layer),this.updateGeoJson()}catch(st){console.error("Error during removal of layer:",st)}});let Wt=this.getGeoJson();Wt&&(this.drawItems=Mt.geoJSON(Wt,{pointToLayer:(rt,st)=>{if(rt.properties&&rt.properties.type==="Circle"){let Ft=Mt.circle(st,{radius:rt.properties.radius,color:U.geoMan.color||"#3388ff",fillColor:U.geoMan.filledColor||"#cad9ec",fillOpacity:.4});return Ft.circleData={center:st,radius:rt.properties.radius},Ft}return Mt.circleMarker(st,{radius:15,color:"#3388ff",fillColor:"#3388ff",fillOpacity:.6})},style:function(rt){if(rt.geometry.type==="Polygon")return{color:U.geoMan.color||"#3388ff",fillColor:U.geoMan.filledColor||"blue",weight:2,fillOpacity:.4}},onEachFeature:(rt,st)=>{typeof rt.properties.title<"u"?st.bindPopup(rt.properties.title):rt.geometry.type==="Polygon"?st.bindPopup("Polygon Area"):rt.geometry.type==="Point"&&st.bindPopup("Point Location"),U.geoMan.editable&&(rt.geometry.type==="Polygon"?st.pm.enable({allowSelfIntersection:!1}):rt.geometry.type==="Point"&&st.pm.enable({draggable:!0})),st.on("pm:edit",()=>{this.updateGeoJson()})}}).addTo(this.map),U.geoMan.editable&&this.drawItems.eachLayer(rt=>{rt.pm.enable({allowSelfIntersection:!1})}),this.map.fitBounds(this.drawItems.getBounds()))}},createMarkerIcon(){if(U.markerIconUrl)return Mt.icon({iconUrl:U.markerIconUrl,iconSize:U.markerIconSize,iconAnchor:U.markerIconAnchor,className:U.markerIconClassName});let ct=`<svg xmlns="http://www.w3.org/2000/svg" class="map-icon" fill="${U.markerColor||"#3b82f6"}" width="36" height="36" viewBox="0 0 24 24"><path d="M12 0c-4.198 0-8 3.403-8 7.602 0 4.198 3.469 9.21 8 16.398 4.531-7.188 8-12.2 8-16.398 0-4.199-3.801-7.602-8-7.602zm0 11c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3z"/></svg>`;return Mt.divIcon({html:U.markerHtml||ct,className:U.markerIconClassName,iconSize:U.markerIconSize,iconAnchor:U.markerIconAnchor})},initFormRestoration:function(){this.formRestorationHiddenInput=document.getElementById(U.statePath+"_fmrest"),window.addEventListener("pageshow",W=>{let ct=this.getFormRestorationState();if(ct){let F=new Mt.LatLng(ct.lat,ct.lng);U.zoom=ct.zoom,U.controls.zoom=ct.zoom,this.setCoordinates(F)}})},setFormRestorationState:function(W=null,ct=null){W=W||this.getFormRestorationState()||this.getCoordinates(),this.map&&(W.zoom=ct??this.map.getZoom()),this.formRestorationHiddenInput.value=JSON.stringify(W)},getFormRestorationState:function(){return this.formRestorationHiddenInput.value?JSON.parse(this.formRestorationHiddenInput.value):!1},updateGeoJson:function(){try{let W={type:"FeatureCollection",features:[]};this.drawItems.eachLayer(ct=>{if(ct.getRadius){let F=ct.circleData||{center:ct.getLatLng(),radius:ct.getRadius()};W.features.push({type:"Feature",properties:{type:"Circle",radius:F.radius},geometry:{type:"Point",coordinates:[F.center.lng,F.center.lat]}})}else{let F=ct.toGeoJSON();W.features.push(F)}}),$.set(U.statePath,{...$.get(U.statePath),lat:this.marker?this.marker.getLatLng().lat:this.map.getCenter().lat,lng:this.marker?this.marker.getLatLng().lng:this.map.getCenter().lng,geojson:W},!0)}catch(W){console.error("Error updating GeoJSON:",W)}},getGeoJson:function(){return($.get(U.statePath)??{}).geojson},updateLocation:function(){let W=this.getCoordinates(),ct=this.map.getCenter();U.clickable&&(ct=this.marker.getLatLng());let F=U.minChange||1e-5;(Math.abs(W.lng-ct.lng)>F||Math.abs(W.lat-ct.lat)>F)&&(this.setCoordinates(ct),this.setMarkerRange())},removeMap:function(W){this.marker&&(this.marker.remove(),this.marker=null),this.tile.remove(),this.tile=null,this.map.off(),this.map.remove(),this.map=null},getCoordinates:function(){if(ue)return ue;let W=$.get(U.statePath)??{};return W.hasOwnProperty("lat")&&W.hasOwnProperty("lng")&&W.lat!==null&&W.lng!==null||(W={lat:U.default.lat,lng:U.default.lng}),W},setCoordinates:function(W){return this.marker&&U.showMarker&&this.marker.setLatLng(W),this.setFormRestorationState(W),this.debouncedUpdate||(this.debouncedUpdate=this.debounce(ct=>{$.set(U.statePath,{...$.get(U.statePath),lat:ct.lat,lng:ct.lng}),U.liveLocation.send&&$.$refresh(),this.updateMarker()},U.updateDelay||500)),this.debouncedUpdate(W),W},attach:function(W){this.createMap(W),new IntersectionObserver(F=>{F.forEach(Wt=>{Wt.intersectionRatio>0?this.map||this.createMap(W):this.removeMap(W)})},{root:null,rootMargin:"0px",threshold:1}).observe(W)},fetchCurrentLocation:function(){"geolocation"in navigator?navigator.geolocation.getCurrentPosition(async W=>{let ct=new Mt.LatLng(W.coords.latitude,W.coords.longitude);await this.map.flyTo(ct),this.updateLocation(),this.updateMarker()},W=>{console.error("Error fetching current location:",W)}):alert("Geolocation is not supported by this browser.")},addLocationButton:function(){let W=document.createElement("button");W.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M12 0C8.25 0 5 3.25 5 7c0 5.25 7 13 7 13s7-7.75 7-13c0-3.75-3.25-7-7-7zm0 10c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm0-5c-1.11 0-2 .89-2 2s.89 2 2 2 2-.89 2-2-.89-2-2-2z"/></svg>',W.type="button",W.classList.add("map-location-button"),W.onclick=()=>this.fetchCurrentLocation(),this.map.getContainer().appendChild(W)},setMarkerRange:function(){if(U.clickable&&!this.marker||!this.rangeSelectField)return;let W=parseInt(this.rangeSelectField.value||0),ct=this.getCoordinates(),F={color:"blue",fillColor:"#f03",fillOpacity:.5,radius:W};if(this.rangeCircle){this.rangeCircle.setLatLng(ct).setRadius(W);return}this.rangeCircle=Mt.circle(ct,F).addTo(this.map)},init:function(){this.$wire=$,this.config=U,this.state=ue,this.rangeSelectField=document.getElementById(U.rangeSelectField),this.initFormRestoration();let W=this;this.rangeSelectField&&this.rangeSelectField.addEventListener("change",function(){W.updateMarker()}),$.on("refreshMap",this.refreshMap.bind(this))},updateMarker:function(){U.showMarker&&this.marker&&(this.marker.setLatLng(this.getCoordinates()),this.setMarkerRange(),this.updateLocation())},refreshMap:function(){this.map.flyTo(this.getCoordinates()),this.updateMarker()}});window.mapPicker=j,window.dispatchEvent(new CustomEvent("map-script-loaded"))});
/*! Bundled license information:

leaflet/dist/leaflet-src.js:
  (* @preserve
   * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
   * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
   *)
*/

/**
 * <PERSON>ript final pour la recherche d'adresse et la mise à jour de la carte
 * Version simplifiée qui force un rafraîchissement de la page
 */

// Fonction pour rechercher une adresse
function searchMapAddress() {
    console.log('Fonction searchMapAddress appelée');
    
    // Trouver le champ d'adresse par son ID
    let addressInput = document.getElementById('search_address_input');
    
    // Si le champ n'est pas trouvé, essayer de le trouver par son nom
    if (!addressInput) {
        console.log('Champ d\'adresse non trouvé par ID, recherche par nom');
        const addressInputs = document.querySelectorAll('input[name="search_address"]');
        if (addressInputs.length > 0) {
            addressInput = addressInputs[0];
        }
    }
    
    // Si le champ n'est toujours pas trouvé, demander à l'utilisateur de saisir une adresse
    if (!addressInput) {
        console.log('Aucun champ d\'adresse trouvé, demande à l\'utilisateur');
        const address = prompt('Veuillez entrer une adresse à rechercher:');
        if (!address || !address.trim()) {
            alert('Aucune adresse saisie');
            return;
        }
        processAddress(address.trim());
        return;
    }
    
    // Récupérer l'adresse
    const address = addressInput.value ? addressInput.value.trim() : '';
    if (!address) {
        const manualAddress = prompt('Veuillez entrer une adresse à rechercher:');
        if (!manualAddress || !manualAddress.trim()) {
            alert('Aucune adresse saisie');
            return;
        }
        processAddress(manualAddress.trim());
        return;
    }
    
    // Traiter l'adresse trouvée
    processAddress(address);
}

// Fonction pour traiter une adresse
function processAddress(address) {
    console.log('Traitement de l\'adresse:', address);
    
    // Trouver le bouton de recherche
    const searchButton = document.getElementById('final_search_button');
    
    // Afficher un indicateur de chargement
    if (searchButton) {
        searchButton.innerHTML = '<span>Recherche en cours...</span>';
        searchButton.disabled = true;
    }
    
    // Construire l'URL de l'API Nominatim
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`;
    
    // Effectuer la requête
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                
                console.log('Coordonnées trouvées:', lat, lng);
                
                // Mettre à jour les champs de latitude et longitude
                updateCoordinates(lat, lng);
                
                // Stocker les coordonnées dans le localStorage pour les récupérer après le rafraîchissement
                localStorage.setItem('map_lat', lat);
                localStorage.setItem('map_lng', lng);
                
                // Afficher un message de succès
                alert('Adresse trouvée ! La page va être rafraîchie pour mettre à jour la carte.');
                
                // Rafraîchir la page après un court délai
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                console.warn('Aucun résultat trouvé');
                alert('Aucun résultat trouvé pour cette adresse. Veuillez essayer avec une adresse plus précise.');
                
                // Restaurer le bouton
                if (searchButton) {
                    searchButton.innerHTML = '<span>Rechercher l\'adresse</span>';
                    searchButton.disabled = false;
                }
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche d\'adresse:', error);
            alert('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
            
            // Restaurer le bouton
            if (searchButton) {
                searchButton.innerHTML = '<span>Rechercher l\'adresse</span>';
                searchButton.disabled = false;
            }
        });
}

// Fonction pour mettre à jour les champs de latitude et longitude
function updateCoordinates(lat, lng) {
    console.log('Mise à jour des coordonnées:', lat, lng);
    
    // Trouver les champs de latitude et longitude
    const latitudeInput = document.querySelector('input[name="latitude"]');
    const longitudeInput = document.querySelector('input[name="longitude"]');
    
    // Mettre à jour les valeurs
    if (latitudeInput) {
        latitudeInput.value = lat;
        console.log('Champ latitude mis à jour');
        latitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    if (longitudeInput) {
        longitudeInput.value = lng;
        console.log('Champ longitude mis à jour');
        longitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    // Mettre à jour le champ location pour la carte
    const locationInputs = [
        document.querySelector('input[name="data.location"]'),
        document.querySelector('input[name="location"]')
    ].filter(Boolean);
    
    if (locationInputs.length > 0) {
        const locationState = { lat, lng };
        
        locationInputs.forEach(input => {
            input.value = JSON.stringify(locationState);
            console.log('Champ location mis à jour');
            
            // Déclencher plusieurs événements
            ['input', 'change'].forEach(eventType => {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
        });
    }
    
    // Essayer de déclencher un événement Livewire pour rafraîchir la carte
    if (window.Livewire) {
        console.log('Livewire trouvé, déclenchement de l\'événement refreshMap');
        window.Livewire.dispatch('refreshMap');
    }
}

// Fonction exécutée quand le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé, vérification des coordonnées stockées');
    
    // Vérifier si des coordonnées sont stockées dans le localStorage
    const lat = localStorage.getItem('map_lat');
    const lng = localStorage.getItem('map_lng');
    
    if (lat && lng) {
        console.log('Coordonnées trouvées dans le localStorage:', lat, lng);
        
        // Mettre à jour les champs avec les coordonnées stockées
        updateCoordinates(parseFloat(lat), parseFloat(lng));
        
        // Supprimer les coordonnées du localStorage
        localStorage.removeItem('map_lat');
        localStorage.removeItem('map_lng');
    }
});

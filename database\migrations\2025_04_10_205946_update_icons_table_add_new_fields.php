<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('icons', function (Blueprint $table) {
            // Ajouter de nouveaux champs pour la gestion centralisée des icônes
            if (!Schema::hasColumn('icons', 'icon_type')) {
                $table->string('icon_type')->default('image')->after('type'); // image, svg, fontawesome, etc.
            }

            if (!Schema::hasColumn('icons', 'icon_value')) {
                $table->string('icon_value')->nullable()->after('icon_type'); // classe FontAwesome ou code SVG
            }

            if (!Schema::hasColumn('icons', 'content_type')) {
                $table->string('content_type')->nullable()->after('icon_value'); // propriete, section, etc.
            }

            if (!Schema::hasColumn('icons', 'description')) {
                $table->text('description')->nullable()->after('label'); // Description détaillée de l'icône
            }

            if (!Schema::hasColumn('icons', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('sort_order'); // Activer/désactiver l'icône
            }

            if (!Schema::hasColumn('icons', 'color')) {
                $table->string('color')->nullable()->after('is_active'); // Couleur de l'icône (pour FontAwesome ou SVG)
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('icons', function (Blueprint $table) {
            // Supprimer les nouveaux champs
            $table->dropColumn([
                'icon_type',
                'icon_value',
                'content_type',
                'description',
                'is_active',
                'color'
            ]);
        });
    }
};

<?php

namespace App\Filament\Forms\Components;

use App\Models\Icon;
use Filament\Forms\Components\Select;

class IconSelect extends Select
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Icône')
            ->relationship('icon', 'label')
            ->searchable()
            ->preload()
            ->nullable()
            ->createOptionAction(function ($action) {
                return $action->form([
                    \Filament\Forms\Components\TextInput::make('label')
                        ->label('Libellé')
                        ->required()
                        ->maxLength(255),
                    \Filament\Forms\Components\Textarea::make('description')
                        ->label('Description')
                        ->maxLength(1000),
                    \Filament\Forms\Components\Select::make('type')
                        ->label('Type d\'utilisation')
                        ->options([
                            'hero' => 'Hero',
                            'section' => 'Section',
                            'page' => 'Page',
                            'autre' => 'Autre',
                        ])
                        ->default('autre')
                        ->required(),
                    \Filament\Forms\Components\Toggle::make('is_active')
                        ->label('Actif')
                        ->default(true),
                    \Filament\Forms\Components\Select::make('icon_type')
                        ->label('Type d\'icône')
                        ->options([
                            'image' => 'Image',
                            'fontawesome' => 'Font Awesome',
                            'svg' => 'SVG',
                        ])
                        ->default('fontawesome')
                        ->reactive()
                        ->required(),
                    \Filament\Forms\Components\FileUpload::make('image')
                        ->label('Image')
                        ->image()
                        ->directory('icons')
                        ->visible(fn (callable $get) => $get('icon_type') === 'image')
                        ->required(fn (callable $get) => $get('icon_type') === 'image')
                        ->nullable(),
                    \Filament\Forms\Components\TextInput::make('icon_value')
                        ->label('Classe Font Awesome')
                        ->placeholder('fas fa-home')
                        ->helperText('Exemple: fas fa-home, far fa-user, etc.')
                        ->visible(fn (callable $get) => $get('icon_type') === 'fontawesome')
                        ->required(fn (callable $get) => $get('icon_type') === 'fontawesome'),
                    \Filament\Forms\Components\Textarea::make('icon_value')
                        ->label('Code SVG')
                        ->placeholder('<svg>...</svg>')
                        ->helperText('Collez ici le code SVG complet')
                        ->visible(fn (callable $get) => $get('icon_type') === 'svg')
                        ->required(fn (callable $get) => $get('icon_type') === 'svg'),
                    \Filament\Forms\Components\ColorPicker::make('color')
                        ->label('Couleur')
                        ->visible(fn (callable $get) => $get('icon_type') === 'fontawesome' || $get('icon_type') === 'svg'),
                ]);
            })
            ->options(function () {
                return \App\Models\Icon::where('is_active', true)->pluck('label', 'id');
            })
            ->getOptionLabelUsing(function ($value) {
                if (!$value) return null;
                
                $record = \App\Models\Icon::find($value);
                if (!$record) return null;
                
                $preview = '';
                
                switch ($record->icon_type) {
                    case 'fontawesome':
                        $preview = '<i class="' . $record->icon_value . '" style="color: ' . ($record->color ?: 'inherit') . ';"></i>';
                        break;
                    case 'svg':
                        $preview = $record->icon_value;
                        break;
                    case 'image':
                        if ($record->image) {
                            $preview = '<img src="' . asset('storage/' . $record->image) . '" alt="' . $record->label . '" style="max-width: 20px; max-height: 20px;">';
                        }
                        break;
                }
                
                return $preview . ' ' . $record->label;
            })
            ->allowHtml();
    }
}

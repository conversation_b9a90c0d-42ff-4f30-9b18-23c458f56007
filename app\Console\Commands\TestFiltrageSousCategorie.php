<?php

namespace App\Console\Commands;

use App\Models\Propriete;
use Illuminate\Console\Command;

class TestFiltrageSousCategorie extends Command
{
    protected $signature = 'test:filtrage-sous-categorie {type}';
    protected $description = 'Test le filtrage par sous-catégorie';

    public function handle()
    {
        $type = $this->argument('type');
        $this->info("Filtrage des propriétés par sous-catégorie : {$type}");
        
        $proprietes = Propriete::where(function ($query) use ($type) {
            $query->whereJsonContains('sous_categorie_json', $type);
        })->get();
        
        $this->info('Propriétés trouvées : ' . $proprietes->count());
        
        foreach ($proprietes as $index => $propriete) {
            $this->info("Propriété #{$index} - ID: {$propriete->id} - Titre: {$propriete->titre}");
            $this->info("Sous-catégories : " . json_encode($propriete->sous_categorie_json));
        }
        
        return Command::SUCCESS;
    }
}

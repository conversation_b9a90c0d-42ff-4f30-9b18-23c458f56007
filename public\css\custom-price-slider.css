/* Styles pour le slider de prix personnalisé */
#custom-price-slider-container {
    width: 100%;
    padding: 20px 10px;
    box-sizing: border-box;
}

/* Affichage des prix */
.price-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
}

/* Conteneur de la piste du slider */
.slider-track {
    position: relative;
    width: 100%;
    height: 5px;
    background-color: #e1e4e9;
    border-radius: 5px;
    margin: 30px 0;
}

/* Partie active de la piste */
.slider-track-active {
    position: absolute;
    height: 100%;
    background-color: #8ac473;
    border-radius: 5px;
}

/* Style des sliders */
.slider {
    position: absolute;
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 5px;
    background: transparent;
    outline: none;
    margin: 0;
    padding: 0;
    pointer-events: none;
}

/* Style des poignées */
.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #8ac473;
    cursor: pointer;
    pointer-events: auto;
    margin-top: -8px;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #8ac473;
    cursor: pointer;
    pointer-events: auto;
}

/* Hover effect */
.slider::-webkit-slider-thumb:hover {
    background-color: #f5f5f5;
}

.slider::-moz-range-thumb:hover {
    background-color: #f5f5f5;
}

/* Active effect */
.slider::-webkit-slider-thumb:active {
    background-color: #8ac473;
    border-color: #6ca353;
}

.slider::-moz-range-thumb:active {
    background-color: #8ac473;
    border-color: #6ca353;
}

<?php

namespace App\Filament\Resources\ArticlesBlogResource\Pages;

use App\Filament\Resources\ArticlesBlogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListArticlesBlogs extends ListRecords
{
    protected static string $resource = ArticlesBlogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

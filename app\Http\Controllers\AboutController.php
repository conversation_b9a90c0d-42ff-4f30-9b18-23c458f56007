<?php

namespace App\Http\Controllers;

use App\Models\Equipe;
use App\Models\Icon;
use App\Models\Section;
use Illuminate\Http\Request;

class AboutController extends Controller
{
    public function index()
    {


        // Récupère la section par son nom (par exemple "proprietes_vedettes")
        $sections = Section::with('contenuSections')->where('page_id', 2)->get();
        $equipes = Equipe::all();
        $icons = Icon::where('type', 'hero')->get();

        // Passage des données à la vue index.blade.php
        // return view('web.index', compact('page', 'sections', 'proprietes', 'developpements', 'articles', 'contact'));
        return view('web.about', compact('sections', 'equipes', 'icons'));
    }
}

<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HeroResource\Pages;
use App\Filament\Resources\HeroResource\RelationManagers;
use App\Filament\Resources\HeroResource\RelationManagers\IconsRelationManager;
use App\Models\Hero;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HeroResource extends Resource
{
    protected static ?string $model = Hero::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Titre')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('subtitle')
                    ->label('Sous-titre')
                    
                    ->maxLength(255),
                Forms\Components\FileUpload::make('video')
                    ->label('Téléverser une vidéo')
                    ->acceptedFileTypes(['video/*'])
                    ->directory('videos') // Optional: specify the directory to store uploaded videos
                    ->maxSize(102400) // Optional: set maximum file size in kilobytes (e.g., 100 MB)
                    ,
                Forms\Components\TextInput::make('poster')
                    ->label('Image de la vidéo')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\FileUpload::make('background_image')
                    ->label('Image de fond')
                    ->image(),
                Forms\Components\Toggle::make('has_video')
                    ->label('A une vidéo ?')
                    ,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('subtitle')
                    ->searchable(),
                Tables\Columns\TextColumn::make('poster')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('background_image'),
                Tables\Columns\IconColumn::make('has_video')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHeroes::route('/'),
        ];
    }
}

/* Reset & Basics */

html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,strike,strong,tt,var,b,u,i,center,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}
article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}
body{line-height:1}
ol,ul{list-style:none}
blockquote,q{quotes:none}
blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}
table{border-collapse:collapse;border-spacing:0}

/* Variables */

body {
    --mfn-column-gap-top: 0;
    --mfn-column-gap-bottom: 40px;
    --mfn-column-gap-left: 12px;
    --mfn-column-gap-right: 12px;
    --mfn-article-box-decoration: #0089F7; /* podpiac pod themecolor */
    --mfn-before-after-slider: #fff;
    --mfn-before-after-label: rgba(0,0,0,0.25);
    --mfn-before-after-label-color: #fff;
    --mfn-blog-slider-date-bg: #f8f8f8;
    --mfn-blog-date-bg: #f8f8f8;
    --mfn-contactbox-line: rgba(255,255,255,.2);
    --mfn-infobox-line: rgba(255,255,255,.2);
    --mfn-faq-line: rgba(0,0,0,.1);
    --mfn-clients-tiles: rgba(0,0,0,.08);
    --mfn-clients-tiles-hover: #0089F7;
    --mfn-offer-thumbs-nav: #0089F7;
    --mfn-timeline-date-bg: #f8f8f8;
    --mfn-sliding-box-bg: #0089F7;
    --mfn-tabs-border-width: 1px;
    --mfn-tabs-border-color: rgba(0,0,0,.08);
    --mfn-shape-divider: #000;
    --mfn-icon-box-icon: #0089F7;
    --mfn-popup-tmpl-offset: 30px;
    --mfn-exitbutton-font-size: 16px;
    --mfn-exitbutton-size: 30px;
    --mfn-exitbutton-item-size: 16px;
    --mfn-exitbutton-offset-horizontal: 0px;
    --mfn-wpml-arrow-size: 10px;
}

body.style-simple {
    --mfn-contactbox-line: rgba(255,255,255,.1);
    --mfn-infobox-line: rgba(255,255,255,.1);
}

img:not([src$=".svg"]){
    max-width: 100%;
    height: auto;
}

/* Basic Styles */

html{height:100%}
body{-webkit-font-smoothing:antialiased;-webkit-text-size-adjust:100%}
body.mfn-body-offset-header{transition: 0.3s;}
/* TwentyTwenty */

*,*::before,*::after{box-sizing:inherit;-webkit-font-smoothing:antialiased}

/* Typography */

h1,h2,h3,h4,h5,h6{margin-bottom:15px}
h1 a,h2 a,h3 a,h4 a,h5 a,h6 a{font-weight:inherit}
h1 a:hover,h2 a:hover,h3 a:hover,h4 a:hover,h5 a:hover,h6 a:hover{text-decoration:none}
p{margin:0 0 15px}
em,i{font-style:italic}
b,strong,dt{font-weight:700}
.big{font-size:110%;line-height:180%}
.bypostauthor{border-color:#eee}
.gallery-caption{display:block}

/* Code Hightlighter */

code,pre,q{font-family:Consolas,monospace!important;border:1px solid #e8e8e8;background:#fff;border-radius:3px}
code,q{padding:2px 4px;white-space:nowrap;margin:0 2px;color:#2991d6}
pre{padding:21px 15px 20px;margin:15px 0;display:block;line-height:21px!important;background:#fff;background:-moz-linear-gradient(top,#fff 50%,#fafafa 50%);background:-webkit-linear-gradient(top,#fff 50%,#fafafa 50%);background:-o-linear-gradient(top,#fff 50%,#fafafa 50%);background:linear-gradient(top,#fff 50%,#fafafa 50%);background-size:42px 42px;white-space:pre-wrap;white-space:-moz-pre-wrap;white-space:-pre-wrap;white-space:-o-pre-wrap}
code p,pre p{margin-bottom:0!important}

/* Links */

a, a:visited, a:focus{text-decoration:none;outline:0}
a:hover{text-decoration:underline}
p a, p a:visited{line-height:inherit}

/* Images */

img.scale-with-grid,.content_wrapper img{max-width:100%;height:auto}

#Content .is-cropped img{height:inherit}
.google-map img{max-width:none!important}
iframe{max-width:100%}

.gallery .gallery-item img[src$=".svg"],
.list_image img[src$=".svg"],
.post-photo-wrapper img[src$=".svg"],
.slider_images img[src$=".svg"]{width:100%}

/* button */

button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}

.button,button,input[type="button"],input[type="reset"],input[type="submit"]{display:inline-block;position:relative;padding:10px 20px;font-size:inherit;overflow:hidden;text-decoration:none!important;cursor:pointer;border-style:solid;transition:color .1s ease-in-out, background-color .1s ease-in-out, border-color .1s ease-in-out}

.button .button_icon{float:left}

.button.has-icon{white-space:nowrap}
.button.has-icon .button_label{margin-left:20px;white-space:normal}

.button_right .button_icon{float:right}
.button_right.has-icon .button_label{margin:0 20px 0 0}

.action_button{display:block;position:relative;float:left;overflow:hidden;padding:10px 20px;text-decoration:none!important;line-height:21px;box-sizing:border-box;transition:color .1s ease-in-out, background-color .1s ease-in-out, border-color .1s ease-in-out}

.footer_button{display:inline-block;position:relative;overflow:hidden;width:42px;height:42px;line-height:42px!important;font-size:20px!important;margin:0;text-align:center;text-decoration:none!important;font-family:inherit!important;transition:color .1s ease-in-out, background-color .1s ease-in-out, border-color .1s ease-in-out}

/* button | default */

.button-default .button,.button-default button,
.button-default input[type="button"],.button-default input[type="reset"],.button-default input[type="submit"]{border:none;border-radius:5px;box-shadow:inset 0 0 0 1px rgba(0,0,0,.03),inset 0px 4px 3px -2px rgba(0,0,0,.07)}

.button-default .button.has-icon .button_label{margin-left:40px}
.button-default .button_right.has-icon .button_label{margin-left:0;margin-right:40px}

.button-default .button .button_icon{position:relative;margin:0 -8px}
.button-default .button .button_icon:after{content:"";position:absolute;background-color:rgba(0,0,0,.05);display:block;width:calc(100% + 100px);height:calc(100% + 100px);top:-50px;right:-12px}
.button-default .button .button_icon i{color:rgba(0,0,0,.5)}
.button-default .button_right .button_icon:after{left:-12px;right:auto;}

.button-default .button_dark .button_icon:after{background-color:rgba(255,255,255,.05)}
.button-default .button_dark .button_icon i{color:rgba(255,255,255,.7)}

.button-default .action_button,
.button-default .footer_button{border-radius:5px}

@media ( min-width: 768px ){
	.button-default .button:after,.button-default button:after, .button-default .action_button:after, .button-default .footer_button:after,
	.button-default input[type="submit"]:after,.button-default input[type="reset"]:after,.button-default input[type="button"]:after{content:"";position:absolute;left:0;top:0;height:100%;width:0;background:rgba(0,0,0,.05);z-index:1;transition:width .2s ease-in-out}
	.button-default .button:hover:after,.button-default button:hover:after, .button-default .action_button:hover:after, .button-default .footer_button:hover:after,
	.button-default input[type="submit"]:hover:after,.button-default input[type="reset"]:hover:after,.button-default input[type="button"]:hover:after{width:100%}
}

/* button | flat */

.button-flat .button,.button-flat button,
.button-flat input[type="button"],.button-flat input[type="reset"],.button-flat input[type="submit"]{border:none}

.button-flat .button .button_icon{position:relative;margin:0 -8px}

.button-flat .button:after,.button-flat button:after, .button-flat .action_button:after, .button-flat .footer_button:after,
.button-flat input[type="submit"]:after,.button-flat input[type="reset"]:after,.button-flat input[type="button"]:after{content:"";position:absolute;left:0;top:0;height:100%;width:100%;background:rgba(0,0,0,.05);z-index:1;transition:opacity .2s ease-in-out;opacity:0}
.button-flat .button:hover:after,.button-flat button:hover:after, .button-flat .action_button:hover:after, .button-flat .footer_button:hover:after,
.button-flat input[type="submit"]:hover:after,.button-flat input[type="reset"]:hover:after,.button-flat input[type="button"]:hover:after{opacity:1}

/* button | round */

.button-round .button,.button-round button, .button-round .action_button,
.button-round input[type="button"],.button-round input[type="reset"],.button-round input[type="submit"]{border:none;padding:10px 35px;border-radius:50px}

.button-round .button .button_icon{position:relative;margin:0 -8px}

.button-round .button:after,.button-round button:after, .button-round .action_button:after,
.button-round input[type="submit"]:after,.button-round input[type="reset"]:after,.button-round input[type="button"]:after{content:"";position:absolute;left:0;top:0;height:100%;width:100%;background:rgba(0,0,0,.05);z-index:1;transition:opacity .2s ease-in-out;opacity:0}
.button-round .button:hover:after,.button-round button:hover:after, .button-round .action_button:hover:after,
.button-round input[type="submit"]:hover:after,.button-round input[type="reset"]:hover:after,.button-round input[type="button"]:hover:after{opacity:1}

.button-round .footer_button{padding:0 15px;border-radius:50px}

/* button | stroke */

.button-stroke .button,.button-stroke button, .button-stroke .action_button, .button-stroke .footer_button,
.button-stroke input[type="button"],.button-stroke input[type="reset"],.button-stroke input[type="submit"]{background-color:transparent;border-width:2px;border-style:solid;border-radius:3px;transition:background-color .2s ease-in-out, color .2s ease-in-out;}

.button-stroke .button .button_icon{position:relative;margin:0 -8px}
.button-stroke .button .button_icon i{transition:color .2s ease-in-out}

/* button | full width */

.button.button_full_width,button.button_full_width,
input[type="submit"].button_full_width,input[type="reset"].button_full_width,input[type="button"].button_full_width{width:100%;box-sizing:border-box;text-align:center}

.button.button_full_width .button_icon{float:none;margin:0;background:none}
.button.button_full_width .button_icon:after{display:none}
.button.button_full_width .button_label{margin:0 0 0 10px!important}

/* button | sizes */

.button.button_size_1{font-size:95%;padding-top:7px;padding-bottom:7px}

.button.button_size_3{font-size:110%;padding-top:11px;padding-bottom:11px}
.button.button_size_3 .button_label{padding:0 10px}

.button.button_size_4{font-size:120%;padding-top:15px;padding-bottom:15px}
.button.button_size_4 .button_label{padding:0 15px}

/* button | custom */

.button-custom .button .button_label{padding:0}
.button-custom .button.has-icon .button_label{padding:0;margin-left:10px}

.button-custom .action_button{border-style:solid;padding:12px 20px !important;line-height:14px!important;top:0}
.button-custom .footer_button{border-style:solid;padding:0 !important}

.button-custom .button_size_1{transform:scale(0.9) translateX(-5%);}
.button-custom .button_size_3{transform:scale(1.1) translateX(5%);}
.button-custom .button_size_4{transform:scale(1.2) translateX(8%);}

.button-custom .content_wrapper .button.the-icon{padding-left:10px;padding-right:10px}

/* button | in the content */

.content_wrapper .button,.content_wrapper button,.content_wrapper input[type="button"],.content_wrapper input[type="reset"],.content_wrapper input[type="submit"]{margin:0 10px 15px 0}

/* button | colors */

/* red */
a.button.button_red{background-color:#e7432b;color:#fff}
.button-stroke a.button.button_red,.button-stroke a.button.button_red .button_icon i,.button-stroke a.tp-button.red{color:#e7432b;border-color:#e7432b;background-color:transparent}
.button-stroke a:hover.button.button_red,.button-stroke a:hover.tp-button.red{background-color:#e7432b;color:#fff}
/* blue */
a.button.button_blue{background-color:#2e96db;color:#fff}
.button-stroke a.button.button_blue,.button-stroke a.button.button_blue .button_icon i,.button-stroke a.tp-button.blue{color:#2e96db;border-color:#2e96db;background-color:transparent}
.button-stroke a:hover.button.button_blue,.button-stroke a:hover.tp-button.blue{background-color:#2e96db;color:#fff}
/* turquoise */
a.button.button_turquoise{background-color:#22e387;color:#fff}
.button-stroke a.button.button_turquoise,.button-stroke a.button.button_turquoise .button_icon i,.button-stroke a.tp-button.turquoise{color:#22e387;border-color:#22e387;background-color:transparent}
.button-stroke a:hover.button.button_turquoise,.button-stroke a:hover.tp-button.turquoise{background-color:#22e387;color:#fff}
/* yellow */
a.button.button_yellow{background-color:#face43;color:#fff}
.button-stroke a.button.button_yellow,.button-stroke a.button.button_yellow .button_icon i,.button-stroke a.tp-button.yellow{color:#face43;border-color:#face43;background-color:transparent}
.button-stroke a:hover.button.button_yellow,.button-stroke a:hover.tp-button.yellow{background-color:#face43;color:#fff}
/* grey */
a.button.button_grey{background-color:#8f8f8f;color:#fff}
.button-stroke a.button.button_grey,.button-stroke a.button.button_grey .button_icon i,.button-stroke a.tp-button.grey{color:#8f8f8f;border-color:#8f8f8f;background-color:transparent}
.button-stroke a:hover.button.button_grey,.button-stroke a:hover.tp-button.grey{background-color:#8f8f8f;color:#fff}
/* navy */
a.button.button_navy{background-color:#3b5982;color:#fff}
.button-stroke a.button.button_navy,.button-stroke a.button.button_navy .button_icon i,.button-stroke a.tp-button.navy{color:#3b5982;border-color:#3b5982;background-color:transparent}
.button-stroke a:hover.button.button_navy,.button-stroke a:hover.tp-button.navy{background-color:#3b5982;color:#fff}
/* orange */
a.button.button_orange{background-color:#ff8125;color:#fff}
.button-stroke a.button.button_orange,.button-stroke a.button.button_orange .button_icon i,.button-stroke a.tp-button.orange{color:#ff8125;border-color:#ff8125;background-color:transparent}
.button-stroke a:hover.button.button_orange,.button-stroke a:hover.tp-button.orange{background-color:#ff8125;color:#fff}
/* green */
a.button.button_green{background-color:#88be4c;color:#fff}
.button-stroke a.button.button_green,.button-stroke a.button.button_green .button_icon i,.button-stroke a.tp-button.green{color:#88be4c;border-color:#88be4c;background-color:transparent}
.button-stroke a:hover.button.button_green,.button-stroke a:hover.tp-button.green{background-color:#88be4c;color:#fff}

/* Icons */

@font-face{font-family:'mfn-icons';src:url(../fonts/mfn/icons.eot@31690507);src:url(../fonts/mfn/icons.eot@31690507) format("embedded-opentype"),url(../fonts/mfn/icons.woff@31690507) format("woff"),url(../fonts/mfn/icons.ttf@31690507) format("truetype"),url(../fonts/mfn/icons.svg@31690507) format("svg");font-weight:400;font-style:normal;font-display:swap}
[class^="icon-"]:before,[class*=" icon-"]:before{font-family:"mfn-icons";font-style:normal;font-weight:400;speak:none;display:inline-block;text-decoration:none!important;width:1em;margin-right:.2em;text-align:center;font-variant:normal;text-transform:none;line-height:28px;margin-left:.2em}

.icon-acrobat:before{content:'\e800'}.icon-address:before{content:'\e801'}.icon-adjust:before{content:'\e802'}.icon-aim:before{content:'\e803'}.icon-air:before{content:'\e804'}.icon-alert:before{content:'\e805'}.icon-amazon:before{content:'\e806'}.icon-android:before{content:'\e807'}.icon-angellist:before{content:'\e808'}
.icon-appnet:before{content:'\e809'}.icon-appstore:before{content:'\e80a'}.icon-archive:before{content:'\e80b'}.icon-arrow-combo:before{content:'\e80c'}.icon-arrows-ccw:before{content:'\e80d'}.icon-attach:before{content:'\e80e'}.icon-attach-line:before{content:'\e80f'}.icon-attention:before{content:'\e810'}.icon-back:before{content:'\e811'}
.icon-back-in-time:before{content:'\e812'}.icon-bag:before{content:'\e813'}.icon-basket:before{content:'\e814'}.icon-battery:before{content:'\e815'}.icon-beaker-line:before{content:'\e816'}.icon-bell:before{content:'\e817'}.icon-bitbucket:before{content:'\e818'}.icon-bitcoin:before{content:'\e819'}.icon-block:before{content:'\e81a'}
.icon-blogger:before{content:'\e81b'}.icon-book:before{content:'\e81c'}.icon-book-open:before{content:'\e81d'}.icon-bookmark:before{content:'\e81e'}.icon-bookmarks:before{content:'\e81f'}.icon-box:before{content:'\e820'}.icon-briefcase:before{content:'\e821'}.icon-brush:before{content:'\e822'}.icon-bucket:before{content:'\e823'}
.icon-buffer:before{content:'\e824'}.icon-calendar:before{content:'\e825'}.icon-calendar-line:before{content:'\e826'}.icon-call:before{content:'\e827'}.icon-camera:before{content:'\e828'}.icon-camera-line:before{content:'\e829'}.icon-cancel:before{content:'\e82a'}.icon-cancel-circled:before{content:'\e82b'}.icon-cancel-squared:before{content:'\e82c'}
.icon-cart:before{content:'\e82d'}.icon-cc:before{content:'\e82e'}.icon-cc-by:before{content:'\e82f'}.icon-cc-nc:before{content:'\e830'}.icon-cc-nc-eu:before{content:'\e831'}.icon-cc-nc-jp:before{content:'\e832'}.icon-cc-nd:before{content:'\e833'}.icon-cc-pd:before{content:'\e834'}.icon-cc-remix:before{content:'\e835'}
.icon-cc-sa:before{content:'\e836'}.icon-cc-share:before{content:'\e837'}.icon-cc-zero:before{content:'\e838'}.icon-ccw:before{content:'\e839'}.icon-cd:before{content:'\e83a'}.icon-cd-line:before{content:'\e83b'}.icon-chart-area:before{content:'\e83c'}.icon-chart-bar:before{content:'\e83d'}.icon-chart-line:before{content:'\e83e'}
.icon-chart-pie:before{content:'\e83f'}.icon-chat:before{content:'\e840'}.icon-check:before{content:'\e841'}.icon-clipboard:before{content:'\e842'}.icon-clock:before{content:'\e843'}.icon-clock-line:before{content:'\e844'}.icon-cloud:before{content:'\e845'}.icon-cloud-line:before{content:'\e846'}.icon-cloud-thunder:before{content:'\e847'}
.icon-cloudapp:before{content:'\e848'}.icon-code:before{content:'\e849'}.icon-cog:before{content:'\e84a'}.icon-cog-line:before{content:'\e84b'}.icon-comment-fa:before{content:'\e84c'}.icon-comment-line:before{content:'\e84d'}.icon-compass:before{content:'\e84e'}.icon-credit-card:before{content:'\e84f'}.icon-cup:before{content:'\e850'}
.icon-cup-line:before{content:'\e851'}.icon-cw:before{content:'\e852'}.icon-database-line:before{content:'\e853'}.icon-delicious:before{content:'\e854'}.icon-desktop-line:before{content:'\e855'}.icon-diamond-line:before{content:'\e856'}.icon-digg:before{content:'\e857'}.icon-direction:before{content:'\e858'}.icon-disqus:before{content:'\e859'}
.icon-doc:before{content:'\e85a'}.icon-doc-landscape:before{content:'\e85b'}.icon-doc-line:before{content:'\e85c'}.icon-doc-text:before{content:'\e85d'}.icon-doc-text-inv:before{content:'\e85e'}.icon-docs:before{content:'\e85f'}.icon-dot:before{content:'\e860'}.icon-dot-2:before{content:'\e861'}.icon-dot-3:before{content:'\e862'}
.icon-down:before{content:'\e863'}.icon-down-bold:before{content:'\e864'}.icon-down-circled:before{content:'\e865'}.icon-down-dir:before{content:'\e866'}.icon-down-open:before{content:'\e867'}.icon-down-open-big:before{content:'\e868'}.icon-down-open-mini:before{content:'\e869'}.icon-down-thin:before{content:'\e86a'}.icon-download:before{content:'\e86b'}
.icon-drive:before{content:'\e86c'}.icon-droplet:before{content:'\e86d'}.icon-drupal:before{content:'\e86e'}.icon-duckduckgo:before{content:'\e86f'}.icon-dwolla:before{content:'\e870'}.icon-ebay:before{content:'\e871'}.icon-email:before{content:'\e872'}.icon-erase:before{content:'\e873'}.icon-eventasaurus:before{content:'\e874'}
.icon-eventbrite:before{content:'\e875'}.icon-eventful:before{content:'\e876'}.icon-export:before{content:'\e877'}.icon-eye:before{content:'\e878'}.icon-eye-line:before{content:'\e879'}.icon-fast-backward:before{content:'\e87a'}.icon-fast-forward:before{content:'\e87b'}.icon-feather:before{content:'\e87c'}.icon-fire-line:before{content:'\e87d'}
.icon-fivehundredpx:before{content:'\e87e'}.icon-flag:before{content:'\e87f'}.icon-flash:before{content:'\e880'}.icon-flashlight:before{content:'\e881'}.icon-flight:before{content:'\e882'}.icon-floppy:before{content:'\e883'}.icon-flow-branch:before{content:'\e884'}.icon-flow-cascade:before{content:'\e885'}.icon-flow-line:before{content:'\e886'}
.icon-flow-parallel:before{content:'\e887'}.icon-flow-tree:before{content:'\e888'}.icon-folder:before{content:'\e889'}.icon-food-line:before{content:'\e88a'}.icon-forrst:before{content:'\e88b'}.icon-forward:before{content:'\e88c'}.icon-gauge:before{content:'\e88d'}.icon-globe:before{content:'\e88e'}.icon-globe-line:before{content:'\e88f'}
.icon-gmail:before{content:'\e890'}.icon-googleplay:before{content:'\e891'}.icon-gowalla:before{content:'\e892'}.icon-graduation-cap:before{content:'\e893'}.icon-graduation-cap-line:before{content:'\e894'}.icon-grooveshark:before{content:'\e895'}.icon-guest:before{content:'\e896'}.icon-hackernews:before{content:'\e897'}.icon-heart-empty-fa:before{content:'\e898'}
.icon-heart-fa:before{content:'\e899'}.icon-heart-line:before{content:'\e89a'}.icon-help:before{content:'\e89b'}.icon-help-circled:before{content:'\e89c'}.icon-home:before{content:'\e89d'}.icon-hourglass:before{content:'\e89e'}.icon-html5:before{content:'\e89f'}.icon-ie:before{content:'\e8a0'}.icon-inbox:before{content:'\e8a1'}
.icon-inbox-line:before{content:'\e8a2'}.icon-infinity:before{content:'\e8a3'}.icon-info:before{content:'\e8a4'}.icon-info-circled:before{content:'\e8a5'}.icon-install:before{content:'\e8a6'}.icon-instapaper:before{content:'\e8a7'}.icon-intensedebate:before{content:'\e8a8'}.icon-itunes:before{content:'\e8a9'}.icon-key:before{content:'\e8aa'}
.icon-key-line:before{content:'\e8ab'}.icon-keyboard:before{content:'\e8ac'}.icon-klout:before{content:'\e8ad'}.icon-lamp:before{content:'\e8ae'}.icon-language:before{content:'\e8af'}.icon-lanyrd:before{content:'\e8b0'}.icon-layout:before{content:'\e8b1'}.icon-leaf:before{content:'\e8b2'}.icon-left:before{content:'\e8b3'}
.icon-left-bold:before{content:'\e8b4'}.icon-left-circled:before{content:'\e8b5'}.icon-left-dir:before{content:'\e8b6'}.icon-left-open:before{content:'\e8b7'}.icon-left-open-big:before{content:'\e8b8'}.icon-left-open-mini:before{content:'\e8b9'}.icon-left-thin:before{content:'\e8ba'}.icon-lego:before{content:'\e8bb'}.icon-level-down:before{content:'\e8bc'}
.icon-level-up:before{content:'\e8bd'}.icon-lifebuoy:before{content:'\e8be'}.icon-light-down:before{content:'\e8bf'}.icon-light-up:before{content:'\e8c0'}.icon-lightbulb-line:before{content:'\e8c1'}.icon-link:before{content:'\e8c2'}.icon-list:before{content:'\e8c3'}.icon-list-add:before{content:'\e8c4'}.icon-lkdto:before{content:'\e8c5'}
.icon-location:before{content:'\e8c6'}.icon-location-line:before{content:'\e8c7'}.icon-lock:before{content:'\e8c8'}.icon-lock-line:before{content:'\e8c9'}.icon-lock-open:before{content:'\e8ca'}.icon-login:before{content:'\e8cb'}.icon-logout:before{content:'\e8cc'}.icon-loop:before{content:'\e8cd'}.icon-macstore:before{content:'\e8ce'}
.icon-magnet:before{content:'\e8cf'}.icon-mail:before{content:'\e8d0'}.icon-mail-line:before{content:'\e8d1'}.icon-map:before{content:'\e8d2'}.icon-meetup:before{content:'\e8d3'}.icon-megaphone:before{content:'\e8d4'}.icon-megaphone-line:before{content:'\e8d5'}.icon-menu:before{content:'\e8d6'}.icon-mic:before{content:'\e8d7'}
.icon-minus:before{content:'\e8d8'}.icon-minus-circled:before{content:'\e8d9'}.icon-minus-squared:before{content:'\e8da'}.icon-mobile:before{content:'\e8db'}.icon-mobile-line:before{content:'\e8dc'}.icon-money-line:before{content:'\e8dd'}.icon-monitor:before{content:'\e8de'}.icon-moon:before{content:'\e8df'}.icon-mouse:before{content:'\e8e0'}
.icon-music:before{content:'\e8e1'}.icon-music-line:before{content:'\e8e2'}.icon-mute:before{content:'\e8e3'}.icon-myspace:before{content:'\e8e4'}.icon-network:before{content:'\e8e5'}.icon-newspaper:before{content:'\e8e6'}.icon-ninetyninedesigns:before{content:'\e8e7'}.icon-note:before{content:'\e8e8'}.icon-note-beamed:before{content:'\e8e9'}
.icon-note-line:before{content:'\e8ea'}.icon-openid:before{content:'\e8eb'}.icon-opentable:before{content:'\e8ec'}.icon-palette:before{content:'\e8ed'}.icon-paper-plane:before{content:'\e8ee'}.icon-paper-plane-line:before{content:'\e8ef'}.icon-params-line:before{content:'\e8f0'}.icon-pause:before{content:'\e8f1'}.icon-pencil:before{content:'\e8f2'}
.icon-pencil-line:before{content:'\e8f3'}.icon-phone:before{content:'\e8f4'}.icon-photo-line:before{content:'\e8f5'}.icon-picture:before{content:'\e8f6'}.icon-pinboard:before{content:'\e8f7'}.icon-plancast:before{content:'\e8f8'}.icon-play:before{content:'\e8f9'}.icon-plurk:before{content:'\e8fa'}.icon-plus:before{content:'\e8fb'}
.icon-plus-circled:before{content:'\e8fc'}.icon-plus-squared:before{content:'\e8fd'}.icon-pocket:before{content:'\e8fe'}.icon-podcast:before{content:'\e8ff'}.icon-popup:before{content:'\e900'}.icon-posterous:before{content:'\e901'}.icon-print:before{content:'\e902'}.icon-progress-0:before{content:'\e903'}.icon-progress-1:before{content:'\e904'}
.icon-progress-2:before{content:'\e905'}.icon-progress-3:before{content:'\e906'}.icon-publish:before{content:'\e907'}.icon-quora:before{content:'\e908'}.icon-quote:before{content:'\e909'}.icon-record:before{content:'\e90a'}.icon-reddit:before{content:'\e90b'}.icon-reply:before{content:'\e90c'}.icon-reply-all:before{content:'\e90d'}
.icon-resize-full:before{content:'\e90e'}.icon-resize-small:before{content:'\e90f'}.icon-retweet:before{content:'\e910'}.icon-right:before{content:'\e911'}.icon-right-bold:before{content:'\e912'}.icon-right-circled:before{content:'\e913'}.icon-right-dir:before{content:'\e914'}.icon-right-open:before{content:'\e915'}.icon-right-open-big:before{content:'\e916'}
.icon-right-open-mini:before{content:'\e917'}.icon-right-thin:before{content:'\e918'}.icon-rocket:before{content:'\e919'}.icon-rss:before{content:'\e91a'}.icon-search:before{content:'\e91b'}.icon-search-line:before{content:'\e91c'}.icon-share:before{content:'\e91d'}.icon-shareable:before{content:'\e91e'}.icon-shop-line:before{content:'\e91f'}
.icon-shuffle:before{content:'\e920'}.icon-signal:before{content:'\e921'}.icon-smashmag:before{content:'\e922'}.icon-songkick:before{content:'\e923'}.icon-sound:before{content:'\e924'}.icon-sound-line:before{content:'\e925'}.icon-stackoverflow:before{content:'\e926'}.icon-star:before{content:'\e927'}.icon-star-empty:before{content:'\e928'}
.icon-star-line:before{content:'\e929'}.icon-statusnet:before{content:'\e92a'}.icon-stop:before{content:'\e92b'}.icon-suitcase:before{content:'\e92c'}.icon-switch:before{content:'\e92d'}.icon-t-shirt-line:before{content:'\e92e'}.icon-tag:before{content:'\e92f'}.icon-tag-line:before{content:'\e930'}.icon-tape:before{content:'\e931'}
.icon-target:before{content:'\e932'}.icon-thermometer:before{content:'\e933'}.icon-thumbs-up:before{content:'\e934'}.icon-thumbs-down:before{content:'\e935'}.icon-thumbs-up-line:before{content:'\e936'}.icon-ticket:before{content:'\e937'}.icon-to-end:before{content:'\e938'}.icon-to-start:before{content:'\e939'}.icon-tools:before{content:'\e93a'}
.icon-traffic-cone:before{content:'\e93b'}.icon-trash:before{content:'\e93c'}.icon-trash-line:before{content:'\e93d'}.icon-trophy:before{content:'\e93e'}.icon-truck-line:before{content:'\e93f'}.icon-tv-line:before{content:'\e940'}.icon-up:before{content:'\e941'}.icon-up-bold:before{content:'\e942'}.icon-up-circled:before{content:'\e943'}
.icon-up-dir:before{content:'\e944'}.icon-up-open:before{content:'\e945'}.icon-up-open-big:before{content:'\e946'}.icon-up-open-mini:before{content:'\e947'}.icon-up-thin:before{content:'\e948'}.icon-upload:before{content:'\e949'}.icon-upload-cloud:before{content:'\e94a'}.icon-user:before{content:'\e94b'}.icon-user-add:before{content:'\e94c'}
.icon-user-line:before{content:'\e94d'}.icon-users:before{content:'\e94e'}.icon-vcard:before{content:'\e94f'}.icon-viadeo:before{content:'\e950'}.icon-video:before{content:'\e951'}.icon-videocam-line:before{content:'\e952'}.icon-vk:before{content:'\e953'}.icon-volume:before{content:'\e954'}.icon-w3c:before{content:'\e955'}
.icon-wallet-line:before{content:'\e956'}.icon-water:before{content:'\e957'}.icon-weibo:before{content:'\e958'}.icon-wikipedia:before{content:'\e959'}.icon-window:before{content:'\e95a'}.icon-wordpress:before{content:'\e95b'}.icon-xing:before{content:'\e95c'}.icon-yahoo:before{content:'\e95d'}.icon-yelp:before{content:'\e95e'}
.icon-youtube:before{content:'\e95f'}.icon-menu-fine:before{content:'\e960'}.icon-bag-fine:before{content:'\e961'}.icon-search-fine:before{content:'\e962'}.icon-cancel-fine:before{content:'\e963'}.icon-plus-fine:before{content:'\e964'}.icon-minus-fine:before{content:'\e965'}.icon-gplus-circled:before{content:'\f059'}.icon-github-circled:before{content:'\f09b'}
.icon-gplus:before{content:'\f0d5'}.icon-comment-empty-fa:before{content:'\f0e5'}.icon-instagram:before{content:'\f16d'}.icon-tumblr:before{content:'\f173'}.icon-windows:before{content:'\f17a'}.icon-foursquare:before{content:'\f180'}.icon-google:before{content:'\f1a0'}.icon-behance:before{content:'\f1b4'}.icon-steam:before{content:'\f1b6'}
.icon-spotify:before{content:'\f1bc'}.icon-database:before{content:'\f1c0'}.icon-qq:before{content:'\f1d6'}.icon-paypal:before{content:'\f1ed'}.icon-stripe:before{content:'\f1f5'}.icon-whatsapp:before{content:'\f232'}.icon-medium:before{content:'\f23a'}.icon-tripadvisor:before{content:'\f262'}.icon-chrome:before{content:'\f268'}.icon-scribd:before{content:'\f28a'}
.icon-github:before{content:'\f300'}.icon-flickr:before{content:'\f303'}.icon-flickr-circled:before{content:'\f304'}.icon-vimeo:before{content:'\f306'}.icon-vimeo-circled:before{content:'\f307'}.icon-twitter:before{content:'\f309'}.icon-twitter-circled:before{content:'\f30a'}.icon-facebook:before{content:'\f30c'}.icon-facebook-circled:before{content:'\f30d'}
.icon-facebook-squared:before{content:'\f30e'}.icon-pinterest:before{content:'\f312'}.icon-pinterest-circled:before{content:'\f313'}.icon-tumblr-circled:before{content:'\f316'}.icon-linkedin:before{content:'\f318'}.icon-linkedin-circled:before{content:'\f319'}.icon-dribbble:before{content:'\f31b'}.icon-dribbble-circled:before{content:'\f31c'}.icon-stumbleupon:before{content:'\f31e'}
.icon-stumbleupon-circled:before{content:'\f31f'}.icon-lastfm:before{content:'\f321'}.icon-lastfm-circled:before{content:'\f322'}.icon-rdio:before{content:'\f324'}.icon-rdio-circled:before{content:'\f325'}.icon-spotify-circled:before{content:'\f328'}.icon-dropbox:before{content:'\f330'}.icon-evernote:before{content:'\f333'}.icon-flattr:before{content:'\f336'}
.icon-skype:before{content:'\f339'}.icon-skype-circled:before{content:'\f33a'}.icon-renren:before{content:'\f33c'}.icon-sina-weibo:before{content:'\f33f'}.icon-picasa:before{content:'\f345'}.icon-soundcloud:before{content:'\f348'}.icon-mixi:before{content:'\f34b'}.icon-google-circles:before{content:'\f351'}.icon-vkontakte:before{content:'\f354'}
.icon-smashing:before{content:'\f357'}.icon-db-shape:before{content:'\f600'}.icon-sweden:before{content:'\f601'}.icon-logo-db:before{content:'\f603'}.icon-houzz:before{content:'\f27c'}.icon-snapchat:before{content:'\f2ac'}

/* Admin bar Live Builder link */
#wp-admin-bar-mfn-live-builder .ab-item:before{content:"";background-image:url(../muffin-options/svg/be-mono.svg)!important;background-size:17px;background-repeat:no-repeat;background-position:left center;height:100%;padding:0 0 0 17px}

/* bbPress */

.bbpress #Subheader .title{width:60%}
.bbpress #Subheader ul.breadcrumbs{width:40%}
.bbpress #Subheader ul.breadcrumbs .bbp-breadcrumb-current{margin:0}
.bbpress #bbpress-forums div.bbp-search-form{float:none;width:100%;margin-bottom:20px}
.bbpress #bbpress-forums #subscription-toggle{color:#ffffff}
.bbpress #bbpress-forums #bbp-search-form #bbp_search{width:100%;margin:0}
.bbpress #bbpress-forums div.bbp-topic-tags{clear:both;margin-bottom:20px}
.bbpress #bbpress-forums div.bbp-topic-tags p{margin:0}
.bbpress .widget_display_search .screen-reader-text{display:none}
.bbpress .widget_display_search #bbp_search{margin:0}
.bbpress .bbp_widget_login .bbp-logged-in h4{font-size:14px;font-weight:400}
.bbpress .bbp_widget_login .bbp-logged-in a.button{margin:0}
.bbpress .widget_display_views ul,.bbpress .widget_display_forums ul{list-style-type:square;padding:0 0 0 30px}
.bbpress .widget_display_views ul li a,.bbpress .widget_display_forums ul li a{display:block;padding:4px 0 7px 4px}

/* BuddyPress */

#buddypress{margin-bottom:40px}
#buddypress #whats-new-options{height:auto!important;overflow:visible!important}
#buddypress div.dir-search{margin:0}
#buddypress #search-members-form > *{float:left}
#buddypress div.dir-search input[type="text"],#buddypress li.groups-members-search input[type="text"]{padding:2px 3px;font-size:100%;margin-right:5px;font-weight:400}
#buddypress div#subnav.item-list-tabs ul li > *{float:left}
#buddypress div#subnav.item-list-tabs ul li label{margin:10px 5px 0 0;font-weight:400}
#buddypress div#item-header div#item-meta{font-size:100%}
#buddypress #message-recipients .highlight{background:none;color:inherit}
#buddypress #message-recipients .button{display:inline}

/* Contact Form 7 */

.wpcf7::after{clear:both;content:"";display:block}
.wpcf7-form .column{padding: 0 10px 10px}
.wpcf7-mail-sent-ok{border:none!important;margin:25px 0 0!important;padding:7px!important;background:#7DCC68!important;color:#fff;text-align:center;float:left;width:98%;box-sizing:border-box;margin:0 1%!important;}
.wpcf7-mail-sent-ng{border:none!important;margin:25px 0 0!important;padding:7px!important;background:#fb5455!important;color:#fff;text-align:center;float:left;width:98%;box-sizing:border-box;margin:0 1%!important;}
.cf7p-message .wpcf7-validation-errors{border:none!important;margin:25px 0 0!important;padding:7px!important;background:#fb5455!important;color:#fff;text-align:center;float:left;width:98%;box-sizing:border-box;margin:0 1%!important;}
body:not(.cf7p-message) .wpcf7-validation-errors{border:none!important;display:none!important}
body:not(.cf7p-message) .wpcf7-not-valid-tip{position:absolute!important;left:-50px!important;top:0!important;width:44px!important;height:44px!important;padding:0!important;background-color:#ed3b2e; text-indent:-9999px!important;padding:0;border:none!important}
body:not(.cf7p-message) .wpcf7-not-valid-tip:after{content:'\e82a';font-family:"mfn-icons";position:absolute;left:18px;top:11px;font-size:20px;color:#fff;text-indent:0}
body:not(.cf7p-message) .wpcf7-not-valid-tip:before{content:'';position:absolute;right:-5px;top:16px;background-color:#ed3b2e;width:11px;height:11px;transform:rotate(45deg)}
.wpcf7-captchar{margin-bottom:0}
span.wpcf7-form-control-wrap{width:100%;position:relative!important;display:inline-block;margin-bottom:3px}
span.wpcf7-form-control-wrap .wpcf7-date,span.wpcf7-form-control-wrap .wpcf7-quiz,span.wpcf7-form-control-wrap .wpcf7-number,span.wpcf7-form-control-wrap .wpcf7-select,span.wpcf7-form-control-wrap .wpcf7-text,span.wpcf7-form-control-wrap .wpcf7-textarea{width:100%;box-sizing:border-box;margin-bottom:0}
.wpcf7 input[type="submit"]{font-size:inherit;margin-bottom:0!important;-webkit-appearance:none;-moz-appearance:none;appearance:none}
.wpcf7 form .wpcf7-response-output{border:none;margin-top:0;clear:both}
.wpcf7-form.init .wpcf7-spinner,.wpcf7-form.invalid .wpcf7-spinner{display:none}

/* Contact Form 7 | Popup */

#popup_contact{position:fixed;right:20px;bottom:20px;z-index:9001}
#popup_contact .popup_contact_wrapper{width:250px;padding:20px;background:#fbfbfb;position:absolute;bottom:65px;right:0;display:none}
#popup_contact.focus .popup_contact_wrapper{display:block}
#popup_contact .popup_contact_wrapper span.arrow{position:absolute;right:15px;bottom:-8px;margin:0 auto;width:0;height:0;display:block;border-top:8px solid #fbfbfb;border-right:8px solid transparent;border-left:8px solid transparent}
#popup_contact .popup_contact_wrapper form input[type="text"],#popup_contact .popup_contact_wrapper form input[type="email"],#popup_contact .popup_contact_wrapper form textarea{background:#fff;margin-bottom:10px}
#popup_contact .popup_contact_wrapper form textarea{resize:vertical;min-height:100px}
#popup_contact .popup_contact_wrapper form input[type="submit"]{margin:0;float:right}
#popup_contact .wpcf7-form.invalid > p:after{display:block}
#popup_contact .wpcf7-form-control-wrap{display:inline-block;margin-right:0;width:100%}
#popup_contact div.wpcf7 img.ajax-loader{margin-top:10px}

#popup_contact .footer_button{position:absolute;bottom:0;right:0}

/* Easy Digital Downloads */

.edd_downloads_list .edd_download_inner{background:#fff;margin:0 4% 20px;padding:0 15px 20px}
.edd_downloads_list .edd_download_inner .edd_download_image{margin:0 -15px}
.edd_downloads_list .edd_download_inner .edd_download_title{padding-top:15px}
.edd_downloads_list .edd_download_inner .edd_download_buy_button .edd_purchase_submit_wrapper{text-align:right}
.edd_downloads_list .edd_download_inner .edd_download_buy_button .edd_purchase_submit_wrapper .edd-cart-added-alert{position:static;margin-top:15px}
.edd_downloads_list .edd_download_inner .edd_download_buy_button a.button{margin:0}
.edd_download_image{border-style:solid;border-width:8px;box-sizing:border-box;display:block;line-height:0;max-width:calc(100% + 30px);position:relative}
.edd_download_image:after{content:"";display:block;width:100%;height:100%;position:absolute;left:0;top:0}
#edd_checkout_cart th,#edd_checkout_cart td{text-align:left;border-width:1px;border-style:solid;border-color:inherit;padding:10px}
#edd_checkout_cart .edd_cart_header_row th{font-weight:700;background:#f9f9f9;box-shadow:inset 0px 4px 3px -2px rgba(0,0,0,.04);padding:10px}
#edd_checkout_cart .edd_cart_tax_row th,#edd_checkout_cart .edd_cart_discount_row th{background:inherit}
#edd_checkout_cart tr:hover td a{color:inherit}
table#edd_purchase_receipt,table#edd_purchase_receipt_products{margin-bottom:40px}
#edd_checkout_form_wrap label,#edd_checkout_form_wrap span.edd-description{font-size:100%}
#edd_checkout_form_wrap span.edd-description{margin-bottom:10px}
#edd_checkout_form_wrap input[type="text"],#edd_checkout_form_wrap input[type="email"],#edd_checkout_form_wrap input[type="password"],#edd_checkout_form_wrap textarea{padding:10px}
.edd-submit,#edd-purchase-button,input[type="submit"].edd-submit{padding:11px 20px;font-size:100%;font-weight:400;border:inherit}
#edd_checkout_form_wrap #edd-login-account-wrap,#edd_checkout_form_wrap #edd-new-account-wrap,#edd_checkout_form_wrap #edd_final_total_wrap,#edd_checkout_form_wrap #edd_show_discount,#edd_checkout_form_wrap .edd-cart-adjustment{background:#fafafa none repeat scroll 0 0;color:#444;padding:14px 20px}

/* Google Map */

.gm-style button{border-radius:0;box-shadow:unset}

/* Gravity Forms */

.gform_wrapper label{font-weight:400;margin:.188em 0 .75em}
.gform_wrapper .top_label .gfield_label{font-weight:700}
.gform_wrapper input,.gform_wrapper select{margin:0}
.gform_wrapper input[type="checkbox"],.gform_wrapper input[type="radio"]{margin-left:3px}
.gform_wrapper div.validation_error{border:none!important;border-radius:5px;margin-bottom:30px;padding:20px!important;box-sizing:border-box;background:#fb5455;color:#fff!important;font-weight:400!important;font-size:inherit!important}
.gform_wrapper li.gfield.gfield_error,.gform_wrapper li.gfield.gfield_error.gfield_contains_required.gfield_creditcard_warning{border:none!important}
.gform_wrapper .button::after{content:unset}
.gform_confirmation_wrapper .gform_confirmation_message{border-radius:5px;margin-bottom:30px;padding:20px;background:#80B736;color:#fff}

table.gsurvey-likert td input{position:static}
table.gsurvey-likert td.gsurvey-likert-choice{background-image:unset}

/* Iubenda */
#iubenda-cs-banner .iubenda-cs-opt-group button:after{content:unset;}

/* Mailchimp */

#mc_embed_signup .clear{height:auto;overflow:visible;visibility:visible}
.mc4wp-form p > input{float:left;margin:0 3px}

/* Magnific Popup */

.mfp-bg{top:0;left:0;width:100%;height:100%;z-index:9042;overflow:hidden;position:fixed;background:#0b0b0b;opacity:.8}
.mfp-wrap{top:0;left:0;width:100%;height:100%;z-index:9043;position:fixed;outline:none!important;-webkit-backface-visibility:hidden}
.mfp-container{text-align:center;position:absolute;width:100%;height:100%;left:0;top:0;padding:0 8px;box-sizing:border-box}
.mfp-container:before{content:'';display:inline-block;height:100%;vertical-align:middle}
.mfp-align-top .mfp-container:before{display:none}
.mfp-content{position:relative;display:inline-block;vertical-align:middle;margin:0 auto;text-align:left;z-index:9045}
.-holder .mfp-content,.mfp-ajax-holder .mfp-content{width:100%;cursor:auto}
.mfp-ajax-cur{cursor:progress}
.mfp-zoom-out-cur,.mfp-zoom-out-cur .mfp-image-holder .mfp-close{cursor:-moz-zoom-out;cursor:-webkit-zoom-out;cursor:zoom-out}
.mfp-zoom{cursor:pointer;cursor:-webkit-zoom-in;cursor:-moz-zoom-in;cursor:zoom-in}
.mfp-auto-cursor .mfp-content{cursor:auto}
.mfp-close,.mfp-arrow,.mfp-preloader,.mfp-counter{-webkit-user-select:none;-moz-user-select:none;user-select:none;background:none!important}
.mfp-loading.mfp-figure{display:none}
.mfp-hide{display:none!important}
.mfp-preloader{color:#CCC;position:absolute;top:50%;width:auto;text-align:center;margin-top:-.8em;left:8px;right:8px;z-index:9044}
.mfp-preloader a{color:#CCC}
.mfp-preloader a:hover{color:#FFF}
.mfp-s-ready .mfp-preloader{display:none}
.mfp-s-error .mfp-content{display:none}
button.mfp-close,button.mfp-arrow{overflow:visible;cursor:pointer;background:transparent;border:0;-webkit-appearance:none;display:block;outline:none;padding:0;z-index:9046;box-shadow:none;touch-action:manipulation}
button::-moz-focus-inner{padding:0;border:0}
.mfp-close{width:44px;height:44px;line-height:44px;position:absolute;right:0;top:0;text-decoration:none;text-align:center;opacity:.65;padding:0 0 18px 10px;color:#FFF;font-style:normal;font-size:28px;font-family:Arial,Baskerville,monospace}
.mfp-close:hover,.mfp-close:focus{opacity:1}
.mfp-close:active{top:1px}
.mfp-close-btn-in .mfp-close{font-size:26px;padding:0;color:#333}
.mfp-image-holder .mfp-close,.mfp-iframe-holder .mfp-close{color:#FFF;right:-6px;text-align:right;padding-right:6px;width:100%}
.mfp-counter{position:absolute;top:0;right:0;color:#CCC;font-size:12px;line-height:18px;white-space:nowrap}
.mfp-arrow{position:absolute;opacity:.65;margin:0;top:50%;margin-top:-55px;padding:0;width:90px;height:110px;-webkit-tap-highlight-color:transparent}
.mfp-arrow:active{margin-top:-54px}
.mfp-arrow:hover,.mfp-arrow:focus{opacity:1}
.mfp-arrow:before,.mfp-arrow:after{content:''!important;display:block!important;width:0;height:0;position:absolute;left:0;top:0;margin-top:35px;margin-left:35px;border:medium inset transparent;background:none}
.mfp-arrow:after{border-top-width:13px;border-bottom-width:13px;top:8px}
.mfp-arrow:before{border-top-width:21px;border-bottom-width:21px;opacity:.7}
.mfp-arrow-left{left:0}
.mfp-arrow-left:after{border-right:17px solid #FFF;margin-left:31px}
.mfp-arrow-left:before{margin-left:25px;border-right:27px solid #3F3F3F}
.mfp-arrow-right{right:0}
.mfp-arrow-right:after{border-left:17px solid #FFF;margin-left:39px}
.mfp-arrow-right:before{border-left:27px solid #3F3F3F}
.mfp-iframe-holder{padding-top:40px;padding-bottom:40px}
.mfp-iframe-holder .mfp-content{line-height:0;width:100%;max-width:900px}
.mfp-iframe-holder .mfp-close{top:-40px}
.mfp-iframe-scaler{width:100%;height:0;overflow:hidden;padding-top:56.25%}
.mfp-iframe-scaler iframe{position:absolute;display:block;top:0;left:0;width:100%;height:100%;box-shadow:0 0 8px rgba(0,0,0,0.6);background:#000}
img.mfp-img{width:auto;max-width:100%;height:auto;display:block;line-height:0;box-sizing:border-box;padding:40px 0;margin:0 auto}
.mfp-figure{line-height:0}
.mfp-figure:after{content:'';position:absolute;left:0;top:40px;bottom:40px;display:block;right:0;width:auto;height:auto;z-index:-1;box-shadow:0 0 8px rgba(0,0,0,0.6);background:#444}
.mfp-figure small{color:#BDBDBD;display:block;font-size:12px;line-height:14px}
.mfp-figure figure{margin:0}
.mfp-bottom-bar{margin-top:-36px;position:absolute;top:100%;left:0;width:100%;cursor:auto}
.mfp-title{text-align:left;line-height:18px;color:#F3F3F3;word-wrap:break-word;padding-right:36px}
.mfp-image-holder .mfp-content{max-width:100%}
.mfp-gallery .mfp-image-holder .mfp-figure{cursor:pointer}

.mfp-wrap button:after{width:0!important;height:0!important;background:none!important}
.button-flat .mfp-arrow:after, .button-round .mfp-arrow:after{top:8px;opacity:1}
.button-stroke .mfp-close{color:#fff!important}
.button-stroke .mfp-close:hover{background:none!important}
.button-stroke .mfp-arrow:hover{background:none!important}

.mfp-inline{display:block!important;position:relative;background:#FFF;padding:25px 30px;width:auto;max-width:500px;margin:20px auto;}
.mfp-inline p:nth-last-child(2){margin-bottom:0;}
.button-stroke .mfp-inline .mfp-close{color:#333!important}

.mfp-iframe-holder .mfp-content{text-align:center}
.mfp-mp4{display:inline-block!important;position:relative;width:auto;max-width:80vw;max-height:80vh;margin:0 auto;}
.mfp-mp4 video{max-width:100%;height:auto;margin:0 auto}
.mfp-mp4 .mfp-close{right:-30px}

@media screen and (max-width: 800px) and (orientation: landscape),screen and (max-height: 300px) {
	.mfp-img-mobile .mfp-image-holder{padding-left:0;padding-right:0}
	.mfp-img-mobile img.mfp-img{padding:0}
	.mfp-img-mobile .mfp-figure:after{top:0;bottom:0}
	.mfp-img-mobile .mfp-figure small{display:inline;margin-left:5px}
	.mfp-img-mobile .mfp-bottom-bar{background:rgba(0,0,0,0.6);bottom:0;margin:0;top:auto;padding:3px 5px;position:fixed;box-sizing:border-box}
	.mfp-img-mobile .mfp-bottom-bar:empty{padding:0}
	.mfp-img-mobile .mfp-counter{right:5px;top:3px}
	.mfp-img-mobile .mfp-close{top:0;right:0;width:35px;height:35px;line-height:35px;background:rgba(0,0,0,0.6);position:fixed;text-align:center;padding:0}
}

@media all and (max-width: 900px) {
	.mfp-arrow{-webkit-transform:scale(0.75);transform:scale(0.75)}
	.mfp-arrow-left{-webkit-transform-origin:0;transform-origin:0}
	.mfp-arrow-right{-webkit-transform-origin:100%;transform-origin:100%}
	.mfp-container{padding-left:6px;padding-right:6px}
}

/* Slick Slider */

.slick-slider{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;-ms-touch-action:pan-y;touch-action:pan-y;-webkit-tap-highlight-color:transparent}
.slick-list{position:relative;display:block;overflow:hidden;margin:0;padding:0}
.slick-list:focus{outline:none}
.slick-list.dragging{cursor:pointer;cursor:hand}
.slick-slider .slick-track,.slick-slider .slick-list{-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);-o-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}
.slick-track{position:relative;top:0;left:0;display:block}
.slick-track:before,.slick-track:after{display:table;content:''}
.slick-track:after{clear:both}
.slick-loading .slick-track{visibility:hidden}
.slick-slide{display:none;float:left;height:100%;min-height:1px;outline:none}
[dir='rtl'] .slick-slide{float:right!important}
.slick-slide img{display:block;margin-left:auto;margin-right:auto;}
.slick-slide.slick-loading img{display:none}
.slick-slide.dragging img{pointer-events:none}
.slick-initialized .slick-slide{display:block}
.slick-loading .slick-slide{visibility:hidden}
.slick-vertical .slick-slide{display:block;height:auto;border:1px solid transparent}
.slick-arrow{line-height:20px;margin-bottom:0!important}
.slick-arrow.slick-hidden{display:none}
.slider_pager .slick-dots{margin:0}

/* Slider Pro */

#Content .slider-pro img.sp-image{max-width:none}

/* Toolset */

.the_content_wrapper .pagination-dots{margin:0}

/* The Events Callendar */

body .tribe-events button { background-color: transparent; box-shadow: unset; color: inherit; }
body .tribe-events button:after { background: transparent; }

.tribe-events .tribe-events-c-subscribe-dropdown__button button { margin: 0; }

.tribe-events-event-meta.primary,
.tribe-events-event-meta.secondary { flex-grow: 1; }

.tribe-events-event-meta dt { width: auto; float: unset; }
.tribe-events-event-meta dd,
.tribe-events-event-meta dt { border: 0; }

.tribe-events .datepicker th,
.tribe-events .datepicker td { box-shadow: unset; }
.tribe-events .datepicker th { background-color: transparent; }

.tribe-events .tribe-events-c-events-bar .tribe-common-form-control-text__input { box-shadow: unset; }

/* UI Datepicker */

.ui-datepicker .ui-datepicker-title select{padding:inherit;width:auto;display:inline-block;margin:auto;-webkit-appearance:menulist}
.ui-datepicker .ui-datepicker-calendar .ui-state-highlight{margin:-1px}
.ui-datepicker .ui-datepicker-calendar .ui-state-default{box-shadow:0 0 0 0 rgba(0,0,0,.0)}
.ui-datepicker table tr{border:0!important}
.ui-datepicker table tr td{border-width:0 1px 1px 0 !important;border-style:solid;border-color:#DEDEDE;padding:1px}
.ui-datepicker table td a,.ui-datepicker table td span{width:auto!important;display:block!important}
.ui-datepicker table tr:first-child td{background:inherit!important}
.ui-datepicker table tr:hover td{background:inherit!important;color:inherit!important}
.ui-datepicker table tr:hover td a{color:inherit!important}
.ui-datepicker table tr:nth-child(2n) td{background:inherit!important}

/* Visual Composer | Frontend Editor */

.wpb_wrapper > div{margin-bottom:35px}
.wpb_wrapper.ui-sortable .vc_call_to_action{background:none;border:none;padding:0}
body.layout-boxed .vc_row[data-vc-stretch-content="true"]{left:0!important;width:auto!important;margin-left:-2.7%;margin-right:-2.7%}
body.layout-boxed .vc_row-no-padding .wpb_column{padding:0}

.vc_images_carousel{width:auto!important}
.vc-hoverbox-block{-webkit-backface-visibility:hidden}

.wpb_wrapper .portfolio_group{font-size:inherit!important;line-height:inherit!important;color:inherit!important}
.wpb_wrapper .portfolio_group.flat .portfolio-item,.wpb_wrapper .portfolio_group.masonry-flat .portfolio-item{margin-bottom:0}
.wpb_wrapper .portfolio_group .portfolio-item .image_frame{margin-bottom:0}

.wpb_wrapper .wpcf7-form{display:inline-block;width:100%}

/* Accessibility PBL */

body.keyboard-support a:focus{ outline: auto !important; }
body.keyboard-support *[role="link"]:focus{ outline: auto !important; }
body.keyboard-support button:focus{ outline: auto !important; }

body.keyboard-support #Top_bar .menu li.submenu > a > span{padding-right:30px}
body.keyboard-support #Top_bar .menu li.submenu .menu-toggle{display:block;position:absolute;width:20px;height:20px;line-height:20px;text-align:center;padding:0;top:50%;right:5px;transform:translateY(-50%);opacity:.3}
body.keyboard-support #Top_bar .menu li.submenu .menu-toggle:after{content:"+";display:block;height:20px;line-height:20px}
body.keyboard-support #Top_bar .menu li.submenu .menu-toggle:focus{opacity:1}
body.keyboard-support #Top_bar .menu li.submenu ul li .menu-arrow{display:none}
body.keyboard-support #Top_bar .menu li.submenu ul li .menu-toggle{border:none}

body.keyboard-support .image_frame{ overflow:unset; }
body.keyboard-support .portfolio-photo .portfolio-item{ overflow:unset; }

body.keyboard-support .slider_pagination li.slick-active:focus {outline: auto;}
body.keyboard-support .slider_pagination li a {text-indent: 0; color:transparent;}

body.keyboard-support .wpml-lang-dropdown[aria-expanded="true"] { display: block !important}

body.underline-links .mfn-builder-content .column_column a:not(.button),
body.underline-links .the_content a:not(.button),
body.underline-links .widget_text a:not(.button){text-decoration:underline}

#skip-links-menu {position: absolute; z-index: 99; top:-200px;}
#skip-links-menu ul{ display:flex; flex-direction:row; flex-wrap:wrap; }
#skip-links-menu ul li{ background: white; color:#006edf; margin:20px 0px; }
#skip-links-menu ul li a{ background: white; color:#006edf; margin:5px; padding:15px 15px; font-weight:500 }

body.keyboard-support .mfn-menu-item-megamenu[aria-expanded="true"]{display: block !important}
body.keyboard-support .mfn-submenu[aria-expanded="true"]{display: block !important}
body.keyboard-support .sub-menu[aria-expanded="true"]{display: block !important}
body.keyboard-support .mfn-header-tmpl-menu-sidebar[aria-expanded="false"]{display: none !important}
body.keyboard-support .mfn-header-tmpl-menu-sidebar[aria-expanded="true"]{display: block !important}

/* Grid 1240px */

body{min-width:960px}

body.admin-bar{position:relative}
/* #wpadminbar ~ div:nth-last-child(2){height:auto!important} */

#Wrapper{max-width:1240px;margin:0 auto;overflow:hidden;position:relative;}

.layout-boxed{padding:25px 0}
.layout-boxed.boxed-no-margin{padding:0}
.layout-boxed #Wrapper{box-shadow:0 0 15px rgba(0,0,0,.06)}

.layout-full-width{padding:0}
.layout-full-width #Wrapper{max-width:100%!important;width:100%!important;margin:0!important}

#Content{width:100%;padding-top:30px;z-index: 0;position:relative;}
#Content.no-padding,.template-slider #Content,.with_aside #Content{padding-top:0}

.with_aside .sections_group{padding-top:30px}

.section{position:relative;box-sizing:border-box;}
.section_wrapper,.container{max-width:1220px;height:100%;margin:0 auto;position:relative}
.section.full-width > .section_wrapper{max-width:100%!important;padding-left:0!important;padding-right:0!important}
.full-screen {}
.full-screen .section_wrapper {  min-height: 100vh; align-content: center; }
.section_wrapper:after,.container:after{clear:both;content:" ";display:block;height:0;visibility:hidden;width:100%;}

.section.full-width-deprecated > .one.column .mcb-column-inner,
.section.full-width-deprecated > .section_wrapper > .one.column .mcb-column-inner,
.section.full-width-deprecated .one.wrap .one.column .mcb-column-inner{width:100%;margin:0}

.mcb-wrap{float:left;position:relative;z-index:1;box-sizing:border-box}
.mcb-wrap.divider{width:100%;height:0!important}
.mcb-wrap.move-up{z-index:28}

.mcb-wrap.wrap-sticky-spacer{position:relative;min-height:1px;z-index:2}
.mcb-wrap.wrap-sticky-rails{position:absolute;width:100%}
.mcb-wrap.wrap-sticky-spacer .mcb-wrap{align-self:unset!important}
.mcb-wrap.sticky.fixed{position:fixed}
.mcb-wrap.sticky.stick-bottom{position:absolute;top:auto!important;bottom:0}

.mcb-wrap.sticky.sticky-desktop:not(.stickied) .animate{opacity: 0 !important;}

.column,.columns{float:left;margin:0;}

.the_content_wrapper{margin:0 1%;width:98%;}
.has_content .the_content_wrapper{margin-bottom:15px}
.column_content .the_content_wrapper{margin:0}

.sidebar,.widget-area{box-sizing:border-box}

.full-width-site .container,
.full-width-site .content_wrapper,
.full-width-site .section_wrapper{max-width:unset!important}

.full-width-content #Content .content_wrapper,
.full-width-content #Content .section_wrapper{max-width:unset!important}

/* Wrap | Equal Height */
.equal-height-wrap .mcb-wrap .mcb-wrap-inner{position:relative;float:left;width:100%}
.equal-height-wrap .mcb-wrap.valign-middle .mcb-wrap-inner{align-content:center;}
.equal-height-wrap .mcb-wrap.valign-bottom .mcb-wrap-inner{align-content:flex-end;}

/* Background fix */
.section, .mcb-wrap, .mcb-column { background-position: left top; background-repeat: no-repeat; }

/* Sizes */

/* .mcb-wrap */
.one-sixth.mcb-wrap			{ width: 16.666%; }	/* 1/6 */
.one-fifth.mcb-wrap			{ width: 20%; }			/* 1/5 */
.one-fourth.mcb-wrap		{ width: 25%; }			/* 1/4 */
.one-third.mcb-wrap			{ width: 33.333%; }	/* 1/3 */
.two-fifth.mcb-wrap			{ width: 40%; }			/* 2/5 */

.one-second.mcb-wrap		{ width: 50%; }			/* 1/2 */
.three-fifth.mcb-wrap		{ width: 60%; }			/* 3/5 */
.two-third.mcb-wrap			{ width: 66.666%; }	/* 2/3 */
.three-fourth.mcb-wrap	{ width: 75%; }			/* 3/4 */
.four-fifth.mcb-wrap		{ width: 80%; }			/* 4/5 */
.five-sixth.mcb-wrap		{ width: 83.333%; }	/* 5/6 */
.one.mcb-wrap						{ width: 100%; }		/* 1/1 */

/* .column */
.one-sixth.column				{ width: 16.666%; }	/* 1/6 */
.one-fifth.column				{ width: 20%; }			/* 1/5 */
.one-fourth.column,
.four.columns						{ width: 25%; }			/* 1/4 */
.one-third.column				{ width: 33.333%; }	/* 1/3 */
.two-fifth.column				{ width: 40%; }			/* 2/5 */

.one-second.column			{ width: 50%; }			/* 1/2 */
.three-fifth.column			{ width: 60%; }			/* 3/5 */
.two-third.column				{ width: 66.666%; }	/* 2/3 */
.three-fourth.column		{ width: 75%; }			/* 3/4 */
.four-fifth.column			{ width: 80%; }			/* 4/5 */
.five-sixth.column			{ width: 83.333%; }	/* 5/6 */
.one.column							{ width: 100%; }			/* 1/1 */

.mcb-column { position: relative; }
.mcb-column-inner { position: relative; margin-top: var(--mfn-column-gap-top); margin-bottom: var(--mfn-column-gap-bottom); margin-left: var(--mfn-column-gap-left); margin-right: var(--mfn-column-gap-right); }
.mcb-column-absolute .mcb-column-inner { width: 100%; z-index: 1; }
.column_trailer_box .mcb-column-inner{overflow: hidden;}

.mfn-item-inline.column { width: auto !important; flex: 0 0 auto !important; max-width: 100%; }

/* Equal height */
.equal-height .mcb-wrap .mcb-wrap-inner { align-items: stretch; } /* Columns */
.equal-height .mcb-wrap .mcb-column-inner { height: calc(100% - var(--mfn-column-gap-bottom)); }
.equal-height.no-margin-v .mcb-wrap .mcb-column-inner,
.equal-height .column-margin-0px .mcb-column-inner { height: 100%; } /* no-margin-h, margin-bottom-0px fix */
.equal-height-wrap .section_wrapper { align-items: stretch; } /* Wraps */

/* .Sidebar | .with_aside */
.with_aside .content_wrapper{max-width:1240px;margin:0 auto}
.with_aside .sections_group{width:75%;float:left}
.with_aside #Content .section_wrapper{max-width:98%;max-width:calc(100% - 20px)}
.with_aside .four.columns{float:right;margin:0}

.aside_left .sections_group{float:right}
.aside_left .four.columns{float:left}

/* Sidebar - Both | .aside_both */
.aside_both .sections_group{width:60%;margin-left:20%}
.aside_both .sidebar.columns{width:18%}
.aside_both .sidebar-1{float:left!important;margin-left:-79%}

/* Column */

/* Column | Margin - Horizontal */
.no-margin-h .column .mcb-column-inner { margin-left:0; margin-right:0; }

/* Column | Margin - Bottom */

/* set in section options */
.column.column_divider .mcb-item-divider-inner{margin-bottom:0!important;height:auto!important}

.column-margin-0px .mcb-column-inner	{ margin-bottom: 0 !important;}
.column-margin-10px .mcb-column-inner	{ margin-bottom: 10px !important;}
.column-margin-20px .mcb-column-inner	{ margin-bottom: 20px !important;}
.column-margin-30px .mcb-column-inner	{ margin-bottom: 30px !important;}
.column-margin-40px .mcb-column-inner	{ margin-bottom: 40px !important;}
.column-margin-50px .mcb-column-inner	{ margin-bottom: 50px !important;}

/* set in item options */
.column-margin-0px.mcb-column-inner		{ margin-bottom: 0 !important;}
.column-margin-10px.mcb-column-inner	{ margin-bottom: 10px !important;}
.column-margin-20px.mcb-column-inner	{ margin-bottom: 20px !important;}
.column-margin-30px.mcb-column-inner	{ margin-bottom: 30px !important;}
.column-margin-40px.mcb-column-inner	{ margin-bottom: 40px !important;}
.column-margin-50px.mcb-column-inner	{ margin-bottom: 50px !important;}

/* Column | Equal Height */

.equal-height .column .column_attr{height:100%;box-sizing:border-box}

/* Column | Align */

.column_attr.align_left{text-align:left}
.column_attr.align_right{text-align:right}
.column_attr.align_center{text-align:center}
.column_attr.align_justify{text-align:justify}

/* Extra Content */

.extra_content .the_content_wrapper{margin:0 1%}
.extra_content .has_content .the_content_wrapper,.extra_content .category_description .the_content_wrapper{margin-bottom:40px}
.the_content_wrapper.is-elementor{margin-bottom:0!important;}

/* Clearing */

.container:after{content:"\0020";display:block;height:0;clear:both;visibility:hidden}
.clearfix:before,.clearfix:after{content:'\0020';display:block;overflow:hidden;visibility:hidden;width:0;height:0}
.clearfix:after{clear:both}
.clear{clear:both;display:block;overflow:hidden;visibility:hidden;width:0;height:0}

/* Global */

.mfn-main-slider{position:relative;z-index:0} /* HB2: when wrap is move up its below slider */
.rev_slider iframe{max-width:1220px}
.rev_slider ul{margin:0!important}
.tp-static-layers{z-index:200}
.mfn-layer-slider{position:relative;z-index:0} /* HB2: slider overlapping header submenu */
.mfn-layer-slider .ls-yourlogo{z-index:1}

/* Section */

.section[data-parallax="3d"]{overflow:hidden}
.section .mfn-parallax{position:absolute;left:0;top:0;max-width:none!important;transition:opacity .2s;}
.wrap[data-parallax="3d"]{overflow:hidden;position:relative}
.wrap[data-parallax="3d"] > .mcb-wrap-inner,.wrap[data-parallax="3d"] > .column{position:relative}
.wrap[data-parallax="3d"] > .mcb-wrap-inner{background:unset!important}
.section > .mcb-background-overlay { width: 100%; height: 100%; position: absolute; z-index: 1; top: 0; left: 0;}
.wrap > .mcb-wrap-inner > .mcb-wrap-background-overlay{width:100%;height:100%;position:absolute;left:0;top:0;border-radius:inherit}
.section.center{text-align:center}
.section.no-margin .column .mcb-column-inner,.section.no-margin-v .column .mcb-column-inner {margin-bottom:0}
.the_content_wrapper pre{margin-bottom:20px}

/* Section | Highlight */

.highlight-left,.highlight-right{position:relative}
.highlight-left:after,.highlight-right:after{content:"";position:absolute;width:50%;height:100%;top:0;z-index:1}
.highlight-left:after{left:0}
.highlight-right:after{right:0}
.highlight-left .section_wrapper,.highlight-right .section_wrapper{z-index:2}

@media only screen and (max-width: 767px) {
	.highlight-left:after,.highlight-right:after{content:none}
}

/* Transparent */

.tr-content #Wrapper,.tr-content #Content{background:none}
.tr-header #Wrapper{background:none}
.tr-header #Header_wrapper{background:none!important}
.tr-footer #Wrapper{background:none}
.tr-footer #Footer{background-color:transparent!important}

/* No Shadow */

.no-shadows #Wrapper,.no-shadows #Top_bar.is-sticky,.no-shadows #Header_creative,
.no-shadows input[type="date"],.no-shadows input[type="email"],.no-shadows input[type="number"],.no-shadows input[type="password"],.no-shadows input[type="search"],.no-shadows input[type="tel"],
.no-shadows input[type="text"],.no-shadows input[type="url"],.no-shadows select,.no-shadows textarea,.no-shadows .woocommerce .quantity input.qty,
.no-shadows table th, .no-shadows table tr:first-child td,
.no-shadows .accordion .question > div{box-shadow:0 0 0 transparent;-webkit-box-shadow:0 0 0 transparent}
.no-shadows #Subheader:after{display:none}
.no-shadows .ui-tabs .ui-tabs-nav, .no-shadows .ui-tabs .ui-tabs-panel{background-image:none}

/* Blank & Under Construction */

.page.hide-title-area #Content,.no-content-padding #Content,.template-blank #Content,.under-construction #Content{padding:0!important}
.with_aside.page.hide-title-area .sections_group,.with_aside.no-content-padding .sections_group{padding:0!important}
.under-construction .section-uc-1{padding-top:40px;background-color:#fff}
.under-construction .section-uc-2{padding-top:40px;background-color:#f5f5f5;background:url(../images/stripes/stripes_3_b.png) repeat center}
.under-construction .section-uc-3{padding-top:40px}
.under-construction .section-border-top{border-top:1px solid rgba(0,0,0,0.08)}

.under-construction .text-logo{font-size:50px}
.under-construction #mfn-gdpr.show{ display: none; }

/* Slider | Pagination */

.slider_pagination{text-align:center;line-height:0}
.slider_pagination a{display:inline-block;width:12px;height:12px;text-indent:-9999px;margin:0 9px;background:rgba(0,0,0,.15);-webkit-border-radius:100%;border-radius:100%;position:relative;cursor:pointer}
.slider_pagination a:hover{background:rgba(0,0,0,.25)}

.slider_pagination a.selected,
.slider_pagination .slick-active a{width:10px;height:8px;margin:0 10px;position:relative;top:4px;background:rgba(0,0,0,.15);-webkit-border-radius:2px;border-radius:2px}

.slider_pagination a.selected:after,
.slider_pagination .slick-active a:after{content:"";display:block;width:8px;height:8px;-webkit-border-radius:1px;border-radius:1px;position:absolute;left:1px;top:-3px;background:#D6D6D6;-webkit-transform:rotate(45deg);transform:rotate(45deg)}

/* Slider Revolution */

.mfn-rev-slider input{display:inline-block}
.rs-p-wp-fix{margin-bottom:0}

/* Sliding top */

#Sliding-top{position:absolute;left:0;top:0;width:100%;z-index:800}
#Sliding-top:after{content:"";height:3px;width:100%;display:block;position:absolute;left:0;bottom:-3px;z-index:1;box-shadow:inset 0px 4px 3px -2px rgba(0,0,0,.06)}
#Sliding-top .widgets_wrapper{padding:15px 0;display:none}
#Sliding-top .widgets_wrapper .mcb-column-inner{margin-bottom:0}
#Sliding-top .widgets_wrapper .widget{margin-bottom:0;padding:15px 0}
#Sliding-top .widgets_wrapper .widget:after{display:none}
#Sliding-top a.sliding-top-control{display:block;width:0;height:0;border-style:solid;border-width:0 45px 45px 0;border-color:transparent;position:absolute;z-index:801;right:0;bottom:-45px}
#Sliding-top a.sliding-top-control span{display:block;width:26px;height:30px;line-height:25px;text-align:center;position:absolute;right:-45px;top:0;color:#fff;font-size:18px}
#Sliding-top a.sliding-top-control .minus{display:none}
#Sliding-top a.sliding-top-control .plus{display:inline-block}
#Sliding-top.active a.sliding-top-control .minus{display:inline-block}
#Sliding-top.active a.sliding-top-control .plus{display:none}
#Sliding-top.st-center a.sliding-top-control{border-color:transparent;border-width:45px 45px 0;left:50%;right:auto;transform:translateX(-50%);margin-right:0}
#Sliding-top.st-center a.sliding-top-control span{left:-14px;right:auto;top:-45px}
#Sliding-top.st-left a.sliding-top-control{border-color:transparent;border-width:45px 45px 0 0;left:0;right:auto;margin-right:0}
#Sliding-top.st-left a.sliding-top-control span{left:-3px;right:auto;top:-45px}
#Sliding-top .Recent_posts ul li .desc{background:rgba(0,0,0,.1)}
#Sliding-top .widget_mfn_menu ul li a{background:rgba(0,0,0,.1);color:#ccc}
#Sliding-top .widget_recent_entries ul li{background:rgba(0,0,0,.1)}
#Sliding-top ul.list_mixed li:after,#Sliding-top ul.list_check li:after,#Sliding-top ul.list_star li:after,#Sliding-top ul.list_idea li:after{background:rgba(255,255,255,.08)}
#Sliding-top .widget_mfn_recent_comments ul li .date_label{background-color:rgba(0,0,0,.07)}
#Sliding-top .widget_mfn_recent_comments ul li .date_label:after{border-left-color:rgba(0,0,0,.07)}

/* Off canvas sidebar ------ */
.mfn-off-canvas-sidebar{position:fixed;top:0;left:-320px;display:flex; flex-direction: column; width:320px;max-width:100%;height:100%;z-index:100000;transition:0.3s;box-sizing:border-box;}
.mfn-off-canvas-sidebar .mfn-off-canvas-switcher{display:flex;align-items:center;justify-content:center;height:60px;width:30px;position:absolute;top:50%;transform:translateY(-50%);left:100%;background-color:#fff;box-shadow: 0px 5px 15px 0px rgba(8, 8, 14, 0.13);cursor:pointer;border-radius:0 4px 4px 0;}
.mfn-off-canvas-sidebar .mfn-off-canvas-switcher svg { width: 22px; }
.mfn-off-canvas-sidebar .mfn-off-canvas-switcher svg .path { stroke: #333; }
.mfn-off-canvas-sidebar .mfn-off-canvas-switcher i { font-size: 16px; color: #333; }
.mfn-off-canvas-sidebar .mfn-off-canvas-content-wrapper {position:relative;overflow:hidden;height: 100%;background-color:#fff;}
.mfn-off-canvas-sidebar .mfn-off-canvas-content-wrapper .mfn-off-canvas-content { height: 100%; overflow-x: hidden; overflow-y: auto; padding: 20px; }
.mfn-off-canvas-sidebar .widget:first-child { margin-top: 0; }
.mfn-off-canvas-sidebar .widget:last-child { margin-bottom: 0; }
.mfn-off-canvas-sidebar .widget:after { display: none; }

.mfn-off-canvas-overlay{ display: none; }

.mfn-ofcs-opened { overflow: hidden; padding-right: 15px; } /* <html> class */
.mfn-ofcs-opened  .mfn-off-canvas-overlay { display: block; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); position: fixed; top: 0; left: 0; z-index: 1000; }
.mfn-ofcs-opened .mfn-off-canvas-sidebar{left:0}

/* Pager */

.pager_wrapper{margin-bottom:0}
.pager{text-align:center}
.pager .pages{display:inline-block;margin:20px 30px 0;padding:4px 3px;-webkit-border-radius:5px;border-radius:5px;background-color:#f8f8f8;background-image:url(../images/stripes/stripes_3_b.png)}
.pager .pages a,.pager .pages span.page-numbers{display:inline-block;margin:0 1px;width:35px;height:35px;line-height:35px;text-align:center;-webkit-border-radius:5px;border-radius:5px}
.pager .pages a:hover{text-decoration:none}
.pager .pages a:hover,.pager .pages a.active,.pager .pages span.page-numbers.current{color:#fff}
.pager a.next_page,.pager a.prev_page{display:inline-block;line-height:43px}
.pager .pages a{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

.button-round .pager .pages{border-radius:50px;background-image:none}
.button-round .pager .pages .page{border-radius:50px}

/* Load more button */

.pager_lm{background:url(../images/preloader.gif) no-repeat center 17px;text-align:center}
.pager_lm .pager_load_more{margin:20px 0}
.pager_lm.loading{min-height:49px}
.pager_lm:not(.loading){background:none}

.mfn-infinite-load-button{clear:both;height:75px}
.mfn-infinite-load-button .pager_wrapper{display:none}

/* Pager single | blog post */

.page-pager .mcb-column-inner,.post-pager .mcb-column-inner{margin-bottom:0!important;}
.pager-single{text-align:center;background:rgba(0,0,0,0.02);margin-top:15px;margin-bottom:40px}
.pager-single span{height:45px;line-height:45px;display:inline-block;padding:0 9px;position:relative;font-weight:700}
.pager-single a span{font-weight:400}
.pager-single span:after{content:"";display:block;position:absolute;left:0;bottom:-1px;width:100%;height:1px}
.pager-single a span:after{display:none}

/* Fixed Navigation */

#Content .fixed-nav{display:none}
.fixed-nav{position:fixed;bottom:40px;height:80px;z-index:90}
.fixed-nav.fixed-nav-prev{left:0}
.fixed-nav.fixed-nav-next{right:0}
.fixed-nav .arrow{display:block;width:35px;height:80px;font-size:15px;position:relative;z-index:92;color:#fff;line-height:80px;text-align:center}
.fixed-nav .photo{height:80px;width:80px;position:relative;z-index:92;overflow:hidden;line-height:0;background-color:#eee}
.fixed-nav .desc{width:190px;padding:6px 15px;min-height:68px;background:#fff;z-index:91}
.fixed-nav .desc h6{margin-bottom:0}
.fixed-nav .desc i{display:inline-block;margin-right:2px}
.fixed-nav .desc i:before{margin-left:0}
.fixed-nav .desc .date{display:inline-block;width:100%;padding-top:3px;border-top-width:1px;border-top-style:solid}

.fixed-nav-prev .arrow,.fixed-nav-prev .photo,.fixed-nav-prev .desc{float:left}
.fixed-nav-prev .desc{margin-left:-335px}
.fixed-nav-prev:hover .desc,
.fixed-nav-prev:focus .desc{margin-left:0}
.fixed-nav-next .arrow,.fixed-nav-next .photo,.fixed-nav-next .desc{float:right}
.fixed-nav-next .desc{margin-right:-335px}
.fixed-nav-next:hover .desc,
.fixed-nav-next:focus .desc{margin-right:0}

.fixed-nav .photo,.fixed-nav .desc{transition:all .3s ease-in-out}
.fixed-nav.format-quote .desc{display:none}
.fixed-nav.format-quote .photo{ background-color:#eee; position: relative; z-index: 91; }
.fixed-nav.format-quote .photo:after {content: '\e909'; font-family: "mfn-icons"; display: block; z-index:91; position: absolute; left: 0; top:0; width: 80px; height: 80px; line-height: 80px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }
.fixed-nav.format-link .photo img{display:none}
.fixed-nav.format-link .photo{background-color:#eee; position: relative; z-index: 91; }
.fixed-nav.format-link .photo:after {content: '\e8c2'; font-family: "mfn-icons"; display: block; z-index:91; position: absolute; right: 0; top:0; width: 80px; height: 80px; line-height: 80px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }

@media only screen and (max-width: 1430px) {
	.fixed-nav-prev .photo{position:static;margin-left:-115px}
	.fixed-nav-prev:hover .photo,
	.fixed-nav-prev:focus .photo{margin-left:0}
	.fixed-nav-prev:focus .photo{margin-left:0}
	.fixed-nav-next .photo{position:static;margin-right:-115px}
	.fixed-nav-next:hover .photo,
	.fixed-nav-next:focus .photo{margin-right:0}
}

.fixed-nav.style-images .arrow{display:none}
.fixed-nav.style-images .photo{background:#EEEEEE;margin:0;}
.fixed-nav.style-images .photo:before{font-family:'mfn-icons';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:32px;color:#ccc;transition:color .1s ease-in-out}
.fixed-nav.style-images:hover .photo:before,
.fixed-nav.style-images:focus .photo:before{color:#aaa}
.fixed-nav-prev.style-images .photo:before{content:'\e8b8'}
.fixed-nav-next.style-images .photo:before{content:'\e916'}
.fixed-nav.style-images .photo:after { display: none; }
.fixed-nav.style-images .photo img{position:relative;transition:opacity .1s ease-in-out}
.fixed-nav.style-images:hover .photo img,
.fixed-nav.style-images:focus .photo img{opacity:0}
.fixed-nav.style-images .desc{display:none}

.fixed-nav.style-arrows .arrow{display:none}
.fixed-nav.style-arrows .photo{background:#EEEEEE;margin:0;}
.fixed-nav.style-arrows .photo:before{font-family:'mfn-icons';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:32px;color:#ccc;transition:color .1s ease-in-out}
.fixed-nav.style-arrows:hover .photo:before,
.fixed-nav.style-arrows:focus .photo:before{color:#aaa}
.fixed-nav-prev.style-arrows .photo:before{content:'\e8b8'}
.fixed-nav-next.style-arrows .photo:before{content:'\e916'}
.fixed-nav.style-arrows .photo:after { display: none; }
.fixed-nav.style-arrows .photo img{display:none;}
.fixed-nav.style-arrows .desc{display:none}

.header-creative.tr-menu .fixed-nav .desc{margin:0;opacity:0}
.header-creative.tr-menu .fixed-nav:hover .desc,
.header-creative.tr-menu .fixed-nav:focus .desc{opacity:1}

/* Filters */

#Filters .mcb-column-inner{margin-bottom:30px}
#Filters .filters_buttons{padding:15px;margin:0;font-size:100%;background-image:url(../images/stripes/stripes_5_b.png);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;overflow:hidden}
#Filters .filters_buttons li{float:left;margin:0 15px 0 0;list-style:none}
#Filters .filters_buttons li.categories i,#Filters .filters_buttons li.tags i{margin-right:3px}
#Filters .filters_buttons li.reset{float:right;margin-right:0}

#Filters .filters_wrapper{display:none;margin-top:20px}
#Filters .filters_wrapper ul{display:none;overflow:hidden;margin:0}
#Filters .filters_wrapper ul li{display:inline-block;width:18.9%;margin:.5%;list-style:none}
#Filters .filters_wrapper ul li a{display:block;padding:7px 10px;background:#fff;border:1px solid #F7F7F7;color:#858585}
#Filters .filters_wrapper ul li a:hover,#Filters .filters_wrapper ul li.current-cat a{text-decoration:none;color:#fff}
#Filters .filters_wrapper ul li.close{width:auto!important}
#Filters .filters_wrapper ul li.close a{text-align:center;width:38px;padding:7px 0;background:#8B8B8B;border:1px solid #F7F7F7!important;color:#fff}
#Filters .filters_wrapper ul li.close a:hover{background:#545454}
#Filters .filters_wrapper li.reset-inner{display:none}

	#Filters.only .filters_buttons{display:none}
	#Filters.only .filters_wrapper{display:block}
	#Filters.only li.reset-inner{display:inline-block}
	#Filters.only li.close{display:none!important}
	#Filters.only-categories .categories{display:block}
	#Filters.only-categories .tags{display:none!important}
	#Filters.only-categories .authors{display:none!important}
	#Filters.only-tags .categories{display:none!important}
	#Filters.only-tags .tags{display:block}
	#Filters.only-tags .authors{display:none!important}
	#Filters.only-authors .categories{display:none!important}
	#Filters.only-authors .tags{display:none!important}
	#Filters.only-authors .authors{display:block}

	#Filters .filters_wrapper ul li a{-webkit-transition:all .2s ease-in-out;-moz-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;-ms-transition:all .2s ease-in-out;transition:all .2s ease-in-out}

/* Header */

#Header_wrapper{position:relative}
body:not(.template-slider) #Header_wrapper{background-repeat:no-repeat;background-position:top center}
body:not(.template-slider) #Header_wrapper.bg-fixed{background-attachment:fixed}
body.mhb #Header_wrapper{background-image:none!important}

#Header{position:relative}
body:not(.template-slider) #Header{min-height:250px}
body.header-below:not(.template-slider) #Header{min-height:0}

.single-template-intro #Header{min-height:0!important}

#Header .container{padding-left:var(--mfn-column-gap-left);padding-right:var(--mfn-column-gap-right);box-sizing:border-box}

/* Action Bar */

#Action_bar{position:absolute;left:0;top:0;width:100%;z-index:30;line-height:21px;}

#Action_bar .column{margin-bottom:0;overflow:hidden;padding: 0 20px; box-sizing: border-box;}
.logo-no-margin #Action_bar .column{padding: unset;}
#Action_bar .contact_details{float:left;color:rgba(255,255,255,0.5)}
#Action_bar .contact_details li{display:inline-block;margin-right:10px;padding:20px 0}
#Action_bar .contact_details li > i{margin-right:2px}
#Action_bar .contact_details li:last-child{margin-right:0}

#Action_bar .social{float:right;padding:20px 0}
#Action_bar .social li{display:inline-block;margin-right:6px}
#Action_bar .social li:last-child{margin-right:0}
#Action_bar .social li a{color:rgba(255,255,255,.3);font-size:15px;line-height:15px;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
#Action_bar .social li a:hover{color:#fff}

#Action_bar .social-menu{float:right;padding:20px 0}
#Action_bar .social-menu li{display:inline-block;margin-right:6px;padding-right:6px;border-right:1px solid rgba(255,255,255,.1)}
#Action_bar .social-menu li:last-child{margin-right:0;padding-right:0;border-right-width:0}

/* Top bar */
#Top_bar{position:absolute;left:0;top:61px;width:100%;border-bottom:1px solid transparent;z-index:30}
#Top_bar .column{display:flex;margin-bottom:0}
.layout-full-width.header-fw #Action_bar .container,.layout-full-width.header-fw #Top_bar .container{max-width:100%;}
.layout-full-width.header-fw #Top_bar .container { padding-left: 0; padding-right: 0; }
#Top_bar .top_bar_left{position:relative;float:left;width:100%}
#Header_creative #Top_bar{background-image:unset}

	/* Top bar | Logo */

	#Top_bar .logo{float:left;margin:0 30px 0 20px;}
	#Top_bar .logo h1{margin:0}
	#Top_bar .logo:not(.text-logo) h1{line-height:0;font-size:0;margin:0}

	#Top_bar #logo{display:block;height:60px;line-height:60px;padding:15px 0;box-sizing:content-box;}
	#Top_bar #logo:hover{text-decoration:none}

	#Top_bar #logo img{vertical-align:middle;max-height:100%}
	#Top_bar #logo img.logo-sticky,#Top_bar #logo img.logo-mobile,#Top_bar #logo img.logo-mobile-sticky{display:none}
  #Top_bar #logo img[height]{width:auto;max-height:100%!important} /* FIX: wprocket */

	#Top_bar .text-logo #logo{font-weight:300}

	#Top_bar .logo .custom-logo-link{display:block;height:60px;line-height:60px;padding:15px 0}
	#Top_bar .logo .custom-logo-link img{vertical-align:middle;max-height:100%;width:auto!important}

	/* Top bar | Logo | Advanced */

	.logo-valign-top #Top_bar #logo img{vertical-align:top}
	.logo-valign-bottom #Top_bar #logo img{vertical-align:bottom}

	.logo-no-margin #Top_bar .logo{margin-left:0!important;padding:0!important}
	.logo-no-margin.header-plain #Top_bar .logo{margin:0!important}
	.logo-no-margin #Header_creative .logo{margin-top:0!important}

	.logo-no-margin.header-fw #Top_bar .column{margin:0;width:100%}

	.logo-no-sticky-padding #Top_bar.is-sticky #logo{padding:0!important}
	.logo-no-sticky-padding #Top_bar.is-sticky #logo img.logo-sticky{max-height:60px}

	.logo-overflow #Top_bar .logo{height:60px;position:relative;z-index:198}
	.logo-overflow #Top_bar #logo{height:auto!important;margin-top:0!important;z-index:199}
	.logo-overflow #Top_bar #logo img{max-height:none;z-index:200}
	.logo-overflow #Top_bar .top_bar_right{z-index:200}

	.logo-overflow #Top_bar.is-sticky #logo{height:auto!important}
	.logo-overflow #Top_bar.is-sticky #logo img.logo-sticky:not(.svg){max-height:110px}

	.logo-overflow.header-creative #Top_bar:not(.is-sticky) .logo,
	.logo-overflow.header-stack #Top_bar:not(.is-sticky) .logo{height:auto}

	/* Top bar | Menu wrapper */

	#Top_bar .menu_wrapper {float:left;z-index:201;}

	/* Top bar | Secondary menu wrapper */

	#Top_bar .secondary_menu_wrapper{display:none;}

	/* Top bar | Menu responsive */

	#Top_bar a.responsive-menu-toggle{display:none;position:absolute;right:15px;top:50%;margin-top:-17px;width:34px;height:34px;text-align:center;border-radius:3px;z-index:200;}
	#Top_bar a.responsive-menu-toggle i{font-size:22px;line-height:34px}
	#Top_bar a.responsive-menu-toggle span{float:right;padding:10px 5px;line-height:14px}

	/* Top bar | Banner */

	#Top_bar .banner_wrapper{display:none;}

	/* Top bar | Search wrapper */

	#Top_bar .search_wrapper {position:absolute;left:50%;transform: translateX(-50%);top:calc(100% + 40px);display:none;z-index:201;width:100%;box-sizing:border-box; width: 600px; max-width: 80%; overflow: hidden;}
    .column_livesearch .mfn-live-search-wrapper {width:100%;box-sizing:border-box;position: relative;}
	#Top_bar .search_wrapper > form,
    .column_livesearch .mfn-live-search-wrapper > form { position: relative; }
    #Top_bar .search_wrapper input[type="text"],
    .column_livesearch .mfn-live-search-wrapper input[type="text"]{width:100%;margin:0;box-sizing:border-box;-webkit-box-shadow:0 0 0;box-shadow:0 0 0;padding: 22px 30px 22px 60px;background:none;border-width:0;font-size:15px;color:rgba(0,0,0,.8);}
	#Top_bar .search_wrapper input[type="text"]:focus,
    .column_livesearch .mfn-live-search-wrapper input[type="text"]:focus{background-color:transparent!important}
	#Top_bar .search_wrapper .icon_search,
    #Top_bar .search_wrapper .icon_close,
    .column_livesearch .mfn-live-search-wrapper .icon_search {position:absolute;top:50%;transform: translateY(-50%);}
	#Top_bar .search_wrapper .icon_search,
    .column_livesearch .mfn-live-search-wrapper .icon_search{left:15px;}
    #Top_bar .search_wrapper .icon_close{right:10px;}


    .mfn-livesearch-loading:after { content: ""; display: block; position: absolute; right: 15px; top: 50%; margin: -10px 0 0 -10px; width: 20px; height: 20px; border-radius: 100%; border-width: 2px; border-style: solid; border-bottom-color: transparent !important; background: none; transform: none; transition: none !important; animation: spin 1.5s linear infinite;}
    .mfn-livesearch-loading .icon_close { display: none; }

    /* Live Search */

    .mfn_live_search_categories{display:none}

    .has-live-search-element{z-index:2}
    .has-live-search-element ~ .mcb-section {z-index:1}

    .column_livesearch{position:relative;z-index:2}
    .mfn-live-search-box{min-height:0px; padding: 15px; overflow-y:auto; z-index:2; clear:both; text-align:center; box-sizing: border-box; transition: height 0.3s ease-in-out;border-top: 1px solid rgba(0,0,0,.08);}
    .column_livesearch .mfn-live-search-box { position: absolute; left: 0; top: 100%; width: 100%; }
    .mfn-live-search-box a.button{ width: 100%; margin-top: 20px; box-sizing: border-box; }
    .mfn-live-search-box a.button.hidden{display: none}

    .mfn-live-search-box .mfn-live-search-list{list-style:none;margin:0;text-align:left;overflow:auto}
    .mfn-live-search-box .mfn-live-search-list > *{display:none}
    .mfn-live-search-box .mfn-live-search-list ul{list-style:none;margin:0}
    .mfn-live-search-box .mfn-live-search-list ul li{margin-bottom:0}
    .mfn-live-search-box .mfn-live-search-list ul li[data-category="info"] { padding: 10px 20px; opacity: .6; }
    .mfn-live-search-box .mfn-live-search-list ul li:not([data-category="info"]){display:flex; justify-content: flex-start; text-align: left; align-items: center; border-bottom: 1px solid rgba(0,0,0,0.08); padding: 15px 20px; transition:all .2s ease-in-out }
    .mfn-live-search-box .mfn-live-search-list ul li:not([data-category="info"]):last-child { border-bottom: 0; }
    .mfn-live-search-box .mfn-live-search-list ul li:not([data-category="info"]):hover { background: rgba(0,0,0,0.02); }
    .mfn-live-search-box .mfn-live-search-list ul li img {margin-right:20px; height: 100%; max-height:50px}
    .mfn-live-search-box .mfn-live-search-list .mfn-live-search-texts{ flex: 1;min-width: 0; }
    .mfn-live-search-box .mfn-live-search-list .mfn-live-search-texts p{ margin: 0; font-size: 14px; line-height: 1.6;white-space: normal;}
    .mfn-live-search-box .mfn-live-search-list .mfn-live-search-texts > span{float:right;font-size: 14px;}
    .mfn-live-search-box .mfn-live-search-list .mfn-live-search-texts > span del{opacity:.6;font-size:90%}

    .mfn-live-search-box .mfn-live-search-noresults{display: none}

			/* Live search item */

			.column_livesearch .search_wrapper .mfn-live-search-box{display:none}
			.column_livesearch .search_wrapper .mfn-live-search-list > li{display: none}

    #Top_bar .search_wrapper,
    #Top_bar .top_bar_right .mfn-live-search-box,
    .column_livesearch .mfn-live-search-wrapper,
    .column_livesearch .mfn-live-search-box { background-color: #fff; box-shadow: 0px 10px 46px 0px rgba(1,7,39,.1); border-radius: 4px; }

    .content-brightness-dark #Top_bar .search_wrapper input[type="text"],
    .content-brightness-dark .column_livesearch .mfn-live-search-wrapper input[type="text"] { color: rgba(255,255,255,.8); }
    .content-brightness-dark #Top_bar .search_wrapper .icon_search .path,
    .content-brightness-dark .column_livesearch .mfn-live-search-wrapper .icon_search .path { stroke: rgba(255,255,255,.8); }
    .content-brightness-dark .mfn-live-search-box { border-top: 1px solid rgba(255,255,255,.08);}
    .content-brightness-dark .mfn-live-search-box .mfn-live-search-list ul li:not([data-category="info"]){border-bottom: 1px solid rgba(255,255,255,0.08); }
    .content-brightness-dark .mfn-live-search-box .mfn-live-search-list ul li:not([data-category="info"]):hover { background: rgba(255,255,255,0.02); }


	/* Top bar | Right */

	#Top_bar .top_bar_right{float:right;position:relative;padding:0 20px;flex-shrink:0}

	#Top_bar .top_bar_right .top_bar_right_wrapper{display:flex;align-items:center;height:100%}

	#Top_bar .top_bar_right .top-bar-right-icon{display:flex;align-items:center;margin:0 5px;line-height:0;font-size:20px;text-decoration:none}
	#Top_bar .top_bar_right .top_bar_right_wrapper > a:last-child{margin-right:0}

	#Top_bar .top_bar_right .top-bar-right-icon svg{width:26px}
	#Top_bar .top_bar_right .top-bar-right-icon svg .path{stroke:#444}

  #Top_bar .top_bar_right .top-bar-right-icon-cart{flex-shrink:0}

	/* user */

	#Top_bar .top_bar_right .top-bar-right-icon-user img{border-radius:100%}

	/* wishlist */

	#Top_bar .top-bar-right-icon-wishlist{position:relative}
	#Top_bar .header-wishlist-count{position:relative;margin-left:-5px;top:-10px;display:inline-block;width:18px;line-height:18px;text-align:center;font-size:11px;background-color:#333;color:#fff;border-radius:100%}

	/* icon cart */

	#Top_bar a#header_cart .header-cart-count{position:relative;right:7px;margin-right:-4px;top:-10px;display:inline-block;width:18px;line-height:18px;text-align:center;font-size:11px;color:#fff;border-radius:100%}
  #Top_bar a#header_cart .header-cart-total{margin:0;padding-right:5px;font-size:16px;font-weight:500}

  /* icon user */

  #Top_bar a.myaccount_button img{max-width:unset}

	/* search form */

	#Top_bar .top_bar_right .top-bar-right-input{display:flex;align-items:center;margin:0 5px;white-space:nowrap;flex-shrink:0}
	#Top_bar .top_bar_right .top-bar-right-input form{display:flex;position: relative;}
	#Top_bar .top_bar_right .top-bar-right-input svg { position: absolute; left: 8px; top: 50%; transform: translateY(-50%); }
	#Top_bar .top_bar_right .top-bar-right-input input{margin:0;padding:8px;width:160px; padding-left: 40px;}
    #Top_bar .top_bar_right .top-bar-right-input .mfn-livesearch-loading::after {margin: -8px 0 0 -5px; width: 10px; height: 10px; right: 10px; }

    #Top_bar .top_bar_right .top-bar-search-form { position: relative; }
    #Top_bar .top_bar_right .top-bar-search-form .mfn-live-search-box { position: absolute; z-index: 202; right: 0; top: calc(100% + 10px); width: 400px; border-top: 0; }

	/* action button */

	#Top_bar .top_bar_right .action_button{display:flex;align-items:center;margin:0 5px;white-space:nowrap;flex-shrink:0}

	/* @since 20.8.9 colors fallback */

	.action_button{background-color:#f7f7f7;color:#747474;}
	.button-stroke a.action_button:hover{background-color:#747474!important;color:#fff}

	/* WPML */

	#Top_bar .wpml-languages{display:flex;align-items:center;margin:0 5px;position:relative;z-index:210;font-size:13px;line-height:13px;white-space:nowrap;}
	#Top_bar .wpml-languages a.active{display:flex;align-items:center;box-sizing:content-box;padding:10px 6px 10px 10px;border:1px solid rgba(0,0,0,.1);-webkit-border-radius:5px;border-radius:5px}
	#Top_bar .wpml-languages.disabled a.active{padding-right:10px;}
	#Top_bar .wpml-languages a.active i{font-size:14px;margin:0 0 0 5px}
	#Top_bar .wpml-languages a.active i:before{margin:0}
	#Top_bar .wpml-languages a:hover.active{text-decoration:none}
	#Top_bar .wpml-languages.enabled:hover a.active{border-radius:5px 5px 0 0;border-bottom-color:transparent}
  #Top_bar .wpml-languages a img{max-width:unset}
  #Top_bar .wpml-languages ul.wpml-lang-dropdown{position:absolute;left:0;top:100%;width:100%;-webkit-border-radius:0 0 5px 5px;border-radius:0 0 5px 5px;z-index:205;overflow:hidden;display:none;border-width:0 1px 1px;border-style:solid;border-color:rgba(0,0,0,.1);-webkit-box-sizing:border-box;box-sizing:border-box}
	#Top_bar .wpml-languages ul.wpml-lang-dropdown li{border-bottom:1px solid rgba(0,0,0,0.05)}
	#Top_bar .wpml-languages ul.wpml-lang-dropdown li:last-child{border-bottom:0}
	#Top_bar .wpml-languages ul.wpml-lang-dropdown li a{font-size:12px;display:block;text-align:center;padding:10px 5px;opacity:.75;}
	#Top_bar .wpml-languages ul.wpml-lang-dropdown li a:hover{text-decoration:none;opacity:1;}
	#Top_bar .wpml-languages:hover{height:auto}
	#Top_bar .wpml-languages:hover ul.wpml-lang-dropdown{display:block}
	#Top_bar .wpml-languages a.active,#Top_bar .wpml-languages ul.wpml-lang-dropdown{background:#fff}
	#Top_bar .wpml-languages.horizontal{display:flex;flex-shrink:0;padding:10px;border:1px solid #e8e8e8;background:#fff;-webkit-border-radius:5px;border-radius:5px}
	#Top_bar .wpml-languages.horizontal ul li{float:left;margin-right:8px}
	#Top_bar .wpml-languages.horizontal ul li:last-child{margin-right:0}

    .mfn-language-switcher-dropdown .wpml-ls-legacy-list-horizontal{ border: none; padding: 0; }
    .mfn-language-switcher-dropdown .wpml-ls ul{ display: flex; flex-wrap: wrap; }
    .mfn-language-switcher-dropdown .wpml-ls ul li{ display: none; order: 2; float: none; width: 100%;}
    .mfn-language-switcher-dropdown .wpml-ls ul li a{ display: flex; align-items: center; }
    .mfn-language-switcher-dropdown .wpml-ls ul li a img{ height: auto; }
    .mfn-language-switcher-dropdown .wpml-ls ul li a img.wpml-ls-flag{ width: 18px; }
    .mfn-language-switcher-dropdown .wpml-ls ul li a, .mfn-language-switcher-dropdown .wpml-ls ul li a:hover{ text-decoration: none; }
    .mfn-language-switcher-dropdown .wpml-ls ul li.wpml-ls-current-language{ display: flex; order: 1; position: relative; }
    .mfn-language-switcher-dropdown .wpml-ls ul li ul{ display: none; box-sizing: border-box; }
    .mfn-language-switcher-dropdown .wpml-ls ul li:hover ul{ display: block; position: absolute; top: 100%; left: 0; z-index: 4; background-color: #fff; }
    .mfn-language-switcher-dropdown .wpml-ls ul li ul li{ display: flex; }
    .mfn-language-switcher-dropdown.mfn-language-switcher-dropdown-icon ul li.wpml-ls-current-language > a span.mfn-arrow-icon{ content: ""; margin-left: 5px; line-height: 1em; font-size: var(--mfn-wpml-arrow-size); display: flex; align-items: center; justify-content: center; }
    .mfn-language-switcher-dropdown.mfn-language-switcher-dropdown-icon ul li a span.mfn-arrow-icon img{width: var(--mfn-wpml-arrow-size);}

/* Main Menu */

/* 1st level */
#Top_bar #menu{z-index:201}
#Top_bar .menu{z-index:202}
#Top_bar .menu.menu-mobile{display:none}
#Top_bar .menu .mfn-megamenu{width:98%!important;margin:0 1%}

#Top_bar .menu > li{margin:0;z-index:203;display:block;float:left}
#Top_bar .menu > li:not(.mfn-megamenu-parent){position:relative}
#Top_bar .menu > li.hover{z-index:204}
#Top_bar .menu > li > a{display:block;line-height:60px;padding:15px 0;position:relative}
#Top_bar .page-menu > li > a{padding:15px 20px}
#Top_bar .menu > li > a:not(.menu-toggle):after{content:"";height:4px;width:100%;position:absolute;left:0;top:-4px;z-index:203;opacity:0}
#Top_bar .menu > li > a span:not(.description){display:block;line-height:60px;padding:0 20px;white-space:nowrap;border-right-width:1px;border-style:solid}
#Top_bar .menu > li > a span.description{font-size:11px;line-height:12px!important;margin:-12px -15px 0;color:#aaa;font-weight:300;text-align:center;display:block}
#Top_bar .menu > li > a span > span:not(.description){display:inline!important;padding:0!important}
#Top_bar .menu > li:last-child > a span{border:0}
#Top_bar .menu > li > a:hover{text-decoration:none}

#Top_bar .menu > li a.menu-toggle{display:none}

.menuo-arrows:not(.keyboard-support) #Top_bar .menu > li.submenu > a:not(.menu-toggle) > span{padding-right:30px}
.menuo-arrows:not(.keyboard-support) #Top_bar .menu > li.submenu > a:not(.menu-toggle):after{content:"";display:block!important;width:0;height:0;position:absolute;top:50%;left:auto;right:10px;margin-top:-2px;border-top:5px solid #ccc;border-left:5px solid transparent;border-right:5px solid transparent;background:none;opacity:.6}

	/* Animation */

	#Top_bar .menu > li.current-menu-item > a:after,
	#Top_bar .menu > li.current_page_item > a:after,
	#Top_bar .menu > li.current-menu-parent > a:after,
	#Top_bar .menu > li.current-page-parent > a:after,
	#Top_bar .menu > li.current-menu-ancestor > a:after,
	#Top_bar .menu > li.current_page_ancestor > a:after,
	#Top_bar .menu > li.hover > a:after{opacity:1}

#Top_bar .menu.page-menu > li > a {border-right:1px solid rgba(0, 0, 0, 0.05);margin:15px 0;padding:0 20px;}
#Top_bar .menu.page-menu > li:last-child > a {border-right:none;}
#Top_bar .menu.page-menu > li > a:after {display:none;}

#Top_bar.is-sticky .menu_wrapper .menu.page-menu > li > a {margin:0;padding-left:20px;padding-right:20px;}

/* 2nd level */
#Top_bar .menu li ul{position:absolute;left:0;top:100%;z-index:205;margin:0;display:none;background-image:url(../images/box_shadow.png);background-repeat:repeat-x;background-position:left top}
#Top_bar .menu li > ul{box-shadow:2px 2px 2px 0 rgba(0,0,0,0.03);-webkit-box-shadow:2px 2px 2px 0 rgba(0,0,0,0.03)}
#Top_bar .menu li ul li{padding:0;width:200px;position:relative;font-weight:400}
#Top_bar .menu li ul li a{padding:10px 20px;display:block;border-bottom:1px solid rgba(0,0,0,0.05)}
#Top_bar .menu li ul li a span{display:inline-block;position:relative}
#Top_bar .menu li ul li a .menu-arrow{position:absolute;right:7px;top:11px;font-size:12px;color:rgba(0,0,0,0.35)}
#Top_bar .menu > li ul li a:hover,#Top_bar .menu > li ul li.hover > a{text-decoration:none;background:rgba(0,0,0,.06)}

.menuo-sub-active #Top_bar .menu > li ul li.current-menu-item > a,.menuo-sub-active #Top_bar .menu > li ul li.current-menu-ancestor > a{background:rgba(0,0,0,.06)}
/* .menuo-sub-limit #Top_bar .menu li ul li{width:200px} */

	/* WPML */
	#Top_bar .menu li ul li.wpml-ls-item{width:auto}
	#Top_bar .menu li ul li.wpml-ls-item a{padding-right:20px;white-space:nowrap}

/* 3rd level */
#Top_bar .menu li ul li ul{position:absolute;left:200px;top:0;z-index:204}
.menuo-last #Top_bar .menu > li.last ul:not(.mfn-megamenu){right:0;left:auto;-webkit-box-shadow:-2px 2px 2px 0 rgba(0,0,0,0.03);box-shadow:-2px 2px 2px 0 rgba(0,0,0,0.03)}
.menuo-last #Top_bar .menu > li.last ul:not(.mfn-megamenu) li ul{right:200px}

	/* Animation */
	#Top_bar .menu li ul li a{-webkit-transition:all .2s ease-in-out;-moz-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;-ms-transition:all .2s ease-in-out;transition:all .2s ease-in-out}

/* Secondary Menu ------------------------------------------------------------------------- */
#Header #menu-secondary-menu{z-index:220}
#Header .secondary-menu{z-index:221}
#Header .secondary-menu > li{margin:0;z-index:222;display:block;float:left;position:relative;padding:8px 9px;line-height:100%;-webkit-border-radius:4px;border-radius:4px}
#Header .secondary-menu > li.submenu{-webkit-border-radius:4px 4px 0 0;border-radius:4px 4px 0 0}
#Header .secondary-menu > li > a{display:block}
#Header .secondary-menu > li > a:hover{text-decoration:none}

#Header .secondary-menu li ul{position:absolute;left:0;padding:7px;top:100%;z-index:223;margin:0;display:none;-webkit-border-radius:0 4px 4px 4px;border-radius:0 4px 4px 4px}
#Header .secondary-menu li ul li:last-child{border-bottom:0}
#Header .secondary-menu li ul li a{display:block;text-align:center;white-space:nowrap;padding:6px 8px}
#Header .secondary-menu li ul li a:hover{text-decoration:none}

#Header .secondary-menu > li > a{color:#A8A8A8}
#Header .secondary-menu > li.hover,#Header .secondary-menu li ul{background:#F9F9F9}
#Header .secondary-menu li ul li{border-bottom:1px solid rgba(0,0,0,0.05)}
#Header .secondary-menu li ul li a{color:#8B8B8B}
#Header .secondary-menu li ul li a:hover{color:#5F5F5F;background:rgba(255,255,255,0.8)}

/* Header styles --------------------------------------------------------------------- */
.tr-menu .top_bar_left{background-color:transparent!important}
.tr-menu .top_bar_right,.tr-menu .top_bar_right:before{background:none!important}

/* Header | Modern */
.header-modern #Top_bar .top_bar_right{top:-4px;margin-left:10px}
.header-modern #Top_bar .top_bar_right:before{content:"";display:block;height:100%;width:10px;position:absolute;left:-10px;top:2px;transform:skewX(0deg) skewY(-25deg)}
.header-modern #Top_bar.is-sticky .top_bar_right{top:0}

/* Header | Classic */
.header-classic #Header .top_bar_left,.header-classic #Header .top_bar_right{background-color:transparent}
.header-classic #Top_bar{position:static;background-color:#fff}
.header-classic #Top_bar .top_bar_right:before{display:none}
.header-classic #Action_bar{position:static;background-color:#2C2C2C}
.header-classic #Action_bar .contact_details li,.header-classic #Action_bar .social,.header-classic #Action_bar .social-menu{padding:12px 0}

/* Header | Fixed */
@media only screen and (min-width: 768px) {
	.header-fixed #Action_bar{position:fixed;top:0;left:0;background-color:#2c2c2c}
	.header-fixed #Action_bar .contact_details li,.header-fixed #Action_bar .social,.header-fixed #Action_bar .social-menu{padding:5px 0}
	.header-fixed #Top_bar{position:fixed;width:100%;left:0;top:0!important;z-index:701;background:#fff;opacity:.97;box-shadow:0 2px 5px 0 rgba(0,0,0,0.1)}
	.header-fixed.ab-show #Top_bar{top:31px!important}
	.header-fixed #Top_bar .top_bar_left,.header-fixed #Top_bar .top_bar_right,.header-fixed #Top_bar .top_bar_right:before{background:none}
	.header-fixed #Top_bar .logo{width:auto;margin:0 30px 0 20px;padding:0}
	.header-fixed #Top_bar #logo,
	.header-fixed #Top_bar .custom-logo-link{padding:5px 0;height:50px;line-height:50px}
	.header-fixed #Top_bar .menu_wrapper{clear:none}
	.header-fixed #Top_bar .menu > li > a{padding:15px 0}
	.header-fixed #Top_bar .menu > li > a,.header-fixed #Top_bar .menu > li > a span{line-height:30px}
	.header-fixed #Top_bar .menu > li > a:not(.menu-toggle):after{top:auto;bottom:-4px}
	.header-fixed #Top_bar .menu > li > a span.description{margin-top:-5px;margin-bottom:-7px}
	.header-fixed #Top_bar .secondary_menu_wrapper{display:none}
	.tr-menu.header-fixed #Top_bar:not(.is-sticky){background:none!important;box-shadow:0 0 0 0 transparent}

	.header-fixed.admin-bar #Action_bar{top:32px}
	.header-fixed.woocommerce-demo-store #Action_bar{top:45px}
	.header-fixed.admin-bar.woocommerce-demo-store #Action_bar{top:77px}

	.header-fixed.admin-bar #Top_bar{top:32px!important}
	.header-fixed.woocommerce-demo-store #Top_bar{top:45px!important}
	.header-fixed.admin-bar.woocommerce-demo-store #Top_bar{top:77px!important}

	.header-fixed.admin-bar.ab-show #Top_bar{top:63px!important}
	.header-fixed.woocommerce-demo-store.ab-show #Top_bar{top:76px!important}
	.header-fixed.admin-bar.woocommerce-demo-store.ab-show #Top_bar{top:108px!important}
}

/* Header | Below */
.header-below #Header .top_bar_left,.header-below #Header .top_bar_right{background-color:transparent}
.header-below #Top_bar{position:static;background-color:#fff}
.header-below #Top_bar .top_bar_right{top:0}
.header-below #Top_bar .top_bar_right:before{display:none}
.header-below #Action_bar{position:static}

/* Header | Plain */
.header-plain #Top_bar{border-bottom-width:1px;border-style:solid;position:static}
.header-plain.layout-boxed #Top_bar .container{max-width:100%}
.header-plain #Top_bar .one.column{width:100%;margin:0}
.header-plain #Header .top_bar_left,.header-plain #Header .top_bar_right{background-color:transparent}
.header-plain #Top_bar .top_bar_right,.header-plain #Top_bar .top_bar_right_wrapper{top:0}
.header-plain #Top_bar .top_bar_right:before{display:none}
.header-plain #Action_bar{position:static}
.header-plain #Action_bar .contact_details li,.header-plain #Action_bar .social,.header-plain #Action_bar .social-menu{padding:12px 0}

	/* Header plain | Logo */
	.header-plain #Top_bar #logo,
	.header-plain #Top_bar .custom-logo-link{height:50px;line-height:50px}

	/* Header plain | Menu */
	.header-plain #Top_bar .menu_wrapper{float:right}
	.header-plain #Top_bar .menu > li > a{padding-top:0!important;padding-bottom:0!important}
	.header-plain #Top_bar .menu > li > a:not(.menu-toggle):after{display:none}
	.header-plain #Top_bar .menu > li > a span:not(.description){line-height:80px;padding:0 30px}
	.header-plain #Top_bar .menu > li:first-child > a span:not(.description){border-left-width:1px}
	.header-plain.menu-highlight #Top_bar .menu > li,.header-plain.menu-highlight #Top_bar .menu > li > a{margin:0}

	/* Header plain | Top bar right */
	.header-plain #Top_bar .top_bar_right{padding:0;margin-left:auto}

	.header-plain #Top_bar .top_bar_right .top-bar-right-icon,
	.header-plain #Top_bar .top_bar_right .top-bar-right-input{height:100%;margin:0;padding:0 20px;border-left-width:1px;border-style:solid}

	.header-plain #Top_bar a#header_cart,.header-plain #Top_bar a#search_button{border-left-width:1px;border-style:solid}
	.header-plain #Top_bar a#header_cart span{margin-right:-9px}
	.header-plain #Top_bar .wpml-languages{margin:0;padding:0 20px;height:100%;border-left-width:1px;border-style:solid}
	.header-plain #Top_bar .wpml-languages a.active{border:0;padding:0;height:auto}
	.header-plain #Top_bar .wpml-languages ul.wpml-lang-dropdown li a{line-height:40px}
	.header-plain #Top_bar .wpml-languages a.active{background:none}
	.header-plain #Top_bar .wpml-languages ul.wpml-lang-dropdown{border:0;border-radius:0}

	.header-plain #Top_bar .action_button{height:100%;margin:0;border-radius:0;border-left:1px solid #F2F2F2;padding:0 30px!important}
	.header-plain.button-stroke #Top_bar .action_button{border-width:0 0 0 1px;background-color:#f7f7f7}

	/* Header plain | Sticky */
	.header-plain #Top_bar.is-sticky .menu > li > a span:not(.description){line-height:60px!important}
	.header-plain #Top_bar.is-sticky .top_bar_right{padding:0;height:60px;top:0}
	.header-plain #Top_bar.is-sticky .wpml-languages{padding:0 25px;height:60px;line-height:60px}
	.header-plain #Top_bar.is-sticky .wpml-languages{top:0}

	.header-plain #Top_bar.is-sticky .action_button{line-height:60px!important}

	/* Header plain | Colors */
	.header-plain #Action_bar{background-color:#2C2C2C}
	.header-plain #Top_bar{background-color:#fff}
	.header-plain #Top_bar .top_bar_right .top-bar-right-icon,
	.header-plain #Top_bar .top_bar_right .top-bar-right-button,
	.header-plain #Top_bar .top_bar_right .top-bar-right-input,
	.header-plain #Top_bar .wpml-languages{border-color:#f2f2f2}

/* Header | Split */
.header-split #Header .top_bar_left{width:100%!important}
.header-split #Header .top_bar_left,.header-split #Header .top_bar_right{background-color:transparent}
.header-split #Header .top_bar_left .menu_wrapper{width:100%}
.header-split #Header .top_bar_left .menu_left{float:left;width:38%;text-align:center}
.header-split #Header .top_bar_left .menu_right{float:right;width:38%;text-align:center}
.header-split #Header .top_bar_left .menu > li{display:inline-block;float:none}
.header-split #Header .top_bar_left .menu li ul li a{padding:10px}
.header-split #Header .top_bar_left .logo{width:100%;margin:0;text-align:center}
@media only screen and (min-width: 1240px) {
	.header-split #Header .top_bar_left .logo{position:absolute;left:38%;width:24%}
}
.header-split #Top_bar{position:static;background-color:#fff}
.header-split #Top_bar .top_bar_right{display:none}
.header-split #Action_bar{position:static;background-color:#2C2C2C}
.header-split #Action_bar .contact_details li,.header-split #Action_bar .social,.header-split #Action_bar .social-menu{padding:12px 0}

/* Header | Shop */

.header-shop #Top_bar{position:static;background-color:#fff}
.header-shop #Top_bar .column{display:block}
.header-shop #Action_bar{position:static;background-color:#101015}
.header-shop #Action_bar .contact_details li,.header-shop #Action_bar .social,.header-shop #Action_bar .social-shop{padding:12px 0}

.header-shop #Top_bar .top_bar_row { display: flex; align-items: center; }
.header-shop #Top_bar .top_bar_row_first { z-index: 2; }
.header-shop #Top_bar .top_bar_row_second { margin-top: -10px }

.header-shop #Top_bar .logo a.responsive-menu-toggle { display: none; }

.header-shop #Top_bar .top_bar_right{background-color:transparent}
.header-shop #Top_bar .top_bar_right  { flex: auto; padding-right: 10px; }
.header-shop.logo-no-margin:not(.header-fw) #Top_bar .top_bar_right  { padding-right: 0; }
.header-shop #Top_bar .top_bar_right .top_bar_right_wrapper { width: 100%; justify-content: flex-end; }
.header-shop #Top_bar .top_bar_right .top-bar-right-input { flex: auto; margin-right: 40px;  }
.header-shop #Top_bar .top_bar_right .top-bar-right-input > form,
.header-shop #Top_bar .top_bar_right .top-bar-right-input input[type="text"],
.header-shop #Top_bar .top_bar_right .top-bar-right-input .mfn-live-search-box { width: 100%; }
.header-shop #Top_bar .top_bar_right a.responsive-menu-toggle { position: static; margin-top: unset; }

.header-shop #Top_bar .menu_wrapper { width: 100%;}
.header-shop #Top_bar .menu_wrapper #menu ul:nth-child(2) { margin-left: auto; }
.header-shop #Top_bar .menu > li > a { padding: 5px 0; }

body.header-shop.menu-highlight #Top_bar .menu > li > a { margin: 5px 0; }

.header-shop #Top_bar.is-sticky .responsive-menu-toggle{display:block}

/* Header | Shop Split */

.header-shop-split #Top_bar{position:static;background-color:#fff}
.header-shop-split #Action_bar{position:static;background-color:#101015}
.header-shop-split #Action_bar .contact_details li,.header-shop-split #Action_bar .social,.header-shop-split #Action_bar .social-shop{padding:12px 0}

.header-shop-split #Top_bar .top_bar_row { display: flex; align-items: center; width: 100%; }
.header-shop-split #Top_bar .menu_wrapper { flex: 2; }
.header-shop-split #Top_bar .logo { flex: 1; text-align: center; margin: 0 15px; }
.header-shop-split #Top_bar .top_bar_right  { flex: 2; padding: 0; }

.header-shop-split #Top_bar .top_bar_right{background-color:transparent}
.header-shop-split #Top_bar .top_bar_right .top_bar_right_wrapper { width: 100%; justify-content: flex-end; box-sizing: border-box; padding: 0 20px; }
.header-shop-split #Top_bar .top_bar_right .top-bar-right-input { flex: auto; }
.header-shop-split #Top_bar .top_bar_right .top-bar-right-input > form,
.header-shop-split #Top_bar .top_bar_right .top-bar-right-input input[type="text"],
.header-shop-split #Top_bar .top_bar_right .top-bar-right-input .mfn-live-search-box { width: 100%; }

@media only screen and (min-width: 768px) {
    .header-shop-split.menuo-right #Top_bar .menu_wrapper { order: 3; display: flex; justify-content: flex-end; }
    .header-shop-split.menuo-right #Top_bar .logo { order: 2; }
    .header-shop-split.menuo-right #Top_bar .top_bar_right  { order: 1; }
    .header-shop-split.menuo-right #Top_bar .top_bar_right .top_bar_right_wrapper { justify-content: flex-start; }
    .header-shop-split #Top_bar .menu_wrapper a.responsive-menu-toggle { position: static; margin-top: unset; }
}

/* Header | Stack */
.header-stack #Top_bar:not(.is-sticky) .top_bar_left{width:100%!important}
.header-stack #Header .top_bar_left,.header-stack #Header .top_bar_right{background-color:transparent}
.header-stack #Top_bar{position:static;background-color:#fff}
.header-stack #Top_bar .logo{width:100%;margin:0;padding:0 30px;text-align:left;border-bottom-width:1px;border-style:solid;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.header-stack.header-center #Top_bar .logo{text-align:center}
.header-stack.header-center #Top_bar .menu_wrapper{text-align:center;line-height:0}
.header-stack.header-center #Top_bar #menu{line-height:21px;line-height:initial;text-align:left;text-align:initial}
.header-stack.header-center #Top_bar:not(.is-sticky) .menu_wrapper{width:100%}
.header-stack.header-right #Top_bar .logo{text-align:right}
.header-stack #Top_bar .logo #logo,
.header-stack #Top_bar .logo .custom-logo-link{display:inline-block;height:auto}
.header-stack #Top_bar .menu_wrapper{clear:both}
.header-stack #Top_bar .menu_wrapper .menu > li > a{padding:0}
.header-stack #Top_bar .menu > li > a span.description{margin-bottom:10px}
.header-stack #Top_bar .top_bar_right{position:absolute;right:0;bottom:0;top:auto;height:60px}
.header-stack #Top_bar .top_bar_right_wrapper{top:10px}
.header-stack #Top_bar .top_bar_right:before{display:none}
.header-stack #Top_bar .secondary_menu_wrapper{position:absolute;right:20px;top:35px;display:block}
.header-stack.header-right #Top_bar .secondary_menu_wrapper{left:20px;right:auto}
.header-stack #Action_bar{position:static;background-color:#2C2C2C}
.header-stack #Action_bar .contact_details li,.header-stack #Action_bar .social,.header-stack #Action_bar .social-menu{padding:12px 0}

.header-stack #Top_bar.is-sticky .logo{border-bottom:unset}

/* Header | Magazine */
.header-magazine #Top_bar .logo{width:auto;border:0}
.header-magazine #Top_bar .top_bar_right:before{display:none}
.header-magazine #Top_bar .secondary_menu_wrapper{display:none}
.header-magazine #Top_bar .banner_wrapper{display:block;position:absolute;right:20px;top:20px;width:468px;height:60px;text-align:right;z-index:2}
.header-magazine #Top_bar .banner_wrapper a{display:block;line-height:0}
.header-magazine #Top_bar .banner_wrapper img{display:inline-block;max-width:100%;height:auto;max-height:60px}

/* Header | Simple */
.header-simple #Top_bar .top_bar_left{width:100%!important;background:none}
.header-simple #Top_bar:not(.is-sticky) .top_bar_left{top:-60px}
.header-simple.ab-show #Top_bar:not(.is-sticky) .top_bar_left{top:0}
.header-simple #Top_bar .top_bar_right,.header-simple #Top_bar .top_bar_right:before{display:none}
.header-simple #Top_bar .menu > li > a span.description{margin:0 0 0 5px}
.header-simple.ab-hide #Action_bar{display:none}

/* Header | Empty */
.header-empty #Header{position:static;min-height:0!important}
.header-empty #Subheader{display:none}
.header-empty #Content{padding:0!important}

/* Header | Transparent */
.header-transparent #Top_bar .top_bar_left,
.header-transparent #Top_bar .top_bar_right,
.header-transparent #Top_bar .top_bar_right:before{background:none;box-shadow:unset}
.header-transparent #Top_bar .top_bar_right{top:0}
.header-transparent #Top_bar #logo,
.header-transparent #Top_bar .custom-logo-link{padding:0}
.header-transparent #Top_bar .menu > li > a span{border-color:rgba(0,0,0,0.03)}
.header-transparent #Top_bar .menu li > ul:not(.mfn-megamenu-bg){background-image:none}
.header-transparent.ab-hide #Top_bar{top:0}
.header-transparent #Top_bar.is-sticky .menu_wrapper .menu > li > a{padding:0}
.header-transparent #Top_bar.is-sticky .menu > li > a span:not(.description){line-height:60px}

/* Header | Overlay */
.header-overlay.ab-hide #Top_bar{top:40px}
.header-overlay #Top_bar #logo,
.header-overlay #Top_bar .custom-logo-link{height:auto}
.header-overlay .top_bar_right{display:none}
.header-overlay .overlay-menu-toggle{position:absolute;right:40px;top:40px;height:45px;width:45px;line-height:45px;text-align:center;font-size:29px;z-index:9911}
.header-overlay .overlay-menu-toggle.focus{color:#fff;-webkit-transition:all .3s;-moz-transition:all .3s;transition:all .3s}
.header-overlay .overlay-menu-toggle .close{display:none}
.header-overlay .overlay-menu-toggle.focus .open{display:none}
.header-overlay .overlay-menu-toggle.focus .close{display:block}
.header-overlay.sticky-header .overlay-menu-toggle{position:fixed;margin-right:10px}

	/* Overlay | Menu Overlay */
	#Overlay{position:fixed;top:0;left:0;width:100%;height:100%;z-index:9910;background:rgba(41,145,214,.95);display:none}
	#overlay-menu{position:absolute;width:700px;left:50%;margin-left:-350px;top:50%;margin-top:-150px}
	#overlay-menu .menu.menu-mobile{display:none}
	#overlay-menu ul li{text-align:center}
	#overlay-menu ul li a{color:#fff;font-size:34px;line-height:52px;letter-spacing:3px;text-decoration:none}
	#overlay-menu ul li a:hover{opacity:.8}
	#overlay-menu ul li a:before,#overlay-menu ul li a:after{display:inline-block;opacity:0;-webkit-transition:-webkit-transform 0.3s,opacity .2s;-moz-transition:-moz-transform 0.3s,opacity .2s;transition:transform 0.3s,opacity .2s}
	#overlay-menu ul li a:before{margin-right:20px;content:'[';-webkit-transform:translateX(20px);-moz-transform:translateX(20px);transform:translateX(20px)}
	#overlay-menu ul li a:after{margin-left:20px;content:']';-webkit-transform:translateX(-20px);-moz-transform:translateX(-20px);transform:translateX(-20px)}
	#overlay-menu ul li a:hover:before,#overlay-menu ul li a:hover:after,#overlay-menu ul li a:hover:before,#overlay-menu ul li a:hover:after{opacity:1;-webkit-transform:translateX(0px);-moz-transform:translateX(0px);transform:translateX(0px)}

/* Side Slide ------------------------------------------------------------------------- */
#body_overlay{position:fixed;top:0;left:0;width:100%;height:120%;background:rgba(0,0,0,.6);z-index:9002;display:none;} /* height +20% - mobile fallback */
body.no-overlay #body_overlay{display:none!important}

body.mobile-side-slide{position:relative;overflow-x:visible;}

#Side_slide{display:block;position:fixed;top:0px;right:-250px;max-width:100%;width:250px;height:100%;overflow:auto;border-bottom-width:60px;border-bottom-style:solid;z-index:99999;z-index:100000;} /* border-bottom:60px - mobile fallback */
#Side_slide.left{left:-250px;right:auto}

#Side_slide .close-wrapper{height:60px}
#Side_slide .close-wrapper a.close{height:34px;width:34px;display:block;float:right;margin:13px 13px 0 0}
#Side_slide .close-wrapper a.close i{font-size:22px;line-height:34px;}

#Side_slide .extras{padding:0 20px}
#Side_slide .extras .action_button{float:none;width:100%;margin:0 0 20px;text-align:center;text-decoration:none}

#Side_slide .extras .extras-wrapper{display:flex;align-items:center;flex-wrap:wrap;justify-content:center;margin-bottom:20px;}
#Side_slide .extras .extras-wrapper a{display:flex;align-items:center;text-decoration:none;padding:5px;font-size:20px}

#Side_slide .extras .extras-wrapper .top-bar-right-icon-user img{border-radius:100%}
#Side_slide .extras .extras-wrapper .header-wishlist-count{position:relative;margin-left:-3px;top:-10px;text-align:center;font-size:11px}
#Side_slide .extras .extras-wrapper .header-cart-count{position:relative;margin-left:-3px;top:-10px;text-align:center;font-size:11px}
#Side_slide .extras .extras-wrapper .header-cart-total{margin:0;padding-right:5px;font-size:16px;font-weight:500}
#Side_slide .extras .extras-wrapper .lang-active i{margin-left:-5px}

#Side_slide #menu{display:block!important;margin-bottom:20px;max-height:none!important}
#Side_slide #menu ul{width:100%!important;}
#Side_slide #menu ul li{width:100%;position:relative;border-top:1px solid rgba(255,255,255,.03)}
#Side_slide #menu > ul:last-child > li:last-child{border-bottom:1px solid rgba(255,255,255,.03)}
#Side_slide #menu ul li a{display:block;padding:11px 5px 10px 20px;margin-right:50px;text-decoration:none;line-height:19px}
#Side_slide #menu ul li a span.description{display:block;opacity:.5}
#Side_slide #menu ul li a .menu-arrow{display:none}
#Side_slide #menu ul li ul{display:none;background:rgba(255,255,255,.025)}
#Side_slide #menu ul li ul li a{padding-left:35px}
#Side_slide #menu ul li ul li ul li a{padding-left:50px}

#Side_slide #menu ul li.submenu .menu-toggle{display:block;position:absolute;right:5px;top:0;width:40px;height:40px;margin:0;padding:0;line-height:40px;font-size:22px;font-weight:100;text-align:center;cursor:pointer;opacity:0.5;}
#Side_slide #menu ul li.submenu .menu-toggle:after{content:"+"}
#Side_slide #menu ul li.hover > .menu-toggle{opacity:1}
#Side_slide #menu ul li.hover > .menu-toggle:after{content:"-"}

#Side_slide #menu ul.mfn-megamenu-bg{background-image:none!important}
#Side_slide #menu ul.mfn-megamenu li .menu-toggle{display:none}
#Side_slide #menu ul.mfn-megamenu > li > ul{display:block!important}

#Side_slide #menu ul.mfn-megamenu > li > ul:first-child {background-color:transparent}
#Side_slide #menu ul.mfn-megamenu > li > ul:first-child > li:first-child{border-top-width:0}

#Side_slide .menu.menu-mobile{display:none}

#Side_slide .lang-wrapper{margin-bottom:20px;text-align:center;display:none;}
#Side_slide .lang-wrapper ul li{border-top:1px solid rgba(255,255,255,.03)}
#Side_slide .lang-wrapper ul li:last-child{border-bottom:1px solid rgba(255,255,255,.03)}
#Side_slide .lang-wrapper ul li a{display:block;padding:11px 20px 10px 20px;text-decoration:none;line-height:19px}

#Side_slide .search-wrapper{margin-bottom:20px;position:relative;display:none;}
#Side_slide .search-wrapper > form { position: relative; }
#Side_slide .search-wrapper input.field{width:100%;background:none!important;border-width:1px 0 1px 0;border-color:rgba(255,255,255,.05);line-height:20px;padding:10px 55px 10px 20px;box-sizing:border-box;box-shadow:0 0 0 0 transparent;}
#Side_slide .search-wrapper a.submit{position:absolute;top:0;right:5px;font-size:20px;padding:10px}
#Side_slide .mfn-live-search-box .mfn-live-search-list { max-height: unset; }
#Side_slide .mfn-livesearch-loading:after { margin: -8px 0 0 -5px; width: 10px; height: 10px; right: 50px; }

#Side_slide .contact_details{text-align:center;margin-bottom:20px}
#Side_slide .contact_details li{margin-bottom:1px}

#Side_slide .social{text-align:center;margin:0 20px 13px}
#Side_slide .social li{display:inline-block}
#Side_slide .social li a{display:block;padding:3px 5px;text-decoration:none}

#Side_slide .social-menu{text-align:center}
#Side_slide .social-menu li{display:inline-block;margin-right:6px;padding-right:6px;border-right: 1px solid rgba(255,255,255,.1)}
#Side_slide .social-menu li:last-child{margin-right:0;padding-right:0;border-right-width:0}

/* Side Slide | Hide */
#Side_slide.hide-social .social{display:none!important}

/* Side Slide | Color */
#Side_slide{background-color:#191919;border-color:#191919} /* border-bottom:60px - mobile fallback */

#Side_slide,
#Side_slide .search-wrapper input.field,
#Side_slide a:not(.action_button),
#Side_slide #menu ul li.submenu .menu-toggle{color:#a6a6a6}

#Side_slide a:not(.action_button):hover,
#Side_slide a.active,
#Side_slide #menu ul li.hover > .menu-toggle{color:#ffffff;}

#Side_slide #menu ul li.current-menu-item > a,#Side_slide #menu ul li.current_page_item > a,
#Side_slide #menu ul li.current-menu-parent > a,#Side_slide #menu ul li.current-page-parent > a,
#Side_slide #menu ul li.current-menu-ancestor > a,#Side_slide #menu ul li.current-page-ancestor > a,#Side_slide #menu ul li.current_page_ancestor > a,
#Side_slide #menu ul li.hover > a,#Side_slide #menu ul li:hover > a{color:#ffffff;}

/* Side Slide | Light */
#Side_slide.light #menu ul li{border-top-color:rgba(0,0,0,.03)}
#Side_slide.light #menu > ul:last-child > li:last-child{border-bottom-color:rgba(0,0,0,.03)}
#Side_slide.light #menu ul li ul{background:rgba(0,0,0,.02)}
#Side_slide.light .lang-wrapper ul li{border-top-color:rgba(0,0,0,.03)}
#Side_slide.light .lang-wrapper ul li:last-child{border-bottom-color:rgba(0,0,0,.03)}
#Side_slide.light .search-wrapper input.field{border-color:rgba(0,0,0,.05)}

/* Side Slide | Icons */
.mobile-icon-user-hide #Side_slide .top-bar-right-icon-user,
.mobile-icon-user-tb #Side_slide .top-bar-right-icon-user,
.mobile-icon-wishlist-hide #Side_slide .top-bar-right-icon-wishlist,
.mobile-icon-wishlist-tb #Side_slide .top-bar-right-icon-wishlist,
.mobile-icon-cart-hide #Side_slide .top-bar-right-icon-cart,
.mobile-icon-cart-tb #Side_slide .top-bar-right-icon-cart,
.mobile-icon-search-hide #Side_slide .top-bar-right-icon-search,
.mobile-icon-search-tb #Side_slide .top-bar-right-icon-search,
.mobile-icon-wpml-hide #Side_slide .wpml-languages,
.mobile-icon-wpml-tb #Side_slide .wpml-languages,
.mobile-icon-action-hide #Side_slide .action_button,
.mobile-icon-action-tb #Side_slide .action_button{display:none!important}

/* Subheader ------------------------------------------------------------------------- */
#Subheader{background-color:rgba(0,0,0,.02);background-position:center top;background-repeat:no-repeat;padding:30px 0;position:relative}
.subheader-transparent #Subheader{background:none}
.hide-title-area #Subheader{display:none}
#Subheader .title{margin-bottom:0;width:70%;float:left}
#Subheader ul.breadcrumbs{display:block;width:30%;margin:1px 0 0;font-size:1em!important;float:right;text-align:right}
#Subheader ul.breadcrumbs li{display:inline-block}
#Subheader ul.breadcrumbs li,#Subheader ul.breadcrumbs li a{color:rgba(0,0,0,.3)}
#Subheader ul.breadcrumbs li span{margin:0 10px;opacity:.4}
#Subheader:after{content:"";height:3px;width:100%;display:block;position:absolute;left:0;bottom:-3px;z-index:1;box-shadow:inset 0px 4px 3px -2px rgba(0,0,0,.06)}
#Subheader ul.woocommerce-breadcrumb li:last-child span{display:none}

	/* style */
	.subheader-title-right #Subheader .title{float:right;text-align:right}
	.subheader-title-right #Subheader .breadcrumbs{float:left;text-align:left}
	.subheader-both-left #Subheader .title{width:100%}
	.subheader-both-left #Subheader .breadcrumbs{width:100%;text-align:left;margin-top:10px}
	.subheader-both-right #Subheader .title{width:100%;text-align:right}
	.subheader-both-right #Subheader .breadcrumbs{width:100%;text-align:right;margin-top:10px}
	.subheader-both-center #Subheader .title{width:100%;text-align:center}
	.subheader-both-center #Subheader .breadcrumbs{width:100%;text-align:center;margin-top:10px}

/* Intro ------------------------------------------------------------------------- */
#Intro{text-align:center;position:relative;background-color:#000119;background-position:center top;}
#Intro .intro-inner{position:relative;padding:250px 10%}
#Intro .intro-title{margin-bottom:20px;word-wrap:break-word}
#Intro .intro-meta > div{display:inline-block;margin:0 10px}
#Intro .intro-next{cursor:pointer;font-size:38px;height:50px;left:50%;bottom:30px;line-height:50px;margin:0 0 0 -25px;position:absolute;text-align:center;width:50px;z-index:1;-webkit-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

#Intro.parallax{overflow:hidden}
#Intro.parallax .mfn-parallax{position:absolute;left:0;top:0;max-width:unset!important;transition:opacity .2s;}

/* Light */
#Intro .intro-title{color:#fff}
#Intro .intro-meta,#Intro .intro-meta a{color:rgba(255,255,255,.7)}
#Intro .intro-next{color:rgba(255,255,255,0.2)}
#Intro .intro-next:hover{color:rgba(255,255,255,0.5)}

/* Dark */
#Intro.light .intro-title{color:#212121}
#Intro.light .intro-meta,#Intro.light .intro-meta a{color:rgba(33,33,33,.7)}
#Intro.light .intro-next{color:rgba(33,33,33,0.2)}
#Intro.light .intro-next:hover{color:rgba(33,33,33,0.5)}


/* Post ------------------------------------------------------------------------------ */
.post-item{margin-bottom:40px;position:relative;float:left;width:100%;overflow:hidden;}
.post-item .date_label{position:absolute;left:0;top:7px;display:none;background-color:var(--mfn-blog-date-bg);}
.post-item .date_label:after{border-left-color:var(--mfn-blog-date-bg);}
.post-photo-wrapper{width:37%;float:left}
.post-photo-wrapper iframe{width:100%}
.post-desc-wrapper{width:63%;float:left}
.no-img > .image_frame{display:none}
.no-img > .post-desc-wrapper{width:100%}
.post-desc{padding:15px 0 0 20px}

.search-results #Content { padding-top: 80px; }
.search-results .search-item { display: flex; align-items: center; margin-bottom: 40px; }
.search-results .search-item .post-featured-image { width: 30%; flex-shrink: 0; margin-right: 40px; }
.search-results .search-item .search-content { width: 70%; }
.search-results .search-item:not(.no-image) .search-content h4 { margin-bottom: 10px; }
.search-results .search-item .search-content .post-product-price { margin-bottom: 10px; }
.search-results .search-item.no-image .search-content{width:100%}
.search-results .search-item .search-footer.align-right{text-align:right}
.search-results .search-item .search-footer a i{margin-right:5px}

.search-results .search-item.has-image-on-right{flex-direction:row-reverse}
.search-results .search-item.has-image-on-right .post-featured-image{margin-left:40px;margin-right:0}

	.post-meta{margin-bottom:8px}
	.post-meta .author-date{float:left}
	.post-meta .author-date a{border-bottom-width:1px;border-style:dotted;text-decoration:none!important}
	.post-meta .category{float:right;position:relative}
	.post-meta .category.mata-tags{margin-right:10px}
	.post-meta .category .cat-btn{cursor:pointer}
	.post-meta .category .cat-wrapper{position:absolute;right:0;top:100%;display:none;z-index:21}
	.post-meta .category:hover .cat-wrapper{display:block}
	.post-meta .category .cat-wrapper ul{padding:7px;min-width:70px;background:#F9F9F9}
	.post-meta .category .cat-wrapper ul li{border-bottom:1px solid rgba(0,0,0,0.05)}
	.post-meta .category .cat-wrapper ul li:last-child{border-bottom:0}
	.post-meta .category .cat-wrapper ul li a{display:block;text-align:center;padding:1px 5px;color:#8B8B8B}
	.post-meta .category .cat-wrapper ul li a:hover{text-decoration:none;color:#5F5F5F;background:rgba(255,255,255,0.8)}

	.post-excerpt{margin-bottom:15px}
	.cat_description{margin-bottom:40px}

	.post-footer{background-color:rgba(0,0,0,.02);padding:7px 15px;overflow:hidden;line-height:30px}
	.post-footer .button-love{float:left}
	.post-footer .button-love a.mfn-love{display:inline-block;position:relative;padding-left:24px;margin-left:5px}
	.post-footer .button-love a.mfn-love i{position:absolute;left:0;top:0;font-size:16px}
	.post-footer .button-love a.mfn-love:hover{text-decoration:none}
	.post-footer .button-love a.mfn-love i:last-child{opacity:0;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
	.post-footer .button-love a:hover.mfn-love i:last-child,.post-footer .button-love a.loved.mfn-love i:last-child{opacity:1}
	.post-footer .post-links{float:right;border-left-width:1px;border-style:solid;padding-left:10px}
	.post-footer .post-links .post-comments{margin-right:10px}

	.has-custom-bg .post-footer{background-color:transparent}
	.hide-more .post-footer .post-links .icon-doc-text,.hide-more .post-footer .post-links .post-more{display:none}
	.blog_slider.hide-more .item_wrapper .hr_color,.blog_slider.hide-more .item_wrapper .button{display:none}

		/* Grid */
		.grid .post-item{width:31.33%;margin:0 1% 20px;background:#fff}
		.grid .post-photo-wrapper{width:100%;float:none}
		.grid .post-desc-wrapper{width:100%;float:none}
		.grid .post-desc-wrapper .post-desc{padding:20px 20px 0}
		.grid .post-footer{margin:20px -20px 0;box-sizing:border-box}
		.grid .post-meta .author-date .author span.label{display:none}
		.grid .post-desc-wrapper .post-meta .category .cat-btn{display:none}
		.grid .post-desc-wrapper .post-footer .button-love .love-text{display:none}

			/* Columns 2-6 */
			.posts_group.grid.col-2 .post-item{width:47.99%}
			.posts_group.grid.col-3 .post-item{width:31.33%}
			.posts_group.grid.col-4 .post-item{width:22.99%}
			.posts_group.grid.col-5 .post-item{width:18.99%;margin:0 .5% 20px}
			.posts_group.grid.col-6 .post-item{width:15.66%;margin:0 .5% 20px}

			.posts_group.grid.col-2 .post-item:nth-child(2n+1),
			.posts_group.grid.col-3 .post-item:nth-child(3n+1),
			.posts_group.grid.col-4 .post-item:nth-child(4n+1),
			.posts_group.grid.col-5 .post-item:nth-child(5n+1),
			.posts_group.grid.col-6 .post-item:nth-child(6n+1){clear:both}

		/* Masonry */
		.masonry:not(.tiles) .post-item{width:31.33%;margin:0 1% 20px;background:#fff}
		.masonry .post-photo-wrapper{width:100%;float:none}
		.masonry .post-desc-wrapper{width:100%;float:none}
		.masonry .post-desc-wrapper .post-desc{padding:20px 20px 0}
		.masonry .post-footer{margin:0 -20px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
		.masonry .post-meta .author-date .author span.label{display:none}
		.masonry .post-desc-wrapper .post-meta .category .cat-btn{display:none}
		.masonry .post-desc-wrapper .post-footer .button-love .love-text{display:none}

			/* Columns 2-6 */
			.posts_group.masonry.col-2 .post-item{width:47.99%}
			.posts_group.masonry.col-3 .post-item{width:31.33%}
			.posts_group.masonry.col-4 .post-item{width:22.99%}
			.posts_group.masonry.col-5 .post-item{width:18.99%;margin:0 .5% 20px}
			.posts_group.masonry.col-6 .post-item{width:15.66%;margin:0 .5% 20px}

		/* Masonry tiles */
		.masonry.tiles{position:relative}
		.masonry.tiles .post-item{margin:0!important;overflow:hidden;background-color:transparent}
		.masonry.tiles .post-item:not(.no-img) .post-desc-wrapper{position:absolute;z-index:4;left:0;bottom:-20px}
		.masonry.tiles .post-item:not(.no-img) .post-desc-wrapper .post-desc{background:url(../images/blog_masonry_tile_gradient.png) top left repeat-x;padding:70px 30px 30px}
		.masonry.tiles .post-item .post-desc-wrapper .post-desc{padding:50% 30px 10px}
		.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-meta .author-date .post-links{display:inline-block;margin-left:10px}
		.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-excerpt{display:none}

			/* Photo wrapper */
			.masonry.tiles .post-item:not(.no-img) .post-photo-wrapper{line-height:0;position:relative}
			.masonry.tiles .post-item:not(.no-img) .post-photo-wrapper:after{content:"";position:absolute;z-index:2;left:0;top:0;width:100%;height:100%;background:rgba(0,0,0,.2);opacity:0;transition:all .6s ease-out}
			.masonry.tiles .post-item:not(.no-img):hover .post-photo-wrapper:after{opacity:1}

			/* Posts */
			.masonry.tiles .format-link .post-title .icon-link{display:none}
			.masonry.tiles .format-link .post-title .link-wrapper{margin-left:0;padding-top:5px}
			.masonry.tiles .format-quote blockquote{margin-left:0;top:0;margin-bottom:25px}
			.masonry.tiles .format-quote blockquote:after{display:none}
			.masonry.tiles .format-quote blockquote a{text-decoration:none}

			/* Post icon */
			.masonry.tiles .post-item .post-format-icon{position:absolute;z-index:3;left:25px;top:25px;font-size:35px;line-height:35px;color:#fff}

			/* Line */
			.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-title:after{content:"";display:block;height:3px;margin-top:20px;width:0;transition:all .4s ease-out}
			.masonry.tiles .post-item:hover .post-desc-wrapper .post-desc .post-title:after{width:40%}

			/* Desc wrapper animation */
			.masonry.tiles .post-item .post-desc-wrapper{transition:all .4s ease-out}
			.masonry.tiles .post-item:hover .post-desc-wrapper{transform:translateY(-20px)}

			/* Columns 2-6 */
			.posts_group.masonry.tiles.col-2 .post-item{width:49.99%}
			.posts_group.masonry.tiles.col-3 .post-item{width:33.33%}
			.posts_group.masonry.tiles.col-4 .post-item{width:24.99%}
			.posts_group.masonry.tiles.col-5 .post-item{width:19.99%}
			.posts_group.masonry.tiles.col-6 .post-item{width:16.66%}

			/* With margin */
			.posts_group.masonry.margin .post-item{margin:0 1% 25px!important}
			.posts_group.masonry.margin.col-2 .post-item{width:47.99%}
			.posts_group.masonry.margin.col-3 .post-item{width:31.33%}
			.posts_group.masonry.margin.col-4 .post-item{width:22.99%}
			.posts_group.masonry.margin.col-5 .post-item{width:18.99%;margin:0 .5% 12px!important}
			.posts_group.masonry.margin.col-6 .post-item{width:15.66%;margin:0 .5% 12px!important}

			/* Colors */
			.masonry.tiles .post-item.format-quote blockquote,.masonry.tiles .post-item.format-quote blockquote a,.masonry.tiles .post-item.format-link .post-title .icon-link,.masonry.tiles .post-item.format-link .post-title .link-wrapper h4,.masonry.tiles .post-item.format-link .post-title .link-wrapper a,.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-title .entry-title a{color:#fff}
			.masonry.tiles .post-item.no-img .post-desc-wrapper .post-desc .post-title:after,.masonry.tiles .post-item.format-quote .post-desc-wrapper .post-desc .post-title:after,.masonry.tiles .post-item.format-link .post-desc-wrapper .post-desc .post-title:after{background-color:#fff}
			.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-head .post-meta,.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-head .post-meta a,.masonry.tiles .post-item .post-desc-wrapper .post-desc .post-excerpt{color:rgba(255,255,255,.7)}

		/* Timeline */
		.timeline .post-item{float:none;width:auto;padding-left:200px;margin-bottom:0;padding-bottom:40px;background:url(../images/timeline_right.png) no-repeat 90px top}
		.timeline .post-item:last-child{padding-bottom:20px;margin-bottom:20px}
		.timeline .format-quote .post-meta,.timeline .format-link .post-meta{padding-top:7px}
		.timeline .post-item:before{content:"";width:7px;height:7px;box-sizing:content-box;border-width:4px;border-style:solid;-webkit-border-radius:100%;border-radius:100%;position:absolute;left:126px;top:11px;display:block;visibility:visible;z-index:1}
		.timeline .date_label{display:block}
		.timeline .post-meta .author-date .date{display:none}

		/* Photo */
		.photo .post-item{float:none}
		.photo .post-photo-wrapper{width:100%;float:none}
		.photo .post-desc-wrapper{width:100%;float:none;text-align:center}
		.photo .post-desc .post-head .post-meta{display:inline-block}
		.photo .post-desc .post-head .post-footer{display:inline-block;background:none;padding:0;line-height:inherit}
		.photo .post-desc .post-excerpt{margin-bottom:0}

			.photo .post-desc .post-head .post-meta .author-date,.photo .post-desc .post-head .post-meta .category,.photo .post-desc .post-head .post-footer .button-love,.photo .post-desc .post-head .post-footer .post-links{float:none;display:inline-block}
			.photo .post-desc .post-head .post-meta .author-date{margin-right:20px}
			.photo .post-desc .post-head .post-meta .author-date .label{display:none}
			.photo .post-desc .post-head .post-footer .button-love{margin-right:20px}
			.photo .post-desc .post-head .post-footer .button-love .love-text{display:none}
			.photo .post-desc .post-head .post-footer .post-links{border:0;padding:0}
			.photo .post-desc .post-head .post-footer .post-links .icon-doc-text,.photo .post-desc .post-head .post-footer .post-links .post-more{display:none}

			.photo .format-image{text-align:center}
			.photo .format-image .post-photo-wrapper{display:inline-block;width:auto}
			.photo .format-link .post-title{display:inline-block;text-align:left}

		/* Photo 2 */
		.photo2 .post-item{margin:0}
		.photo2 .post-photo-wrapper{width:100%;float:none}

		.photo2 .image_frame{border:none;text-align:center}
		.photo2 .image_frame .image_wrapper .mask,
		.photo2 .image_frame .image_wrapper .image_links{display:none}
		.photo2 .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){top:0!important;margin-bottom:0!important}

		.photo2 .post .post-desc-wrapper{width:70%;float:none;position:relative;top:-75px;margin:0 auto;box-sizing:border-box;z-index:2}
		.photo2 .post-desc-wrapper .post-desc{padding:35px 50px}

		.photo2 .post-meta{margin-bottom:15px}
		.photo2 .author-date{display:none}
		.photo2 .category{float:none}
		.photo2 .category .cat-btn{display:none}
		.photo2 .category .cat-wrapper{display:block;position:static}
		.photo2 .category .cat-wrapper ul{margin-left:-2px;padding:0;min-width:unset;background-color:transparent}
		.photo2 .category .cat-wrapper ul li{display:inline-block;border-bottom:unset;padding:0 5px 5px 0}
		.photo2 .category .cat-wrapper ul li a{background-color:rgba(0,0,0,.05)!important;color:rgba(0,0,0,.6);padding:3px 12px;border-radius:20px}
		.photo2 .category .cat-wrapper ul li a:hover{color:rgba(0,0,0,.8)}

		.photo2 .button-love{position:absolute;right:25px;top:25px;padding:4px 13px;border-radius:20px;background-color:#272727;z-index:2}
		.photo2 .button-love a:hover{text-decoration:none}
		.photo2 .button-love .icons-wrapper i{color:#fff;margin-right:5px}
		.photo2 .button-love .icons-wrapper i.icon-heart-empty-fa{position:absolute}
		.photo2 .button-love .icons-wrapper i.icon-heart-fa{opacity:0}
		.photo2 .button-love a.loved .icons-wrapper i.icon-heart-fa{opacity:1}

		.photo2 .post-footer{background:none;margin-top:20px;padding:15px 0 0;border-top:1px solid rgba(0,0,0,.1)}
		.photo2 .post-footer .post-author{margin-right:10px}
		.photo2 .post-footer .post-author .avatar{position:relative;top:6px;margin-right:6px;border-radius:50%}

		.photo2 .post.no-img{margin-bottom:75px}
		.photo2 .post.no-img .post-desc-wrapper{top:0}
		.photo2 .post.no-img .button-love{right:17%;right:calc(15% + 25px);z-index:3}

		.photo2 .bg-dark.post-desc-wrapper{color:rgba(255,255,255,.8)}
		.photo2 .bg-dark .entry-title a{color:#fff}
		.photo2 .bg-dark .post-footer{border-top-color:rgba(255,255,255,.1)}
		.photo2 .bg-dark .category .cat-wrapper ul li a{background-color:rgba(255,255,255,.1)!important;color:rgba(255,255,255,.6)}
		.photo2 .bg-dark .category .cat-wrapper ul li a:hover{color:rgba(255,255,255,.8)}

			/* Columns 2-3 */
			.photo2.col-2 .post-item{width:47.99%; margin:0 1% 20px}
			.photo2.col-2 .post-desc-wrapper{width:85%;top:-55px}
			.photo2.col-2 .post.no-img .button-love{right:11%;right:calc(7.5% + 25px)}

			.photo2.col-3 .post-item{width:31.33%; margin:0 1% 40px}
			.photo2.col-3 .post-desc-wrapper{width:100%;top:0;}
			.photo2.col-3 .post.no-img .button-love{right:25px}

			.photo2.col-2 .post-item:nth-child(2n+1),
			.photo2.col-3 .post-item:nth-child(3n+1){clear:both}

	/* Post types */
	.format-quote .post-photo-wrapper{display:none}
	.format-quote .post-desc{padding:0}
	.format-quote .post-desc-wrapper{width:100%;float:none}

	.format-video .image_frame .image_wrapper img{margin-bottom:0!important}
	.format-video .image_frame:hover .image_wrapper img,
	.format-video .image_frame:focus .image_wrapper img{top:0}

	.format-link .post-photo-wrapper{display:none}
	.format-link .post-desc-wrapper{width:100%;float:none}
	.format-link .post-desc{padding:0}
	.format-link .post-title{overflow:hidden}
	.format-link .post-title .icon-link{display:block;width:80px;height:80px;font-size:60px;line-height:80px;border-right-width:1px;border-style:solid;float:left;text-align:center}
	.format-link .post-title .link-wrapper{margin-left:100px;padding-top:14px}
	.format-link .post-title .link-wrapper h4{margin-bottom:7px;font-size:20px;line-height:22px}

/* Single Post ----------------------------------------------------------------------- */
.post-nav{padding:10px 15px 4px 130px;margin-bottom:20px;background-image:url(../images/stripes/stripes_5_b.png);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;overflow:hidden}
.post-nav .next-prev-nav{float:left}
.post-nav .next-prev-nav li{float:left;margin-right:5px}
.post-nav .next-prev-nav a.button{margin:0}
.post-nav .list-nav{float:right;line-height:49px}

.template-slider .post-nav{margin-top:30px}
.no-share .post-nav,.share-simple .post-nav{padding-left:15px;padding-right:15px}

.post-nav.minimal.column{padding:0;background:none;position:relative;height:40px!important;}
.post-nav.minimal a{position:absolute;top:0;opacity:.6;-webkit-transition:all .3s ease-in-out;transition:all .3s ease-in-out;}
.post-nav.minimal a:hover {opacity:1}
.post-nav.minimal a.prev{left:0;}
.post-nav.minimal a.next{right:0;}
.post-nav.minimal a.home{left:50%;margin:3px 0 0 -13px;}
.post-nav.minimal a i{font-size:25px;line-height:30px;color:#626262}
.post-nav.minimal a svg{fill:#626262}

.post-header{margin-bottom:20px}
.post-header .button-love{width:99px;float:left;text-align:center}
.post-header .button-love a.mfn-love{display:inline-block;position:relative;padding-left:28px;font-size:17px;margin-top:25px}
.no-title .post-header .button-love a.mfn-love{margin-top:0}
.post-header .button-love a.mfn-love i{position:absolute;left:0;top:0;font-size:18px}
.post-header .button-love a.mfn-love:hover{text-decoration:none}
.post-header .button-love a.mfn-love i:last-child{opacity:0;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.post-header .button-love a:hover.mfn-love i:last-child,.post-header .button-love a.loved.mfn-love i:last-child{opacity:1}
.post-header .title_wrapper{margin-left:99px;border-left-width:1px;border-style:solid;padding-left:30px}
.post-header .title_wrapper h1{font-size:35px;line-height:35px}

.single-photo-wrapper .share_wrapper{float:left}
.single-photo-wrapper .image_frame{margin-left:120px}
.single-photo-wrapper .image_frame iframe{width:100%}
.no-share .single-photo-wrapper .image_frame,
.share-simple .single-photo-wrapper .image_frame{margin-left:0}
.single-photo-wrapper.image{text-align:center}
.single-photo-wrapper.image .image_frame{margin-left:0;display:inline-block}

.section-post-header .single-photo-wrapper.image .image_frame{max-width:80%;max-width:calc(100% - 130px)}
.share-simple .section-post-header .single-photo-wrapper.image .image_frame{max-width:100%}
.no-share .section-post-header .single-photo-wrapper.image .image_frame{max-width:100%}

.share_wrapper{background:#fff;border-width:1px;border-style:solid;width:98px;text-align:center;padding:10px 0 5px}
.share_wrapper .stButton{margin-bottom:10px}

.section-post-intro-share .share_wrapper{float:right;background:none;border:none;width:unset;padding:0}
.section-post-intro-share .share_wrapper > span{float:left}
.section-post-intro-share .share_wrapper .stButton .stBubble{display:none!important}

.author-box .avatar-wrapper{width:64px;height:64px;float:left;border-width:8px;border-style:solid;display:block;line-height:0;-webkit-border-radius:100%;border-radius:100%;overflow:hidden}
.author-box .desc-wrapper{background:rgba(0,0,0,.02);padding:20px;position:relative;margin-left:105px}
.author-box .desc-wrapper:after{content:"";display:block;position:absolute;left:-6px;top:35px;width:0;height:0;border-style:solid;border-width:6px 6px 6px 0;border-color:transparent rgba(0,0,0,.02) transparent transparent}
.author-box .desc-wrapper h5{margin-bottom:5px}

	/* Hide Love */
	.hide-love .button-love{display:none!important}
	.hide-love .post-header .title_wrapper{margin-left:0;padding-left:10px;border-left:none}
	.hide-love .post-nav{padding-left:15px}
	.hide-love .portfolio_group .portfolio-item .desc .title_wrapper{padding-right:0}

	/* Post related */
	.section-post-related .section-related-adjustment{border-top-width:1px;border-style:solid;padding-top:20px}
	.section-post-related .post-related{position:relative;width:33.333%}

	.section-post-related .col-2 .post-related{width:50%}
	.section-post-related .col-3 .post-related{width:33.333%}
	.section-post-related .col-4 .post-related{width:25%}
	.section-post-related .col-5 .post-related{width:20%}
	.section-post-related .col-6 .post-related{width:16.666%}

	.section-post-related .col-2 .post-related:nth-child(2n+1){clear:both}
	.section-post-related .col-3 .post-related:nth-child(3n+1){clear:both}
	.section-post-related .col-4 .post-related:nth-child(4n+1){clear:both}
	.section-post-related .col-5 .post-related:nth-child(5n+1){clear:both}
	.section-post-related .col-6 .post-related:nth-child(6n+1){clear:both}

	.section-post-related .post-related .image_frame{margin-left:30px;margin-bottom:15px}
	.section-post-related .post-related .fullscreen-container{height:180px!important}
	.section-post-related .post-related .date_label{position:absolute;left:0;top:30px;z-index:20}
	.section-post-related .post-related hr{margin-left:30px;width:40%}
	.section-post-related .post-related a.button{margin-left:30px;margin-bottom:0}

	.section-post-related .format-quote blockquote{margin-top:70px;margin-left:40px}
	.section-post-related .format-link .image_frame{height:180px;display:block; background-color:rgba(255,255,255,.5);}
	.section-post-related .format-link .image_frame:after {content: '\e8c2'; font-family: "mfn-icons"; display: block; position: absolute; left: 0; top:0; width: 100%; height: 180px; line-height: 180px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }
	.section-post-related .format-standard.no-img .image_frame{display:block;height:180px;background-color:rgba(255,255,255,.5); }
	.section-post-related .format-standard.no-img .image_frame:after {content: '\e8f6'; font-family: "mfn-icons"; display: block; position: absolute; left: 0; top:0; width: 100%; height: 180px; line-height: 180px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }
	.section-post-related .post-related .image_frame iframe{width:100%}

		/* Section post related - simple */
		.section-post-related .simple .post-related .image_frame{margin-left:0px;}
		.section-post-related .simple .post-related .date_label{margin-bottom:5px;position:static;padding:0;background-color:transparent;background-image:none;}
		.section-post-related .simple .post-related .date_label:after{display:none;}
		.section-post-related .simple .post-related hr { display: none; }
		.section-post-related .simple .post-related a.button{margin-left:0px;}

		.section-post-related .simple .post-related.format-quote .date_label{margin-bottom:10px;}
		.section-post-related .simple .format-quote blockquote{margin-top:0px;}

	/* Format | Link */
	.single-format-link .single-photo-wrapper .share_wrapper{float:none;width:auto;padding:10px 15px 15px}
	.single-format-link .single-photo-wrapper .share_wrapper .stButton{margin:0 10px 0 0}
	.single-format-link .section-post-header .single-photo-wrapper .image_frame{display:none}

	/* Format | Quote */
	.single-format-quote #Subheader .title{width:100%}
	.single-format-quote #Subheader ul.breadcrumbs{display:none}
	.single-format-quote .single-photo-wrapper .share_wrapper{float:none;width:auto;padding:10px 15px 15px}
	.single-format-quote .single-photo-wrapper .share_wrapper .stButton{margin:0 10px 0 0}
	.single-format-quote .section-post-header .single-photo-wrapper .image_frame{display:none}

	/* NO img */
	.portfolio.no-img .single-photo-wrapper .share_wrapper,.format-image.no-img .single-photo-wrapper .share_wrapper,.format-standard.no-img .single-photo-wrapper .share_wrapper{float:none;width:auto;padding:10px 15px 15px}
	.portfolio.no-img .single-photo-wrapper .share_wrapper .stButton,.format-image.no-img .single-photo-wrapper .share_wrapper .stButton,.format-standard.no-img .single-photo-wrapper .share_wrapper .stButton{margin:0 10px 0 0}
	.portfolio.no-img .section-post-header .single-photo-wrapper .image_frame,.format-image.no-img .section-post-header .single-photo-wrapper,.format-standard.no-img .section-post-header .single-photo-wrapper{display:none!important}

	/* Project decription */
	.project-description li{width:99.9%;clear:both;padding:7px 10px;background:rgba(0,0,0,.01);border-style:solid;border-color:rgba(0,0,0,.03);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
	.project-description li .label{font-weight:700;min-width:40px;padding-right:10px;display:inline-block}
	.project-description li.one-third{float:left;width:33.3%;clear:none;padding-right:15px;border-width:0 1px 1px 0}
	.project-description li:nth-child(3){border-right-color:transparent}

	/* Share Item */
	.share_item{float:none;width:auto;padding:10px 15px 15px}
	.share_item .stButton{margin:0 10px 0 0}


/* Share | Simple | .share-simple-wrapper */
.share-simple .post-header .title_wrapper{margin-left:0;padding-left:0;border-left-width:0}

.share-simple-wrapper{border-top:1px solid rgba(0,0,0,.08);padding:15px 0;text-align:left}
.share-simple-wrapper .share-label{margin-right:10px}
.share-simple-wrapper .icons{display:inline}
.share-simple-wrapper .icons a{padding:0 5px;color:#a8a8a8}
.share-simple-wrapper .icons .facebook:hover{color:#3B5998}
.share-simple-wrapper .icons .instagram:hover{color:#C13584}
.share-simple-wrapper .icons .twitter:hover{color:#1DA1F2}
.share-simple-wrapper .icons .google:hover{color:#DC4E41}
.share-simple-wrapper .icons .linkedin:hover{color:#0077B5}
.share-simple-wrapper .icons .pinterest:hover{color:#BD081B}

.share-simple-wrapper .button-love{float:right}
.share-simple-wrapper .mfn-love{position:relative;display:inline-block;padding-left:24px;margin-left:5px}
.share-simple-wrapper .mfn-love i{position:absolute;left:0;top:0;font-size:16px}
.share-simple-wrapper .mfn-love:hover{text-decoration:none}
.share-simple-wrapper .mfn-love i:last-child{opacity:0;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.share-simple-wrapper .mfn-love:hover i:last-child,
.share-simple-wrapper .mfn-love.loved i:last-child{opacity:1}

/* Templates | Preview --------------------------------------------------------------- */
.single-template .section-post-header,.single-template .section-post-about,.single-template .section-post-related,.single-template .section-post-comments{display:none;}

/* Widgets */

.mcb-sidebar{border-style:solid;padding:0 20px;position:relative}
.mcb-sidebar:before{content:"";display:block;position:absolute;top:0;width:1500px;height:100%;background:rgba(0,0,0,.01);visibility:visible}
.mcb-sidebar .sidebar__inner{overflow:hidden;transform:translate(0, 0);transform:translate3d(0, 0, 0)}

.aside_left .mcb-sidebar{border-right-width:1px}
.aside_left .mcb-sidebar:before{right:0}
.aside_right .mcb-sidebar{border-left-width:1px}
.aside_right .mcb-sidebar:before{left:0}

.mcb-sidebar.style-simple{border-color:transparent!important}
.mcb-sidebar.style-simple:before{content:unset}

.aside_right .is-affixed .inner-wrapper-sticky,
.aside_both .sidebar-2 .is-affixed .inner-wrapper-sticky{margin-left:1px}

.mcb-sidebar.lines-boxed .widget:after{width:100%}
.mcb-sidebar.lines-hidden .widget:after{display:none}

.widget{margin:30px 0;position:relative}
.widget:last-child:after{display:none}
.widget:after{content:"";display:block;position:absolute;bottom:0;width:1500px;height:0;visibility:visible;border-width:1px 0 0;border-style:solid}
.widget:not(.widget_block) > h3{font-size:18px;line-height:22px}

.sidebar.has-lines .widget{padding-bottom:30px}
.aside_left .widget:after{right:0}
.aside_right .widget:after{left:0}

.elementor-widget-sidebar .widget:after{content:unset}

.with_aside.aside_both .sidebar-1{border-right-width:1px}
.with_aside.aside_both .sidebar-1:before{right:0}
.with_aside.aside_both .sidebar-1 .widget:after{right:0}
.with_aside.aside_both .sidebar-2{border-left-width:1px}
.with_aside.aside_both .sidebar-2:before{left:0}
.with_aside.aside_both .sidebar-2 .widget:after{left:0}

.widget_block label{font-size:18px;line-height:22px;font-weight:normal;margin-bottom:15px}

.wp-block-image img { height: auto; }

.wp-block-search .wp-block-search__button{overflow:visible;max-height:39px}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper{border-color:#eee}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__input{border:none;background:none!important;margin-bottom:0;padding:10px}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper .wp-block-search__button{margin-right:0;margin-bottom:0}

	/* Recent posts */
	.Recent_posts ul{margin:0!important}
	.Recent_posts ul li{margin-bottom:10px;list-style:none!important}
	.Recent_posts ul li:last-child{margin-bottom:0}
	.Recent_posts ul li a{text-decoration:none}
	.Recent_posts ul li .desc{margin-right:80px;padding:5px 15px;background:#fff;position:relative;min-height:70px}
	.Recent_posts ul li .desc:after{content:"";display:block;position:absolute;right:0;top:0;width:4px;height:100%}
	.Recent_posts ul li .desc h6{position:relative;z-index:2;margin-bottom:3px;padding-bottom:3px;border-bottom-width:1px;border-style:solid}
	.Recent_posts ul li .desc .date{position:relative;z-index:2}
	.Recent_posts ul li .desc .date i{display:inline-block;margin-right:2px}
	.Recent_posts ul li .photo{width:80px;height:80px;line-height:0;text-align:center;float:right;position:relative}
	.Recent_posts ul li .photo .c{width:25px;height:25px;line-height:25px;z-index:3;text-align:center;color:#fff;position:absolute;right:-12px;bottom:12px;font-size:11px;-webkit-border-radius:3px;border-radius:3px}

	.Recent_posts ul li.no-img{position:relative}
	.Recent_posts ul li.no-img .photo{width:0;position:static}
	.Recent_posts ul li.no-img .desc{margin-right:0;min-height:inherit}

	.Recent_posts.classic ul li a:hover h6,
	.Recent_posts.classic ul li a:hover .desc .date{color:#fff!important}
	.Recent_posts.classic ul li a:hover .desc:after{width:100%}

		/* Formats */
		.Recent_posts ul li.format-link .photo{ background-color:#eee; }
		.Recent_posts ul li.format-link .photo:after {content: '\e8c2'; font-family: "mfn-icons"; display: block; z-index:91; position: absolute; left: 0; top:0; width: 80px; height: 80px; line-height: 80px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }
		.Recent_posts ul li.format-quote .photo{ background-color:#eee;}
		.Recent_posts ul li.format-quote .photo:after {content: '\e909'; font-family: "mfn-icons"; display: block; position: absolute; left: 0; top:0; width: 80px; height: 80px; line-height: 80px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }

		/* Recent posts | style: blog news */
		.Recent_posts.blog_news ul li{margin-bottom:20px;overflow:hidden}
		.Recent_posts.blog_news ul li .photo{float:left;width:30%;height:auto;border-right:3px solid #ddd;background-color:transparent}
		.Recent_posts.blog_news ul li .photo .c{display:none}
		.Recent_posts.blog_news ul li .desc{width:70%;margin-left:30%;padding-top:0;box-sizing:border-box;background:none!important}
		.Recent_posts.blog_news ul li .desc:after{display:none}
		.Recent_posts.blog_news ul li .desc h6{margin-top:-3px}

		.Recent_posts.blog_news ul li.no-img .photo{display:none}
		.Recent_posts.blog_news ul li.no-img .desc{width:100%;margin-left:0;padding-left:0}

		/* Animation */
		.Recent_posts ul li a h6,.Recent_posts ul li a .desc .date,.Recent_posts ul li a .desc:after{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

	/* Widget | Gallery | WordPress 4.9+ */
	.widget_media_gallery .gallery .gallery-item{margin-bottom:2.5%!important}

	/* Recent posts | WordPress */
	.widget_recent_entries ul li{padding:5px 15px;background:#fff;position:relative;margin-bottom:10px}
	.widget_recent_entries ul li a{display:block;text-decoration:none;position:relative;z-index:2;margin-bottom:3px;padding-bottom:3px;border-bottom-width:1px;border-style:solid}
	.widget_recent_entries ul li:last-child{margin-bottom:0}
	.widget_recent_entries ul li:after{content:"";display:block;position:absolute;right:0;top:0;width:4px;height:100%}
	.widget_recent_entries ul li:hover:after{width:100%}
	.widget_recent_entries ul li .post-date{display:block;position:relative;z-index:2}
	.widget_recent_entries ul li:hover a,.widget_recent_entries ul li:hover .post-date{color:#fff}
	.widget_recent_entries ul li:hover a,.widget_recent_entries ul li:hover .post-date,.widget_recent_entries ul li:hover:after{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

	/* Categories */
	.widget_categories ul{list-style-type:square;color:#fff;padding:5px 10px 5px 30px}
	.widget_categories ul li{position:relative}
	.widget_categories ul li:after{content:"";display:block;width:70px;border-width:0 0 1px;border-style:solid;border-color:rgba(255,255,255,.2);position:absolute;left:-30px;bottom:0}
	.widget_categories ul li:last-child:after{display:none}
	.widget_categories ul li a{color:#fff!important;display:inline-block;padding:7px 0 7px 3px}

	/* Archives, Custom menu */
	.widget_archive ul,.widget_nav_menu ul{list-style-type:square;padding:5px 10px 5px 30px;background:rgba(0,0,0,.03)}
	.widget_archive ul li,.widget_nav_menu ul li{position:relative}
	.widget_archive ul li:after,.widget_nav_menu ul li:after{content:"";display:block;width:70px;border-width:0 0 1px;border-style:solid;border-color:rgba(0,0,0,.1);position:absolute;left:-30px;bottom:0}
	.widget_archive ul li:last-child:after,.widget_nav_menu ul li:last-child:after{display:none}
	.widget_archive ul li a,.widget_nav_menu ul li a{display:block;padding:8px 0 9px 3px}

	/* Meta, Pages, RSS */
	.widget_meta ul,.widget_pages ul,.widget_rss ul{list-style-type:square;padding:0 0 0 30px}
	.widget_meta ul li a,.widget_pages ul li a,.widget_rss ul li a{display:block;padding:4px 0 7px 4px}

	/* Recent comments */
	.widget_mfn_recent_comments ul{margin:0!important}
	.widget_mfn_recent_comments ul li{padding-bottom:15px;margin-bottom:0!important;background:url(../images/recent_comments.png) no-repeat 4px top;padding-left:40px;position:relative;list-style:none!important}
	.widget_mfn_recent_comments ul li:last-child{padding-bottom:5px}
	.widget_mfn_recent_comments ul li .date_label{background-color:rgba(0,0,0,.03);margin-top:7px;margin-bottom:5px;position:relative}
	.widget_mfn_recent_comments ul li .date_label:after{border-left-color:rgba(0,0,0,.03)}
	.widget_mfn_recent_comments ul li:after{content:"";width:7px;height:7px;border-width:4px;border-style:solid;-webkit-border-radius:100%;border-radius:100%;position:absolute;left:0;top:11px;display:block;z-index:1}
	.widget_mfn_recent_comments ul li p{margin-bottom:0}

	/* Recent comments wordpress */
	.widget_recent_comments ul li{padding-bottom:5px;padding-top:6px;background:url(../images/recent_comments.png) no-repeat 4px top;padding-left:40px;position:relative}
	.widget_recent_comments ul li:last-child{padding-bottom:5px}
	.widget_recent_comments ul li:after{content:"";width:7px;height:7px;border-width:4px;border-style:solid;-webkit-border-radius:100%;border-radius:100%;position:absolute;left:0;top:11px;display:block;z-index:1}

	/* Search */
	.widget_search input[type="text"]{margin-bottom:0;width:100%}
	.widget_search .icon_close,.widget_search .icon_search{display:none}

	/* Calendar */
	.widget_calendar td,.widget_calendar th{padding:4px 3px}
	.widget_calendar caption{padding:5px;font-size:14px}
	.widget_calendar table tfoot tr:hover td{background:none!important}

	/* Flickr */
	.Flickr{overflow:hidden}
	.Flickr .flickr_badge_image{margin:0;padding:0;float:left;margin:0 1% 2% 1%;width:23%}
	.Flickr .flickr_badge_image a{display:block;line-height:0}

	/* Recent tweets */
	.widget_tp_widget_recent_tweets .tp_recent_tweets{clear:none;float:none}
	.widget_tp_widget_recent_tweets ul{overflow:hidden}
	.widget_tp_widget_recent_tweets ul li:last-child{padding-bottom:0}

	/* Tag cloud */
	.widget_mfn_tag_cloud ul{margin-bottom:0;overflow:hidden}
	.widget_mfn_tag_cloud ul li{margin:0;padding:0;float:left;margin:0 5px 2px 0}
	.widget_mfn_tag_cloud a{overflow:hidden;white-space:nowrap;display:inline-block;height:22px;font-size:12px;padding-right:8px;margin-right:1px}
	.widget_mfn_tag_cloud a:hover{text-decoration:none}
	.widget_mfn_tag_cloud a span{padding-left:8px;height:22px;line-height:22px;display:block;float:left}

	/* Muffin menu */
	.widget_mfn_menu ul li a{display:block;padding:7px 10px;margin-bottom:5px;background:#fff;border:1px solid rgba(0,0,0,.04);color:#858585}
	.widget_mfn_menu ul li a:hover,.widget_mfn_menu ul li.current-menu-item:not(.current-menu-ancestor) > a,.widget_mfn_menu ul li.current_page_item:not(.current_page_ancestor) > a{text-decoration:none;color:#fff!important}
	.widget_mfn_menu ul li ul li a{padding-left:20px}
	.widget_mfn_menu ul li ul li a:before{content:"-";margin-right:5px}
	.widget_mfn_menu ul li ul li ul li a{padding-left:40px}
	.widget_mfn_menu ul li ul li ul li ul li a{padding-left:60px}

	.widget_mfn_menu ul.submenus-hover li ul{overflow:hidden;max-height:0;-webkit-transition:max-height 1s ease-in-out;-moz-transition:max-height 1s ease-in-out;-o-transition:max-height 1s ease-in-out;transition:max-height 1s ease-in-out}
	.widget_mfn_menu ul.submenus-hover li.current_page_item > ul,.widget_mfn_menu ul.submenus-hover li:hover > ul{max-height:10000px}
	.widget_mfn_menu ul.submenu-active li.current-menu-parent > ul,
	.widget_mfn_menu ul.submenu-active li.current-menu-ancestor > ul{max-height:10000px}

	.widget_mfn_menu ul.submenus-click li ul{overflow:hidden;max-height:0}
	.widget_mfn_menu ul.submenus-click li.hover > ul{max-height:10000px}

	/* Muffin login */
	.mfn-login{overflow:hidden}
	.mfn-login form p{margin-bottom:5px}
	.mfn-login form input{margin-bottom:0}
	.mfn-login .sep{margin:0 7px}
	.mfn-login .avatar-wrapper{float:left;width:64px;margin:0 10px 10px 0}
	.mfn-login .author{float:left}
	.mfn-login .alert{padding:5px 10px;margin-bottom:5px}

/* Portfolio ----------------------------------------------------------------------- */
.portfolio_group{margin:0!important}
.portfolio_group .portfolio-item{list-style:none!important;float:left;margin-bottom:0}
.portfolio_group .portfolio-item.isotope-grid-sizer{margin:0!important}
.portfolio_group:not(.list) .portfolio-item{background:none;}

.portfolio_group .portfolio-item .list_style_header{display:none;width:100%;margin-bottom:20px;position:relative;}
.portfolio_group .portfolio-item .list_style_header h3{margin:0;}
.portfolio_group .portfolio-item .list_style_header .links_wrapper{margin-left:auto;flex-shrink:0}
.portfolio_group .portfolio-item .list_style_header .links_wrapper a{margin-bottom:0}
.portfolio_group .portfolio-item .list_style_header .links_wrapper a:last-child{margin-right:0}
.portfolio_group .portfolio-item:first-child .list_style_header .links_wrapper a.portfolio_prev_js{display:none}
.portfolio_group .portfolio-item:last-child .list_style_header .links_wrapper a.portfolio_next_js{display:none}
.portfolio_group .portfolio-item .image_frame{width:100%;margin-bottom:0}

.portfolio_group .portfolio-item .desc{padding:20px;background:#fff;overflow:hidden}
.portfolio_group .portfolio-item .desc .title_wrapper{position:relative;padding-right:43px}
.portfolio_group .portfolio-item .desc .title_wrapper h5{margin-bottom:0}
.portfolio_group .portfolio-item .desc .title_wrapper .button-love{position:absolute;right:0;top:0}
.portfolio_group .portfolio-item .desc .title_wrapper .button-love a.mfn-love{display:inline-block;position:relative;padding-left:24px}
.portfolio_group .portfolio-item .desc .title_wrapper .button-love a.mfn-love i{position:absolute;left:0;top:0;font-size:16px}
.portfolio_group .portfolio-item .desc .title_wrapper .button-love a.mfn-love:hover{text-decoration:none}
.portfolio_group .portfolio-item .desc .title_wrapper .button-love a.mfn-love i:last-child{opacity:0;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.portfolio_group .portfolio-item .desc .title_wrapper .button-love a:hover.mfn-love i:last-child,.portfolio_group .portfolio-item .desc .title_wrapper .button-love a.loved.mfn-love i:last-child{opacity:1}
.portfolio_group .portfolio-item .desc .desc-wrapper{margin-right:280px}
.portfolio_group .portfolio-item .desc .details-wrapper{float:right;width:240px;padding-left:19px;border-left-width:1px;border-style:solid}
.portfolio_group .portfolio-item .desc .details-wrapper dl{margin-bottom:0}
.portfolio_group .portfolio-item .desc .details-wrapper dl > dt{padding:2px 0;border:0;width:80px}
.portfolio_group .portfolio-item .desc .details-wrapper dl > dd{padding:2px 0;border:0;margin-left:90px}

	/* List */
	.portfolio_group.list .portfolio-item{width:100%;border-bottom-width:0;border-style:solid}
	.portfolio_group.list .portfolio-item .portfolio-item-fw-bg{background-position:top center;background-repeat:repeat;padding:35px 0}
	.portfolio_group:not(.list) .portfolio-item .portfolio-item-fw-bg{background:none!important}
	body.with_aside .portfolio_group.list .portfolio-item .portfolio-item-fw-bg{padding-left:5%;padding-right:5%}
	body:not(.with_aside) .portfolio_group.list .portfolio-item .portfolio-item-fw-wrapper{width:1176px;margin:0 auto}
	.portfolio_group.list .portfolio-item .list_style_header{display:flex;align-items:center;}
	.portfolio_group.list .portfolio-item .desc{background:none;padding:20px 0 0}
	.portfolio_group.list .portfolio-item .desc .title_wrapper{display:none}

	/* Flat */
	.portfolio_group.flat .portfolio-item{width:33.3%}
	.portfolio_group.flat .portfolio-item .image_frame{border:0}
	.portfolio_group.flat .portfolio-item .image_frame .mask{box-shadow:0 0 0 0;-webkit-box-shadow:0 0 0 0}
	.portfolio_group.flat .portfolio-item .desc{display:none}

	/* Grid */
	.portfolio_group.grid .portfolio-item{width:31.2%;margin:0 1% 20px}
	.portfolio_group.grid .portfolio-item .desc .desc-wrapper,.portfolio_group.grid .portfolio-item .desc .details-wrapper{display:none}

	/* Masonry */
	.portfolio_group.masonry .portfolio-item{width:31.2%;margin:0 1% 20px}
	.portfolio_group.masonry .portfolio-item .desc .title_wrapper{margin-bottom:15px}
	.portfolio_group.masonry .portfolio-item .desc .desc-wrapper{display:block;margin-right:0}
	.portfolio_group.masonry .portfolio-item .desc .details-wrapper{display:none}

	/* Masonry hover */
	.portfolio_group.masonry-hover .portfolio-item { width: 31.2%; margin: 0 1% 20px; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper { position: relative; overflow: hidden; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc { opacity: 0; position: absolute; left: 0; top: 0; background-color: rgba(0,0,0,.3); height: 100%; width: 100%; padding: 10% 10% 50px; box-sizing: border-box; z-index:2; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc h3:after { content: ""; display: block; margin: 15px 0; width: 20px; height: 3px; background: #fff; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc h3 a { color: #fff; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .desc-inner { height: 100%; overflow: hidden; color: #fff; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper { bottom: 18px; box-sizing: border-box; left: 0; padding: 0 7% 0 8%; position: absolute; width: 100%; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.zoom,
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.link,
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.external { font-size: 25px; color: #fff; position: relative; top: 0; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.zoom {  }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.link { float: right; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.external {}
	.portfolio_group.masonry-hover .portfolio-item .image-wrapper { line-height: 0; }

		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc.bg-light h3 a,
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc.bg-light .desc-inner,
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc.bg-light .links-wrapper a { color: #444; }
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc.bg-light h3:after { background: #444; }

	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper:hover .hover-desc { opacity: 1; }
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a:hover.zoom,
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a:hover.link,
	.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a:hover.external { top: -3px; }

	.portfolio_group.masonry-hover .portfolio-item.no-thumbnail .masonry-hover-wrapper .hover-desc { padding: 10%; }
	.portfolio_group.masonry-hover .portfolio-item.no-thumbnail .masonry-hover-wrapper { overflow: visible; }
	.portfolio_group.masonry-hover .portfolio-item.no-thumbnail .masonry-hover-wrapper .hover-desc { position: static; opacity: 1; }
	.portfolio_group.masonry-hover .portfolio-item.no-thumbnail .masonry-hover-wrapper .hover-desc .desc-inner { margin-bottom: 15px; }
	.portfolio_group.masonry-hover .portfolio-item.no-thumbnail .masonry-hover-wrapper .hover-desc .links-wrapper { position: static; padding: 0; margin-top: 30px; }
	.portfolio_group.masonry-hover .portfolio-item.no-thumbnail .masonry-hover-wrapper .hover-desc .links-wrapper a.zoom { display:none; }

		/* Animation */
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.zoom,
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.link,
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc .links-wrapper a.external,
		.portfolio_group.masonry-hover .portfolio-item .masonry-hover-wrapper .hover-desc { -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; }

	/* Masonry minimal */
	.portfolio_group.masonry-minimal .portfolio-item { width: 31.2%; margin: 0 1% 20px; }

	/* Masonry Flat */
	.portfolio_group.masonry-flat {float:left;width:100%}

	.portfolio_group.masonry-flat .portfolio-item{width:24.9%;width:calc(25% - 1px);display:block;float:left;position:relative}
	.portfolio_group.masonry-flat .portfolio-item.wide{width:49.8%;width:calc(50% - 1px)}

	.portfolio_group.masonry-flat .portfolio-item .portfolio-item-fill { padding-bottom:78%;}
	.portfolio_group.masonry-flat .portfolio-item.tall .portfolio-item-fill { padding-bottom:156%;}
	.portfolio_group.masonry-flat .portfolio-item.wide .portfolio-item-fill { padding-bottom:39%;}
	.portfolio_group.masonry-flat .portfolio-item.tall.wide .portfolio-item-fill { padding-bottom:78%;}

	.portfolio_group.masonry-flat .portfolio-item .image_frame { border: 0; position:absolute; top:0; left:0; bottom:0; right:0; overflow:hidden;}
	.portfolio_group.masonry-flat .portfolio-item .image_frame .image_wrapper { position:static;}
	.portfolio_group.masonry-flat .portfolio-item .image_frame .mask { box-shadow: 0 0 0 0; -webkit-box-shadow: 0 0 0 0; }
	.portfolio_group.masonry-flat .portfolio-item .image_frame img { margin:0 !important; top:0 !important; }

	.portfolio_group.masonry-flat .portfolio-item .desc { display: none; }

		/* Columns 2-6 */

			/* Grid, Masonry, Masonry Hover */
			.portfolio_group.grid.col-2 .portfolio-item,
			.portfolio_group.masonry.col-2 .portfolio-item,
			.portfolio_group.masonry-hover.col-2 .portfolio-item,
			.portfolio_group.masonry-minimal.col-2 .portfolio-item { width: 47.99%;}

			.portfolio_group.grid.col-3 .portfolio-item,
			.portfolio_group.masonry.col-3 .portfolio-item,
			.portfolio_group.masonry-hover.col-3 .portfolio-item,
			.portfolio_group.masonry-minimal.col-3 .portfolio-item { width: 31.33%;}

			.portfolio_group.grid.col-4 .portfolio-item,
			.portfolio_group.masonry.col-4 .portfolio-item,
			.portfolio_group.masonry-hover.col-4 .portfolio-item,
			.portfolio_group.masonry-minimal.col-4 .portfolio-item { width: 22.99%;}

			.portfolio_group.grid.col-5 .portfolio-item,
			.portfolio_group.masonry.col-5 .portfolio-item,
			.portfolio_group.masonry-hover.col-5 .portfolio-item,
			.portfolio_group.masonry-minimal.col-5 .portfolio-item { width: 18.99%; margin: 0 0.5% 20px;}

			.portfolio_group.grid.col-6 .portfolio-item,
			.portfolio_group.masonry.col-6 .portfolio-item,
			.portfolio_group.masonry-hover.col-6 .portfolio-item,
			.portfolio_group.masonry-minimal.col-6 .portfolio-item { width: 15.66%; margin: 0 0.5% 20px;}

			/* Flat */
			.portfolio_group.flat.col-2 .portfolio-item { width: 49.99%;}
			.portfolio_group.flat.col-3 .portfolio-item { width: 33.33%;}
			.portfolio_group.flat.col-4 .portfolio-item { width: 24.99%;}
			.portfolio_group.flat.col-5 .portfolio-item { width: 19.99%;}
			.portfolio_group.flat.col-6 .portfolio-item { width: 16.66%;}

	/* Exposure */
	.portfolio_group.exposure {}
	.portfolio_group.exposure .portfolio-item { width: 100%; position: relative; }
	.portfolio_group.exposure .portfolio-item a.link { display: block; }
	.portfolio_group.exposure .portfolio-item .image-wrapper { line-height: 0; }
	.portfolio_group.exposure .portfolio-item .image-wrapper .mask { position: absolute; left: 0; top: 0; width: 100%; height: 100%; z-index: 1; background: rgba(0,0,0,.5); opacity: 0; }

	.portfolio_group.exposure .portfolio-item .desc-inner { position: absolute; left: 0; top: 30px; z-index: 2; width: 100%; }
	.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper-inner { padding: 0 35px; margin: 0 1%; width: 98%; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }

		.full-width .portfolio_group.exposure .portfolio-item .desc-inner { top: 70px; }
		.full-width .portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper-inner { padding: 0; }

	.portfolio_group.exposure .portfolio-item .desc-inner .line { display: block; width: 0; height: 4px; margin-bottom: 20px; }
	.portfolio_group.exposure .portfolio-item .desc-inner .entry-title { margin-bottom: 20px; }
	.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper { width: 75%; opacity: 0.7; }

	.portfolio_group.exposure .portfolio-item .details-wrapper { position: absolute; right: 0; bottom: 5px; z-index: 2; width: 100%; }
	.portfolio_group.exposure .portfolio-item .details-wrapper .details-wrapper-inner { padding: 0 25px; margin: 0 1%; width: 98%; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }

		.full-width .portfolio_group.exposure .portfolio-item .details-wrapper { bottom: 35px; }
		.full-width .portfolio_group.exposure .portfolio-item .details-wrapper .details-wrapper-inner { padding: 0; }

	.portfolio_group.exposure .portfolio-item .details-wrapper .column { float: right; opacity: 0; -webkit-transform: translateY(-7%); transform: translateY(-7%); border-top: 1px solid rgba(255,255,255,.4); padding: 20px 10px; margin-bottom: 0; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; }
	.portfolio_group.exposure .portfolio-item .details-wrapper .column h5.label { font-weight: 400; margin-bottom: 8px; }
	.portfolio_group.exposure .portfolio-item .details-wrapper .column h5 { font-weight: 700; }

		/* Colors */
		.portfolio_group.exposure .portfolio-item .desc-inner .entry-title,
		.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper,
		.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper h2,
		.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper h3,
		.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper h4,
		.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper h5,
		.portfolio_group.exposure .portfolio-item .desc-inner .desc-wrapper h6,
		.portfolio_group.exposure .portfolio-item .details-wrapper h5,
		.portfolio_group.exposure .portfolio-item .details-wrapper h5 a { color: #fff; }

		/* Hover */
		.portfolio_group.exposure .portfolio-item .desc-inner .line,
		.portfolio_group.exposure .portfolio-item .image-wrapper .mask {
			-webkit-transition: all 0.6s cubic-bezier(0.645, 0.045, 0.355, 1); transition: all 0.6s cubic-bezier(0.645, 0.045, 0.355, 1);
		}
		.portfolio_group.exposure .portfolio-item .details-wrapper .column {
			-webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1); transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
		}

		.portfolio_group.exposure .portfolio-item .details-wrapper .column:first-child {
			-webkit-transition-delay: 0.2s; transition-delay: 0.2s;
		}
		.portfolio_group.exposure .portfolio-item .details-wrapper .column:nth-child(2) {
			-webkit-transition-delay: 0.3s; transition-delay: 0.3s;
		}
		.portfolio_group.exposure .portfolio-item .details-wrapper .column:nth-child(3) {
			-webkit-transition-delay: 0.4s; transition-delay: 0.4s;
		}

		.portfolio_group.exposure .portfolio-item:hover .desc-inner .line { width: 100px; }
		.portfolio_group.exposure .portfolio-item:hover .image-wrapper .mask { opacity: 1; }
		.portfolio_group.exposure .portfolio-item:hover .details-wrapper .column { opacity: 1; -webkit-transform: translateY(0); transform: translateY(0); }

.section_wrapper .portfolio_wrapper .column.one.pager_wrapper .hover-desc{margin:40px 0!important}
.template-slider .section-portfolio-header{margin-top:30px}

/* Footer */

#Footer{background-position:center top;background-repeat:no-repeat;position:relative}

#Footer.full-width .container{max-width:100%}

#Footer .footer_action{background:rgba(0,0,0,.1)}
#Footer .footer_action .mcb-column-inner{margin-bottom:30px;padding-top:30px;text-align:center;font-size:110%;line-height:180%}

#Footer .widgets_wrapper{padding:15px 0}
#Footer .widgets_wrapper .mcb-column-inner{margin-bottom:0}
#Footer .widgets_wrapper .widget:after{display:none}

#Footer .footer_copy .one{margin-bottom:20px;padding-top:30px;min-height:33px}
#Footer .footer_copy .copyright{float:left}

#Footer .footer_copy .social{float:right;margin-right:20px}
#Footer .footer_copy .social li{display:inline-block;margin-right:6px}
#Footer .footer_copy .social li:last-child{margin-right:0}
#Footer .footer_copy .social li a{font-size:15px;line-height:15px;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
#Footer .footer_copy .social-menu{float:right;margin-right:20px}
#Footer .footer_copy .social-menu li{display:inline-block;margin-right:6px;padding-right:6px;border-right:1px solid rgba(255,255,255,.1)}
#Footer .footer_copy .social-menu li:last-child{margin-right:0;padding-right:0;border-right-width:0}

.mfn-footer .footer_copy{border-top:1px solid rgba(255,255,255,.1)}
.mfn-footer .footer_copy .social li a {color:rgba(255,255,255,.3);}
.mfn-footer .footer_copy .social li a:hover{color:#fff}

.footer-copy-center #Footer .footer_copy{text-align:center}
.footer-copy-center #Footer .footer_copy .copyright{float:none;margin:0 0 10px}
.footer-copy-center #Footer .footer_copy #back_to_top{float:none;margin:-10px 0 10px}
.footer-copy-center #Footer .footer_copy .social{float:none;margin:0}
.footer-copy-center #Footer .footer_copy .social-menu{float:none;margin:0}

	/* Back to top */

	#back_to_top{float:right;margin:-9px 0 0}
	#back_to_top.in_footer{position:absolute;bottom:20px;right:75px}
	#back_to_top.hide{display:none}

	#back_to_top.sticky{position:fixed;right:75px;bottom:20px;z-index:9001}
	#back_to_top.sticky.scroll{opacity:0;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
	#back_to_top.sticky.scroll.focus,#back_to_top.sticky.scroll:hover{opacity:1}

	.button-round #back_to_top.sticky{right:100px}

	/* Widgets */

	#Footer .Recent_posts ul li .desc{background:rgba(0,0,0,.1)}
	#Footer .widget_mfn_menu ul li a{background:rgba(0,0,0,.1);color:#ccc}
	#Footer .widget_recent_entries ul li{background:rgba(0,0,0,.1)}
	#Footer ul.list_mixed li:after,#Footer ul.list_check li:after,#Footer ul.list_star li:after,#Footer ul.list_idea li:after{background:rgba(255,255,255,.08)}
	#Footer .widget_mfn_recent_comments ul li .date_label{background-color:rgba(0,0,0,.07)}
	#Footer .widget_mfn_recent_comments ul li .date_label:after{border-left-color:rgba(0,0,0,.07)}

	/* Styles */
	.footer-fixed #Footer{position:fixed;width:100%;bottom:0;left:0;z-index:1}
	.footer-sliding #Wrapper,.footer-sliding #Content{position:relative;z-index:0}
	.footer-sliding #Footer{position:fixed;width:100%;bottom:0;left:0;z-index:-1}
	.footer-stick #Footer.is-sticky{position:fixed;width:100%;bottom:0;left:0}

/* GDPR */

#mfn-gdpr{display:none;align-items:center;position:fixed;z-index:10000;box-sizing:border-box}
#mfn-gdpr.show{display:flex}
#mfn-gdpr .mfn-gdpr-image{line-height:0}
#mfn-gdpr .mfn-gdpr-content > :last-child{margin-bottom:0}

#mfn-gdpr[data-direction="horizontal"]{justify-content:center;padding:30px;width:100%;left:0}
#mfn-gdpr[data-direction="horizontal"] .mfn-gdpr-image{margin-right:25px;flex-shrink:0}
#mfn-gdpr[data-direction="horizontal"] .mfn-gdpr-image img{max-height:48px;width:auto}
#mfn-gdpr[data-direction="horizontal"] .mfn-gdpr-content{margin-right:7vw}
#mfn-gdpr[data-direction="horizontal"] .mfn-gdpr-readmore{margin-right:15px;flex-shrink:0}
#mfn-gdpr[data-direction="horizontal"] .mfn-gdpr-button{flex-shrink:0}

#mfn-gdpr[data-direction="vertical"]{flex-direction:column;padding:40px;bottom:40px;width:320px}
#mfn-gdpr[data-direction="vertical"] .mfn-gdpr-image{margin-bottom:25px}
#mfn-gdpr[data-direction="vertical"] .mfn-gdpr-image img{max-height:64px;max-width:100%}
#mfn-gdpr[data-direction="vertical"] .mfn-gdpr-content{margin-bottom:15px;text-align:center}
#mfn-gdpr[data-direction="vertical"] .mfn-gdpr-readmore{margin-bottom:15px}

#mfn-gdpr[data-aligment="top"]{top:0}
.admin-bar #mfn-gdpr[data-aligment="top"]{top:32px}
#mfn-gdpr[data-aligment="bottom"]{bottom:0}
#mfn-gdpr[data-aligment="left"]{left:40px}
#mfn-gdpr[data-aligment="right"]{right:40px}

#mfn-gdpr .mfn-gdpr-content ul,
#mfn-gdpr .mfn-gdpr-content ol{margin:0 0 15px 20px}
#mfn-gdpr[data-direction="vertical"] .mfn-gdpr-content ul,
#mfn-gdpr[data-direction="vertical"] .mfn-gdpr-content ol{margin-left:0}

#mfn-gdpr .mfn-gdpr-content ul{list-style:disc}
#mfn-gdpr .mfn-gdpr-content ol{list-style:decimal}

#mfn-gdpr{background-color:#eef2f5}
#mfn-gdpr .mfn-gdpr-content,#mfn-gdpr .mfn-gdpr-content h1,#mfn-gdpr .mfn-gdpr-content h2,#mfn-gdpr .mfn-gdpr-content h3,#mfn-gdpr .mfn-gdpr-content h4,#mfn-gdpr .mfn-gdpr-content h5,#mfn-gdpr .mfn-gdpr-content h6,#mfn-gdpr .mfn-gdpr-content ol,#mfn-gdpr .mfn-gdpr-content ul{color:#626262}
#mfn-gdpr .mfn-gdpr-content a,#mfn-gdpr a.mfn-gdpr-readmore{color:#161922}
#mfn-gdpr a.mfn-gdpr-readmore:hover{color:#0089f7}
#mfn-gdpr .mfn-gdpr-button{background-color:#006edf;color:#fff;border:solid 1px transparent}
#mfn-gdpr .mfn-gdpr-button:hover{background-color:#0089f7;color:#fff}

/* Comments -------------------------------------------------------------------------- */
.comments{margin-bottom:0}
#comments .nocomments{margin-bottom:40px}
#comments > :first-child{border-top-width:1px;border-style:solid;padding-top:20px}
.page #comments > :first-child{margin-top:20px}
#comments .commentlist{margin-left:0}
#comments .commentlist li{list-style:none!important;}
#comments .commentlist > li{margin-bottom:30px}
#comments .commentlist > li .comment-body{position:relative;background:#fff;padding:20px;margin-bottom:20px;margin-left:105px}
#comments .commentlist > li .comment-body:after{content:"";display:block;position:absolute;left:-6px;top:35px;width:0;height:0;border-style:solid;border-width:6px 6px 6px 0;border-color:transparent #fff transparent transparent}
#comments .commentlist > li .children{margin-bottom:15px;padding-left:40px;border-left:1px solid #ddd}
#comments .commentlist > li .photo{display:block;width:64px;height:64px;overflow:hidden;line-height:0;position:absolute;left:-105px;top:0;border-width:8px;border-style:solid;-webkit-border-radius:100%;border-radius:100%}
#comments .commentlist > li .comment-author{font-size:15px;color:#444}
#comments .commentlist > li .comment-author .fn{font-weight:700}
#comments .commentlist > li .comment-meta{font-size:12px;font-style:italic}
/* #comments .commentlist > li .comment-meta a.comment-edit-link{float:right;padding-right:52px;position:relative;top:-19px} */
#comments .commentlist > li p{margin:4px 0 10px}
#comments .commentlist > li .reply{position:absolute;right:20px;top:20px}
#comments .commentlist > li .reply a.comment-reply-link{font-size:11px;color:#fff;padding:3px 6px;-webkit-border-radius:1px;-moz-border-radius:1px;border-radius:3px;background-image:url(../images/stripes/stripes_10_w.png)}
#comments .commentlist > li .reply a:hover.comment-reply-link{text-decoration:none}
#comments .commentlist li .comment-body.lastBorder{border-bottom:0}
#comments .commentlist .children{margin-bottom:0;margin-left:30px;padding-left:50px}
#comments .commentlist .children li{margin-bottom:0}
#comments #comments-title{margin-bottom:20px}
#comments #comments-title span{font-style:italic}

/* Respond -------------------------------------------------------------------------- */
#respond{overflow:hidden;margin-bottom:40px}
#respond .comment-reply-title{font-size:21px;line-height:25px}
#respond .comment-reply-title > small{margin-left:10px;font-size:13px}
#respond p{margin-bottom:15px;padding-left:0!important}
#respond .comment-notes .required{position:static}
#respond input[type="text"]{margin-bottom:0}
#respond label{margin-bottom:3px}
#respond .comment-form-author{width:31.3%;margin-right:2%!important;float:left;position:relative}
#respond .comment-form-email{width:31.3%;margin-right:2%!important;float:left;position:relative}
#respond .comment-form-url{width:33.3%;float:left;position:relative}
#respond input[type="text"],#respond input[type="password"],#respond input[type="email"],#respond select{width:100%}
#respond .comment-form-comment{width:100%}
#respond .comment-form-comment textarea{width:100%;margin-bottom:10px}
#respond .form-submit{margin:0}
#respond .form-submit input[type="submit"]{font-size:14px;float:right!important;margin:0!important}
#respond .form-allowed-tags{display:none}
#respond .form-allowed-tags code{margin-top:10px}

/* Testimonials | Single | @you should not see this page ----------------------------- */
.single-testimonial .post-nav,.single-testimonial .post-meta .category,.single-testimonial .fixed-nav{display:none}

/* Error 404 | #Error_404 ------------------------------------------------------------ */
body.error404{height:100%}
body.error404.custom-404,body.error404.events-archive{height:auto}
body.error404:not(.events-archive) #Content{padding:0!important}

#Error_404{overflow:hidden;position:absolute;top:50%;margin-top:-150px;left:30px}
#Error_404 .error_pic{width:30%;float:left;text-align:center}
#Error_404 .error_pic i{font-size:250px;line-height:250px}
#Error_404 .error_desk{width:70%;float:left;padding-top:40px}
#Error_404 .error_desk h2{font-size:45px;line-height:45px}
#Error_404 .error_desk h4{font-size:26px;line-height:30px}
#Error_404 .error_desk p .check{line-height:45px;font-size:16px}
#Error_404 .error_desk p .button{margin:0;display:inline;margin-left:20px}

/* Search Results | .search-not-found ------------------------------------------------ */
.search-not-found { margin: 40px 1% 70px; }
.search-not-found .snf-pic{text-align:center;margin-bottom:20px;}
.search-not-found .snf-pic i{font-size:130px;line-height:130px;}
.search-not-found .snf-desc{text-align:center;}

/* Video Section --------------------------------------------------------------------- */
.section.has-video{position:relative;overflow:hidden}
.section.has-video .section_video{position:absolute;top:0;left:0;min-height:100%;min-width:100%;overflow:hidden}
.section.has-video .section_video .mask{position:absolute;top:0;left:0;height:100%;width:100%;background:url(../images/videomask.png) repeat center}
.section.has-video .section_video video{position:absolute;top:0;left:0;min-height:100%;min-width:100%}
.section.has-video .section_wrapper{position:relative}

@media only screen and (max-device-width:959px) {
	.no-section-video-desktop .section.has-video .section_video{display:none!important}
}

/* Section Navigation ---------------------------------------------------------------- */
.section.has-navi .section-nav{position:absolute;left:50%;z-index:201;cursor:pointer;font-size:38px;width:50px;height:50px;line-height:50px;margin:0 0 0 -25px;text-align:center;color:rgba(0,0,0,.2)}
.section.has-navi .section-nav:hover{color:rgba(0,0,0,.8)}
.section.has-navi .section-nav.prev{top:25px}
.section.has-navi .section-nav.next{bottom:25px}
.section.has-navi:first-of-type .section-nav.prev,
.section.has-navi:last-of-type .section-nav.next{display:none}
.section.has-navi .section-nav{color:rgba(0,0,0,.2)}
.section.has-navi .section-nav:hover{color:rgba(0,0,0,.8)}
.section.has-navi.dark .section-nav{color:rgba(255,255,255,.2)}
.section.has-navi.dark .section-nav:hover{color:rgba(255,255,255,.8)}
.section.has-navi .section-nav{-webkit-transition:all .5s ease-in-out;-moz-transition:all .5s ease-in-out;-o-transition:all .5s ease-in-out;transition:all .5s ease-in-out}

/* Dark ------------------------------------------------------------------------------ */
.dark,.dark ul.timeline_items,.dark .icon_box a .desc,.dark .icon_box a:hover .desc,.dark .feature_list ul li a,.dark .list_item a:not(.icon_bar),.dark .list_item a:not(.icon_bar):hover,.dark .widget_recent_entries ul li a{color:#fff!important}
.dark .ui-tabs .ui-tabs-panel,.dark .accordion .question .answer{color:#626262}
.dark .column a{color:rgba(255,255,255,.66)}

/* Blockquote */
.dark blockquote{background-image:url(../images/stripes/textline_dark.png)}
.dark blockquote:after{color:rgba(255,255,255,.1)}

/* Headings font */
.dark h1,.dark h1 a,.dark h1 a:hover{color:#fff}
.dark h2,.dark h2 a,.dark h2 a:hover{color:#fff}
.dark h3,.dark h3 a,.dark h3 a:hover{color:#fff}
.dark h4,.dark h4 a,.dark h4 a:hover{color:#fff}
.dark h5,.dark h5 a,.dark h5 a:hover{color:#fff}
.dark h6,.dark h6 a,.dark h6 a:hover,.dark a.content_link .title{color:#fff}

/* Borders */
.dark .idea_box,.dark table th,.dark table td,.dark .list_item .circle,
.dark input[type="text"],.dark input[type="tel"],.dark input[type="password"],.dark input[type="email"],.dark textarea,.dark select,.dark .promo_box.has_border:after,
.dark dl > dt,.dark dl > dd,.dark .article_box .desc_wrapper p,.dark a.icon_bar,.dark a.content_link,.dark .how_it_works .image,.dark .opening_hours,.dark .opening_hours .opening_hours_wrapper li,
.dark .icon_box.has_border:after,.dark .chart_box:before,.dark .pricing-box,.dark .team_list .bq_wrapper,.dark .post-footer .post-links,.dark .format-link .post-title .icon-link,
.dark .share_wrapper,.dark .post-header .title_wrapper,.dark .section-post-related .section-related-adjustment,.dark .comments,.dark .mcb-sidebar,.dark .widget:after,
.dark .fixed-nav .desc h6,.dark .portfolio_group.list .portfolio-item,.dark .portfolio_group .portfolio-item .desc .details-wrapper,.dark .Recent_posts ul li .desc h6,
.dark .widget_recent_entries ul li a,.dark .woocommerce .widget_best_sellers li,.dark .woocommerce .widget_featured_products li,.dark .woocommerce .widget_recent_reviews li,
.dark .woocommerce .widget_recent_products li,.dark .woocommerce .widget_recently_viewed_products li,.dark .woocommerce .widget_random_products li,.dark .woocommerce .widget_top_rated_products li,
.dark .woocommerce .widget_onsale li,.dark .woocommerce .widget_layered_nav li,.dark .woocommerce .widget_shopping_cart ul.product_list_widget li,
.dark .woocommerce .widget_products li,.dark .woocommerce .product .related.products,.dark .woocommerce .product div.entry-summary h1.product_title:after,
.dark .woocommerce .quantity input.qty{border-color:rgba(255,255,255,.08)}

/* Grey */
.dark .blockquote p.author span,.dark .counter .desc_wrapper .title,.dark .article_box .desc_wrapper p,.dark .team .desc_wrapper p.subtitle,.dark .pricing-box .plan-header p.subtitle,.dark .pricing-box .plan-header .price sup.period,.dark .chart_box p,.dark .fancy_heading .inside,
.dark .fancy_heading_line .slogan,.dark .post-meta,.dark .post-meta a,.dark .post-footer,.dark .post-footer a span.label,.dark .pager .pages a,.dark .button-love a .label,
.dark .pager-single a,.dark #comments .commentlist > li .comment-author .says,.dark .fixed-nav .desc .date,.dark .filters_buttons li.label,.dark .Recent_posts ul li a .desc .date,
.dark .widget_recent_entries ul li .post-date,.dark .tp_recent_tweets .twitter_time,.dark .widget_price_filter .price_label,.dark .shop-filters .woocommerce-result-count,
.dark .woocommerce ul.product_list_widget li .quantity,.dark .widget_shopping_cart ul.product_list_widget li dl,.dark .product_meta .posted_in,
.dark .woocommerce .shop_table .product-name .variation > dd,.dark .shipping-calculator-button:after,.dark .shop_slider .shop_slider_ul li .item_wrapper .price del,
.dark .testimonials_slider .testimonials_slider_ul li .author span,.dark .testimonials_slider .testimonials_slider_ul li .author span a{color:#DEDEDE}

/* Dividers */
.dark hr{background-color:rgba(255,255,255,.08);color:rgba(255,255,255,.08)}

/* FAQ */
.dark .faq .question .title{color:#fff}
.dark .faq .question,
.dark .faq .question::after{background-color:rgba(255,255,255,.02)}
.dark .faq .question::before{border-color:rgba(255,255,255,.1)}
.dark .faq .question .title > .acc-icon-plus,
.dark .faq .question .title > .acc-icon-minus{color:rgba(255,255,255,.25)}

.style-simple .dark .faq .question{border-color:rgba(255,255,255,.1)}

/* Others */
.dark blockquote{color:#fff}
.dark .article_box .desc_wrapper h4{color:#444}
.dark .progress_bars .bars_list li h6 .label{color:rgba(255,255,255,0.35);background:rgba(255,255,255,0.05)}
.dark .counter .desc_wrapper .number{color:#fff}
.dark a.content_link .title{color:#444}
.dark .opening_hours,.dark .opening_hours h3{color:#444}
.dark .Recent_posts ul li .desc{background:rgba(0,0,0,.1)}

/* Pricing box */
.dark .pricing-box-box{background:rgba(0,0,0,0.1)}
.dark .pricing-box .plan-inside ul li{border-bottom:1px solid rgba(255,255,255,0.1)}

/* Lists */
.dark .column_column ul,.dark .column_column ol,.dark .the_content_wrapper ul,.dark .the_content_wrapper ol{color:#fff}
.dark .list_item.lists_2 .list_icon i{color:#fff}

/* Pricing table */
.dark .pricing-box-table.pricing-box-featured{background:rgba(0,0,0,.1)}
.dark .pricing-box .plan-header .price sup.period{color:rgba(255,255,255,0.5)!important}

/* Call to action */
.dark .call_to_action .call_left h3,
.dark .call_to_action .call_center a{color:#fff}

/* Fancy heading */
.dark .fancy_heading_line{background-image:url(../images/fancy_heading_hr_dark.png)}

/* Content slider */
.dark .content_slider.flat a.button .button_icon i{color:#fff}

/* Countdown */
.style-simple .dark .quick_fact .number-wrapper{color:#fff}

/* Fancy links */
.dark a.mfn-link,.dark a:hover.mfn-link{color:#fff}
.dark a.hover.mfn-link-2 span:before,.dark a.mfn-link-8:after,.dark a.mfn-link-8:before{background:#fff}
.dark a.mfn-link-4:hover:before,.dark a.mfn-link-4:hover:after,.dark a.hover.mfn-link-4:before,.dark a.hover.mfn-link-4:after,.dark a.mfn-link-7:after,.dark a.mfn-link-7:before{background:#fff}
.dark a.mfn-link-6:before{border-bottom-color:#fff}

/* Pagination */
.dark .slider_pagination a{background:rgba(255,255,255,0.3)}

/* dropcap */

.dropcap{display:inline-block;float:left;width:35px;height:35px;line-height:35px;font-size:20px;margin:0 10px 5px 0;text-align:center;-webkit-border-radius:5px;border-radius:5px;background-image:url(../images/stripes/stripes_10_w.png)}
.dropcap_circle{-webkit-border-radius:100%;border-radius:100%}
.dropcap.transparent{background:none!important}
.dropcap.size-2{width:50px;height:50px;line-height:50px;font-size:35px}
.dropcap.size-3{width:60px;height:60px;line-height:60px;font-size:40px}

/* highlight */

.highlight{padding:1px 7px;-webkit-border-radius:3px;border-radius:3px;background-image:url(../images/stripes/stripes_10_w.png)}
.highlight.highlight_image{padding:0 2px 7px}

  /* Highlight underline */
  .highlight.highlight-underline {position: relative; padding: 0; border-radius: unset; background-color: unset !important; background-image: unset; }
  .highlight.highlight-underline .highlight-word { position: relative; z-index: 1; }
  .highlight.highlight-underline .highlight-word .highlight-border { content: ""; position: absolute; left: 0; bottom: 20%; width: calc(100% + 0.3em); height: 15%; margin-left: -0.15em; transform: skew(-12deg) translateX(0); background:#0089F7;  z-index: -1; }


/* tooltip*/

.tooltip{display:inline;position:relative;cursor:help;border-bottom-width:1px;border-style:dotted}
.tooltip:hover:before,
.tooltip:focus:before,
.tooltip.tooltip-img:hover .tooltip-content,
.tooltip.tooltip-img.hover .tooltip-content { opacity: 1; transform: translate(-50%, -10px); }
.tooltip:hover:after,
.tooltip:focus:after { opacity: 1; transform: translate(-50%, -10px) rotate(45deg); }
.tooltip:before,
.tooltip.tooltip-img .tooltip-content { content: attr(data-tooltip); position: absolute; z-index: 98; text-align: center; text-indent: 0; bottom: 100%; left: 50%; padding: 6px 12px; font-size: 12px; line-height: 20px; opacity: 0; transform: translate(-50%, 0); pointer-events: none; border-radius: 3px; }
.tooltip:after { position: absolute; z-index: 98; display: block; bottom: 100%; left: 50%; content: ''; width: 10px; height: 10px; border-radius: 2px; margin-bottom: -4px; transform: translate(-50%, 0) rotate(45deg); opacity: 0; pointer-events: none; }
.tooltip:before { max-width: 250px; width: 100%; width: max-content; }
.tooltip.tooltip-img .tooltip-content{width:300px;font-size:13px;line-height:18px;text-align:center;padding: 12px 12px 10px;white-space:normal;}

.tooltip[data-position="bottom"]:before,
.tooltip[data-position="bottom"].tooltip-img .tooltip-content { transform: translate(-50%, 0); top: 100%; bottom: auto; }
.tooltip[data-position="bottom"]:hover:before,
.tooltip[data-position="bottom"]:focus:before,
.tooltip[data-position="bottom"].tooltip.tooltip-img:hover .tooltip-content,
.tooltip[data-position="bottom"].tooltip.tooltip-img.hover .tooltip-content { opacity: 1; transform: translate(-50%, 10px); }
.tooltip[data-position="bottom"]:after { transform: translate(-50%, 0) rotate(45deg); top: 100%; bottom: auto; }
.tooltip[data-position="bottom"]:hover:after,
.tooltip[data-position="bottom"]:focus:after { transform: translate(-50%, 10px) rotate(45deg); margin-top: -4px; }

.tooltip[data-position="left"]:before,
.tooltip[data-position="left"].tooltip-img .tooltip-content { transform: translate(0, -50%); left: auto; bottom: auto; right: 100%; top: 50%; }
.tooltip[data-position="left"]:hover:before,
.tooltip[data-position="left"]:focus:before,
.tooltip[data-position="left"].tooltip.tooltip-img:hover .tooltip-content,
.tooltip[data-position="left"].tooltip.tooltip-img.hover .tooltip-content { transform: translate(-10px, -50%); }
.tooltip[data-position="left"]:after { transform: translate(5px, -5px) rotate(45deg); left: auto; bottom: auto; right: 100%; top: 50%; }
.tooltip[data-position="left"]:hover:after,
.tooltip[data-position="left"]:focus:after { transform: translate(-60%, -5px) rotate(45deg); }

.tooltip[data-position="right"]:before,
.tooltip[data-position="right"].tooltip-img .tooltip-content { transform: translate(0, -50%); left: 100%; bottom: auto; right: auto; top: 50%; }
.tooltip[data-position="right"]:hover:before,
.tooltip[data-position="right"]:focus:before,
.tooltip[data-position="right"].tooltip.tooltip-img:hover .tooltip-content,
.tooltip[data-position="right"].tooltip.tooltip-img.hover .tooltip-content  { transform: translate(10px, -50%); }
.tooltip[data-position="right"]:after { transform: translate(-5px, -5px) rotate(45deg); left: 100%; bottom: auto; right: auto; top: 50%; }
.tooltip[data-position="right"]:hover:after,
.tooltip[data-position="right"]:focus:after { transform: translate(60%, -5px) rotate(45deg); }

.tooltip:before,
.tooltip.tooltip-img .tooltip-content {
    transition: transform 0.2s ease-out;
}
.tooltip:after {
    transition: transform 0.2s ease-out;
}

/* blockquote */

blockquote{font-size:17px;line-height:31px;display:block;height:auto;margin:0 0 35px 25px;top:15px;position:relative;/*background:url(../images/stripes/textline.png) repeat-y;*/}
blockquote:before{content:""}
.blockquote { position: relative; }
.blockquote .mfn-blockquote-icon{ display:block;font-size:65px;position:absolute;left: 25px; top: 25px; transform: translate(-50%,-50%); color:rgba(0,0,0,.1)}
.blockquote{margin-bottom:20px}
.blockquote blockquote { margin-bottom:25px; }
body:not(.style-simple) .blockquote blockquote { text-decoration: underline; text-decoration-color: rgba(0,0,0,.1); text-underline-offset: 8px; text-align: left; text-decoration-thickness: 1px; text-underline-position: from-font; }
.blockquote p.author{margin-left:25px}
.blockquote p.author i{margin-right:5px}

/* button */

.column_button .button{margin:0!important;vertical-align:middle}
.button_align.align_center{text-align:center}
.button_align.align_right{text-align:right}

/* images */

.column_image{line-height:0}

.image_frame,
.wp-caption{display:block;overflow:hidden;margin:0;border-style:solid;border-width:0;max-width:100%;line-height:0;box-sizing:border-box}
.image_item.image_frame:not(.svg) { display: inline-block; }
.column_column .image_item.image_frame:not(.svg) { vertical-align: top; } /* Fix for line-height problem in column */
.image_frame .image_wrapper,
.wp-caption img{box-sizing:border-box}
.image_frame a,
.wp-caption a{display:block}

.wp-caption-text{display:block;text-align:center;margin:0;padding:10px 0 5px;line-height:normal}
.wp-caption-text.hide{display:none}

.if-caption-on .gallery-item{position:relative}
.if-caption-on .image_frame{position:relative}
.if-caption-on .wp-caption-text{position:absolute;bottom:15px;left:15px;padding:5px 10px!important;width:calc(100% - 30px);box-sizing:border-box;background:rgba(0,0,0,.5);color:rgba(255,255,255,.7);border-radius:3px;}

.image_frame.no_border{border-width:0}
.image_frame.no_border .wp-caption-text,
.if-zoom .image_frame:not(.has_border) .wp-caption-text{padding:10px 0;}
.the_content_wrapper .image_frame,
.the_content_wrapper .wp-caption{margin-bottom:20px}
.the_content_wrapper .wp-caption.alignnone,
.the_content_wrapper .wp-caption.aligncenter{clear:both}

	/* image_frame */

	.image_frame .image_wrapper{position:relative;overflow:hidden}
	.image_frame .image_wrapper .mask{position:absolute;left:0;width:100%;height:100%;z-index:2}
	.image_frame .image_wrapper img:not(.ls-l){position:relative;top:0;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}
	/* .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:-15px!important} */ /* this brokes images after import until regenerate thumbnails */
	.masonry-flat .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:0!important}
	.image_frame:hover .image_wrapper img,
	.image_frame:focus .image_wrapper img,
  .image_frame.hover .image_wrapper img{top:-15px}
	.image_frame:hover .image_wrapper .ls-container img,
	.image_frame:focus .image_wrapper .ls-container img,
  .image_frame.no_link:hover .image_wrapper img,
  .image_frame.no_link:focus .image_wrapper img{top:0}
	.image_frame .image_wrapper .mask:after{content:"";display:block;position:absolute;left:0;width:100%;height:100%;z-index:3;opacity:0}
	.image_frame:hover .image_wrapper .mask:after,
	.image_frame:focus .image_wrapper .mask:after,
  .image_frame.hover .image_wrapper .mask:after{opacity:1}
	.image_frame.no_link:hover .image_wrapper .mask:after,
	.image_frame.no_link:focus .image_wrapper .mask:after{opacity:0}
	.image_frame .image_wrapper .image_links{ display: flex; width:100%;height:60px;position:absolute;left:0;bottom:-60px;z-index:4;overflow:hidden}
	.image_frame:hover .image_wrapper .image_links,
	.image_frame:focus .image_wrapper .image_links,
  .image_frame.hover .image_wrapper .image_links{bottom:0}
	.image_frame .image_wrapper .image_links a{display:flex;justify-content:center;align-items:center; flex: 1;position: relative;width:100%; border-style: solid; border-color: transparent; }
    .image_frame .image_wrapper .image_links a:not(:last-child){border-right-width: 1px;}
    .image_frame .image_wrapper .image_links a svg { width: 32px; }
    .image_frame .image_wrapper .image_links a.loading svg { visibility: hidden; }
    .image_frame .image_wrapper .image_links a.loading:before { display: none; }
    .image_frame .image_wrapper .image_links a.loading:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; right: auto; bottom: auto; margin: -10px 0 0 -10px; width: 20px; height: 20px; border-radius: 100%; border-width: 2px; border-style: solid; border-bottom-color: transparent !important; background: none; transform: none; transition: none !important; animation: spin 1.5s linear infinite;}
	.image_frame .image_wrapper .image_links.hover-title a{font-size:15px;text-decoration:none}

	/* hover-secondary-image */

	.image_frame .image_wrapper.hover-secondary-image .image-primary{display:inline-block;opacity:1;transform: unset !important;}
	.image_frame .image_wrapper.hover-secondary-image .image-secondary{display:block;position:absolute;left:50%;top:50%!important;opacity:0;transform:translate(-50%,-50%)!important;}

	.image_frame:hover .image_wrapper.hover-secondary-image .image-primary,
	.image_frame:focus .image_wrapper.hover-secondary-image .image-primary,
	.image_frame.hover .image_wrapper.hover-secondary-image .image-primary{opacity:0}
	.image_frame:hover .image_wrapper.hover-secondary-image .image-secondary,
	.image_frame:focus .image_wrapper.hover-secondary-image .image-secondary,
	.image_frame.hover .image_wrapper.hover-secondary-image .image-secondary{opacity:1}
	.image_frame:hover .image_wrapper.hover-secondary-image .image-primary,
	.image_frame:focus .image_wrapper.hover-secondary-image .image-primary,
	.image_frame.hover .image_wrapper.hover-secondary-image .image-primary{opacity:0}
	.image_frame:hover .image_wrapper.hover-secondary-image .image-primary,
	.image_frame:focus .image_wrapper.hover-secondary-image .image-primary,
	.image_frame.hover .image_wrapper.hover-secondary-image .image-primary{top: 0!important;}

	.image_frame .image_wrapper.hover-secondary-image .wp-post-image{transition:opacity .4s ease-in-out}

  @keyframes spin {
      100% {
          -webkit-transform: rotate(360deg);
          transform:rotate(360deg);
      }
  }

	/* alignment */

	.alignleft{float:left;margin:15px 15px 15px 0}
	.alignright{float:right;margin:15px 0 15px 15px}
	.aligncenter{text-align:center;margin:0 auto;display:block}
    .aligncenter.image_frame {width:100%;}
	.aligncenter img{display:inline}

	/* animations */

	.image_frame .image_wrapper img,
	.image_frame .image_wrapper .mask:after,
	.image_frame .image_wrapper .image_links{-webkit-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
	.image_frame .image_wrapper .image_links a{-webkit-transition:all .1s ease-in-out;transition:all .1s ease-in-out}

	/* greyscale */

	.greyscale .image_wrapper .mask{display:none}
	.greyscale img{filter:grayscale(100%)}
	.greyscale img:hover, .greyscale a:hover img, .greyscale .client_wrapper:hover img{filter:none}

    /* invert */
    .invert img { filter: invert(1) hue-rotate(180deg); }

    /* greyscale + invert */
    .greyscale.invert img { filter: grayscale(100%) invert(1) hue-rotate(180deg); }

	/* if-overlay */

	.if-overlay .image_frame .image_wrapper .image_links{opacity:0;width:auto;height:auto;bottom:auto;top:50%;left:50%;z-index:4;transform:translate(-50%,-50%);}
    .if-overlay .image_frame .image_wrapper .image_links a{height:60px;width:60px;}
	.if-overlay .image_frame .image_wrapper .image_links.hover-title{width:100%;left:0;margin-left:0;transform:translateY(-50%)}
	.if-overlay li.product .product-loading-icon,.if-overlay li.product .added-cart{border-radius:0!important;width:60px!important;height:60px!important;margin:-30px 0 0 -30px !important}
	.if-overlay li.product.adding-to-cart .added-cart,.if-overlay li.product.added-to-cart .added-cart{z-index:5!important}
	.if-overlay .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:0!important}
	.if-overlay .image_frame:hover .image_wrapper .image_links,
	.if-overlay .image_frame:focus .image_wrapper .image_links,
  .if-overlay .image_frame.hover .image_wrapper .image_links{animation: if-overlay-animation 0.3s normal forwards ease-in-out;}
	.if-overlay .image_frame:hover .image_wrapper img,
	.if-overlay .image_frame:focus .image_wrapper img,
  .if-overlay .image_frame.hover .image_wrapper img{top:0;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}
	.if-overlay .image_item:hover .image_wrapper img,
	.if-overlay .image_item:focus .image_wrapper img,
  .if-overlay .image_item.hover .image_wrapper img{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}

    @keyframes if-overlay-animation {
        0%   {
            opacity: 0;
        }
        100%  {
            opacity: 1;
        }
    }

	/* if-overlay modern */

	.if-modern-overlay .image_frame .image_wrapper .image_links{ flex-direction: column;width:auto;height:auto;bottom:auto;top:10px;left: auto;right: 10px;z-index:4;background-color: transparent;overflow:visible;}
    .if-modern-overlay .image_frame .image_wrapper .image_links a{opacity: 0;flex: auto;width:36px;height: 36px; border-radius: 100%; margin-bottom: 7px;cursor: pointer; border-width: 1px;}
    .if-modern-overlay .image_frame .image_wrapper .image_links a:last-child { margin-bottom: 0; }
    .if-modern-overlay .image_frame .image_wrapper .image_links a svg { width: 24px; }
    .if-modern-overlay .image_frame .image_wrapper .image_links a.loved:before,
    .if-modern-overlay .image_frame .image_wrapper .image_links a.loved:after,
    .if-modern-overlay .image_frame .image_wrapper .image_links a:focus:before,
    .if-modern-overlay .image_frame .image_wrapper .image_links a:focus:after { opacity: 0; }
	.if-modern-overlay .image_frame .image_wrapper .image_links.hover-title{width:80%;left:10%; top: auto; right: auto; bottom: 10%; }
    .if-modern-overlay .image_frame .image_wrapper .image_links.hover-title a { border-radius: 3px; width: auto; height: auto; padding: 10px; line-height: initial; }
	.if-modern-overlay .image_frame:hover .image_wrapper img,
	.if-modern-overlay .image_frame:focus .image_wrapper img,
  .if-modern-overlay .image_frame.hover .image_wrapper img{top:0}
	.if-modern-overlay .image_frame .image_wrapper .mask{display:none}
	.if-modern-overlay .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:0!important}
	.if-modern-overlay .image_frame:hover .image_wrapper .image_links a,
	.if-modern-overlay .image_frame:focus .image_wrapper .image_links a,
  .if-modern-overlay .image_frame.hover .image_wrapper .image_links a{animation: if-modern-overlay-animation 0.2s normal forwards ease-in-out; }
    .if-modern-overlay .image_frame .image_wrapper .image_links a.loading:after { margin: -8px 0 0 -8px; width: 16px; height: 16px; border-width: 1.5px; }

    .image_frame .image_wrapper .image_links a { background-color: #fff; }
    .image_frame .image_wrapper .image_links a .path { stroke: #161922; }
    .image_frame .image_wrapper .image_links a.loading:after { border-color: #161922; }
    .image_frame .image_wrapper .image_links a.mfn-wish-button.loved .path { fill: #161922; stroke: #161922; }
    .image_frame .image_wrapper .image_links a:hover,
    .image_frame .image_wrapper .image_links a:focus { background-color: #fff; }
    .image_frame .image_wrapper .image_links a:hover .path{ stroke: #0089f7; }
    .image_frame .image_wrapper .image_links a { box-shadow: inset -1px 0 0 0 rgba(0,0,0,0) }


    @keyframes if-modern-overlay-animation {
        0%   {
            opacity: 0;
            transform: translateY(10px);
        }
        100%  {
            opacity: 1;
            transform: translateY(0);
        }
    }
    .if-modern-overlay .image_frame .image_wrapper .image_links a:nth-child(1) { animation-delay: 0s }
    .if-modern-overlay .image_frame .image_wrapper .image_links a:nth-child(2) { animation-delay: .03s }
    .if-modern-overlay .image_frame .image_wrapper .image_links a:nth-child(3) { animation-delay: .06s }
    .if-modern-overlay .image_frame .image_wrapper .image_links a:nth-child(4) { animation-delay: .09s }
    .if-modern-overlay .image_frame .image_wrapper .image_links a:nth-child(5) { animation-delay: .12s }

	/* if-zoom */

	.if-zoom .image_frame .image_wrapper .image_links{display:none}
	.if-zoom .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:0!important}
	.if-zoom .image_frame:hover .image_wrapper img,
	.if-zoom .image_frame:focus .image_wrapper img,
  .if-zoom .image_frame.hover .image_wrapper img{top:0}
	.if-zoom .image_frame .image_wrapper .mask{display:none}
	.if-zoom .image_frame:hover .image_wrapper img,
	.if-zoom .image_frame:focus .image_wrapper img,
  .if-zoom .image_frame.hover .image_wrapper img{-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-o-transform:scale(1.1);transform:scale(1.1)}
	.if-zoom .image_frame.no_link:hover .image_wrapper img,
	.if-zoom .image_frame.no_link:focus .image_wrapper img,
  .if-zoom image_frame.no_link.hover .image_wrapper img{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}

	.if-zoom #Content .image_frame .image_wrapper img{max-width:100.1%}

	/* if-disable  */

	.if-disable .image_frame .image_wrapper .image_links{display:none}
	.if-disable .image_frame:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:0!important}
	.if-disable .image_frame:hover .image_wrapper img,
	.if-disable .image_frame:focus .image_wrapper img,
  .if-zoom .image_frame.hover .image_wrapper img{top:0}
	.if-disable .image_frame .image_wrapper .mask{display:none}

	/* hover-disable */

	.image_frame.hover-disable .image_wrapper .image_links{display:none}
	.image_frame.hover-disable .image_wrapper .mask{display:none!important}
	.image_frame.hover-disable .image_wrapper img{margin-bottom:0!important;top:0!important;-moz-transform:scale(1)!important;-webkit-transform:scale(1)!important;-o-transform:scale(1)!important;transform:scale(1)!important}
	.image_frame.hover-disable:not(.no_link) .image_wrapper img:not(.ls-l){margin-bottom:0!important}

/* video */
/* in this case, the aspect ratio is 16:9, which means that the height will be 56.25% of the width; for a video with a 4:3 aspect ratio, we set padding-bottom to 75%. */

.content_video.iframe.auto-wh{position:relative;padding-bottom:56.25%;height:0;overflow:hidden}
.content_video.iframe.auto-wh iframe{position:absolute;top:0;left:0;width:100%;height:100%}

.post-photo-wrapper.embed .image_wrapper,
.single-photo-wrapper.embed .image_wrapper{position:relative;padding-bottom:56.25%;height:0;overflow:hidden}
.post-photo-wrapper.embed .image_wrapper iframe,
.single-photo-wrapper.embed .image_wrapper iframe{position:absolute;top:0;left:0;width:100%;height:100%}

.post-photo-wrapper.html5 .jp-video:not(.jp-video-full) .jp-jplayer,
.single-photo-wrapper.html5 .jp-video:not(.jp-video-full) .jp-jplayer{position:relative;padding-bottom:56.25%;height:0!important;overflow:hidden}
.post-photo-wrapper.html5 .jp-video:not(.jp-video-full) img,
.single-photo-wrapper.html5 .jp-video:not(.jp-video-full) img{position:absolute}
.post-photo-wrapper.html5 .jp-video:not(.jp-video-full) video,
.single-photo-wrapper.html5 .jp-video:not(.jp-video-full) video{position:absolute;top:0;left:0;width:100%;height:100%}

.column_video .mcb-item-video-inner { overflow: hidden; line-height: 0; }

.mfn-lottie-wrapper{ display: flex; position: relative; }
.mfn-lottie-wrapper .lottie{ position: relative;  }

/* PBL Mask Shapes */

.image_frame.mfn-mask-shape img{-webkit-mask-size:contain;-webkit-mask-position:center center;-webkit-mask-repeat:no-repeat;-webkit-transition:unset;transition:unset}

.content_video.mfn-mask-shape iframe,
.content_video.mfn-mask-shape video{-webkit-mask-size:contain;-webkit-mask-position:center center;-webkit-mask-repeat:no-repeat;cursor:unset;pointer-events:none}

.image_frame.mfn-mask-shape.circle img,
.content_video.mfn-mask-shape.circle iframe,
.content_video.mfn-mask-shape.circle video{-webkit-mask-image:url(../images/mask_shapes/circle.svg)!important}

.image_frame.mfn-mask-shape.blob img,
.content_video.mfn-mask-shape.blob iframe,
.content_video.mfn-mask-shape.blob video{-webkit-mask-image:url(../images/mask_shapes/blob.svg)!important}

.image_frame.mfn-mask-shape.blob-2 img,
.content_video.mfn-mask-shape.blob-2 iframe,
.content_video.mfn-mask-shape.blob-2 video{-webkit-mask-image:url(../images/mask_shapes/blob-2.svg)!important}

.image_frame.mfn-mask-shape.brush img,
.content_video.mfn-mask-shape.brush iframe,
.content_video.mfn-mask-shape.brush video{-webkit-mask-image:url(../images/mask_shapes/brush.svg)!important}

.image_frame.mfn-mask-shape.brush-2 img,
.content_video.mfn-mask-shape.brush-2 iframe,
.content_video.mfn-mask-shape.brush-2 video{-webkit-mask-image:url(../images/mask_shapes/brush-2.svg)!important}

.image_frame.mfn-mask-shape.cross img,
.content_video.mfn-mask-shape.cross iframe,
.content_video.mfn-mask-shape.cross video{-webkit-mask-image:url(../images/mask_shapes/cross.svg)!important}

.image_frame.mfn-mask-shape.stain img,
.content_video.mfn-mask-shape.stain iframe,
.content_video.mfn-mask-shape.stain video{-webkit-mask-image:url(../images/mask_shapes/stain.svg)!important}

.image_frame.mfn-mask-shape.triangle img,
.content_video.mfn-mask-shape.triangle iframe,
.content_video.mfn-mask-shape.triangle video{-webkit-mask-image:url(../images/mask_shapes/triangle.svg)!important}

.image_frame.mfn-mask-shape.irregular-circle img,
.content_video.mfn-mask-shape.irregular-circle iframe,
.content_video.mfn-mask-shape.irregular-circle video{-webkit-mask-image:url(../images/mask_shapes/irregular-circle.svg)!important}

/* gallery */

.gallery .gallery-item{overflow:hidden;margin:0 0 2.5%!important}
.gallery .gallery-item .gallery-icon{border:0;width:95%;overflow:hidden;line-height:0;margin-right:2.5%!important;margin-left:2.5%!important;padding:0!important;position:relative}
.gallery .gallery-item img{display:block;line-height:0;max-width:100%;height:auto;border:0!important;}
.gallery .image_frame{margin-bottom:0}

.column_image_gallery .mcb-item-image_gallery-inner { overflow: hidden; } /* Overflow fix */

.gallery-columns-2 .gallery-item:nth-of-type(2n+1){clear:both}
.gallery-columns-3 .gallery-item:nth-of-type(3n+1){clear:both}
.gallery-columns-4 .gallery-item:nth-of-type(4n+1){clear:both}
.gallery-columns-5 .gallery-item:nth-of-type(5n+1){clear:both}
.gallery-columns-6 .gallery-item:nth-of-type(6n+1){clear:both}
.gallery-columns-7 .gallery-item:nth-of-type(7n+1){clear:both}
.gallery-columns-8 .gallery-item:nth-of-type(8n+1){clear:both}
.gallery-columns-9 .gallery-item:nth-of-type(9n+1){clear:both}

	/* gallery flat */

	.gallery.flat .gallery-item{margin:0!important}
	.gallery.flat .gallery-item .gallery-icon{margin:0!important;width:100%}
	.gallery.flat .gallery-item .gallery-icon .image_frame{border-width:0;margin-bottom:0}

	/* gallery fancy */

	.gallery.fancy .gallery-item:nth-child(2n+1){transform:rotate(-2deg)}
	.gallery.fancy .gallery-item:nth-child(2n){transform:rotate(2deg)}
	.gallery.fancy .gallery-item .image_frame{margin:7%}

	/* gallery masonry */

	.gallery.masonry .gallery-item{margin:0!important}
	.gallery.masonry .gallery-item .gallery-icon{margin:0!important;width:100%}
	.gallery.masonry .gallery-item .image_frame{margin-bottom:0}

/* divider */

hr{display:block;border:none;outline:none;height:1px;width:100%;margin:0 auto 15px;clear:both}
hr,.hr_wide,.hr_zigzag,.hr_dots{margin:0 auto 15px;clear:both}
.elementor-element[data-widget_type*=mfn] hr{margin:0 auto 15px;clear:both}
hr.hr_narrow{width:7%}
hr.no_line{background:none;color:transparent;}
.hr_wide{position:relative;height:1px}
.hr_wide hr{position:absolute;left:-1000px;top:0;width:3000px}
.aside_left .hr_wide hr{left:0}
.aside_right .hr_wide hr{right:0;left:auto}
.hr_zigzag{text-align:center;margin-bottom:10px}
.hr_zigzag i{font-size:25px;line-height:25px;margin:0 -11px;display:inline-block}
.hr_dots{text-align:center;line-height:0}
.hr_dots span{display:inline-block;width:5px;height:5px;overflow:hidden;margin:0 5px;-webkit-border-radius:5px;border-radius:5px}
.column_divider hr,.column_divider .hr_wide,.column_divider .hr_zigzag,.column_divider .hr_dots{margin-bottom:0}

/* fancy-divider */

.fancy-divider svg{display:block}
.fancy-divider svg:not(:root){overflow:hidden}

/* section-divider */

.section .section-divider{display:block;width:50px;height:50px;position:absolute;left:50%;margin-left:-25px;background-color:inherit;z-index:1}
.section-divider.triangle.up,.section-divider.triangle.down{-moz-transform:rotate(45deg);-webkit-transform:rotate(45deg);-o-transform:rotate(45deg);-ms-transform:rotate(45deg);transform:rotate(45deg)}
.section-divider.triangle.up{top:-25px}
.section-divider.triangle.down{bottom:-25px}
.section-divider.triple-triangle:after,.section-divider.triple-triangle:before{content:"";display:block;width:50px;height:50px;position:absolute;background-color:inherit;z-index:1}
.section-divider.triple-triangle.up:after{left:-36px;top:50px}
.section-divider.triple-triangle.up:before{right:-50px;top:-36px}
.section-divider.triple-triangle.down:after{left:-51px;top:35px}
.section-divider.triple-triangle.down:before{right:-35px;top:-50px}
.section-divider.triple-triangle.up,.section-divider.triple-triangle.down{-moz-transform:rotate(45deg);-webkit-transform:rotate(45deg);-o-transform:rotate(45deg);-ms-transform:rotate(45deg);transform:rotate(45deg)}
.section-divider.triple-triangle.up{top:-25px}
.section-divider.triple-triangle.down{bottom:-25px}
.section-divider.square.up,.section-divider.square.down{width:70px;height:70px;margin-left:-35px}
.section-divider.square.up{top:-25px}
.section-divider.square.down{bottom:-25px}
.section-divider.circle.up,.section-divider.circle.down{width:70px;height:70px;margin-left:-35px;-webkit-border-radius:100%;border-radius:100%}
.section-divider.circle.up{top:-25px}
.section-divider.circle.down{bottom:-25px}

/* section-decoration | height:100px - fallback */

.section .section-decoration{width:100%;height:100px;position:absolute;left:0;background-repeat:repeat-x;z-index:2}
.section .section-decoration.top{top:0;background-position:center top;}
.section .section-decoration.bottom{bottom:0;background-position:center bottom;}

/* section shape dividers */

.mfn-shape-divider{position: absolute; left: 0; width: 100%; overflow: hidden; line-height: 0; }

.mfn-shape-divider[data-name="top"]{ top:0;}
.mfn-shape-divider[data-name="top"][data-invert="1"]{ transform: rotate(180deg) }

.mfn-shape-divider[data-name="bottom"]{ transform: rotate(180deg); bottom:0; }
.mfn-shape-divider[data-name="bottom"][data-invert="1"]{ transform: unset }

.mfn-shape-divider[data-bring-front="1"] { z-index: 2 }
.mfn-shape-divider[data-flip="1"] svg { transform: rotateY(180deg) }

.mfn-shape-divider svg{display:block;position:relative;width:100%;height:150px;}
.mfn-shape-divider svg circle,
.mfn-shape-divider svg ellipse,
.mfn-shape-divider svg rect,
.mfn-shape-divider svg path:not(.transparent){fill:var(--mfn-shape-divider)}

.mfn-shape-divider svg .opacity-5{opacity:.5}

/* idea_box */

.idea_box{padding:25px 25px 25px 70px;position:relative;border-top-width:1px;border-style:solid;background:rgba(0,0,0,.02);clear:both}
.idea_box .icon{font-size:30px;line-height:30px;position:absolute;left:15px;top:50%;margin-top:-15px;color:rgba(0,0,0,.15)}

/* google_font */

.google_font{margin-bottom:15px;}
.google_font.inline{margin-bottom:0;display:inline}

/* popup-content */

.popup-content{display:none;}

/* single_icon */

.single_icon{display:block;margin:0 auto 15px}
.single_icon.icon_left{text-align:left}
.single_icon.icon_right{text-align:right}
.single_icon.icon_center{text-align:center}

/* form */

form,fieldset{margin-bottom:0}
textarea{min-height:60px;line-height:20px}
label,legend{display:block;margin-bottom:5px;font-weight:500}
input[type="checkbox"]{display:inline}
input[type="checkbox"] ~ label{display:inline}
input[type="checkbox"] ~ label:before{content:" "}
input[type="date"],
input[type="email"],
input[type="number"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="text"],
input[type="url"],
select,textarea{padding:10px;outline:none;margin:0;width:230px;max-width:100%;display:block;margin-bottom:20px;font-size:15px;border-width:1px;border-style:solid;border-radius:0;box-sizing:border-box;-webkit-appearance:none}

input[type="date"]::-webkit-datetime-edit{font-size:15px;line-height:19px}
input[type="date"]::-webkit-calendar-picker-indicator{font-size:80%}

/* list ul ol li */

ul{list-style:none outside}
ol{list-style:decimal}

.column_column ul,.column_helper ul,.column_visual ul,.icon_box ul,.mfn-acc ul,.ui-tabs-panel ul,.post-excerpt ul,.the_content_wrapper ul,.column_product_short_description ul,.elementor-widget-text-editor ul{list-style:disc outside;margin:0 0 15px 30px}
.column_column ol,.column_helper ol,.column_visual ol,.icon_box ol,.mfn-acc ol,.ui-tabs-panel ol,.post-excerpt ol,.the_content_wrapper ol,.column_product_short_description ol,.elementor-widget-text-editor ol{margin:0 0 15px 30px}
.column_column ul li,.column_helper ul li,.column_visual ul li,.icon_box ul li,.mfn-acc ul li,.ui-tabs-panel ul li,.post-excerpt ul li,.the_content_wrapper ul li,.column_product_short_description ul li,.elementor-widget-text-editor ul li,
.column_column ol li,.column_helper ol li,.column_visual ol li,.icon_box ol li,.mfn-acc ol li,.ui-tabs-panel ol li,.post-excerpt ol li,.the_content_wrapper ol li,.column_product_short_description ol li,.elementor-widget-text-editor ol li{margin-bottom:10px}
.column_column ul li ul,.column_column ol li ol,.column_helper ul li ul,.column_helper ol li ol,.column_visual ul li ul,.column_visual ol li ol,.icon_box ul li ul,.icon_box ol li ol,.mfn-acc ul li ul,.mfn-acc ol li ol,.ui-tabs-panel ul li ul,.ui-tabs-panel ol li ol,.post-excerpt ul li ul,.post-excerpt ol li ol,.the_content_wrapper ul li ul,.the_content_wrapper ol li ol,.column_product_short_description ul li ul,.column_product_short_description ol li ol{margin-top:10px;margin-bottom:0}

ul.list_mixed,ul.list_check,ul.list_star,ul.list_idea,ul.list_custom{list-style:none;margin-left:0}
ul.list_mixed li,ul.list_check li,ul.list_star li,ul.list_idea li,ul.list_custom li{position:relative;margin:0 0 20px 0;padding-left:50px}
ul.list_mixed li:after,ul.list_check li:after,ul.list_star li:after,ul.list_idea li:after,ul.list_custom li:after{content:"";width:70px;height:1px;overflow:hidden;background:rgba(0,0,0,.08);position:absolute;left:0;bottom:-11px}
ul.list_mixed li:last-child:after,ul.list_check li:last-child:after,ul.list_star li:last-child:after,ul.list_idea li:last-child:after,ul.list_custom li:last-child:after{display:none}

ul.list_custom li i{position:absolute;left:25px;top:4px;font-size:17px;transform:translateX(-50%);line-height:20px}
ul.list_check li:before,li.list_check:before{content:'\e841'}
ul.list_star li:before,li.list_star:before{content:'\e927'}
ul.list_idea li:before,li.list_idea:before{content:'\e8ae'}

ul.list_mixed li:before,ul.list_check li:before,ul.list_star li:before,ul.list_idea li:before{font-family:"mfn-icons";overflow:hidden;position:absolute;left:20px;top:0;font-size:17px}
ul.list_mixed li:before,ul.list_check li:before,ul.list_star li:before,ul.list_idea li:before,ul.list_custom li i{color:#3E3E3E}

dl{margin:0 0 30px}
dl > dt,dl > dd{border-width:1px 0 0;border-style:solid;padding:10px 0;margin:0}
dl > dt:first-of-type,dl > dd:first-of-type{padding-top:0;border-top-width:0}
dl > dt{display:block;float:left;width:100px;font-weight:700}
dl > dd{margin-left:110px}
dl > dd:after{content:"";clear:both;display:block}

/* alert */

.alert{ display: flex; align-items: center; margin-bottom:30px;padding:15px 25px; box-sizing:border-box;position:relative;width:100%}
.alert .alert_icon { flex-shrink:0;width:30px;height:30px; margin-right: 20px;}
.alert .alert_wrapper { margin-right: 20px; }
.alert .alert_wrapper a { border-bottom: 1px solid; text-decoration: none; }
.alert .alert_wrapper a.separated{margin-left:10px}
.alert a.close{flex-shrink:0;margin-left: auto; margin-right: -10px; text-decoration: none; }

.alert_warning{background:#fef8ea;}
.alert_warning,.alert_warning a,.alert_warning a:hover,.alert_warning a.close .icon{color:#8a5b20}
.alert_warning .path{stroke:#8a5b20}

.alert_error{background:#fae9e8;}
.alert_error,.alert_error a,.alert_error a:hover,.alert_error a.close .icon{color:#962317}
.alert_error .path{stroke:#8a5b20}

.alert_info{background:#efefef;}
.alert_info,.alert_info a,.alert_info a:hover,.alert_info a.close .icon{color:#57575b}
.alert_info .path{stroke:#57575b}

.alert_success{background:#eaf8ef;}
.alert_success,.alert_success a,.alert_success a:hover,.alert_success a.close .icon{color:#3a8b5b}
.alert_success .path{stroke:#3a8b5b}


/* mfn-link */

a.mfn-link{position:relative;display:inline-block;margin:15px 25px;font-size:15px;text-shadow:0 0 1px rgba(255,255,255,0.3);text-decoration:none;outline:none;white-space:nowrap}
a:hover.mfn-link{text-decoration:none}

	/* #1: Brackets */

	a.mfn-link-1:before,a.mfn-link-1:after{display:inline-block;opacity:0;-webkit-transition:-webkit-transform 0.3s,opacity .2s;-moz-transition:-moz-transform 0.3s,opacity .2s;transition:transform 0.3s,opacity .2s}
	a.mfn-link-1:before{margin-right:10px;content:'[';-webkit-transform:translateX(20px);-moz-transform:translateX(20px);transform:translateX(20px)}
	a.mfn-link-1:after{margin-left:10px;content:']';-webkit-transform:translateX(-20px);-moz-transform:translateX(-20px);transform:translateX(-20px)}
	a:hover.mfn-link-1:before,a:hover.mfn-link-1:after,a.hover.mfn-link-1:before,a.hover.mfn-link-1:after{opacity:1;-webkit-transform:translateX(0px);-moz-transform:translateX(0px);transform:translateX(0px)}

	/* #2: 3D rolling links */

	a.mfn-link-2{line-height:44px;-webkit-perspective:1000px;-moz-perspective:1000px;perspective:1000px}
	a.mfn-link-2 span{position:relative;display:inline-block;padding:0 14px;-webkit-transition:-webkit-transform .3s;-moz-transition:-moz-transform .3s;transition:transform .3s;-webkit-transform-origin:50% 0;-moz-transform-origin:50% 0;transform-origin:50% 0;-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;transform-style:preserve-3d}
	a.mfn-link-2 span:before{position:absolute;top:100%;left:0;width:100%;height:100%;content:attr(data-hover);-webkit-transition:background .3s;-moz-transition:background .3s;transition:background .3s;-webkit-transform:rotateX(-90deg);-moz-transform:rotateX(-90deg);transform:rotateX(-90deg);-webkit-transform-origin:50% 0;-moz-transform-origin:50% 0;transform-origin:50% 0;text-align:center}
	a:hover.mfn-link-2 span,a.hover.mfn-link-2 span{-webkit-transform:rotateX(90deg) translateY(-22px);-moz-transform:rotateX(90deg) translateY(-22px);transform:rotateX(90deg) translateY(-22px)}

	/* #3: bottom line */

	a.mfn-link-3{padding:8px 0}
	a.mfn-link-3:after{position:absolute;top:100%;left:0;width:100%;height:3px;content:'';opacity:0;-webkit-transition:opacity 0.3s,-webkit-transform .3s;-moz-transition:opacity 0.3s,-moz-transform .3s;transition:opacity 0.3s,transform .3s;-webkit-transform:translateY(10px);-moz-transform:translateY(10px);transform:translateY(10px)}
	a:hover.mfn-link-3:after,a.hover.mfn-link-3:after{opacity:1;-webkit-transform:translateY(0px);-moz-transform:translateY(0px);transform:translateY(0px)}

	/* #4: second border slides up */

	a.mfn-link-4{padding:12px 10px 10px;text-shadow:none;font-weight:700}
	a.mfn-link-4:before,a.mfn-link-4::after{position:absolute;top:100%;left:0;width:100%;height:3px;content:'';-webkit-transition:-webkit-transform .3s;-moz-transition:-moz-transform .3s;transition:transform .3s;-webkit-transform:scale(0.85);-moz-transform:scale(0.85);transform:scale(0.85)}
	a.mfn-link-4:after{opacity:0;-webkit-transition:top 0.3s,opacity 0.3s,-webkit-transform .3s;-moz-transition:top 0.3s,opacity 0.3s,-moz-transform .3s;transition:top 0.3s,opacity 0.3s,transform .3s}
	a:hover.mfn-link-4:before,a:hover.mfn-link-4:after,a.hover.mfn-link-4:before,a.hover.mfn-link-4:after{-webkit-transform:scale(1);-moz-transform:scale(1);transform:scale(1)}
	a:hover.mfn-link-4:after,a.hover.mfn-link-4:after{top:0;opacity:1}

	/* #5: reveal, push out */

	a.mfn-link-5{overflow:hidden;margin:0 15px;position:relative;z-index:1}
	a.mfn-link-5 span{display:block;padding:10px 20px;-webkit-transition:-webkit-transform .3s;-moz-transition:-moz-transform .3s;transition:transform .3s}
	a.mfn-link-5:before{position:absolute;top:0;left:0;text-align:left;z-index:-1;padding:10px 20px;width:100%;height:100%;content:attr(data-hover);-webkit-transition:-webkit-transform .3s;-moz-transition:-moz-transform .3s;transition:transform .3s;-webkit-transform:translateX(-25%);-moz-transform:translateX(-25%);transform:translateX(-25%)}
	a:hover.mfn-link-5 span,a.hover.mfn-link-5 span{-webkit-transform:translateX(100%);-moz-transform:translateX(100%);transform:translateX(100%)}
	a:hover.mfn-link-5:before,a.hover.mfn-link-5:before{-webkit-transform:translateX(0%);-moz-transform:translateX(0%);transform:translateX(0%)}

	/* #6: text fill */

	a.mfn-link-6{padding:10px 0;text-shadow:none}
	a.mfn-link-6:before{position:absolute;top:0;left:0;overflow:hidden;padding:10px 0;max-width:0;border-bottom:2px solid;content:attr(data-hover);-webkit-transition:max-width .5s;-moz-transition:max-width .5s;transition:max-width .5s}
	a:hover.mfn-link-6:before,a.hover.mfn-link-6:before{max-width:100%}

	/* #7: border switch */

	a.mfn-link-7{padding:0 20px;height:45px;line-height:45px}
	a.mfn-link-7:before,a.mfn-link-7:after{position:absolute;width:45px;height:2px;content:'';opacity:.2;-webkit-transition:all .3s;-moz-transition:all .3s;transition:all .3s;pointer-events:none}
	a.mfn-link-7:before{top:0;left:0;-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);-webkit-transform-origin:0 0;-moz-transform-origin:0 0;transform-origin:0 0}
	a.mfn-link-7:after{right:0;bottom:0;-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);-webkit-transform-origin:100% 0;-moz-transform-origin:100% 0;transform-origin:100% 0}
	a:hover.mfn-link-7:before,a:hover.mfn-link-7:after,a.hover.mfn-link-7:before,a.hover.mfn-link-7:after{opacity:1}
	a:hover.mfn-link-7:before,a.hover.mfn-link-7:before{left:50%;-webkit-transform:rotate(0deg) translateX(-50%);-moz-transform:rotate(0deg) translateX(-50%);transform:rotate(0deg) translateX(-50%)}
	a:hover.mfn-link-7:after,a.hover.mfn-link-7:after{right:50%;-webkit-transform:rotate(0deg) translateX(50%);-moz-transform:rotate(0deg) translateX(50%);transform:rotate(0deg) translateX(50%)}

	/* #8: cross */

	a.mfn-link-8{padding:0 5px;font-weight:700;-webkit-transition:color .3s;-moz-transition:color .3s;transition:color .3s;position:relative;z-index:1}
	a.mfn-link-8:before,a.mfn-link-8:after{position:absolute;width:100%;left:0;top:50%;height:2px;margin-top:-1px;content:'';z-index:-1;-webkit-transition:-webkit-transform 0.3s,opacity .3s;-moz-transition:-moz-transform 0.3s,opacity .3s;transition:transform 0.3s,opacity .3s;pointer-events:none}
	a.mfn-link-8:before{-webkit-transform:translateY(-20px);-moz-transform:translateY(-20px);transform:translateY(-20px)}
	a.mfn-link-8:after{-webkit-transform:translateY(20px);-moz-transform:translateY(20px);transform:translateY(20px)}
	a:hover.mfn-link-8:before,a:hover.mfn-link-8:after,a.hover.mfn-link-8:before,a.hover.mfn-link-8:after{opacity:.7}
	a:hover.mfn-link-8:before,a.hover.mfn-link-8:before{-webkit-transform:rotate(45deg);-moz-transform:rotate(45deg);transform:rotate(45deg)}
	a:hover.mfn-link-8:after,a.hover.mfn-link-8:after{-webkit-transform:rotate(-45deg);-moz-transform:rotate(-45deg);transform:rotate(-45deg)}

    /* #9: hover icon */
    a.mfn-link-9{line-height:1.2;}
    a.mfn-link-9 span[data-hover] { display: block; opacity: .5; }
    a:hover.mfn-link-9 span[data-hover] { transform: translateY(-60%);opacity:1; }

    a.mfn-link-9 i,
    a.mfn-link-9 svg{display:inline-flex;align-items: center;justify-content: center;position:absolute;top:0;left:50%;height: 80%;transform: translate(-50%,0);opacity:0;}
    a:hover.mfn-link-9 i,
    a.hover.mfn-link-9 i,
    a:hover.mfn-link-9 svg,
    a.hover.mfn-link-9 svg {transform: translate(-50%,80%);opacity:1;}

    a.mfn-link-9 i,
    a.mfn-link-9 span[data-hover],
    a.mfn-link-9 svg{ transition:opacity 0.2s,color 0.2s,transform 0.2s cubic-bezier(.33,0,0,1); }


	/* variables */

	a.mfn-link-4:before,a.mfn-link-4:after,a.mfn-link-3:after,a:hover.mfn-link-8:after,a:hover.mfn-link-8:before,a.hover.mfn-link-8:after,a.hover.mfn-link-8:before{background:rgba(0,0,0,0.1)}
	a.mfn-link-6{border-top:2px solid rgba(0,0,0,.05)}
	a.mfn-link.mfn-link-6{color:rgba(0,0,0,.25)}
	a.mfn-link-2 span,a:hover.mfn-link-2 span:before,a.hover.mfn-link-2 span:before,a.mfn-link-5 span,a.mfn-link-5:before{color:#fff}

/* accordion */

.accordion .question{margin-bottom:5px;-webkit-border-radius:5px;border-radius:5px;overflow:hidden;border-width:1px;border-style:solid}
.accordion .question:last-child{margin-bottom:0}
.accordion .question > .title{padding:14px 14px 14px 60px;font-size:13px;font-weight:700;position:relative;border-width:0;border-style:solid;cursor:pointer; background: #f9f9f9; box-shadow: inset 0px 4px 3px -2px rgba(0,0,0,.04); }
.accordion .question > .title:before{content:"";width:49px;height:100%;border-width:0 1px 0 0;border-style:solid;position:absolute;left:0;top:0;z-index:1}
.accordion .question > .title > .acc-icon-plus,.accordion .question .title > .acc-icon-minus{font-size:17px;line-height:17px;display:flex;align-items:center;justify-content:center;position:absolute;left:0;top:0;width:48px;height:100%;color:rgba(0,0,0,.25)}
.accordion .question > .title > .acc-icon-plus:before,.accordion .question .title > .acc-icon-minus:before{margin:0!important}
.accordion .question > .title > .acc-icon-plus{display:flex}
.accordion .question > .title > .acc-icon-minus{display:none}
.accordion .question > .answer{padding:15px 20px 20px;overflow:hidden;display:none; box-shadow: inset 0px 4px 3px -2px rgba(0,0,0,.06);}
.accordion .question > .answer .wpb_content_element:last-child{margin-bottom:0}
.accordion .question.active > .title{border-width:0 0 1px}
.accordion .question.active >.title > .acc-icon-plus{display:none}
.accordion .question.active > .title > .acc-icon-minus{display:flex}
.accordion .question.active p:last-child{margin-bottom:0}

/* faq */

.faq .question{margin-bottom:5px;overflow:hidden;position:relative;background:rgba(0,0,0,.02)}
.faq .question:before{content:"";width:60px;height:100%;border-width:0 1px 0 0;border-style:solid;border-color:rgba(0,0,0,.1);position:absolute;left:0;top:0;z-index:1}
.faq .question:after{content:"";width:60px;height:100%;background:rgba(0,0,0,.03);position:absolute;left:0;top:0;z-index:1}
.faq .question:last-child{margin-bottom:0}
.faq .question > .title{padding:20px 40px 20px 80px;font-size:15px;position:relative;cursor:pointer;z-index:2}
.faq .question > .title > .acc-icon-plus,.faq .question .title > .acc-icon-minus{font-size:17px;line-height:17px;display:block;position:absolute;right:15px;top:21px;width:20px;height:20px;text-align:center;color:rgba(0,0,0,.25)}
.faq .question > .title > .acc-icon-plus:before,.faq .question .title > .acc-icon-minus:before{margin:0!important}
.faq .question > .title > .acc-icon-plus{display:block}
.faq .question > .title > .acc-icon-minus{display:none}
.faq .question > .title .num{font-weight:700;position:absolute;left:27px;top:20px;font-weight:bold}
.faq .question > .answer{padding:0 20px 20px 80px;overflow:hidden;display:none}
.faq .question.active > .title > .acc-icon-plus{display:none}
.faq .question.active > .title > .acc-icon-minus{display:block}
.faq .question.active p:last-child{margin-bottom:0}

.wpb_wrapper .faq{margin-bottom:5px}
.wpb_wrapper .faq .question .title.wpb_toggle{background-position:23px}
.wpb_wrapper .faq .question .answer.wpb_toggle_content{margin:0}
.wpb_wrapper .faq .question .last_toggle_el_margin{margin-bottom:0}
.wpb_wrapper .faq .question .title > .acc-icon-plus,.wpb_wrapper .faq .question .title > .acc-icon-minus{left:20px;right:auto;color:rgba(0,0,0,.5)}

/* table */

table{width:100%;box-sizing:border-box;margin-bottom:15px;border-collapse:collapse;border-spacing:0;border-radius:5px}
table th,table td{padding:10px;text-align:center;border-width:1px;border-style:solid;vertical-align:middle}
table th{font-weight:700; background: #f9f9f9; box-shadow: inset 0px 4px 3px -2px rgba(0,0,0,.04); }
table th.clear{border:0;background:none}
table tr:first-child td{ box-shadow: inset 0px 4px 3px -2px rgba(0,0,0,.06);}
table tr:nth-child(2n) td{background:rgba(0,0,0,0.01)}

/* table.simple */

table.simple th{background:none}
table.simple th,table.simple td{border-width:0 0 1px}
table.simple tr:first-child td{background:none}
table.simple tr:last-child td{border:none}

/* table-hover */

body.table-hover:not(.woocommerce-page) table tr:hover td{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* feature_list */

.feature_list ul{margin:0;overflow:hidden}
.feature_list ul li{margin:0;list-style:none;width:25%;float:left;display:block;padding:10px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.feature_list ul li a{display:block}
.feature_list ul li a:hover{text-decoration:none}
.feature_list ul li .icon{width:60px;height:60px;line-height:0;margin-right:10px;display:table-cell;vertical-align:middle;text-align:center}
.feature_list ul li .icon i{font-size:43px;line-height:60px}
.feature_list ul li .icon i:before{margin:0}
.feature_list ul li p{display:table-cell;vertical-align:middle;padding:0 0 0 10px}
.feature_list hr{margin:0}
.feature_list ul li a{-webkit-transition:all .1s ease-in-out;-moz-transition:all .1s ease-in-out;-o-transition:all .1s ease-in-out;-ms-transition:all .1s ease-in-out;transition:all .1s ease-in-out}

.feature_list[data-col="2"] ul li{width:50%}
.feature_list[data-col="3"] ul li{width:33.33%}
.feature_list[data-col="4"] ul li{width:25%}
.feature_list[data-col="5"] ul li{width:20%}
.feature_list[data-col="6"] ul li{width:16.66%}

/* list_item */

.list_item{display:block}
.list_item > a{display:block}
.list_item > a:hover{text-decoration:none}
.list_item .list_left{display:flex;justify-content:center;align-items:center;width:80px;height:80px;font-size:50px;overflow:hidden;text-align:center;float:left}
.list_item .list_image{box-sizing:unset;font-size:0}
.list_item .list_left img{max-width:80px!important;max-height:80px!important;vertical-align:middle}
.list_item .list_left i:before{margin:0}
.list_item .circle{display:flex;justify-content:center;align-items:center;width:78px;height:78px;border-width:1px;border-style:solid;font-size:20px;font-weight:700;overflow:hidden;text-align:center;float:left;-webkit-border-radius:100%;border-radius:100%;background-image:url(../images/stripes/stripes_3_b.png);-webkit-box-shadow:inset 0 0 5px 0 rgba(0,0,0,.08);box-shadow:inset 0 0 5px 0 rgba(0,0,0,.08)}
.list_item .list_right{padding:5px 0 0;margin-left:100px;word-wrap:break-word}
.list_item .list_right h4{margin-bottom:7px}
.list_item.lists_1 .list_left{-webkit-border-radius:5px;border-radius:5px;background-image:url(../images/stripes/stripes_3_b.png);-webkit-box-shadow:inset 0 0 5px 0 rgba(0,0,0,.1);box-shadow:inset 0 0 5px 0 rgba(0,0,0,.1)}
.list_item.lists_1 .list_image{width:60px;height:60px;line-height:60px;padding:10px;font-size:0}
.list_item.lists_1 .list_left img{max-width:60px!important;max-height:60px!important}
.list_item.lists_3 .list_left{float:none;margin-bottom:10px}
.list_item.lists_3 .list_icon{width:auto;justify-content:flex-start;}
.list_item.lists_3 .list_right{margin-left:0;padding-top:0}
.list_item.lists_3 .list_right h4{margin-bottom:10px}
.list_item.lists_4 .list_right{padding-top:21px}
.align_right .list_item .list_left{float:right;}
.align_right .list_item .list_right{margin-left:0;margin-right:100px;}

/* pricing-box */

.column_pricing_item .mcb-item-pricing_item-inner{height:100%;border-width:1px;border-style:solid;background:#fff}
.pricing-box { text-align:center; }
.pricing-box .plan-header{padding:20px 15px 0}
.pricing-box .plan-header .image{line-height:0;overflow:hidden;margin-bottom:15px;}
.pricing-box .plan-header h2{margin:0 0 20px;font-size:30px;line-height:30px}
.pricing-box .plan-header .price > span{font-size:45px;line-height:45px;margin:0 5px}
.pricing-box .plan-header .price sup.currency{font-size:20px;line-height:20px;top:-10px;position:relative}
.pricing-box.cp-right .plan-header .price sup.currency{margin-right:5px}
.pricing-box .plan-header .price sup.period{font-size:15px;line-height:15px;top:-15px;position:relative}
.pricing-box .plan-header hr{margin-bottom:0;width:60%;display:inline-block;}
.pricing-box .plan-header p.subtitle{padding:20px 0 0;margin-bottom:0}
.pricing-box .plan-inside{padding:10px 30px;}
.pricing-box .plan-inside .pi-content {display:block;}
.pricing-box .plan-inside ul{margin:0;font-size:100%;line-height:normal}
.pricing-box .plan-inside ul li{text-align:center;padding:11px 10px;display:block;margin:0;border-bottom:1px solid rgba(0,0,0,0.1)}
.pricing-box .plan-inside ul li .yes,.pricing-box .plan-inside ul li .no{display:inline-block;overflow:hidden;width:10px;height:10px;-webkit-border-radius:5px;border-radius:5px;background:rgba(0,0,0,.1)}
.pricing-box .plan-inside ul li:last-child{border-bottom:0}
.pricing-box .plan-footer{padding:0 15px;}
.pricing-box .plan-footer a{margin-right:0}
.pricing-box-box.pricing-box-featured{border-color:transparent}

.pricing-box.pricing-box-label .plan-header *,.pricing-box.pricing-box-label .plan-footer{visibility:hidden}
.pricing-box-label ul li{font-weight:700;text-align:right!important}
.pricing-box-label,.pricing-box-table{border:0;background:none} /* Old */
.pricing_item-style-table .mcb-item-pricing_item-inner,
.pricing_item-style-label .mcb-item-pricing_item-inner {border:0;background:none}
.pricing-box-label .plan-inside,.pricing-box-table .plan-inside{padding-left:0;padding-right:0}
.pricing-box-table.pricing-box-featured{background:rgba(0,0,0,.02);padding-left:10px;padding-right:10px}


/* content_slider */

.content_slider{padding:0 140px;position:relative}
.content_slider .content_slider_ul{margin:0;line-height:0;}
.content_slider .content_slider_ul .slick-list { border-width:8px;border-style:solid;-webkit-box-sizing:border-box;box-sizing:border-box }
.content_slider .content_slider_ul li{display:block;float:left;margin:0!important;}
.content_slider .button{position:absolute;top:50%;margin:-22px 0 0;font-size:13px;z-index:1;}
.content_slider .slider_prev{left:-90px}
.content_slider .slider_next{right:-90px}
.content_slider .slider_pagination{width:100%;margin-top:20px;}
.content_slider .slider_pagination li{display:inline-block;}

.content_slider .content_slider_ul{opacity:0;max-height:300px;transition:opacity 0.3s ease-in-out;}
.content_slider .content_slider_ul.slick-slider{opacity:1;max-height:none}
.content_slider.default .content_slider_ul .slick-list { overflow: hidden; }

.column_column .content_slider{padding:0 10%}
.column_column .content_slider ul{margin-left:0}
.column_column .content_slider a.slider_prev{left:-70px}
.column_column .content_slider a.slider_next{right:-70px}

	/* flat */

	.content_slider.flat .content_slider_ul .slick-list{border-width:0}
	.content_slider.flat a.button{background:none!important;box-shadow:none;border:none}
	.content_slider.flat a.button:after{display:none!important}
	.content_slider.flat a.button:hover{background:none!important}
	.content_slider.flat a.button .button_icon{background:none;padding:0;font-size:50px;opacity:.3}
	.content_slider.flat a.button .button_icon:after{display:none}
	.content_slider.flat a:hover.button .button_icon{opacity:1}

	/* description */

	.content_slider.flat.description ul li{text-align:center}
	.content_slider.flat.description ul li a{display:block;text-decoration:none}
	.content_slider.flat.description ul li img{margin-bottom:30px}
	.content_slider.flat.description ul li h3{margin-bottom:0}
	.content_slider.flat.description ul li .desc{line-height:120%;line-height:initial;padding:0 20%;margin-top:15px}

	/* carousel */

	.content_slider.carousel{padding:0 70px}
	.content_slider.carousel .content_slider_ul .slick-list{border-width:0}
	.content_slider.carousel .content_slider_ul li{text-align:center;padding:0 20px;padding-top:5px}
	.content_slider.carousel .content_slider_ul li img{margin-bottom:20px;opacity:.8;position:relative;top:0}
	.content_slider.carousel .content_slider_ul li a{color:inherit;text-decoration:none}
	.content_slider.carousel .content_slider_ul li .title{opacity:.3;line-height:120%;line-height:initial}
	.content_slider.carousel .content_slider_ul li:hover img{opacity:1;top:-5px}
	.content_slider.carousel .content_slider_ul li:hover .title{opacity:1}

	.content_slider.carousel a.button{background:none!important;box-shadow:none;border:none;top:40%;margin-top:-11px;}
	.content_slider.carousel a.button:after{display:none!important}
	.content_slider.carousel a.button:hover{background:none!important}
	.content_slider.carousel a.button .button_icon{background:none;padding:0;font-size:30px;opacity:.5}
	.content_slider.carousel a.button:hover .button_icon{opacity:1}
	.content_slider.carousel a.slider_prev{left:-70px}
	.content_slider.carousel a.slider_next{right:-70px}

	/* center */

	.content_slider.center{padding:0}
	.content_slider.center .content_slider_ul .slick-list{border-width:0}
	.content_slider.center .content_slider_ul li{position:relative;padding:0 5px;}
	.content_slider.center .content_slider_ul li:not(.slick-center){transform:scale(.98)}
	/* .content_slider.center .content_slider_ul li:after{content:"";position:absolute;top:0;left:0;display:block;width:100%;width:calc(100% - 10px);height:100%;margin-left:5px;background-color:rgba(0,0,0,.15);opacity:0;z-index:2}
	.content_slider.center .content_slider_ul li:not(.slick-center):after{opacity:1;} */
	.content_slider.center .content_slider_ul li a{display:block;position:relative;z-index:9}

	.content_slider.center a.button{background:none!important;box-shadow:none;border:none;z-index:1}
	.content_slider.center a.button.slider_prev{left:0}
	.content_slider.center a.button.slider_next{right:0}
	.content_slider.center a.button:after{display:none!important}
	.content_slider.center a.button:hover{background:none!important}
	.content_slider.center a.button .button_icon{background:none;padding:0;font-size:50px;opacity:.66}
	.content_slider.center a.button .button_icon i{color:#fff;}
	.content_slider.center a.button:hover .button_icon{opacity:1}

	.content_slider.center .content_slider_ul li,
	.content_slider.center .content_slider_ul li:after,
	.content_slider.center a.button .button_icon{transition:all .3s cubic-bezier(.4,0,.2,1)}

	/* hide-arrows */

	.content_slider.hide-arrows{padding:0}
	.content_slider.hide-arrows a.slider_prev,.content_slider.hide-arrows a.slider_next{display:none!important}
	.content_slider.hide-dots .slider_pagination{display:none!important}

	.section.full-width .column.one .content_slider.hide-arrows{padding:0 140px}
	.section.full-width .column.one .content_slider.center{padding:0}

	/* animation */

	.content_slider.carousel ul li img,.content_slider.carousel ul li .title,.content_slider.flat a.button .button_icon{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* offer */

.offer{position:relative}

.offer .offer_ul{margin:0!important;opacity:0;max-height:500px;overflow:hidden}
.offer .offer_ul.slick-slider{opacity:1;max-height:none;overflow:visible}
.offer .offer_li{float:left;width:100%;list-style:none;margin:0!important}

.offer .offer_li .image_wrapper{float:left;width:50%;line-height:0;overflow:hidden;}
.offer .offer_li .image_wrapper img{float:right}

.offer .offer_li .desc_wrapper{float:left;width:570px;width:calc(50% - 42px);margin:30px 12px 30px 30px}
.offer .offer_li .desc_wrapper .title{margin-bottom:30px;position:relative;min-height:43px;padding-right:160px}
.offer .offer_li .desc_wrapper.no-link .title{padding:0}
.offer .offer_li .desc_wrapper .title h3{margin:0;font-size:35px;line-height:35px;padding-top:3px}
.offer .offer_li .desc_wrapper .title h3 em{color:rgba(0,0,0,.1);font-style:normal}
.offer .offer_li .desc_wrapper .title a.button{margin:0;position:absolute;right:0;top:0}

.offer .offer_li .desc_wrapper.align_left{text-align:left}
.offer .offer_li .desc_wrapper.align_right{text-align:right}
.offer .offer_li .desc_wrapper.align_center{text-align:center}
.offer .offer_li .desc_wrapper.align_justify{text-align:justify}

.offer .offer_li .desc_wrapper.align_right.has-link .title{padding-left:160px;padding-right:0}
.offer .offer_li .desc_wrapper.align_right .title a.button{left:0;right:auto;}
.offer .offer_li .desc_wrapper.align_center.has-link .title{text-align:left}

.offer_li ul{list-style:disc inside none;margin-bottom:10px}
.offer_li ol{list-style:decimal inside none;margin-bottom:10px}
.offer_li li{margin-bottom:5px}

.offer .slick-arrow{position:absolute;width:46px;height:46px;padding:0;text-align:center;margin:0;background-color:#262626;}
.offer a.slider_prev:after,.offer a.slider_next:after{background:rgba(0,0,0,.2)}
.offer a.slider_prev .button_icon,.offer a.slider_next .button_icon{line-height:46px;font-size:13px}
.offer a.slider_prev .button_icon i,.offer a.slider_next .button_icon i{color:#fff}
.offer a.slider_prev{left:50px;top:50%;z-index:2;margin-top:-69px;border-radius:5px 5px 0 0}
.offer a.slider_next{left:50px;top:50%;z-index:2;margin-top:23px;border-radius:0 0 5px 5px}

.offer .slider_pagination{opacity:0;left:50px;top:50%;color:#6C6C6C;margin-top:-23px;position:absolute;z-index:1;width:46px;height:46px;line-height:46px;text-align:center;font-size:13px;background:#1c1c1c}
.offer .slider_pagination.show{opacity:1}
.offer .slider_pagination .current,.offer .slider_pagination .count{color:#fff}

.button-stroke .offer a.slider_prev .button_icon,.button-stroke .offer a.slider_next .button_icon{padding:0;width:42px;height:42px;line-height:42px;text-align:center}
.button-stroke .offer a.slider_prev{margin-top:-75px}
.button-stroke .offer a.slider_next{margin-top:28px}
.button-stroke .offer .slider_pagination{border-radius:3px;background:rgba(0,0,0,0.6)}

/* offer_thumb */

.offer_thumb{position:relative;padding-left:120px;-webkit-box-sizing:border-box;box-sizing:border-box}

.offer_thumb_ul{margin:0!important;opacity:0;max-height:500px;overflow:hidden}
.offer_thumb_ul.slick-slider{opacity:1;max-height:none;overflow:visible}
.offer_thumb_ul .offer_thumb_li{float:left;display:block;margin:0!important;padding-left:30px;-webkit-box-sizing:border-box;box-sizing:border-box}

.offer_thumb_ul .offer_thumb_li .desc_wrapper{float:left;width:39%;margin-right:4%;padding-top:20px}
.offer_thumb_ul .offer_thumb_li .desc_wrapper .title{margin-bottom:15px;position:relative;min-height:43px;padding-right:160px}
.offer_thumb_ul .offer_thumb_li .desc_wrapper.no-link .title{padding:0}
.offer_thumb_ul .offer_thumb_li .desc_wrapper .title h3{margin:0;font-size:35px;line-height:35px;padding-top:3px}
.offer_thumb_ul .offer_thumb_li .desc_wrapper .title a.button{margin:0;position:absolute;right:0;top:0}

.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_left{text-align:left}
.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_right{text-align:right}
.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_center{text-align:center}
.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_justify{text-align:justify}

.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_right.has-link .title{padding-left:160px;padding-right:0}
.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_right .title a.button{left:0;right:auto;}
.offer_thumb_ul .offer_thumb_li .desc_wrapper.align_center.has-link .title{text-align:left}

.offer_thumb_ul .offer_thumb_li .image_wrapper{float:right;width:57%;overflow:hidden;}

.offer_thumb .slick-dots, .offer_thumb .slick-dots li{margin:0}
.offer_thumb .slider_pagination li{display:inline-block}

.offer_thumb .slider_pagination li a:before,
.offer_thumb .slider_pagination li.slick-active a:after { background-color: var(--mfn-offer-thumbs-nav); }

.mfn-looped-slider-section .slick-dots{ display: flex; margin: 20px 0;}

@media only screen and (min-width: 768px){
	.offer_thumb .slider_pagination{position:absolute;left:0;top:0;border-width:1px;border-style:solid}
	.offer_thumb .slider_pagination li{display:block}
	.offer_thumb .slider_pagination li a{display:block;box-sizing:content-box;margin:0;width:85px;height:85px;line-height:85px;text-align:center;padding:8px;text-indent:0;border-bottom-width:1px;border-style:solid;border-radius:0;-webkit-border-radius:0;background-color:#fff}
	.offer_thumb .slider_pagination li a img{vertical-align:middle}

	.offer_thumb .slider_pagination li.slick-active a{top:0;background-color:#fff;background-image:url(../images/stripes/stripes_3_b.png)}
	.offer_thumb .slider_pagination li:not(.slick-active) a:hover img{opacity:.6}

	body:not(.style-simple) .offer_thumb:not(.bottom) .slider_pagination li:last-child a{border:0}

	.offer_thumb .slider_pagination a:before{content:"";display:block;width:5.5px;height:100%;right:-5px;top:0;position:absolute;opacity:0}
	.offer_thumb .slider_pagination li.slick-active a:before{opacity:1}
	.offer_thumb .slider_pagination a:after{content:"";left:auto;right:-9px;top:50%;margin-top:-4px;width:8px;height:8px;opacity:0;border-radius:0;-webkit-border-radius:0}
	.offer_thumb .slider_pagination li.slick-active a:after{opacity:1}
}

.offer_thumb .slider_pagination a img{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

	/* offer_thumb bottom */

	.offer_thumb.bottom{padding-left:0}

	.offer_thumb.bottom .offer_thumb_ul .offer_thumb_li .image_wrapper{width:48%;text-align:center}
	.offer_thumb.bottom .offer_thumb_ul .offer_thumb_li .desc_wrapper{margin-right:4%;width:48%}

	@media only screen and (min-width: 768px){
		.offer_thumb.bottom .slider_pagination{position:static;border:0;text-align:center;margin-top:50px}
		.offer_thumb.bottom .slider_pagination li{display:inline-block;margin:-1px 0 0 -1px;}
		.offer_thumb.bottom .slider_pagination li a{display:inline-block;border-width:1px;border-style:solid}
		.offer_thumb.bottom .slider_pagination li a:before{width:100%;height:5.5px;left:0;right:auto;top:-5px}
		.offer_thumb.bottom .slider_pagination li a:after{left:50%;left:calc(50% - 4px);right:auto;top:-5px}
	}

/* Checkout */

.woocommerce-checkout #wcpay-payment-request-wrapper{ display: block; clear: both; width: 100%; }
.woocommerce-checkout #wcpay-payment-request-button-separator{ display: block; width: 100%; clear: both; }

/* Latest_news */
.Latest_news { overflow: hidden; }
.Latest_news ul{float:left;width:100%;margin:0}
.Latest_news ul li{margin:0 0 20px;list-style:none;position:relative;overflow:hidden}
.Latest_news ul li .photo{width:30%;border-right-width:3px;border-style:solid;float:left;line-height:0;overflow:hidden;}
.Latest_news ul li .desc{width:100%;padding:3px 12px 3px 0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.Latest_news ul li.has-post-thumbnail .desc{margin-left:30%;width:70%;padding-left:12px;}
.Latest_news ul li .desc h5{margin:0 0 7px}
.Latest_news ul li .desc .post-excerpt{margin:10px 0 5px}
.Latest_news ul li .desc_footer{overflow:hidden;border-top-width:1px;border-style:solid;padding-top:5px;color:#a8a8a8}
.Latest_news ul li .desc_footer .date{float:left;margin-right:5px}
.Latest_news ul li .desc_footer .button-love{float:left;margin-right:5px}
.Latest_news ul li .desc_footer .button-love a.mfn-love{display:inline-block;position:relative;padding-left:24px;margin-left:5px}
.Latest_news ul li .desc_footer .button-love a.mfn-love i{position:absolute;left:0;top:0;font-size:16px}
.Latest_news ul li .desc_footer .button-love a.mfn-love:hover{text-decoration:none}
.Latest_news ul li .desc_footer .button-love a.mfn-love i:last-child{opacity:0;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.Latest_news ul li .desc_footer .button-love a:hover.mfn-love i:last-child,.Latest_news ul li .desc_footer .button-love a.loved.mfn-love i:last-child{opacity:1}
.Latest_news .button{float:left;clear:both;}

.Latest_news.featured ul{float:left;width:49%;}
.Latest_news.featured ul.ul-first{margin-right:1%;}
.Latest_news.featured ul.ul-second{margin-left:1%;}
.Latest_news.featured ul.ul-first li .photo{width:100%;float:left;margin-bottom:10px;border-right-width:0;border-bottom-width:3px;border-bottom-style:solid;}
.Latest_news.featured ul.ul-first li .desc{width:100%;margin:0;padding:0;float:left;}
.Latest_news.featured ul.ul-first li .desc h4{margin:0 0 7px;}

.column.two-fifth .Latest_news.featured ul,
.column.one-third .Latest_news.featured ul,
.column.one-fourth .Latest_news.featured ul{width:100%}

.wrap.three-fifth .column.one-second .Latest_news.featured ul,
.wrap.one-second .column.one-second .Latest_news.featured ul,
.wrap.one-second .column.three-fifth .Latest_news.featured ul,
.wrap.two-fifth .column.three-fourth .Latest_news.featured ul,
.wrap.two-fifth .column.one-second .Latest_news.featured ul,
.wrap.two-fifth .column.three-fifth .Latest_news.featured ul,
.wrap.one-fourth .Latest_news.featured ul,
.wrap.one-fifth .Latest_news.featured ul,
.wrap.one-sixth .Latest_news.featured ul{width:100%}

/* blog-teaser */
.blog-teaser { overflow: hidden; }
.blog-teaser .teaser-wrapper{float:left;width:100%;margin:0;}

.blog-teaser li{display:block;position:relative;overflow:hidden}
.the_content_wrapper .blog-teaser li{margin-bottom:0;}
.blog-teaser li:first-child{float:left;width:66%;}
.blog-teaser li:not(:first-child){float:right;width:33%;}
.blog-teaser li:last-child{margin-top:1%;}
.blog-teaser li:last-child .photo-wrapper{margin-bottom:-3.2%;}

.blog-teaser.margin-no li:first-child{width:66.6666%;}
.blog-teaser.margin-no li:not(:first-child){float:right;width:33.3333%;}
.blog-teaser.margin-no li:last-child{margin-top:0;}
.blog-teaser.margin-no li:last-child .photo-wrapper{margin-bottom:0;}

.blog-teaser li .photo-wrapper {position:relative;line-height:0;}
.blog-teaser li .photo-wrapper:after{content:"";position:absolute;z-index:2;left:0;top:0;width:100%;height:100%;background:rgba(0,0,0,.2);opacity:0;transition:all 0.6s ease-out;}
.blog-teaser li:hover .photo-wrapper:after{opacity:1;}

.blog-teaser li .desc-wrapper{position:absolute;left:0;bottom:-20px;width:100%;z-index:4;transition:all 0.4s ease-out;}
.blog-teaser li:hover .desc-wrapper{transform:translateY(-20px);}
.blog-teaser li .desc-wrapper .desc{background:url(../images/blog_masonry_tile_gradient.png) top left repeat-x;padding:70px 25px 30px;}

.blog-teaser li .desc-wrapper .desc .post-meta .author .label{display:none;}
.blog-teaser li .desc-wrapper .desc .post-meta .comments{margin-left:5px;}
.blog-teaser li .desc-wrapper .desc .post-title a {color:#fff;}
.blog-teaser li .desc-wrapper .desc .post-title:after{content:"";display:block;height:3px;margin-top:20px;width:0;transition:all 0.4s ease-out;background-color:#fff;}
.blog-teaser li:hover .desc-wrapper .desc .post-title:after{width:40%}
.blog-teaser li:first-child:hover .desc-wrapper .desc .post-title:after{width:20%}

.blog-teaser li .desc-wrapper .desc .post-meta,
.blog-teaser li .desc-wrapper .desc .post-meta a{color:rgba(255,255,255,.7);}

.blog-teaser li:not(.has-post-thumbnail) .photo-wrapper{padding-top:75%;padding-bottom:3.2%;}
.blog-teaser li:not(.has-post-thumbnail) .desc-wrapper .desc{background:none;}
.blog-teaser li:not(.has-post-thumbnail) .desc-wrapper .desc .post-title:after{background:#fff;}


/* helper */

.helper{overflow:hidden}
.helper .helper_header{display:flex;align-items:center;padding:15px 20px;min-height:35px}
.helper .helper_header .title{margin:0 15px 0 0;line-height:1.2;}
.helper .helper_header .links{flex-shrink:0;margin-left:auto;}
.helper .helper_header .links a.link{display:inline-block;height:35px;line-height:35px;margin:0 5px;padding:0 10px;text-decoration:none;-webkit-border-radius:5px;border-radius:5px;-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.helper .helper_content .item{padding:0 20px;overflow:hidden;max-height:0;}
.helper .helper_content .item.active{padding-bottom:20px;max-height:unset}
.helper .helper_header .links a.link{color:#999;background:#fff}
.helper .helper_header .links a.link.active,.helper .helper_header .links a:hover.link{color:#999;background:rgba(0,0,0,.06)}

.column_helper .mcb-item-helper-inner {background:#f5f5f5}



/* heading */

.column_heading .title{margin-bottom:0}
.column_heading .title.mfn-mask-shape{-webkit-text-fill-color:transparent;-webkit-background-clip:text;background-position:center center}

/* Icon box 2 .mfn-icon-box */

.column_icon_box_2 a,
.column_icon_box_2 a:hover { text-decoration: none; color: inherit; }

.mfn-icon-box { display: flex; align-items: center; box-sizing: border-box; overflow: hidden; }
.mfn-icon-box .icon-wrapper { display: flex; align-items: center; justify-content: center; flex-shrink: 0; line-height: 0; overflow: hidden; }
.mfn-icon-box .icon-wrapper .icon-label{ line-height: 1.3em; font-size: 50px; color: var(--mfn-icon-box-icon); }
.mfn-builder-content .mfn-icon-box .icon-wrapper i { font-size: 7vh; line-height: 1; color: var(--mfn-icon-box-icon); }
.mfn-icon-box .desc-wrapper { flex-grow: 1; }
.mfn-icon-box .desc-wrapper *:last-child { margin-bottom: 0; }

    /* Left * Right */
    .mfn-icon-box-left .icon-wrapper { margin: 0 20px 0 0; }
    .mfn-icon-box-right .icon-wrapper { margin: 0 0 0 20px; }
    .mfn-icon-box-right .icon-wrapper { order: 2; }
    .mfn-icon-box-right .desc-wrapper { order: 1; }

    /* Top & Bottom */
    .mfn-icon-box-top,
    .mfn-icon-box-bottom { flex-direction: column; }
    .mfn-icon-box-top .desc-wrapper,
    .mfn-icon-box-bottom .desc-wrapper { width: 100%; text-align: center; }
    .mfn-icon-box-top .icon-wrapper { margin: 0 0 30px 0; }
    .mfn-icon-box-bottom .icon-wrapper { margin: 30px 0 0 0; }
    .mfn-icon-box-bottom .icon-wrapper { order: 2; }
    .mfn-icon-box-bottom .desc-wrapper { order: 1; }

    /* Width */
    .mfn-icon-box-top.mfn-icon-box-center .icon-wrapper,
    .mfn-icon-box-bottom.mfn-icon-box-center .icon-wrapper { width: 40%; }
    .mfn-icon-box-left .icon-wrapper,
    .mfn-icon-box-right .icon-wrapper { width: 25% }

    /* Alignment */
    .mfn-icon-box-start { align-items: flex-start; }
    .mfn-icon-box-center { align-items: center; }
    .mfn-icon-box-end { align-items: flex-end; }

    @media(max-width: 959px) {

        .mcb-wrap.sticky.sticky-desktop:not(.stickied) .animate{opacity: 1 !important;}
        .mcb-wrap.sticky.sticky-tablet:not(.stickied) .animate{opacity: 0 !important;}

        /* Left * Right */
        .mfn-icon-box-tablet-left,
        .mfn-icon-box-tablet-right { flex-direction: row; }
        .mfn-icon-box-tablet-left .icon-wrapper { margin: 0 20px 0 0; }
        .mfn-icon-box-tablet-right .icon-wrapper { margin: 0 0 0 20px; }
        .mfn-icon-box-tablet-left .icon-wrapper { order: 1; }
        .mfn-icon-box-tablet-left .desc-wrapper { order: 2; }
        .mfn-icon-box-tablet-right .icon-wrapper { order: 2; }
        .mfn-icon-box-tablet-right .desc-wrapper { order: 1; }
        /* Top & Bottom */
        .mfn-icon-box-tablet-top,
        .mfn-icon-box-tablet-bottom { flex-direction: column; }
        .mfn-icon-box-tablet-top .icon-wrapper { margin: 0 0 30px 0; }
        .mfn-icon-box-tablet-bottom .icon-wrapper { margin: 30px 0 0 0; }
        .mfn-icon-box-tablet-top .icon-wrapper { order: 1; }
        .mfn-icon-box-tablet-top .desc-wrapper { order: 2; }
        .mfn-icon-box-tablet-bottom .icon-wrapper { order: 2; }
        .mfn-icon-box-tablet-bottom .desc-wrapper { order: 1; }
        /* Width */
        .mfn-icon-box-tablet-top.mfn-icon-box-tablet-center .icon-wrapper,
        .mfn-icon-box-tablet-bottom.mfn-icon-box-tablet-center .icon-wrapper { width: 40%; }
        .mfn-icon-box-tablet-left .icon-wrapper,
        .mfn-icon-box-tablet-right .icon-wrapper { width: 25% }
        /* Alignment */
        .mfn-icon-box-tablet-start { align-items: flex-start; }
        .mfn-icon-box-tablet-center { align-items: center; }
        .mfn-icon-box-tablet-end { align-items: flex-end; }
    }
    @media(max-width: 767px) {

        .mcb-wrap.sticky.sticky-desktop:not(.stickied) .animate{opacity: 1 !important;}
        .mcb-wrap.sticky.sticky-tablet:not(.stickied) .animate{opacity: 1 !important;}

        .mcb-wrap.sticky.sticky-mobile:not(.stickied) .animate{opacity: 0 !important;}

        /* Left * Right */
        .mfn-icon-box-mobile-left,
        .mfn-icon-box-mobile-right { flex-direction: row; }
        .mfn-icon-box-mobile-left .icon-wrapper { margin: 0 20px 0 0; }
        .mfn-icon-box-mobile-right .icon-wrapper { margin: 0 0 0 20px; }
        .mfn-icon-box-mobile-left .icon-wrapper { order: 1; }
        .mfn-icon-box-mobile-left .desc-wrapper { order: 2; }
        .mfn-icon-box-mobile-right .icon-wrapper { order: 2; }
        .mfn-icon-box-mobile-right .desc-wrapper { order: 1; }
        /* Top & Bottom */
        .mfn-icon-box-mobile-top,
        .mfn-icon-box-mobile-bottom { flex-direction: column; }
        .mfn-icon-box-mobile-top .icon-wrapper { margin: 0 0 30px 0; }
        .mfn-icon-box-mobile-bottom .icon-wrapper { margin: 30px 0 0 0; }
        .mfn-icon-box-mobile-top .icon-wrapper { order: 1; }
        .mfn-icon-box-mobile-top .desc-wrapper { order: 2; }
        .mfn-icon-box-mobile-bottom .icon-wrapper { order: 2; }
        .mfn-icon-box-mobile-bottom .desc-wrapper { order: 1; }
        /* Width */
        .mfn-icon-box-mobile-top.mfn-icon-box-mobile-center .icon-wrapper,
        .mfn-icon-box-mobile-bottom.mfn-icon-box-mobile-center .icon-wrapper { width: 40%; }
        .mfn-icon-box-mobile-left .icon-wrapper,
        .mfn-icon-box-mobile-right .icon-wrapper { width: 25% }
        /* Alignment */
        .mfn-icon-box-mobile-start { align-items: flex-start; }
        .mfn-icon-box-mobile-center { align-items: center; }
        .mfn-icon-box-mobile-end { align-items: flex-end; }
    }

    /* Animations */
    .mfn-icon-box-box-scale:hover { transform: scale(1.04); }
    .mfn-icon-box-icon-scale:hover .icon-wrapper { transform: scale(1.04); }
    .mfn-icon-box-move-up:hover { transform: translateY(-10px); }

    .mfn-icon-box,
    .mfn-icon-box .icon-wrapper { transition: all 0.2s ease-in-out; }
    .mfn-icon-box .icon-wrapper i { transition: all 0.2s ease-in-out; }

/* icon_box */

.icon_box{position:relative;padding:10px 0}
.icon_box.has_border:after{content:"";display:block;width:0;height:100%;border-width:0 1px 0 0;border-style:solid;position:absolute;right:-2.4%;top:0}
.icon_box a{display:block}
.icon_box a:hover{text-decoration:none}
.icon_box .icon_wrapper{box-sizing:unset;width:110px;height:110px;font-size:50px;position:relative;overflow:hidden;margin:0 auto 15px;border-width:8px;border-style:solid;display:flex;align-items:center;justify-content:center;-webkit-border-radius:100%;border-radius:100%;background-image:url(../images/stripes/stripes_3_b.png);background-clip: content-box;-webkit-background-clip: content-box;}
.icon_box .icon_wrapper i{z-index:2;position:relative}
.icon_box .icon_wrapper:before{content:"";display:block;width:100%;height:100%;position:absolute;left:0;top:0;-webkit-border-radius:100%;border-radius:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.icon_box .icon_wrapper{background-color:#fff;-webkit-box-shadow:inset 0 0 7px 0 rgba(0,0,0,.08), 0px 0px 0px 1px red inset;box-shadow:inset 0 0 7px 0 rgba(0,0,0,.08), 0px 0px 0px 1px rgba(0,0,0,.05) inset;}
.icon_box .image_wrapper{text-align:center;margin-bottom:15px}
.icon_box .desc_wrapper{text-align:center}

	/* icon_position_left */

	.icon_box.icon_position_left{min-height:126px;padding-left:145px}
	.icon_box.icon_position_left .icon_wrapper{position:absolute;left:0;top:10px;margin-bottom:0}
	.icon_box.icon_position_left .desc_wrapper{text-align:left;padding-top:10px}
	.icon_box.icon_position_left .image_wrapper{position:absolute;left:0;top:10px;margin-bottom:0;width:126px;height:126px;overflow:hidden}

	/* animation */

	.icon_box .icon_wrapper,.icon_box .icon_wrapper:before{-webkit-transition:background-color .3s ease-in-out;-moz-transition:background-color .3s ease-in-out;-o-transition:background-color .3s ease-in-out;-ms-transition:background-color .3s ease-in-out;transition:background-color .3s ease-in-out}
	.icon_box .icon_wrapper{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1);transform:scale(1)}
	.icon_box:hover .icon_wrapper{-webkit-transform:scale(0.93);-moz-transform:scale(0.93);-ms-transform:scale(0.93);-o-transform:scale(0.94);transform:scale(0.94)}

	/* icon box & mfn-link */

	.icon_box a.mfn-link,.icon_box a.button{display:inline-block}

/* article_box */
.column_article_box .mcb-item-article_box-inner { overflow: hidden; }
.article_box{display:flex;overflow:hidden}
.article_box a{display:flex;width:100%}
.article_box a:hover{text-decoration:none}
.article_box .photo_wrapper{float:left;width:50%;line-height:0}
.article_box .desc_wrapper{display:flex;flex-direction:column;justify-content:center;width:50%;padding:10px 15px;background-color:#f8f8f8;background-image:url(../images/stripes/stripes_3_b.png);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.article_box .desc_wrapper p{margin-bottom:10px;border-bottom-width:1px;border-style:solid;padding-bottom:7px}
.article_box .desc_wrapper h4{margin-bottom:5px}
.article_box .desc_wrapper i.icon-right-open{color:var(--mfn-article-box-decoration);display:block;position:relative;left:-5px}

/* sliding_box */

.sliding_box{position:relative}
.sliding_box a{display:block}
.sliding_box a:hover{text-decoration:none}
.sliding_box .photo_wrapper{line-height:0;position:relative;bottom:-15px;-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1);transform:scale(1)}
.sliding_box .desc_wrapper{padding:15px 20px;width:100%;text-align:center;z-index:2;position:relative;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;background-color:var(--mfn-sliding-box-bg);}
.sliding_box .desc_wrapper:after{content:"";display:block;position:absolute;left:50%;top:0;margin-left:-8px;width:0;height:0;border-style:solid;border-width:0 8px 8px;border-color:transparent;border-bottom-color:var(--mfn-sliding-box-bg);}
.sliding_box .desc_wrapper h4{margin-bottom:0}

.sliding_box .photo_wrapper{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.sliding_box:hover .photo_wrapper{bottom:-1px}
.sliding_box .desc_wrapper:after{-webkit-transition:all .5s ease-in-out;-moz-transition:all .5s ease-in-out;-o-transition:all .5s ease-in-out;-ms-transition:all .5s ease-in-out;transition:all .5s ease-in-out}
.sliding_box:hover .desc_wrapper:after{top:-8px}

/* story_box */

.story_box{padding:0 11%}
.story_box a{display:block}
.story_box a,.story_box a:hover{text-decoration:none}
.story_box .photo_wrapper{line-height:0;overflow:hidden}
.story_box .photo_wrapper img{display:block;opacity:.7}
.story_box .desc_wrapper{margin-right:20%;position:relative;z-index:2}
.story_box .desc_wrapper h3{margin-left:-30px;margin-top:-26px}
.story_box .desc_wrapper hr{margin-left:0;width:20%}
.story_box .desc a{display:inline}
.story_box:hover .desc_wrapper hr{width:40%}
.story_box:hover .photo_wrapper img{display:block;opacity:1}
.story_box.vertical{padding:0 17%}

.story_box .desc_wrapper hr,.story_box .photo_wrapper img{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* promo_box */

.promo_box{position:relative}
.promo_box.has_border:after{content:"";display:block;width:0;height:100%;border-width:0 1px 0 0;border-style:solid;position:absolute;right:-2.4%;top:0}
.promo_box_wrapper{overflow:hidden}
.promo_box_wrapper .photo_wrapper{width:36%;float:left;text-align:left;line-height:0;overflow:hidden;}
.promo_box_wrapper .desc_wrapper{width:56%;margin:0 2% 0 6%;float:left;padding-top:10px}
.promo_box_wrapper .desc_wrapper .desc{margin-bottom:15px}
.promo_box_wrapper.promo_box_right .photo_wrapper{float:right;text-align:right}
.promo_box_wrapper.promo_box_right .desc_wrapper{margin:0 6% 0 2%;text-align:right}
.promo_box_wrapper.promo_box_right .desc_wrapper a.button{margin-right:0}

/* zoom_box */
.column_zoom_box .mcb-item-zoom_box-inner { text-align:center;line-height:0; overflow: hidden; }
.zoom_box{position:relative;line-height:0;overflow:hidden;max-width:100%}
.zoom_box .photo img,.zoom_box .desc{transition:all .3s ease-in-out}
.zoom_box .photo img{transform:scale(1)}
.zoom_box:hover .photo img{transform:scale(1.15)}
.zoom_box .desc{background:rgba(0,0,0,.8);width:100%;height:100%;position:absolute;left:0;top:0;text-align:center;opacity:0;transform:scale(1.15)}
.zoom_box .desc_wrap{position:absolute;width:100%;top:50%;transform:translateY(-50%)}
.zoom_box:hover .desc{opacity:1;transform:scale(1)}
.zoom_box .desc .desc_img img{max-width:30%!important;padding-bottom:15px}
.zoom_box .desc .desc_txt{padding:0 40px;font-size:26px;line-height:30px;color:#fff}

#Content .zoom_box .photo img{max-width:100.1%}

/* counter */

.counter{padding:20px;text-align:center;position:relative}
.column_counter .mcb-item-counter-inner {background-image:url(../images/stripes/stripes_10_b.png);}
.counter .icon_wrapper{margin-bottom:15px;line-height:0;width:100%}
.counter .icon_wrapper i:before{margin:0}
.counter .icon_wrapper i{display:inline-block;width:50px;height:50px;line-height:50px;font-size:50px}
.counter .desc_wrapper .number-wrapper{font-size:50px;line-height:50px;margin-bottom:5px}
.counter .desc_wrapper .number-wrapper .label{font-size:70%;line-height:100%}
.counter .desc_wrapper .title{font-size:15px;line-height:20px;margin:0}
.counter.counter_horizontal{text-align:left;min-height:75px}
.counter.counter_horizontal .icon_wrapper{width:50px;height:50px;position:absolute;left:30px;top:32px;margin:0}
.counter.counter_horizontal .desc_wrapper{padding-left:85px}

/* chart_box */

.chart_box{position:relative;text-align:center}
.chart_box:before{content:"";display:block;z-index:2;width:138px;height:138px;border-width:1px;border-style:solid;position:absolute;left:50%;margin-left:-69px;top:1px;-webkit-border-radius:100%;border-radius:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;background-color:#fff;background-image:url(../images/stripes/stripes_3_b.png)}
.chart_box .chart{width:140px;margin:0 auto 10px;position:relative}
.chart_box .chart canvas{position:relative;z-index:2}
.chart_box .chart .icon,.chart_box .chart .image,.chart_box .chart .num{width:100%;height:100%;display:flex;justify-content:center;align-items:center;overflow:hidden;position:absolute;left:0;top:0;z-index:3;}
.chart_box .chart .icon{font-size:45px}
.chart_box .chart .icon i:before{margin:0}
.chart_box .chart .image img{vertical-align:middle}
.chart_box .chart .num{font-size:50px;line-height:1;}
.chart_box p{margin-bottom:0}

/* progress_bars */

.progress_bars .bars_list{margin:0}
.progress_bars .bars_list li{margin-bottom:20px;list-style:none;overflow:hidden}
.progress_bars .bars_list li:last-child{margin-bottom:0}
.progress_bars .bars_list li h6{margin-bottom:7px;position:relative;padding-right:40px}
.progress_bars .bars_list li h6 .label{display:block;font-size:11px;line-height:17px;padding:0 5px;font-weight:400;position:absolute;right:0;top:1px;-webkit-border-radius:3px;border-radius:3px;background:rgba(0,0,0,.05);color:rgba(0,0,0,.35)}
.progress_bars .bars_list li h6 .label em{font-style:normal}
.progress_bars .bars_list li .bar{position:relative;width:100%;height:20px;-webkit-border-radius:3px;border-radius:3px;overflow:hidden;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;background:rgba(0,0,0,.02);-webkit-box-shadow:inset 0 0 2px 2px rgba(0,0,0,0.04);box-shadow:inset 0 0 2px 2px rgba(0,0,0,0.04)}
.progress_bars .bars_list li .bar .progress{display:block;height:100%;-webkit-box-shadow:inset 0 0 2px 2px rgba(0,0,0,0.04);box-shadow:inset 0 0 2px 2px rgba(0,0,0,0.04); background-image: repeating-linear-gradient(45deg, transparent, transparent 6px, rgba(0,0,0,.07) 6px, rgba(0,0,0,.07) 14px);}
.progress_bars .bars_list:not(.hover) li .bar .progress{width:0!important}

.progress_bars .bars_list.hover li .bar .progress{-webkit-transition:all 1.3s ease-in-out;-moz-transition:all 1.3s ease-in-out;-o-transition:all 1.3s ease-in-out;transition:all 1.3s ease-in-out}

/* progress_icons */

.progress_icons{margin-bottom:12px;overflow:hidden}
.progress_icons .progress_icon{display:inline-flex;align-items:center;justify-content:center;margin:0 7px 7px 0;width:35px;height:35px;font-size:17px;color:#fff;-webkit-border-radius:100%;border-radius:100%}
.progress_icons .progress_icon img{max-width:25px!important;max-height:25px;margin:5px 0 -5px}
.progress_icons .progress_icon:last-child{margin-right:0}
.progress_icons .progress_icon:not(.themebg){background:rgba(0,0,0,.06)}

.progress_icons .progress_icon{-webkit-transition:all .4s ease-in-out;-moz-transition:all .4s ease-in-out;-o-transition:all .4s ease-in-out;transition:all .4s ease-in-out}

.progress_icons.transparent .progress_icon{background-color:transparent!important;width:17px;height:17px;color:rgba(0,0,0,.15);border-color:transparent!important}

/* trailer_box */

.trailer_box{text-align:center;position:relative;line-height:0}
.trailer_box a{display:block}
.trailer_box a:after{content:"";display:block;position:absolute;left:0;bottom:0;z-index:1;width:100%;height:100%;opacity:0;background:rgba(0,0,0,.15)}
.trailer_box:hover a:after{opacity:100}
.trailer_box .desc{position:absolute;left:0;bottom:0;z-index:2;width:100%;padding:20px 15px 35px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.trailer_box .desc h2{margin:0;position:relative;bottom:0;font-size:30px;line-height:30px}
.trailer_box:hover .desc h2{bottom:20px}
.trailer_box .desc .subtitle{display:inline-block;position:relative;bottom:0;line-height:normal;letter-spacing:1px;padding:4px 8px;text-transform:uppercase;margin-bottom:15px;-webkit-border-radius:3px;border-radius:3px;background-image:url(../images/stripes/stripes_10_w.png)}
.trailer_box:hover .desc .subtitle{bottom:20px}
.trailer_box .desc .line{height:1px;width:0;margin:0 auto;background:#fff;overflow:hidden;display:block}
.trailer_box:hover .desc .line{width:60%}

.trailer_box.plain{text-align:left}
.trailer_box.plain .desc{padding:20px 14% 40px}
.trailer_box.plain .desc .subtitle{padding:0;background-color:transparent;font-weight:900;font-style:italic}
.trailer_box.plain .desc h2{font-weight:700}
.trailer_box.plain .desc .line{margin:0;height:2px}
.trailer_box.plain:hover .desc .line{width:20%}

.trailer_box.horizontal .desc{padding-bottom:20px}
.trailer_box.horizontal .desc .subtitle{margin-bottom:10px}
.trailer_box.horizontal:hover .desc h2{bottom:10px}
.trailer_box.horizontal:hover .desc .subtitle{bottom:10px}

.trailer_box a:after,.trailer_box .desc .subtitle,.trailer_box .desc .line,.trailer_box .desc h2{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* quick_fact */

.quick_fact{text-align:center}
.quick_fact.align_left{text-align:left}
.quick_fact.align_right{text-align:right}
.quick_fact .number-wrapper{font-size:90px;line-height:1.3em}
.quick_fact .number-wrapper .label{font-size:70%;line-height:100%}
.quick_fact hr{width:40%;display:inline-block;}

/* countdown */
html .mcb-section .mcb-wrap .column_countdown .column .mcb-column-inner{ border: none; }
.downcount .mcb-column-inner{margin-bottom:0}

/* countdown-inline */

.downcount-inline .label{margin:0 4px}
.downcount-inline.show-dhm .seconds,
.downcount-inline.show-dhm .label-seconds{display:none}
.downcount-inline.show-dh .seconds,
.downcount-inline.show-dh .label-seconds,
.downcount-inline.show-dh .minutes,
.downcount-inline.show-dh .label-minutes{display:none}
.downcount-inline.show-d .seconds,
.downcount-inline.show-d .label-seconds,
.downcount-inline.show-d .minutes,
.downcount-inline.show-d .label-minutes,
.downcount-inline.show-d .hours,
.downcount-inline.show-d .label-hours{display:none}

/* photo_box */

.photo_box{text-align:center}
.photo_box.pb_left{text-align:left}
.photo_box.pb_right{text-align:right}
.photo_box .image_frame{margin-bottom:15px}
.photo_box.without-desc .image_frame{margin-bottom:0}
.photo_box .desc{margin-bottom:15px}

/* flat_box */

.flat_box a,.flat_box a:hover{display:block;text-decoration:none}
.flat_box .photo_wrapper{margin-bottom:15px;position:relative;line-height:0;overflow:hidden}
.flat_box .photo_wrapper .icon{position:absolute;left:0;top:0;height:100%;width:90px;text-align:center;color:#fff;z-index:2}
.flat_box .photo_wrapper .icon i{display: block; font-size: 35px; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%);}
.flat_box .photo_wrapper .icon img{position:absolute;left:50%;top:50%;max-width:calc(100% - 16px)!important;transform:translate(-50%,-50%);}
.flat_box:hover .photo_wrapper .icon,.flat_box a:hover .photo_wrapper .icon{transform:translateX(-90px)}
.flat_box .photo_wrapper img.photo{position:relative;display:block;z-index:1;transform:translateX(15px)}
.flat_box:hover .photo_wrapper img.photo,.flat_box a:hover .photo_wrapper img.photo{transform:translateX(0px)}
.flat_box .desc_wrapper{margin-left:90px}

@media only screen and (min-width: 960px) {
	.one-fourth .flat_box .photo_wrapper .icon{width:60px}
	.one-fourth .flat_box .photo_wrapper .icon i{height:40px;line-height:40px;font-size:35px;margin-top:-20px}
	.one-fourth .flat_box a:hover .photo_wrapper .icon{-webkit-transform:translateX(-60px);transform:translateX(-60px)}
	.one-fourth .flat_box .desc_wrapper{margin-left:60px}
}

.flat_box .photo_wrapper .icon{-webkit-transition:all .4s ease-in-out;-moz-transition:all .4s ease-in-out;-o-transition:all .4s ease-in-out;transition:all .4s ease-in-out}
.flat_box .photo_wrapper img{-webkit-transition:all .4s ease-in-out;-moz-transition:all .4s ease-in-out;-o-transition:all .4s ease-in-out;transition:all .4s ease-in-out}

/* feature_box */

.column_feature_box .mcb-item-feature_box-inner { padding: 20px; overflow: hidden; }
.feature_box .feature_box_wrapper .photo_wrapper,
.feature_box .feature_box_wrapper .desc_wrapper{display:inline-block;vertical-align:middle}
.feature_box .feature_box_wrapper .photo_wrapper{width:50%;line-height: 0;}
.feature_box .feature_box_wrapper .photo_wrapper a{display:block;line-height:0}
.feature_box .feature_box_wrapper .desc_wrapper{width:50%;padding-left:4%;box-sizing: border-box;}

/* hover_box */

.hover_box{text-align:center;line-height:0}
.hover_box a{display:block;line-height:0}
.hover_box .hover_box_wrapper{position:relative;overflow:hidden;line-height:0;max-width:100%}
.hover_box .hover_box_wrapper .visible_photo{display:inline-block;opacity:1}
.hover_box .hover_box_wrapper .hidden_photo{display:block;position:absolute;left:50%;top:50%;opacity:0;transform:translate(-50%,-50%)}
.hover_box:hover .hover_box_wrapper .visible_photo,.hover_box.hover .hover_box_wrapper .visible_photo{opacity:0}
.hover_box:hover .hover_box_wrapper .hidden_photo,.hover_box.hover .hover_box_wrapper .hidden_photo{opacity:1}

.hover_box .hover_box_wrapper .visible_photo,.hover_box .hover_box_wrapper .hidden_photo{transition:all .4s ease-in-out}

/* hover_color */

.hover_color{text-align:center;border-color:transparent}
.hover_color a{display:block}
.hover_color a:hover{text-decoration:none}
.hover_color .hover_color_bg{box-sizing:border-box}
.hover_color .hover_color_wrapper{text-decoration:none;padding:40px 30px}
.hover_color,.hover_color a,.hover_color a:hover,.hover_color h1 a,.hover_color h2 a,.hover_color h3 a,.hover_color h4 a,.hover_color h5 a,.hover_color h6 a{color:#fff}
.hover_color .hover_color_bg{-webkit-transition:all .4s ease-in-out;-moz-transition:all .4s ease-in-out;-o-transition:all .4s ease-in-out;transition:all .4s ease-in-out}
.hover_color,.hover_color_bg{height:100%}
.hover_color .hover_color_wrapper{-webkit-transition:all .4s ease-in-out;-moz-transition:all .4s ease-in-out;-o-transition:all .4s ease-in-out;transition:all .4s ease-in-out}

.hover_color.align_left{text-align:left}
.hover_color.align_right{text-align:right}
.hover_color.align_center{text-align:center}
.hover_color.align_justify{text-align:justify}

/* content_link */

a.content_link{display:inline-block;position:relative;border-width:1px;border-style:solid;width:140px;padding:25px 15px;text-align:center;margin-left:-1px;background-color:#fff}
a.content_link .icon{display:block;font-size:45px;line-height:45px;margin-bottom:15px}
a.content_link .title{font-weight:700}
a.content_link:after{content:"";z-index:2;opacity:0;height:0;width:100%;border-width:0 0 5px;border-style:solid;position:absolute;left:0;bottom:-6px}
a.content_link:before{content:"";z-index:2;opacity:0;position:absolute;left:50%;bottom:-1px;margin-left:-6px;width:0;height:0;border-style:solid;border-width:0 6px 6px;border-color:transparent}
a:hover.content_link:after{opacity:100}
a:hover.content_link:before{opacity:100}
a:hover.content_link{text-decoration:none;background-image:url(../images/stripes/stripes_3_b.png)}
a.content_link:after,a.content_link:before{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* icon_bar */

a.icon_bar{display:inline-block;overflow:hidden;text-align:center;margin:0 2px 2px 0;border-width:1px;border-style:solid;position:relative;background-color:#fff;background-image:url(../images/stripes/stripes_3_b.png);-webkit-box-shadow:inset 0 0 5px 0 rgba(0,0,0,.08);box-shadow:inset 0 0 5px 0 rgba(0,0,0,.08)}
a.icon_bar span{display:block}
a.icon_bar,a.icon_bar span{width:58px;height:58px;line-height:58px;font-size:20px}
a.icon_bar_small,a.icon_bar_small span{width:43px;height:43px;line-height:43px;font-size:15px}
a.icon_bar span.t{position:relative}
a.icon_bar span.b{position:absolute;top:100%}
a.icon_bar span{-webkit-transition:-webkit-transform .3s;-moz-transition:-moz-transform .3s;transition:transform .3s}
a.icon_bar span.b{-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}
a:hover.icon_bar span{-webkit-transform:translateY(-100%);-moz-transform:translateY(-100%);transform:translateY(-100%)}
.wpb_wrapper a.icon_bar{margin-bottom:2px}

/* social */

a.icon_bar_facebook span.b{background:#3a589b;color:#fff}
a.icon_bar_google span.b{background:#d6492f;color:#fff}
a.icon_bar_twitter span.b{background:#32ccfe;color:#fff}
a.icon_bar_vimeo span.b{background:#1ca7cc;color:#fff}
a.icon_bar_youtube span.b{background:#ff3334;color:#fff}
a.icon_bar_flickr span.b{background:#ff0084;color:#fff}
a.icon_bar_linkedin span.b{background:#007bb6;color:#fff}
a.icon_bar_pinterest span.b{background:#cb2027;color:#fff}
a.icon_bar_dribbble span.b{background:#ec4a89;color:#fff}
a.icon_bar_instagram span.b{background:#e13967;color:#fff}

/* get_in_touch */

.get_in_touch{}
.get_in_touch ul{margin:0;font-size:100%;line-height:normal}
.get_in_touch ul li{margin:0;list-style:none;position:relative;padding:13px 0 13px 50px;word-wrap:break-word}
.get_in_touch ul li:after{content:"";display:block;width:70px;border-width:0 0 1px;border-style:solid;border-color:var(--mfn-contactbox-line);position:absolute;left:-30px;bottom:0}
.get_in_touch ul li p{margin:0}
.get_in_touch ul li .icon{position:absolute;left:0;top:5px;display:block;color:rgba(0,0,0,.7);width:30px;height:30px;line-height:30px;font-size:23px;text-align:center}
.get_in_touch ul li:last-child:after{display:none}

.column_contact_box .mcb-item-contact_box-inner,
.column_column .get_in_touch {padding:25px 20px 25px 30px;background-repeat:no-repeat;background-position:right bottom}

/* infobox */

.infobox{}
.infobox ul{margin:0;font-size:100%;line-height:normal}
.infobox ul li{margin:0;list-style:none;position:relative;padding:13px 0 13px 50px;word-wrap:break-word}
.infobox ul li p{margin:0}
.infobox ul li:before{content: '\e841'; font-family: "mfn-icons"; color: rgba(0,0,0,.7); font-size: 20px; display:block;width:16px;height:16px;position:absolute;left:0;top:10px;}
.infobox ul li:after{content:"";display:block;width:70px;border-width:0 0 1px;border-style:solid;border-color:var(--mfn-infobox-line);position:absolute;left:-30px;bottom:0}
.infobox ul li:last-child:after{display:none}
.infobox .ib-desc { display: block; }

.column_info_box .mcb-item-info_box-inner {padding:25px 20px 25px 30px;background-repeat:no-repeat;background-position:right bottom}

/* opening_hours */
.opening_hours .opening_hours_wrapper .ohw-desc {display:block;}
.opening_hours .opening_hours_wrapper ul{margin:0}
.opening_hours .opening_hours_wrapper li{position:relative;padding-bottom:15px;margin-bottom:15px;text-align:center;border-bottom-width:1px;border-style:dashed;list-style:none}
.opening_hours .opening_hours_wrapper li:last-child{border:0;padding-bottom:0;margin-bottom:0}
.opening_hours .opening_hours_wrapper li label{margin-bottom:8px;font-size:12px;line-height:18px;display:inline-block;font-weight:400;padding:5px 11px;-webkit-border-radius:4px;border-radius:4px;background-image:url(../images/stripes/stripes_3_b.png);background-color:#f8f8f8}
.opening_hours .opening_hours_wrapper li span{font-size:22px;line-height:22px;margin:0;padding:0 14px;display:block}
.opening_hours .opening_hours_wrapper li span sup{font-size:13px;line-height:13px;position:relative;top:4px;margin-left:2px}

.column_opening_hours .mcb-item-opening_hours-inner { padding:25px;border-width:1px;border-style:solid;background-color:#fff;background-repeat:no-repeat;background-position:right bottom }

.get_in_touch, .infobox, .opening_hours{height:100%;box-sizing:border-box}

/* timeline_items */

.timeline_items{margin:0!important;position:relative;background:url(../images/timeline_top.png) no-repeat top center;padding-top:9px;box-sizing:content-box}
.timeline_items:after{content:"";width:0;height:0;border-width:3px;border-style:solid;-webkit-border-radius:100%;border-radius:100%;position:absolute;left:50%;bottom:-10px;margin-left:-3px;display:block;z-index:1}
.timeline_items > li{margin:0!important;list-style:none!important;width:45%;padding:0 0 25px 55%;position:relative;background:url(../images/timeline_right.png) no-repeat top center}
.timeline_items > li:nth-child(even){padding:0 55% 25px 0;background:url(../images/timeline_left.png) no-repeat top center}

.timeline_items > li h3{font-size:30px;line-height:35px}
.timeline_items > li h2{font-size:30px;line-height:35px}
.timeline_items > li h3 span{position:absolute;right:55%;top:8px;}
.timeline_items > li h2 span{position:absolute;right:55%;top:8px;}
.timeline_items > li h3:before{content:"";width:7px;height:7px;border-width:4px;border-style:solid;-webkit-border-radius:100%;border-radius:100%;position:absolute;left:50%;top:11px;margin-left:-7px;display:block;z-index:1}
.timeline_items > li h2:before{content:"";width:7px;height:7px;border-width:4px;border-style:solid;-webkit-border-radius:100%;border-radius:100%;position:absolute;left:50%;top:11px;margin-left:-7px;display:block;z-index:1}
.timeline_items > li:nth-child(even) h3{text-align:right}
.timeline_items > li:nth-child(even) h2{text-align:right}
.timeline_items > li:nth-child(even) h3 span{left:55%;right:auto}
.timeline_items > li:nth-child(even) h2 span{left:55%;right:auto}
.timeline_items > li .desc{position:relative;font-size:15px;line-height:31px;}
body:not(.style-simple) .timeline_items > li .desc { text-decoration: underline; text-decoration-color: rgba(0,0,0,.1); text-underline-offset: 8px; text-decoration-thickness: 1px; }
.timeline_items > li .desc:before{content:"";width:100%;height:100%;position:absolute;left:-122%;top:0;background-image:url(../images/stripes/stripes_10_b.png)}
.timeline_items > li:nth-child(even) .desc:before{left:auto;right:-122%}
.timeline_items > li:nth-child(even) .desc{text-align:right}

.timeline_items > li .desc p{margin-bottom:0}
.timeline_items > li .desc ol{list-style-position:inside}
.timeline_items > li .desc ul{list-style:disc inside none}

/* how_it_works */

.how_it_works{text-align:center;position:relative}
.how_it_works.has_border:after{content:"";display:block;width:12%;height:4px;background:rgba(0,0,0,.03);-webkit-border-radius:2px;border-radius:2px;position:absolute;right:-10.5%;top:100px}
.how_it_works .image_wrapper{position:relative;margin:0 auto 15px;width: 200px;}
.how_it_works .image_wrapper .number{display: inline-flex; justify-content: center; align-items: center;position:absolute;right:10px;bottom:10px;width:35px;height:35px;font-size:15px;text-align:center;border-radius:100%;-webkit-box-shadow:inset 0 0 0 2px rgba(0,0,0,.1);box-shadow:inset 0 0 0 2px rgba(0,0,0,.1)}
.how_it_works .image{display:flex;align-items:center;justify-content:center;overflow:hidden;width:196px;height:196px;text-align:center;position:relative;background:#fff;border-width:2px;border-style:solid;border-radius:100%}
.how_it_works .image img{max-width:116px!important;max-height:116px!important;width:auto;}

.with_aside .how_it_works .image{width:146px;height:146px;}
.with_aside .how_it_works .image img{max-width:90px!important;max-height:90px!important}
.with_aside .how_it_works .image_wrapper .number{bottom:4px;right:4px}
.with_aside .how_it_works.has_border:after{top:75px}

.how_it_works.fill .image img,
.with_aside .how_it_works.fill .image img {max-width: 100% !important; max-height: 100% !important; border-radius: unset;}

.how_it_works.no-img .image{height:55px;background:none;border-width:0}
.how_it_works.no-img .image_wrapper .number{left:50%;margin-left:-18px}

/* column_map_basic */

.column_map_basic{line-height:0;text-align:center}
.column_map_basic .mcb-item-map_basic-inner{overflow:hidden}
.column_map_basic iframe{width:100%;line-height:normal}

/* map advanced */

.google-map-wrapper{position:relative}

.google-map{height:400px;border-width:8px;border-style:solid;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.google-map-wrapper.no_border .google-map,.section.full-width .one .google-map{border-width:0}

.google-map-contact-wrapper{position:absolute;z-index:10;right:50px;top:50px;}
.section.full-width .wrap.one .column_map.one .google-map-contact-wrapper{left:50%;margin-left:300px;right:auto;}
.google-map-contact-wrapper .get_in_touch{width:210px;padding:25px;position:relative}
.google-map-contact-wrapper .get_in_touch:after{content:"";display:block;position:absolute;left:0;bottom:-30px;width:0;height:0;border-style:solid;border-width:30px 30px 0 0;border-color:transparent;opacity:0}
.google-map-contact-wrapper .get_in_touch ul li:after {left:-25px;}

.google-map-contact-wrapper.style-bar{position:static;margin-left:0!important;}
.google-map-contact-wrapper.style-bar .get_in_touch{float:none;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box}
.google-map-contact-wrapper.style-bar .get_in_touch:after{display:none;}

/* team */

.team .image_frame{margin-bottom:15px}
.team .desc_wrapper h4{margin-bottom:3px}
.team .desc_wrapper hr{display:inline-block;width:40%;}
.team .desc_wrapper .desc{margin-bottom:15px}
.team .desc_wrapper .links{line-height:0}
.team .desc_wrapper .links a.icon_bar span { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; }
.team.team_horizontal{overflow:hidden}
.team.team_horizontal .image_frame{width:48%;float:left;margin-right:4%;margin-bottom:0}
.team.team_horizontal .desc_wrapper{width:48%;float:left}
.team .desc_wrapper blockquote{margin-top:20px}
.team.team_circle{text-align:center}
.team.team_circle .desc_wrapper hr{margin-left:auto}
.team.team_circle .image_frame.photo{width:120px;height:120px;margin:0 auto 15px;-webkit-border-radius:100%;border-radius:100%;overflow:hidden;position:relative;z-index:1}

/* team_list */

.team_list .column{margin-bottom:0}
.team_list .bq_wrapper{background:rgba(0,0,0,.02);border-left-width:1px;border-style:solid;padding:20px}

/* mfn_heading */

.mfn_heading.align_left{text-align:left}
.mfn_heading.align_center{text-align:center}
.mfn_heading.align_right{text-align:right}
.mfn_heading .title{display:inline-block;position:relative}

.mfn_heading.heading_lines{overflow:hidden}
.mfn_heading.heading_lines .title .line{position:absolute;width:3000px;height:1px;top:50%;background:rgba(0,0,0,.3)}
.mfn_heading.heading_lines .title .line_l{right:100%;margin-right:20px}
.mfn_heading.heading_lines .title .line_r{left:100%;margin-left:20px}

.dark .mfn_heading.heading_lines .title .line{background:rgba(255,255,255,.3)}

/* fancy_heading */

.fancy_heading{text-align:center}
.fancy_heading .title{font-size:42px;line-height:42px}
.fancy_heading_icon .icon_top{font-size:50px;line-height:50px;margin-bottom:15px;overflow:hidden;display:block}
.fancy_heading_icon .icon_top i:before{margin:0}
.fancy_heading_line{background-image:url(../images/fancy_heading_hr.png);background-position:bottom center;background-repeat:no-repeat;padding-bottom:15px}
.fancy_heading_line .slogan{display:block;margin-bottom:7px;text-transform:uppercase;letter-spacing:1px;font-size:14px}
.fancy_heading_line .inside{margin-bottom:15px}

/* call_to_action */

.column_call_to_action{margin-bottom:0}
.call_to_action{overflow:hidden;display:table;padding:40px 0;width:100%}
.call_to_action .call_to_action_wrapper{display:table;width:100%}
.call_to_action .call_left,.call_to_action .call_center,.call_to_action .call_right{display:table-cell;vertical-align:middle;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.call_to_action .call_left{width:40%;padding:0 20px;text-align:center}
.call_to_action .call_left h3{margin-bottom:0}
.call_to_action .call_center{width:20%;text-align:center}
.call_to_action .call_center a{display:inline-block}
.call_to_action .call_center .icon_wrapper{position:relative;display:inline-flex;align-items:center;justify-content:center;border-width:3px;border-style:solid;width:65px;height:65px;border-radius:100%;box-sizing:unset}
.call_to_action .call_center .icon_wrapper i{display:inline-block;font-size:30px}
.call_to_action .call_center .icon_wrapper img{display:inline-block;position:relative;top:50%;transform:translateY(-50%);margin-top:-1px;font-size:30px}
.call_to_action .call_center .button{margin:0;}
.call_to_action .call_right{width:40%;padding:0 20px}

/* clients */

ul.clients{margin:0}
ul.clients li{float:left;width:16.666%;margin:0!important;list-style:none}
ul.clients .client_wrapper{padding:10px 15px;margin:10px;background:rgba(0,0,0,.01);text-align:center;line-height:0;height:75px;position:relative}
ul.clients .client_wrapper:hover{background:rgba(0,0,0,.05)}
ul.clients .client_wrapper a,
ul.clients .client_wrapper .gs-wrapper{display:flex;justify-content:center;align-items:center;width:100%;height:100%;}
ul.clients .client_wrapper .gs-wrapper img {max-height:100%;width: auto;}
ul.clients .client_wrapper .gs-wrapper img[src$=".svg"] { width: 100%; }

ul.clients.clients_tiles li .client_wrapper{background:none}
ul.clients.clients_tiles li .client_wrapper:before{content:"";display:block;width:100%;height:1px;background:var(--mfn-clients-tiles);position:absolute;left:0;bottom:0;z-index:2}
ul.clients.clients_tiles li .client_wrapper:after{content:"";display:none;position:absolute;left:50%;margin-left:-4px;bottom:0;width:0;height:0;border-style:solid;border-width:0 4px 5px;border-color:transparent}
ul.clients.clients_tiles li .client_wrapper:hover:before{height:2px;background:var(--mfn-clients-tiles-hover);}
ul.clients.clients_tiles li .client_wrapper:hover:after{display:block;bottom:2px;border-bottom-color:var(--mfn-clients-tiles-hover);}
ul.clients li .client_wrapper,ul.clients li .client_wrapper img{-webkit-transition:all .4s ease-in-out;-moz-transition:all .4s ease-in-out;-o-transition:all .4s ease-in-out;-ms-transition:all .4s ease-in-out;transition:all .4s ease-in-out}

/* clients_slider */

.clients_slider ul{opacity:0;max-height:115px;transition: opacity 0.3s ease-in-out;}
.clients_slider ul.slick-slider{opacity:1;max-height:none}
.clients_slider ul.slick-slider li img {max-height:100%;width: auto;}
.clients_slider .slick-arrow { margin: 0; min-width: 40px; min-height: 40px; display: inline-flex; align-items: center; justify-content: center; padding: 5px; }


[data-navigation-position="content"] .clients_slider_ul { display: flex; align-items: center; }
[data-navigation-position="content"] .clients_slider_ul .slick-arrow { margin: 0; flex-shrink: 0; }
[data-navigation-position="content"] .clients_slider_ul .slick-list { width: 100%; margin: 0 15px; }

/* blog_slider */

.blog_slider{position:relative}

.blog_slider_header{display: flex;justify-content:space-between;align-items:center;margin-bottom:15px}
.blog_slider_header .title{margin:0 15px 0 0;}
.blog_slider_header .slider_navigation{display:flex;flex-shrink:0;margin-left:auto;}
.blog_slider_header .slick-arrow{margin:0}
.blog_slider_header .slider_next{margin-left:5px}

.blog_slider .blog_slider_ul{margin:0!important;opacity:0;max-height:219px;transition: opacity 0.3s ease-in-out;}
.blog_slider .blog_slider_ul.slick-slider{opacity:1;max-height:none}
.blog_slider .blog_slider_ul li{float:left;width:25%;list-style:none;margin:0!important}
.blog_slider .blog_slider_ul li .item_wrapper{margin:0 10px;position:relative}
.blog_slider .blog_slider_ul li .item_wrapper .image_frame{margin-left:30px;margin-bottom:15px;min-height:52px}
.blog_slider .blog_slider_ul li .item_wrapper .date_label{position:absolute;left:0;top:30px;z-index:20;background-color:var(--mfn-blog-slider-date-bg);}
.blog_slider .blog_slider_ul li .item_wrapper .date_label:after{border-left-color:var(--mfn-blog-slider-date-bg);}
.blog_slider .blog_slider_ul li .item_wrapper hr{margin-left:30px;width:40%}
.blog_slider .blog_slider_ul li .item_wrapper a.button{margin-left:30px;margin-bottom:0}
.blog_slider .blog_slider_ul li.format-quote .item_wrapper{overflow:hidden}
.blog_slider .blog_slider_ul li.format-quote .item_wrapper blockquote{margin-top:70px;margin-left:40px}
.blog_slider .blog_slider_ul li.format-link .item_wrapper .image_frame{height:180px; position: relative; background-color:rgba(255,255,255,.5);}
.blog_slider .blog_slider_ul li.format-link .item_wrapper .image_frame:after {content: '\e8c2'; font-family: "mfn-icons"; display: block; position: absolute; left: 0; top:0; width: 100%; height: 180px; line-height: 180px;text-align: center; font-size: 40px; color: rgba(0,0,0,.1); }

.blog_slider .slider_pager{margin-top:15px}
.blog_slider .slider_pager li{display:inline-block}

.blog_slider.hide-dots .slider_pager{display:none!important}
.blog_slider.hide-arrows .blog_slider_header a.button{display:none!important}
.blog_slider.hide-nav .slider_pager,
.blog_slider.hide-nav .blog_slider_header a.button{display:none!important}

	/* blog_slider flat */

	.blog_slider.flat .blog_slider_ul li .item_wrapper .image_frame{margin-left:0;border:0}
	.blog_slider.flat .blog_slider_ul li .item_wrapper .date_label{position:static;background-color:transparent!important;background-image:none;display:inline-block;padding:0}
	.blog_slider.flat .blog_slider_ul li .item_wrapper .date_label:after{display:none}
	.blog_slider.flat .blog_slider_ul li .item_wrapper .desc hr{margin-left:0;width:0}
	.blog_slider.flat .blog_slider_ul li .item_wrapper .desc a.button{display:none;margin-left:0}
	.blog_slider.flat .blog_slider_ul li .item_wrapper:hover .desc hr{width:100px}
	.blog_slider.flat .blog_slider_ul li .item_wrapper .image_wrapper img,.blog_slider.flat .blog_slider_ul li .item_wrapper .image_wrapper img{opacity:.9}
	.blog_slider.flat .blog_slider_ul li .item_wrapper:hover .image_wrapper img,.blog_slider.flat .blog_slider_ul li .item_wrapper:hover .image_wrapper img{top:0;opacity:1}

	.blog_slider.flat .blog_slider_header{margin:0}
	.blog_slider.flat .blog_slider_header .title{display:none}

	.blog_slider.flat .blog_slider_header .slick-arrow{position:absolute;width:60px;height:100%;border:0;border-radius:0;background-color:rgba(255,255,255,.4)!important;box-shadow:inset 0 0 0 0;background-image:none}
	.blog_slider.flat .blog_slider_header .slick-arrow:hover{background-color:rgba(255,255,255,.7)!important}
	.blog_slider.flat .blog_slider_header .slick-arrow:after{display:none}
	.blog_slider.flat .blog_slider_header .slick-arrow .button_icon{float:none;font-size:15px;padding:0;text-align:center;position:absolute;width:60%;top:50%;margin-top:-4px;background:none}
	.blog_slider.flat .blog_slider_header .slick-arrow .button_icon i{color:#000!important}
	.blog_slider.flat .blog_slider_header .slick-arrow .button_icon:after{display:none}
	.blog_slider.flat .blog_slider_header .button.slider_prev{left:9px;right:auto;z-index:2}
	.blog_slider.flat .blog_slider_header .button.slider_next{right:9px;z-index:2}

	.blog_slider.flat .blog_slider_header{opacity:0}
	.blog_slider.flat:hover .blog_slider_header{opacity:1}

	.blog_slider.flat .blog_slider_header,
	.blog_slider.flat .blog_slider_header .slick-arrow,
	.blog_slider.flat .blog_slider_ul li .item_wrapper .desc hr{transition:all .3s ease-in-out}

/* shop_slider */

.shop_slider .shop_slider_ul{margin:0!important;opacity:0;max-height:219px;transition: opacity 0.3s ease-in-out;}
.shop_slider .shop_slider_ul.slick-slider{opacity:1;max-height:none}
.shop_slider .shop_slider_ul li{float:left;width:25%;list-style:none}
.shop_slider .shop_slider_ul li .item_wrapper{margin:0 10px;position:relative}
.shop_slider .shop_slider_ul li .item_wrapper .desc{background:#fff;padding:15px 20px}
.shop_slider .shop_slider_ul li .item_wrapper .desc h4{margin-bottom:5px}
.shop_slider .shop_slider_ul li .item_wrapper span.onsale{ display: block; position:absolute; left: 15px !important; right: auto !important; top: 15px !important; z-index:9; min-width: 0; min-height: 0; color: #fff; font-size: 12px; line-height: 18px; font-weight: 500; text-transform: uppercase; width: auto; height: auto; padding: 3px 8px; border-radius: 3px; margin: 0 !important; }
.shop_slider .shop_slider_ul li.sale .item_wrapper span.onsale.mfn-new-badge { top: 42px !important; }
.shop_slider .shop_slider_ul li .item_wrapper .price{font-size:18px;margin-bottom:0}
.shop_slider .shop_slider_ul li .item_wrapper .price del{font-size:.67em}

.shop_slider .slider_pagination{margin-top:15px}
.shop_slider .slider_pager li{display:inline-block}

/* portfolio_slider */

.portfolio_slider{position:relative;}
.portfolio_slider ul{margin:0!important;opacity:0;max-height:219px;transition: opacity 0.3s ease-in-out;}
.portfolio_slider ul.slick-slider{opacity:1;max-height:none}
.portfolio_slider ul li{float:left;width:20%;list-style:none;margin:0!important}
.portfolio_slider ul li .image_frame{border:0;margin-bottom:0}
.portfolio_slider .slider_nav{opacity:0;color:#fff;font-size:15px;height:80px;line-height:80px;position:absolute;text-align:center;z-index:5;width:35px;top:50%;margin-top:-40px}
.portfolio_slider .slider_prev{left:0}
.portfolio_slider .slider_next{right:0}
.portfolio_slider.arrows_hover:hover .slider_nav{opacity:1;}
.portfolio_slider.arrows_always .slider_nav{opacity:1;}
.portfolio_slider .slider_nav{-webkit-transition:opacity .3s ease-in-out;-moz-transition:opacity .3s ease-in-out;-o-transition:opacity .3s ease-in-out;transition:opacity .3s ease-in-out}

/* portfolio_grid */

ul.portfolio_grid{overflow:hidden;list-style:none;margin:0}
ul.portfolio_grid li{float:left;margin:0;width:100%;}
ul.portfolio_grid li .image_frame{border:0;margin:0}
.two-third.column_portfolio_grid li,.one-second.column_portfolio_grid li,.three-fifth.column_portfolio_grid li,
.vc_col-sm-6 .portfolio_grid li,.vc_col-sm-8 .portfolio_grid li{width:50%}
.one.column_portfolio_grid li,.three-fourth.column_portfolio_grid li,.four-fifth.column_portfolio_grid li,.five-sixth.column_portfolio_grid li,
.vc_col-sm-9 .portfolio_grid li,.vc_col-sm-10 .portfolio_grid li,.vc_col-sm-12 .portfolio_grid li{width:25%}

/* portfolio-photo */

.portfolio-photo .portfolio-item{overflow:hidden;position:relative}
.portfolio-photo .portfolio-item a.portfolio-item-bg{display:block;position:relative;text-align:center;line-height:0}
.portfolio-photo .portfolio-item a.portfolio-item-bg .mask{position:absolute;z-index:1;left:0;top:0;background:#000;opacity:.3;width:100%;height:100%}
.portfolio-photo .portfolio-item:hover a.portfolio-item-bg .mask{opacity:0}
.portfolio-photo .portfolio-item .portfolio-details{display:block}
.portfolio-photo .portfolio-item .portfolio-details .details{position:absolute;z-index:2;left:50%;top:50%;transform:translate(-50%,-50%);width:240px;padding:55px 30px;text-align:center}
.portfolio-photo .portfolio-item .portfolio-details .details .title{font-size:23px;line-height:27px;border-bottom:1px solid rgba(0,0,0,.1);padding-bottom:15px;margin-bottom:10px}
.portfolio-photo .portfolio-item .portfolio-details:hover .details{opacity:0}
.portfolio-photo .portfolio-item .portfolio-details .more{display:block;opacity:0;text-transform:uppercase;width:70px;height:40px;padding:20px 5px;letter-spacing:1px;text-align:center;position:absolute;left:50%;top:50%;z-index:2;transform: translate(-50%,-50%);border-radius:100%;box-sizing:content-box}
.portfolio-photo .portfolio-item .portfolio-details .more h4{font-size:15px;line-height:18px;margin:2px 0 0}
.portfolio-photo .portfolio-item .portfolio-details:hover .more{opacity:1}
.portfolio-photo .portfolio-item .portfolio-details .details,.portfolio-photo .portfolio-item .portfolio-details .more{background:#fff}
.portfolio-photo .portfolio-item .portfolio-details .details .categories{color:#a8a8a8}
.portfolio-photo .portfolio-item .portfolio-details .more{color:rgba(0,0,0,.6)}
.portfolio-photo .portfolio-item a .mask,.portfolio-photo .portfolio-item .portfolio-details .details,.portfolio-photo .portfolio-item .portfolio-details .more{transition:all .5s ease-in-out}
.portfolio-photo .portfolio-item .portfolio-details .image_links a{transition:all .3s ease-in-out}

.portfolio-photo.margin .portfolio-item{margin-bottom:25px}

/* portfolio items: filters */

.column_portfolio #Filters .filters_wrapper,.column_portfolio #Filters .filters_wrapper ul{display:block}
.wpb_wrapper #Filters .filters_wrapper,.wpb_wrapper #Filters .filters_wrapper ul{display:block}
.the_content_wrapper #Filters.filters4portfolio .filters_wrapper,.the_content_wrapper #Filters.filters4portfolio .filters_wrapper ul{display:block}

/* testimonials_slider */

.testimonials_slider{position:relative}
.testimonials_slider a.slider_prev,.testimonials_slider a.slider_next{position:absolute;top:30px;z-index:2;margin:0}
.testimonials_slider a.slider_prev{left:0}
.testimonials_slider a.slider_next{right:0}

.testimonials_slider .slider_images{width:100%;height:auto;line-height:0;text-align:center;margin:0 0 30px;position:relative;z-index:2}
.testimonials_slider .slider_images:before{content:"";display:block;height:100%;width:3000px;position:absolute;left:-1000px;top:0;z-index:1}
.with_aside .testimonials_slider .slider_images:before{display:none}

.testimonials_slider .slider_images li{display:inline-block;margin-bottom:0}
.testimonials_slider .slider_images li a{display:block;position:relative;height:85px;width:85px;line-height:0;cursor:pointer;opacity:.5;z-index:10}
.testimonials_slider .slider_images li a:after{content:"";display:none;height:6px;width:100%;position:absolute;left:0;bottom:-6px}
.testimonials_slider .slider_images li.slick-active a,.testimonials_slider .slider_images li a:hover{opacity:1}
.testimonials_slider .slider_images li.slick-active a:after{display:block}

.testimonials_slider .testimonials_slider_ul{margin:0!important;opacity:0;max-height:250px;transition:opacity 0.3s ease-in-out}
.testimonials_slider .testimonials_slider_ul.slick-slider{opacity:1;max-height:none}

.testimonials_slider .testimonials_slider_ul li{float:left;width:100%;padding:0 80px;list-style:none;-webkit-box-sizing:border-box;box-sizing:border-box}
.testimonials_slider .testimonials_slider_ul li .bq_wrapper{background:rgba(0,0,0,0.02);padding:30px 40px 10px;margin-bottom:20px}
.testimonials_slider .testimonials_slider_ul li .hr_dots{margin-bottom:15px}
.testimonials_slider .testimonials_slider_ul li .author{text-align:center}
.testimonials_slider .testimonials_slider_ul li .author h5{margin-bottom:2px}

.testimonials_slider .testimonials_slider_ul li .single-photo-img{display:none}
.testimonials_slider.hide-photos .testimonials_slider_ul li .single-photo-img{display:none !important}

.column:not(.one) .testimonials_slider:not(.single-photo) a.slider_prev,.column:not(.one) .testimonials_slider:not(.single-photo) a.slider_next{display:none!important}
.column:not(.one) .testimonials_slider:not(.single-photo) .testimonials_slider_ul li{padding:0 20px}
.one-third .testimonials_slider .slider_images:before,.one-fourth .testimonials_slider .slider_images:before,.one-second .testimonials_slider .slider_images:before{display:none}
.wpb_wrapper .testimonials_slider .slider_images:before{display:none}

.testimonials_slider .slider_images a{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;-ms-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

	/* testimonials_slider single-photo */

	.testimonials_slider.single-photo a.slider_prev,.testimonials_slider.single-photo a.slider_next{display:none!important}
	.testimonials_slider.single-photo .testimonials_slider_ul li{padding:0;text-align:center}
	.testimonials_slider.single-photo .testimonials_slider_ul li .bq_wrapper{margin:0 6% 20px}
	.testimonials_slider.single-photo .testimonials_slider_ul li .single-photo-img{display:block;margin:0 auto 20px;height:85px;width:85px;line-height:0;overflow:hidden;-webkit-border-radius:50%;border-radius:50%}
	.testimonials_slider.single-photo .slider_pager{margin-top:30px}
	.testimonials_slider.single-photo .slider_pager li{display:inline-block}
	.testimonials_slider.single-photo .slider_pager li a{top:0}
	.testimonials_slider.single-photo .slider_pager li a img{display:none}

	/* testimonials_slider hide-bar */

	.testimonials_slider.hide-bar .slider_images:before {display:none!important}
	.testimonials_slider.hide-bar .slider_images {background-color:transparent!important}

	/* testimonials_list */

	.testimonials_list .item{overflow:hidden;clear:both}
	.testimonials_list .item .photo{width:23%;float:left;margin:0 1% 40px}
	.testimonials_list .item .desc{width:73%;float:left;margin:0 1% 40px}
	.testimonials_list .item .desc h4{margin-bottom:3px;margin-top:10px}
	.testimonials_list .item .desc p.subtitle{opacity:.6}
	.testimonials_list .item .desc hr.hr_color{width:20%;margin-left:0}
	.testimonials_list .item.no-img .desc{width:98%}

/* Payment methods */
.column_payment_methods ul.payment-methods-list { display: flex; flex-wrap: wrap; }
.column_payment_methods ul.payment-methods-list li { margin: 0 1px 2px; box-sizing: content-box; }
.column_payment_methods ul.payment-methods-list li img { display: block; width: 40px; }

/* Before After | TwentyTwenty plugin ---------- */

.twentytwenty-horizontal .twentytwenty-handle:before,.twentytwenty-horizontal .twentytwenty-handle:after,.twentytwenty-vertical .twentytwenty-handle:before,.twentytwenty-vertical .twentytwenty-handle:after{content:" ";display:block;background:#fff;position:absolute;z-index:30;-webkit-box-shadow:0 0 12px rgba(51,51,51,0.5);-moz-box-shadow:0 0 12px rgba(51,51,51,0.5);box-shadow:0 0 12px rgba(51,51,51,0.5)}
.twentytwenty-horizontal .twentytwenty-handle:before,.twentytwenty-horizontal .twentytwenty-handle:after{width:3px;height:9999px;left:50%;margin-left:-1.5px}
.twentytwenty-vertical .twentytwenty-handle:before,.twentytwenty-vertical .twentytwenty-handle:after{width:9999px;height:3px;top:50%;margin-top:-1.5px}
.twentytwenty-before-label,.twentytwenty-after-label,.twentytwenty-overlay{position:absolute;top:0;width:100%;height:100%;-webkit-transition-duration:.5s;-moz-transition-duration:.5s;transition-duration:.5s}
.twentytwenty-before-label,.twentytwenty-after-label{-webkit-transition-property:opacity;-moz-transition-property:opacity;transition-property:opacity}
.twentytwenty-before-label:before,.twentytwenty-after-label:before{color:var(--mfn-before-after-label-color);font-size:13px;letter-spacing:.1em;position:absolute;background:var(--mfn-before-after-label);line-height:38px;padding:0 20px;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}
.twentytwenty-horizontal .twentytwenty-before-label:before,.twentytwenty-horizontal .twentytwenty-after-label:before{top:50%;margin-top:-19px}
.twentytwenty-vertical .twentytwenty-before-label:before,.twentytwenty-vertical .twentytwenty-after-label:before{left:50%;margin-left:-45px;text-align:center;width:90px}
.twentytwenty-left-arrow,.twentytwenty-right-arrow,.twentytwenty-up-arrow,.twentytwenty-down-arrow{width:0;height:0;border:6px inset transparent;position:absolute}
.twentytwenty-left-arrow,.twentytwenty-right-arrow{top:50%;margin-top:-6px}
.twentytwenty-up-arrow,.twentytwenty-down-arrow{left:50%;margin-left:-6px}
.twentytwenty-container{max-width:1920px;margin:0 auto;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;z-index:0;overflow:hidden;position:relative;-webkit-user-select:none;-moz-user-select:none}
.twentytwenty-container img{max-width:100%;position:absolute;top:0;display:block}
.twentytwenty-container.active .twentytwenty-overlay,.twentytwenty-container.active :hover.twentytwenty-overlay{background:rgba(0,0,0,0)}
.twentytwenty-container.active .twentytwenty-overlay .twentytwenty-before-label,.twentytwenty-container.active .twentytwenty-overlay .twentytwenty-after-label,.twentytwenty-container.active :hover.twentytwenty-overlay .twentytwenty-before-label,.twentytwenty-container.active :hover.twentytwenty-overlay .twentytwenty-after-label{opacity:0}
.twentytwenty-container *{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}
.twentytwenty-before-label{opacity:0}
.twentytwenty-after-label{opacity:0}
.twentytwenty-before-label::before{content:attr(data-content)}
.twentytwenty-after-label::before{content:attr(data-content)}
.twentytwenty-horizontal .twentytwenty-before-label:before{left:10px}
.twentytwenty-horizontal .twentytwenty-after-label:before{right:10px}
.twentytwenty-vertical .twentytwenty-before-label:before{top:10px}
.twentytwenty-vertical .twentytwenty-after-label:before{bottom:10px}
.twentytwenty-overlay{-webkit-transition-property:background;-moz-transition-property:background;transition-property:background;background:rgba(0,0,0,0);z-index:25}

.twentytwenty-overlay:hover .twentytwenty-after-label{opacity:1}
.twentytwenty-overlay:hover .twentytwenty-before-label{opacity:1}
.twentytwenty-before{z-index:20}
.twentytwenty-after{z-index:10}
.twentytwenty-handle{height:38px;width:38px;position:absolute;left:50%;top:50%;margin-left:-22px;margin-top:-22px;border:3px solid var(--mfn-before-after-slider);-webkit-border-radius:1000px;-moz-border-radius:1000px;border-radius:1000px;-webkit-box-shadow:0 0 12px rgba(51,51,51,0.5);-moz-box-shadow:0 0 12px rgba(51,51,51,0.5);box-shadow:0 0 12px rgba(51,51,51,0.5);z-index:40;cursor:pointer}
.twentytwenty-horizontal .twentytwenty-handle:before{bottom:50%;margin-bottom:22px;-webkit-box-shadow:0 3px 0 var(--mfn-before-after-slider),0 0 12px rgba(51,51,51,0.5);-moz-box-shadow:0 3px 0 var(--mfn-before-after-slider),0 0 12px rgba(51,51,51,0.5);box-shadow:0 3px 0 var(--mfn-before-after-slider),0 0 12px rgba(51,51,51,0.5);background-color: var(--mfn-before-after-slider);}
.twentytwenty-horizontal .twentytwenty-handle:after{top:50%;margin-top:22px;-webkit-box-shadow:0 -3px 0 var(--mfn-before-after-slider),0 0 12px rgba(51,51,51,0.5);-moz-box-shadow:0 -3px 0 var(--mfn-before-after-slider),0 0 12px rgba(51,51,51,0.5);box-shadow:0 -3px 0 var(--mfn-before-after-slider),0 0 12px rgba(51,51,51,0.5);background-color: var(--mfn-before-after-slider);}
.twentytwenty-vertical .twentytwenty-handle:before{left:50%;margin-left:22px;-webkit-box-shadow:3px 0 0 white,0 0 12px rgba(51,51,51,0.5);-moz-box-shadow:3px 0 0 white,0 0 12px rgba(51,51,51,0.5);box-shadow:3px 0 0 white,0 0 12px rgba(51,51,51,0.5)}
.twentytwenty-vertical .twentytwenty-handle:after{right:50%;margin-right:22px;-webkit-box-shadow:-3px 0 0 white,0 0 12px rgba(51,51,51,0.5);-moz-box-shadow:-3px 0 0 white,0 0 12px rgba(51,51,51,0.5);box-shadow:-3px 0 0 white,0 0 12px rgba(51,51,51,0.5)}
.twentytwenty-left-arrow{border-right:6px solid var(--mfn-before-after-slider);left:50%;margin-left:-17px}
.twentytwenty-right-arrow{border-left:6px solid var(--mfn-before-after-slider);right:50%;margin-right:-17px}
.twentytwenty-up-arrow{border-bottom:6px solid #fff;top:50%;margin-top:-17px}
.twentytwenty-down-arrow{border-top:6px solid #fff;bottom:50%;margin-bottom:-17px}

/* Ultrawide ---------- */

.image_item.stretch:not(.svg) {width:100%;}
.image_frame.stretch .image_wrapper img{width:100%}
.mcb-section.bg-cover,.mcb-wrap.bg-cover,.column_attr.bg-cover{background-size:cover}
.mcb-section.bg-contain,.mcb-wrap.bg-contain,.column_attr.bg-contain{background-size:contain}

@media only screen and (min-width: 1921px){
    .image_item.stretch-ultrawide:not(.svg) {width:100%;}
	.image_frame.stretch-ultrawide .image_wrapper img{width:100%}
	.mcb-section.bg-cover-ultrawide,.mcb-wrap.bg-cover-ultrawide .mcb-wrap-inner,.mcb-column.bg-cover-ultrawide .mcb-column-inner,.column_attr.bg-cover-ultrawide{background-size:cover}
}

/* Others ---------- */

/* Date */

.date_label,.timeline_items > li h3 span{font-size:12px;line-height:18px;display:inline-block;padding:2px 5px;-webkit-border-radius:4px;border-radius:4px;background-image:url(../images/stripes/stripes_3_b.png)}
.date_label,.timeline_items > li h2 span{font-size:12px;line-height:18px;display:inline-block;padding:2px 5px;-webkit-border-radius:4px;border-radius:4px;background-image:url(../images/stripes/stripes_3_b.png)}

.date_label:after,.timeline_items > li h3 span:after{content:"";display:block;position:absolute;right:-5px;top:6px;width:0;height:0;border-style:solid;border-width:5.5px 0 5.5px 5px;border-color:transparent}
.date_label:after,.timeline_items > li h2 span:after{content:"";display:block;position:absolute;right:-5px;top:6px;width:0;height:0;border-style:solid;border-width:5.5px 0 5.5px 5px;border-color:transparent}

.date_label {background-color:#f8f8f8;color:#a8a8a8}
.date_label:after{border-left-color:#f8f8f8}

.timeline_items > li h3 span {background-color: var(--mfn-timeline-date-bg);}
.timeline_items > li h2 span {background-color: var(--mfn-timeline-date-bg);}
.timeline_items > li h3 span:after{border-left-color:var(--mfn-timeline-date-bg);}
.timeline_items > li h2 span:after{border-left-color:var(--mfn-timeline-date-bg);}

@media only screen and (min-width: 768px){
	.timeline_items > li:nth-child(2n) h3 span::after{border-right-color:var(--mfn-timeline-date-bg);border-width:5.5px 5.5px 5.5px 0;left:-5px;right:auto}
    .style-simple .timeline_items > li:nth-child(2n) h3 span:after {border-right-color:transparent;}
}

/* Item | Sidebar Widget */

.column_sidebar_widget .widget:after{display:none}
.column_sidebar_widget .widget{margin-bottom:20px;padding-bottom:20px}
.mcb-item-sidebar_widget-inner{overflow:auto}

/* Isotope Filtering */

.isotope_wrapper{ z-index: 1; position: relative; } /* fix header builder */
.isotope-item{z-index:2}
.isotope-hidden.isotope-item{pointer-events:none;z-index:1}

/* Variables ---------- */
/* #2991d6 - theme color, link, #2275ac - link hover, strong */

/* Selections */

::-moz-selection{color:#fff}
::selection{color:#fff}

/* Borders */

.idea_box,.accordion .question,.accordion .question.active .title,.accordion .question .title:before,table th,table td,.list_item .circle,input[type="date"],input[type="email"],input[type="number"],input[type="password"],
input[type="search"],input[type="tel"],input[type="text"],input[type="url"],select,textarea,.promo_box.has_border:after,dl > dt,dl > dd,.article_box .desc_wrapper p,a.icon_bar,a.content_link,.how_it_works .image,.column_opening_hours .mcb-item-opening_hours-inner,.opening_hours .opening_hours_wrapper li,.icon_box.has_border:after,
.chart_box:before,.column_pricing_item .mcb-item-pricing_item-inner,.team_list .bq_wrapper,.post-footer .post-links,.format-link .post-title .icon-link,.share_wrapper,.post-header .title_wrapper,.section-post-related .section-related-adjustment,#comments > :first-child,.mcb-sidebar,.widget:after,.fixed-nav .desc .date,.portfolio_group.list .portfolio-item,.portfolio_group .portfolio-item .desc .details-wrapper,.Recent_posts ul li .desc h6,
.widget_recent_entries ul li a,.woocommerce .widget_best_sellers li,.woocommerce .widget_featured_products li,.woocommerce .widget_recent_reviews li,.woocommerce .widget_recent_products li,.woocommerce .widget_recently_viewed_products li,.woocommerce .widget_random_products li,.woocommerce .widget_top_rated_products li,.woocommerce .widget_onsale li,.woocommerce .widget_layered_nav li,.woocommerce .widget_shopping_cart ul.product_list_widget li,.woocommerce .widget_products li,
.woocommerce .product .related.products,.woocommerce .product .upsells.products,.woocommerce .product div.entry-summary h1.product_title:after,.woocommerce .quantity input.qty,.header-stack #Top_bar .logo,.offer_thumb .slider_pagination,.offer_thumb .slider_pagination a,.Latest_news ul li .desc_footer,.Latest_news ul li .photo,.offer_thumb.bottom .slider_pagination a,
.style-simple .faq .question,.style-simple #Filters .filters_wrapper ul,#edd_checkout_cart th,#edd_checkout_cart td{border-color:rgba(0,0,0,.08)}

/* Tooltip */

.tooltip-img .tooltip-content,.tooltip:after,.tooltip:before{background:#444;color:#fff}
.tooltip:before { box-shadow: 0 0 15px rgba(0,0,0,.15); }

/* Highlight */

.dropcap,.highlight:not(.highlight-underline){color:#fff}

/* Inputs */

input[type="date"],input[type="email"],input[type="number"],input[type="password"],input[type="search"],input[type="tel"],input[type="text"],input[type="url"],select,textarea,.woocommerce .quantity input.qty{background-color:#fff;color:#626262;box-shadow:inset 0 0 2px 2px rgba(0,0,0,0.02)}
input[type="date"]:focus,input[type="email"]:focus,input[type="number"]:focus,input[type="password"]:focus,input[type="search"]:focus,input[type="tel"]:focus,input[type="text"]:focus,input[type="url"]:focus,select:focus,textarea:focus{background-color:#E9F5FC;border-color:#d5e5ee;color:#1982C2}
select{padding-right:25px;background:#fff url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E') no-repeat right 5px top 55%}
select[multiple]{background-image:none}

/* Dividers */

hr{background-color:rgba(0,0,0,.08);color:rgba(0,0,0,.08)}

/* Header */

#Top_bar .menu > li > a span{border-color:rgba(0,0,0,.05)}

/* Footer */

#Footer .Recent_posts ul li .desc h6,#Footer .widget_recent_entries ul li a,#Footer .widget_best_sellers li,#Footer .widget_featured_products li,#Footer .widget_recent_reviews li,#Footer .widget_recent_products li,#Footer .widget_recently_viewed_products li,#Footer .widget_random_products li,#Footer .widget_top_rated_products li,#Footer .widget_onsale li,#Footer .widget_layered_nav li,#Footer .widget_shopping_cart ul.product_list_widget li,#Footer .widget_products li,#Footer .widget_archive ul li:after,#Footer .widget_nav_menu ul li:after,#Footer table th,#Footer table td{border-color:rgba(255,255,255,.08)}

/* Borders */

#Sliding-top .Recent_posts ul li .desc h6,#Sliding-top .widget_recent_entries ul li a,#Sliding-top .widget_best_sellers li,#Sliding-top .widget_featured_products li,#Sliding-top .widget_recent_reviews li,#Sliding-top .widget_recent_products li,#Sliding-top .widget_recently_viewed_products li,#Sliding-top .widget_random_products li,#Sliding-top .widget_top_rated_products li,#Sliding-top .widget_onsale li,#Sliding-top .widget_layered_nav li,#Sliding-top .widget_shopping_cart ul.product_list_widget li,#Sliding-top .widget_products li,#Sliding-top .widget_archive ul li:after,#Sliding-top .widget_nav_menu ul li:after,#Sliding-top table th,#Sliding-top table td{border-color:rgba(255,255,255,.08)}

/* Image frames & Google maps & Icon bar */

.image_frame,.wp-caption,.google-map,.icon_box .icon_wrapper,.content_slider .content_slider_ul .slick-list,.author-box .avatar-wrapper,#comments .commentlist > li .photo,.edd_download_image{border-color:#f8f8f8}
.wp-caption-text{background:#f8f8f8;color:#A6A6A6}
.image_frame .image_wrapper,.wp-caption img,.icon_box .icon_wrapper:before{border-color:#e2e2e2}
.image_frame .image_wrapper .mask,.edd_download_image:after{box-shadow:inset 0 0 5px 2px rgba(0,0,0,.07)}
.image_frame .image_wrapper .mask:after{background:rgba(0,0,0,.15)}

.no-shadows .image_frame .image_wrapper .mask,.no-shadows .edd_download_image:after{box-shadow:unset}

/* Image frame overlay */

.if-overlay .image_frame .image_wrapper .mask{box-shadow:inset 0 0 0 0 rgba(0,0,0,.0)}

/* Sliding box */

.sliding_box .desc_wrapper h4{color:#fff}

/* Counter & Chart */

.counter .desc_wrapper .number-wrapper,.chart_box .chart .num,.chart_box .chart .icon,.style-simple .quick_fact .number-wrapper,#edd_checkout_cart .edd_cart_header_row th{color:#444}

/* Icon bar */

a.icon_bar,.dark a.icon_bar{color:#444;}

/* Get in touch & Infobox */

.get_in_touch,.get_in_touch a,.get_in_touch a:hover,.get_in_touch h3,.get_in_touch ul,.column_column .get_in_touch ul,.infobox,.infobox a,.infobox h3,.infobox ul{color:#fff;}

/* How it works */

.how_it_works .image_wrapper .number{color:#fff}

/* Trailer box */

.trailer_box .desc h2{color:#fff}
.trailer_box .desc .subtitle{color:#fff}

/* Icon box */

.icon_box:hover .icon_wrapper,.icon_box a:hover .icon_wrapper{color:#fff}

/* List */

.list_item.lists_1 .list_left{color:#fff}

/* Features list */

.feature_list ul li:hover,.feature_list ul li:hover a{color:#fff}
.feature_list ul li:hover .icon i,.feature_list ul li:hover a .icon i{color:#fff}

/* Tabs, Accordion, Toggle, Table, Faq */

.ui-tabs .ui-tabs-nav li a,.accordion .question .title,.faq .question .title,table th{color:#444}
.ui-tabs .ui-tabs-nav li.ui-state-active a:after,body.table-hover:not(.woocommerce-page) table tr:hover td,body.table-hover:not(.woocommerce-page) table tr:hover td a{color:#fff}
.accordion .question .answer,.ui-tabs .ui-tabs-panel,.ui-tabs .ui-tabs-nav li.ui-state-active a,.ui-tabs .ui-tabs-nav li.ui-tabs-selected:after,.ui-tabs .ui-tabs-nav li.ui-state-active:after,.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-active a{background-color:#fff}

/* Fake tabs */

.fake-tabs { margin-bottom: 70px; }
.fake-tabs > ul{ display: flex; justify-content: center; border-bottom: 1px solid rgba(0,0,0,0.08);text-align:center}
.fake-tabs > ul li{display:inline-block}
.fake-tabs > ul li a{display:flex; height: 100%; align-items: center; box-sizing: border-box; position:relative;font-weight:500;text-decoration:none;padding:25px 15px;}
.fake-tabs > ul li a:after { content: ""; display: none; position: absolute; left: 0; bottom: -1px; width: 100%; height: 1px; border-radius: 2px 2px 0 0; }
.fake-tabs > ul li.active a:after { display: block; }
.fake-tabs > ul li a .number { display: inline-block; padding: 0 9px; border-radius: 3px; color: #fff; margin-left: 5px; }
.fake-tabs-count-1 > ul{display:none}
.fake-tabs > .tab{height:0;overflow:hidden;opacity:0;transition:opacity .2s ease-in-out}
.fake-tabs > .tab.active{height:unset;overflow:unset;opacity:1}

.fake-tabs > .tab[tabindex="-1"]{visibility:hidden} /* Accessibility keyboard */

.content-brightness-dark .fake-tabs > ul{ border-color: rgba(255,255,255,0.08); }

/* table of content */
.table_of_content .title{margin-bottom:15px}
.table_of_content .title-inner{display:inline;margin:0}
.table_of_content .title .toggle{margin:0 10px;font-size:80%}
.table_of_content .title .toggle-show{display:none}
.table_of_content.hide .title .toggle-hide{display:none}
.table_of_content.hide .title .toggle-show{display:inline}

.table_of_content.hide_on_start .table_of_content_wrapper{display:none}

.table_of_content .table_of_content_wrapper ol{counter-reset:mfnolnested;line-height: 1.5;margin:0}
.table_of_content .table_of_content_wrapper ol li { margin-bottom:10px; }
.table_of_content .table_of_content_wrapper ol li:not(.mfn_toc_nested){counter-increment:mfnolnested;}

.table_of_content .table_of_content_wrapper li.mfn_toc_nested{margin-left:15px;margin-bottom:15px;list-style:none}
.table_of_content .table_of_content_wrapper li.mfn_toc_nested:last-child{margin-bottom:0}
.table_of_content .table_of_content_wrapper li.mfn_toc_nested li.mfn_toc_nested{margin-left:30px}

.table_of_content .table_of_content_wrapper ol.mfn_toc_numbers,
.table_of_content .table_of_content_wrapper ol.mfn_toc_numbers ol{list-style:none;margin:0}
.table_of_content .table_of_content_wrapper ol.mfn_toc_numbers li:not(.mfn_toc_nested):before{margin-right:10px;content:counters(mfnolnested, ".");}

.table_of_content .table_of_content_wrapper ol.mfn_toc_bullets{margin-left:15px;}
.table_of_content .table_of_content_wrapper ol.mfn_toc_bullets,
.table_of_content .table_of_content_wrapper ol.mfn_toc_bullets ol{list-style-type:square}

.table_of_content .table_of_content_wrapper ol.mfn_toc_custom_icon,
.table_of_content .table_of_content_wrapper ol.mfn_toc_custom_icon ol{list-style:none;margin:0}
.table_of_content .table_of_content_wrapper ol.mfn_toc_custom_icon li i { margin-right: 10px; line-height: inherit; }

.table_of_content .table_of_content_wrapper ol.mfn_toc_numbers li,
.table_of_content .table_of_content_wrapper ol.mfn_toc_custom_icon li { display: flex; align-items: flex-start; }
.table_of_content .table_of_content_wrapper ol.mfn_toc_numbers li:not(.mfn_toc_nested):before,
.table_of_content .table_of_content_wrapper ol.mfn_toc_custom_icon li i { flex-shrink: 0; }
.table_of_content .table_of_content_wrapper ol.mfn_toc_numbers li:last-child,
.table_of_content .table_of_content_wrapper ol.mfn_toc_custom_icon li:last-child{margin-bottom:0}


/* popup  */

.mfn-popup{display:block;position:fixed;top:0;left:0;width:100%;height:100%;z-index:999;background-color:rgba(0,0,0,0.3);}
.mfn-popup .mfn-popup-content{display:block;position:fixed;top:50%;transform:translateY(-50%);max-height:80%;overflow:auto;left:0;right:0;margin:0 auto;max-width:80%;box-sizing:border-box;z-index:1999;background-color:#fff;border-radius: 3px; box-shadow: 0px 2px 4px rgba(105, 103, 139, 0.03);overflow: hidden;animation-name:mfn-popup-open;animation-duration:.3s}
.mfn-popup-940 .mfn-popup-content { width:940px; }

@keyframes mfn-popup-open {
    0%{opacity:0;margin-top:-100px}
    100%{opacity:1;margin-top:0}
}

.mfn-popup-arrow { display: flex; justify-content: center; align-items: center; position: absolute; z-index: 5; width:40px; height:40px; font-size:30px; color: rgba(0,0,0,.3); transition: all 0.3s ease-in-out; cursor:pointer; background-color: transparent;  }
.mfn-popup-arrow:hover { color: rgba(0,0,0,.8);}

/* close button */
.mfn-close-icon{z-index:5;display:flex;justify-content:center;align-items:center;width:40px;height:40px;font-size:20px;cursor:pointer;text-decoration:none!important}
.mfn-close-icon .icon { color: rgba(0,0,0,.3); display: inline-block; transition: all 0.3s ease-in-out;}
.mfn-close-icon:hover .icon { color: rgba(0,0,0,.8); transform: rotate(180deg); }

.content-brightness-dark .mfn-close-icon .icon { color: rgba(255,255,255,.3);}
.content-brightness-dark .mfn-close-icon:hover .icon { color: rgba(255,255,255,.8); }

/* Pricing */

.pricing-box-box.pricing-box-featured,.pricing-box-box.pricing-box-featured .plan-header h2,.pricing-box-box.pricing-box-featured .plan-header .price sup.currency,.pricing-box-box.pricing-box-featured .plan-header .price > span,.pricing-box-box.pricing-box-featured .plan-header .price sup.period,.pricing-box-box.pricing-box-featured .plan-header p.subtitle,.pricing-box-box.pricing-box-featured .plan-inside ul{color:#fff}
.pricing-box-box.pricing-box-featured .plan-header hr{border-color:rgba(0,0,0,.2);background:rgba(0,0,0,.2)}
.pricing-box-box.pricing-box-featured a.button{background-color:#fff;color:#444}
.pricing-box-box.pricing-box-featured a.button:hover{opacity:.9}

/* jQuery UI tabs ---------- */

.ui-helper-reset{border:0;outline:0;line-height:1.3;text-decoration:none;font-size:100%;list-style:none;margin:0;padding:0}
.ui-helper-clearfix:before,.ui-helper-clearfix:after{content:"";display:table}
.ui-helper-clearfix:after{clear:both}
.ui-helper-clearfix{zoom:1}

.ui-tabs{position:relative;overflow:hidden;zoom:1;margin-bottom:15px;background:none!important;border-radius:5px;border-style:solid;border-width:var(--mfn-tabs-border-width);border-color: var(--mfn-tabs-border-color);}
.ui-tabs .ui-tabs-nav{display:block;background-image:url(../images/bg_panel.png);background-repeat:repeat-x;background-position:left top;border-style:solid;border-width:0 0 var(--mfn-tabs-border-width);border-color: var(--mfn-tabs-border-color);margin:0;padding:0;}
.ui-tabs .ui-tabs-nav li{list-style:none;float:left;position:relative;white-space:nowrap;border-style:solid;border-width:0 var(--mfn-tabs-border-width) 0 0;border-color: var(--mfn-tabs-border-color);margin:0;padding:0}
.ui-tabs .ui-tabs-nav li a{text-decoration:none;display:block;font-size:13px;font-weight:700;padding:14px 30px;}
.ui-tabs .ui-tabs-nav li a i{margin-right:4px}
.ui-tabs .ui-tabs-nav li.ui-tabs-selected:after,.ui-tabs .ui-tabs-nav li.ui-state-active:after{content:"";display:block;height:5px;width:100%;position:absolute;left:0;bottom:-5px;z-index:1}
.ui-tabs .ui-tabs-nav li.ui-tabs-selected a,.ui-tabs .ui-tabs-nav li.ui-state-disabled a,.ui-tabs .ui-tabs-nav li.ui-state-processing a,.ui-tabs .ui-tabs-nav li.ui-state-active a{cursor:text}
.ui-tabs .ui-tabs-nav li.ui-tabs-selected a:after,.ui-tabs .ui-tabs-nav li.ui-state-disabled a:after,.ui-tabs .ui-tabs-nav li.ui-state-processing a:after,.ui-tabs .ui-tabs-nav li.ui-state-active a:after{content:"";display:block;height:var(--mfn-tabs-border-width);width:80%;position:absolute;left:50%;margin-left:-40%;top:100%;z-index:2}
.ui-tabs .ui-tabs-nav li a,.ui-tabs.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-selected a{cursor:pointer}
.ui-tabs .ui-tabs-panel{display:block;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;background-image:url(../images/box_shadow.png);background-repeat:repeat-x;background-position:left top;border-width:0;padding:15px 20px 20px;margin:0;overflow:hidden;}
.ui-tabs .ui-tabs-panel p:last-child{margin-bottom:0}

.wpb_content_element.wpb_tabs .wpb_tour_tabs_wrapper .wpb_tab.ui-tabs-panel{background-color:#fff}
.wpb_tour .wpb_tabs_nav a{width:auto!important}

.tabs_centered.ui-tabs .ui-tabs-nav{text-align:center}
.tabs_centered.ui-tabs .ui-tabs-nav li{display:inline-block;float:none}
.tabs_centered.ui-tabs .ui-tabs-nav li:first-child{border-left-width:var(--mfn-tabs-border-width);}

@media only screen and (min-width: 767px){
	.tabs_vertical.ui-tabs{-webkit-border-radius:0!important;border-radius:0!important}
	.tabs_vertical.ui-widget-content{border:0}
	.tabs_vertical.ui-tabs .ui-tabs-nav{width:30%;float:left;background:none;-webkit-border-radius:5px 0 0 5px!important;border-radius:5px 0 0 5px!important;border-width:var(--mfn-tabs-border-width) 0 var(--mfn-tabs-border-width) var(--mfn-tabs-border-width)}
	.tabs_vertical.ui-tabs .ui-tabs-panel{width:69.7%;float:left;min-height:120px;-webkit-border-radius:0 5px 5px 5px!important;border-radius:0 5px 5px 5px!important;background-image:none;border:var(--mfn-tabs-border-width) solid var(--mfn-tabs-border-color) !important;}
	.tabs_vertical.ui-tabs .ui-tabs-nav li{float:none;width:100%;white-space:normal;border-width:0 0 var(--mfn-tabs-border-width)}
	.tabs_vertical.ui-tabs .ui-tabs-nav li:first-child a{-webkit-border-radius:5px 0 0 0!important;border-radius:5px 0 0 0!important}
	.tabs_vertical.ui-tabs .ui-tabs-nav li:last-child{border-bottom:0!important}
	.tabs_vertical.ui-tabs .ui-tabs-nav li a{float:none;line-height:18px;height:auto;padding-left:20px;padding-right:20px;background-image:url(../images/bg_panel.png);background-repeat:repeat-x;background-position:left top}
	.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-tabs-selected a,.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-active a{background-image:none}
	.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-tabs-selected a:after,.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-disabled a:after,.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-processing a:after,.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-active a:after{display:none}
	.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-tabs-selected:after,.tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-active:after{content:"";display:block;height:100%;width:1px;position:absolute;left:auto;right:-1px;top:0;z-index:1}
}

/* Style Simple ---------- */

/* Tabs */

.style-simple .ui-tabs{border:0}
.style-simple .ui-tabs .ui-tabs-panel{background:none}
.style-simple .ui-tabs .ui-tabs-nav{background:none}
.style-simple .ui-tabs .ui-tabs-nav li{border:0;display:inline-block;float:none}
.style-simple .ui-tabs .ui-tabs-nav li.ui-state-active a,.style-simple .ui-tabs .ui-tabs-nav li.ui-tabs-selected:after,.style-simple .ui-tabs .ui-tabs-nav li.ui-state-active:after{background-color:transparent}
.style-simple .ui-tabs .ui-tabs-nav li.ui-tabs-selected a:after,.style-simple .ui-tabs .ui-tabs-nav li.ui-state-disabled a:after,.style-simple .ui-tabs .ui-tabs-nav li.ui-state-processing a:after,.style-simple .ui-tabs .ui-tabs-nav li.ui-state-active a:after{top:100%;height:var(--mfn-tabs-border-width);margin-left:-50%;width:100%}

.style-simple .tabs_vertical.ui-tabs .ui-tabs-panel{border-width:0 0 0 var(--mfn-tabs-border-width) !important}
.style-simple .tabs_vertical.ui-tabs .ui-tabs-nav{border:0}
.style-simple .tabs_vertical.ui-tabs .ui-tabs-nav li.ui-tabs-selected a:after,.style-simple .tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-disabled a:after,.style-simple .tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-processing a:after,.style-simple .tabs_vertical.ui-tabs .ui-tabs-nav li.ui-state-active a:after{display:block;width:var(--mfn-tabs-border-width);height:100%;left:100%;top:0;bottom:auto;right:auto;margin:0}
.style-simple .tabs_vertical.ui-tabs .ui-tabs-nav li a{background:none;text-align:right;padding-right:20px!important;padding-left:10px!important}

/* Accordion & Toggle */

.style-simple .accordion .question{border-width:0 0 0 1px;border-radius:0;position:relative;margin-bottom:0}
.style-simple .accordion .question:after{content:"";display:none;width:2px;height:100%;position:absolute;left:0;top:0;background-color:#ccc}
.style-simple .accordion .question.active:after{display:block}
.style-simple .accordion .question .title{background:none;padding-left:50px;border:0}
.style-simple .accordion .question .title:before{display:none}
.style-simple .accordion .question .answer{background:none;padding:0 20px 10px 50px}

/* FAQ */

.style-simple .faq .question{border-width:0 0 0 1px;border-style:solid;margin-bottom:0;position:relative;background:none}
.style-simple .faq .question:before{display:none}
.style-simple .faq .question:after{content:"";display:none;width:2px;height:100%;position:absolute;top:0;left:-1px;background-color:#ccc}
.style-simple .faq .question .title,.style-simple .faq .question .answer{padding-left:70px}
.style-simple .faq .question.active:after{display:block}

/* Table */

.style-simple table:not(.recaptchatable) th{background:none}
.style-simple table:not(.recaptchatable) tr:first-child td{background:none}
.style-simple table:not(.recaptchatable) th,.style-simple table td{border-width:0 1px 1px 0}
.style-simple table:not(.recaptchatable) tr td:last-child,.style-simple table tr th:last-child{border-right:0}
.style-simple table:not(.recaptchatable) tr:last-child td{border-bottom:0}
.style-simple table:not(.recaptchatable) tr:nth-child(2n) td{background:none}

/* Opening hours */

.style-simple .opening_hours .opening_hours_wrapper li{padding-bottom:0;border:0;margin-bottom:0;text-align:left}
.style-simple .opening_hours .opening_hours_wrapper li label{background-color:transparent;background-image:none;font-size:15px;border-bottom:1px solid #ccc;padding:10px 0;border-radius:0}
.style-simple .opening_hours .opening_hours_wrapper li span{font-size:32px;line-height:32px;padding:10px 0}
.style-simple .opening_hours .opening_hours_wrapper li span sup{top:-1px;font-size:16px;line-height:16px;margin-left:4px}

.style-simple .dark .opening_hours, .style-simple .dark .opening_hours h3{color:rgba(255,255,255,.9)}

.style-simple .column_opening_hours .mcb-item-opening_hours-inner {padding:0;border:0;background-color:transparent}

/* Icon box */

.style-simple .icon_box .image_wrapper,.style-simple .icon_box .icon_wrapper{padding-top:5px;margin-bottom:25px}
.style-simple .icon_box .icon_wrapper{font-size:80px;background-color:transparent;box-shadow:0 0 0 0 rgba(0,0,0,.0);border-width:0;background-image:none;-webkit-transform:scale(1)!important;-moz-transform:scale(1)!important;-ms-transform:scale(1)!important;-o-transform:scale(1)!important;transform:scale(1)!important}
.style-simple .icon_box .icon_wrapper:before{display:none}
.style-simple .icon_box .icon_wrapper i{position:relative;top:0}
.style-simple .icon_box .image_wrapper img{position:relative;top:0}
.style-simple .icon_box .desc_wrapper .title{margin-bottom:20px}
.style-simple .icon_box .desc_wrapper .title:before{content:"";display:block;width:0;margin:0 auto;height:2px;top:-15px;position:relative;background-color:#ccc}
.style-simple .icon_box:hover .icon_wrapper i{top:-5px}
.style-simple .icon_box:hover .image_wrapper img{top:-5px}
.style-simple .icon_box:hover .desc_wrapper .title:before{width:100px}
.style-simple .icon_box .icon_wrapper i,.style-simple .icon_box .image_wrapper img,.style-simple .icon_box .desc_wrapper .title:before{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* Filters */

.style-simple #Filters{text-align:center}
.style-simple #Filters .filters_buttons{background:none;padding:15px 0}
.style-simple #Filters .filters_wrapper{margin-top:0}
.style-simple #Filters .filters_wrapper ul{overflow:visible;display:inline-block;border-bottom-width:1px;border-style:solid}
.style-simple #Filters .filters_wrapper ul li{width:auto;margin:0}
.style-simple #Filters .filters_wrapper ul li a{background:none;border:0;padding:14px 25px;position:relative}
.style-simple #Filters .filters_wrapper ul li a:after{content:"";display:none;position:absolute;left:0;bottom:-1px;width:100%;height:2px;background:#ccc}
.style-simple #Filters .filters_wrapper ul li.close a{color:inherit;padding:14px 15px;width:auto;border:0!important}
.style-simple #Filters .filters_wrapper ul li a:hover,.style-simple #Filters .filters_wrapper ul li.current-cat a{background:none;color:inherit}
.style-simple #Filters .filters_wrapper ul li.current-cat a:after{display:block}

/* Clients */

.style-simple ul.clients.clients_tiles li .client_wrapper::after{display:none}

/* Progress bars */

.style-simple .progress_bars .bars_list li .bar{height:5px}
.style-simple .progress_bars .bars_list li .bar{box-shadow:0 0 0 0 rgba(0,0,0,0) inset;background:rgba(0,0,0,0.03);border-radius:0}
.style-simple .progress_bars .bars_list li .bar .progress{background-image:none}

/* Counters */
.style-simple .column_counter .mcb-item-counter-inner {background-image:none;}

/* Lists */

.style-simple .list_item.lists_1 .list_left{background-image:none;border-radius:2px;box-shadow:0 0 0 0 rgba(0,0,0,0) inset}
.style-simple .list_item .circle{background-image:none;box-shadow:0 0 0 0 rgba(0,0,0,0) inset}

/* Blockquote */

.style-simple blockquote{background:none;line-height:25px;font-style:italic}
.style-simple .blockquote .mfn-blockquote-icon{display:none}

/* Testimonials slider */

.style-simple .testimonials_slider .testimonials_slider_ul li .bq_wrapper{padding-top:0;padding-bottom:1px;background:none;text-align:center}
.style-simple .testimonials_slider .testimonials_slider_ul li .bq_wrapper blockquote{margin-left:0;margin-right:0;}
.style-simple .testimonials_slider .slider_images{background-color:transparent}
.style-simple .testimonials_slider .slider_images:before{display:none}

/* Article box */

.style-simple .article_box .desc_wrapper{background-image:none}
.style-simple .article_box .desc_wrapper p{border-bottom-width:0;margin-bottom:0;padding-bottom:10px}
.style-simple .article_box .desc_wrapper p > span { display: block; }
.style-simple .article_box .desc_wrapper p:after{background-color:var(--mfn-article-box-decoration);content:"";display:inline-block;height:2px;margin-top:10px;width:20px;}
.style-simple .article_box:hover .desc_wrapper p:after{width:40%}
.style-simple .article_box .desc_wrapper i.icon-right-open{display:none}
.style-simple .article_box .desc_wrapper p:after{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* Idea box */

.style-simple .idea_box{border:1px solid rgba(0,0,0,0.08);padding:15px 15px 15px 0;background:none}
.style-simple .idea_box .icon{display:block;position:static;float:left;left:0;top:0;margin-top:0;width:60px;text-align:center;margin-top:5px}
.style-simple .idea_box .desc{border-left:1px solid rgba(0,0,0,0.08);padding:10px 15px 10px 20px;margin-left:60px}

/* Sliding box */

.style-simple .sliding_box .photo_wrapper{top:0}
.style-simple .sliding_box .desc_wrapper{background:none;padding-left:0;padding-right:0}
.style-simple .sliding_box .desc_wrapper h4{color:inherit;margin-bottom:5px}
.style-simple .sliding_box .desc_wrapper:after{display:inline-block;content:"";width:20%;height:2px;border:0;background-color:var(--mfn-sliding-box-bg);position:static;top:0;margin:0}
.style-simple .sliding_box:hover .desc_wrapper:after{width:60%}

/* Trailer box */

.style-simple .trailer_box .desc{padding:27px 15px 30px}
.style-simple .trailer_box .desc .line{display:none}
.style-simple .trailer_box .desc .subtitle{background-image:none;background-color:transparent;color:rgba(255,255,255,.7);margin-bottom:10px;padding:0}
.style-simple .trailer_box:hover .desc h2{bottom:0}
.style-simple .trailer_box:hover .desc .subtitle{bottom:0}
.style-simple .trailer_box .desc{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* Timeline */

.style-simple .timeline_items{background:none}
.style-simple .timeline_items:after{display:none}
.style-simple .timeline_items > li{width:50%;width:calc(50% + 2px);padding:0 0 0 50%;background:none;margin-bottom:15px!important}
.style-simple .timeline_items > li:nth-child(even){padding:0 50% 0 0;background:none}

.style-simple .timeline_items > li h3 span{background:none;border-radius:4px;font-size:14px;font-style:italic;top:22px}
.style-simple .timeline_items > li h2 span{background:none;border-radius:4px;font-size:14px;font-style:italic;top:22px}
.style-simple .timeline_items > li h3 span:after{border-left-color:transparent;}
.style-simple .timeline_items > li h2 span:after{border-left-color:transparent;}
.style-simple .timeline_items > li h3:before{display:none}
.style-simple .timeline_items > li h2:before{display:none}
.style-simple .timeline_items > li .desc:before{display:none}
.style-simple .timeline_items > li h3{border-left:2px solid rgba(0,0,0,0.08);margin:0;padding:15px 0 0 25px;width:100%;box-sizing:padding-box;-webkit-box-sizing:border-box}
.style-simple .timeline_items > li h2{border-left:2px solid rgba(0,0,0,0.08);margin:0;padding:15px 0 0 25px;width:100%;box-sizing:padding-box;-webkit-box-sizing:border-box}
.style-simple .timeline_items > li h3 span{position:absolute;right:52%}
.style-simple .timeline_items > li h2 span{position:absolute;right:52%}
.style-simple .timeline_items > li:nth-child(even) h3{padding:15px 25px 0 0;text-align:right;border-left:0;border-right:2px solid rgba(0,0,0,0.04)}
.style-simple .timeline_items > li:nth-child(even) h2{padding:15px 25px 0 0;text-align:right;border-left:0;border-right:2px solid rgba(0,0,0,0.04)}
.style-simple .timeline_items > li:nth-child(even) h3 span{left:52%;right:auto}
.style-simple .timeline_items > li:nth-child(even) h2 span{left:52%;right:auto}
.style-simple .timeline_items > li .desc{border-left:2px solid rgba(0,0,0,0.08);padding:15px 0 15px 25px;background:none;width:100%;box-sizing:padding-box;-webkit-box-sizing:border-box}
.style-simple .timeline_items > li:nth-child(even) .desc{padding:15px 25px 15px 0;border-left:0;border-right:2px solid rgba(0,0,0,0.04)}
.style-simple .timeline_items > li:hover h3,
.style-simple .timeline_items > li:hover h2,
.style-simple .timeline_items > li:nth-child(even):hover h3,
.style-simple .timeline_items > li:nth-child(even):hover h2,
.style-simple .timeline_items > li:hover .desc,
.style-simple .timeline_items > li:nth-child(even):hover{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}

/* Quick fact */

.style-simple .quick_fact .number-wrapper{font-family:inherit;font-size:60px;line-height:60px}
.style-simple .quick_fact .title{font-family:inherit;font-size:18px;font-weight:400}
.style-simple .quick_fact hr{display:none}

/* countdown */

.style-simple .downcount .title{margin-bottom:0}

/* Chart box */

.style-simple .chart_box:before{background-color:transparent;background-image:none;border-width:0}
.style-simple .chart_box .chart .num{font-family:inherit;font-size:30px}
.style-simple .dark .chart_box .chart .icon,
.style-simple .dark .chart_box .chart .num{color:#fff}

/* Content link */

.style-simple a.content_link{border-width:0 0 2px;margin:0 10px;background-color:transparent;background-image:none}
.style-simple a.content_link:after{border-width:0 0 2px;bottom:-2px}
.style-simple a.content_link:before{border-width:0 5px 6px}

/* How it works */

.style-simple .how_it_works .image{border-width:0 0 2px;background:transparent;border-radius:0;margin-bottom:30px}
.style-simple .how_it_works .image_wrapper .number{border-radius:0;bottom:-17px;left:50%;margin-left:-17px;box-shadow:0 0 0 0 rgba(0,0,0,0) inset}
.style-simple .how_it_works.no-img .image{border-width:0 0 2px}

/* Offer thumb */

.style-simple .offer_thumb_ul li.offer_thumb_li{padding-left:0}
.style-simple .offer_thumb .slider_pagination a img{opacity:.7}
.style-simple .offer_thumb .slider_pagination a.selected img,.style-simple .offer_thumb .slider_pagination a:not(.selected):hover img{opacity:1}

@media only screen and (min-width: 768px) {
	.style-simple .offer_thumb .slider_pagination{border:0}
	.style-simple .offer_thumb .slider_pagination a{margin-bottom:6px;border-width:1px;background:none}
	.style-simple .offer_thumb.bottom .slider_pagination a{margin:3px}
	.style-simple .offer_thumb .slider_pagination a:before,.style-simple .offer_thumb .slider_pagination a:after{display:none}
}

/* Features list */

.style-simple .feature_list hr{background-color:rgba(0,0,0,0)!important;color:rgba(0,0,0,0);}

/* Progress icons */

.style-simple .progress_icons .progress_icon{color:rgba(0,0,0,.3);border-width:1px;border-style:solid}
.style-simple .progress_icons .progress_icon.themebg{border-color:transparent;color:#fff}
.style-simple .progress_icons .progress_icon:not(.themebg){background:none;border-color:rgba(0,0,0,.3)}

/* Pricing box */

.style-simple .pricing-box{border:0}
.style-simple .pricing-box:not(.pricing-box-featured){background:none}

/* Highlight */

.style-simple .highlight{background-image:none;}

/* Get in touch */

.style-simple .get_in_touch ul li{border-bottom:1px solid var(--mfn-contactbox-line);}
.style-simple .get_in_touch ul li:last-child{border:0}
.style-simple .get_in_touch ul li:after{display:none}
.style-simple .get_in_touch ul li .icon{left:5px}

.style-simple  .column_contact_box .mcb-item-contact_box-inner,
.style-simple  .column_column .get_in_touch {padding:25px 30px}

/* Infobox */

.style-simple .column_info_box .mcb-item-info_box-inner {padding:25px 30px}
.style-simple .infobox ul li{border-bottom:1px solid var(--mfn-infobox-line);padding-left:40px}
.style-simple .infobox ul li:last-child{border:0}
.style-simple .infobox ul li:after{display:none}
.style-simple .infobox ul li:before{left:5px}


/* Icon bar */

.style-simple a.icon_bar{border-width:0;background-color:#f0f0f0;background-image:none;-webkit-box-shadow:inset 0 0 0 0 rgba(0,0,0,.0);box-shadow:inset 0 0 0 0 rgba(0,0,0,.0)}
.style-simple a.icon_bar span.t{opacity:1}
.style-simple a:hover.icon_bar span.t{opacity:0}
.style-simple a.icon_bar span.b{position:absolute;top:0;opacity:0}
.style-simple a:hover.icon_bar span.b{opacity:1}
.style-simple a.icon_bar span{-webkit-transition:all .3s ease-in-out;-moz-transition:all .3s ease-in-out;-o-transition:all .3s ease-in-out;transition:all .3s ease-in-out}
.style-simple a.icon_bar span.b{-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}
.style-simple a:hover.icon_bar span{-webkit-transform:translateY(0);-moz-transform:translateY(0);transform:translateY(0)}

/* Hover Efects | Disable ---------- */

.no-hover-all a.button:after,.no-hover-all a.tp-button:after,.no-hover-all a.action_button:after,.no-hover-all button:after,.no-hover-all input[type="submit"]:after,.no-hover-all input[type="reset"]:after,.no-hover-all input[type="button"]:after{background:rgba(0,0,0,0)}
.no-hover-all a.button:hover:after,.no-hover-all a.tp-button:hover:after,.no-hover-all a.action_button:hover:after,.no-hover-all button:hover:after,.no-hover-all input[type="submit"]:hover:after,.no-hover-all input[type="reset"]:hover:after,.no-hover-all input[type="button"]:hover:after{width:0}

.no-hover-all ul.clients .client_wrapper:hover{background:rgba(0,0,0,.01)}
.no-hover-all ul.clients.clients_tiles li .client_wrapper:hover:before{height:1px;background:var(--mfn-clients-tiles-hover);}
.no-hover-all ul.clients.clients_tiles li .client_wrapper:hover:after{display:none}

.no-hover-all a:hover.content_link:before{display:none}
.no-hover-all a:hover.content_link:after{display:none}
.no-hover-all a:hover.content_link{background-image:inherit}

.no-hover-all .feature_list ul li:hover,.no-hover-all .feature_list ul li:hover a{color:inherit}
.no-hover-all .feature_list ul li:hover .icon i,.no-hover-all .feature_list ul li:hover a .icon i{color:inherit}
.no-hover-all .feature_list ul li:hover,.no-hover-all .feature_list ul li:hover a{background:transparent}

.no-hover-all .flat_box:hover .photo_wrapper .icon,.no-hover-all .flat_box a:hover .photo_wrapper .icon{-webkit-transform:translateX(0);transform:translateX(0)}
.no-hover-all .flat_box .photo_wrapper img.photo{-webkit-transform:translateX(0);transform:translateX(0)}

.no-hover-all a:hover.icon_bar{color:inherit!important}
.no-hover-all a:hover.icon_bar span{-webkit-transform:translateY(0);-moz-transform:translateY(0);transform:translateY(0)}

.no-hover-all .icon_box:hover .icon_wrapper{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1);transform:scale(1)}
.no-hover-all .icon_box:hover .icon_wrapper,.no-hover-all .icon_box a:hover .icon_wrapper{color:inherit}
.no-hover-all .icon_box:hover .icon_wrapper:before,.no-hover-all .icon_box a:hover .icon_wrapper::before{background-color:transparent}

.no-hover-all.style-simple .icon_box:hover .icon_wrapper i{top:0}
.no-hover-all.style-simple .icon_box:hover .image_wrapper img{top:0}
.no-hover-all.style-simple .icon_box .desc_wrapper .title::before{display:none}

.no-hover-all .sliding_box:hover .desc_wrapper:after{top:0}
.no-hover-all .sliding_box .photo_wrapper{bottom:0}
.no-hover-all .sliding_box:hover .photo_wrapper{bottom:0}
.no-hover-all .style-simple .sliding_box:hover .desc_wrapper::after{width:20%}

.no-hover-all .story_box .photo_wrapper img{opacity:1}
.no-hover-all .story_box:hover .desc_wrapper hr{width:20%}

.no-hover-all .trailer_box:hover a:after{display:none}
.no-hover-all .trailer_box:hover .desc h2{bottom:0}
.no-hover-all .trailer_box:hover .desc .subtitle{bottom:0}
.no-hover-all .trailer_box:hover .desc .line{width:0}
.no-hover-all .trailer_box.plain .desc .line{display:none}

.no-hover-all .style-simple .trailer_box:hover .desc{background-color:transparent}


/* FLEX || NEW BUILDER */
.section_wrapper { display: flex; align-items: flex-start; flex-wrap: wrap; }
.mcb-wrap { display: flex; align-items: flex-start; }
.mcb-wrap-inner { display: flex; align-content: flex-start; align-items: flex-start; flex-wrap: wrap; position: relative; width: 100%; align-self: stretch;} /* Align self fix for height 100% */
.column_column { display: flex; } /* Fix for align-items: stretch in wrap */
.mcb-item-column-inner { width: 100%; word-break: break-word; } /* Fix for column_column flex width */
/* / FLEX || NEW BUILDER */


/* --------------------------------------------------------------------------------------------------------------- */
/* --------------------------------------------- Header builder -------------------------------------------------- */
/* --------------------------------------------------------------------------------------------------------------- */

/* Variables */
.mfn-header-tmpl {
    --mfn-column-gap-left: 5px;
    --mfn-column-gap-right: 5px;
    --mfn-column-gap-bottom: 0;
    --mfn-header-menu-gap: 0px;
    --mfn-header-menu-icon-gap: 5px;
    --mfn-header-menu-submenu-icon-gap: 10px;
    --mfn-header-menu-sep: #A1C518;
    --mfn-header-menu-animation-height: 3px;
    --mfn-header-menu-icon-size: 25px;
    --mfn-header-submenu-icon-size: 13px;
    --mfn-header-menu-animation-color: #0089f7;
    --mfn-header-menu-sidebar-width: 350px;
    --mfn-header-search-icon-color: #333;
    --mfn-header-icon-color: #333;
    --mfn-header-submenu-border-radius-top: 4px;
    --mfn-header-submenu-border-radius-right: 4px;
    --mfn-header-submenu-border-radius-bottom: 4px;
    --mfn-header-submenu-border-radius-left: 4px;
    --mfn-header-submenu-dropdown-size: 8px;
    --mfn-header-submenu-dropdown-offset: 0px;
    --mfn-header-submenu-dropdown-color: #fff;
}


.mfn-header-tmpl { width: 100%; position: relative; z-index: 3; }
.mfn-header-tmpl-fixed { position: fixed; top: 0; left: 0; }
.mfn-header-tmpl-absolute { position: absolute; left: 0; top: 0; }


.mfn-header-tmpl.mfn-hasSticky .mfn-header-sticky-section{ display: none; }
.mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky .mfn-header-tmpl-builder > .mfn-default-section { display: none; }
.mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky .mfn-header-sticky-section{ display: block; }

.mfn-header-tmpl .mfn-header-mobile-section{ display: none; }

@media ( min-width: 768px ){
    .mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky{ position: fixed; top: 0; left:0 }
    .mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky{ animation-name: mfnShowSticky; animation-duration: 0.5s; }
    html.mfn-disable-css-animations .mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky{ animation-duration: 0s; }
}

@media only screen and (max-width: 767px) {

    .mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky:not(.mfn-hasMobile){ position: fixed;  top: 0; animation-name: mfnShowSticky; animation-duration: 0.5s; }

    .mfn-mobile-header-tmpl-fixed { position: fixed; top: 0; left: 0; }
    .mfn-mobile-header-tmpl-absolute { position: absolute; left: 0; top: 0; }

    .mfn-header-tmpl.mfn-hasMobile .mfn-default-section { display: none; }
    .mfn-header-tmpl.mfn-hasMobile .mfn-header-sticky-section { display: none; }
    .mfn-header-tmpl.mfn-hasMobile .mfn-header-mobile-section{display: block;}
    .mfn-header-tmpl.mfn-hasMobile .mfn-default-section,
    .mfn-header-tmpl.mfn-hasMobile{display: block;}
     .mfn-header-sticky-section{display:none;}

    /* .mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky.mfn-hasMobile .mfn-header-sticky-section{ display: none; }*/

    /* Padding 33px left / right fix */
    .mfn-header-tmpl .section_wrapper { padding-left: 0 !important; padding-right: 0 !important; }

}

/* Animations */
@keyframes mfnShowSticky {
    0%   {top: -100%;}
    100% {top: 0;}
}


/* Dropdown below next section content fix */
.mfn-header-tmpl .mfn-header-tmpl-builder > .mcb-section > .section_wrapper > .mcb-wrap:hover { z-index: 2; }


/* Closeable section */
.close-closeable-section { position: absolute; top: 50%; transform: translateY(-50%); z-index: 1; }
.close-button-left .close-closeable-section { left: 5px;}
.close-button-right .close-closeable-section { right: 5px; }


/* Admin bar fix */
.admin-bar .mfn-header-tmpl-fixed { margin-top: 32px; }
.admin-bar.mfn-header-scrolled .mfn-header-tmpl:not(.mfn-header-tmpl-default) { margin-top: 32px; }

.admin-bar .mfn-header-tmpl-menu-sidebar .mfn-close-icon { top: 42px; }

    @media screen and (max-width: 782px) {
        .admin-bar .mfn-header-tmpl-fixed { margin-top: 46px; }
        .admin-bar.mfn-header-scrolled .mfn-header-tmpl { margin-top: 46px; }
        .admin-bar .mfn-mobile-header-tmpl-fixed { top: 46px; margin-top: 0 !important; }

        .admin-bar .mfn-header-tmpl-menu-sidebar .mfn-close-icon { top: 56px; }
    }


/* Overlay styles */
.mfn-content-gray #Content,
.mfn-content-gray .mfn-main-slider { -webkit-filter: grayscale(100%); filter: grayscale(100%); transition: .3s filter ease-in-out; }

.mfn-content-blur #Content,
.mfn-content-blur .mfn-main-slider { transition: .3s blur ease-in-out; }

.mfn-header-overlay { position: fixed; left: 0; top: 0; width: 100%; height: 0; z-index: 2; opacity: 0; transition: .2s opacity ease-in-out; }
.mfn-content-overlay .mfn-header-overlay,
.mfn-content-blur .mfn-header-overlay { opacity: 1; height: 100%; }

/* Bring to front */
.mfn-bring-to-front { z-index: 4; }


/****************************** Wraps mod ******************************/

.mfn-header-tmpl .mcb-header-wrap { width: auto; max-width: 100%;}
.mfn-header-tmpl .mcb-header-wrap > .mcb-wrap-inner{flex-wrap: nowrap;}
.mfn-header-tmpl .mcb-header-section > .section_wrapper { flex-wrap: nowrap; }
.mfn-header-tmpl .mcb-header-section > .section_wrapper:after { content: unset; }

@media only screen and (min-width: 768px) and (max-width: 959px) {
    .mfn-header-tmpl .tablet-one-sixth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-one-fifth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-one-fourth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-one-third.mcb-header-wrap,
    .mfn-header-tmpl .tablet-two-fifth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-one-second.mcb-header-wrap,
    .mfn-header-tmpl .tablet-three-fifth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-two-third.mcb-header-wrap,
    .mfn-header-tmpl .tablet-three-fourth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-four-fifth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-five-sixth.mcb-header-wrap,
    .mfn-header-tmpl .tablet-one.mcb-header-wrap { width: auto; }
}
@media only screen and (max-width: 767px) {
    .mfn-header-tmpl .mobile-one-sixth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-one-fifth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-one-fourth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-one-third.mcb-header-wrap,
    .mfn-header-tmpl .mobile-two-fifth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-one-second.mcb-header-wrap,
    .mfn-header-tmpl .mobile-three-fifth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-two-third.mcb-header-wrap,
    .mfn-header-tmpl .mobile-three-fourth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-four-fifth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-five-sixth.mcb-header-wrap,
    .mfn-header-tmpl .mobile-one.mcb-header-wrap { width: auto; }
}


/****************************** Items ******************************/

/* Items flex */
.mfn-item-inline.column_header_menu,
.mfn-item-inline.column_header_promo_bar { flex-grow: 1 !important; flex-shrink: 1 !important; flex-basis: 0 !important; }

/*********** Header: Column */
.mfn-header-tmpl .column_column { flex-shrink: 1 !important; }
.mfn-header-tmpl .column_column .column_attr > *:last-child { margin-bottom: 0; }

/*********** Header: Logo */
.column_header_logo .logo-wrapper { display: flex; align-items: center; width: 100%; height: 100%; line-height: 0; }
.column_header_logo .logo-wrapper > * { width: 100%; height: auto;  }

/*********** Header: Promo Bar */

.column_header_promo_bar .promo_bar_slider .pbs_one{ display: none; }
.column_header_promo_bar .promo_bar_slider:not(.mfn-initialized) .pbs_one:first-child, .column_header_promo_bar .promo_bar_slider .pbs_one.pbs-active{ display: block; animation: mfnPBSFadeIn 0.3s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnPBSFadeIn 0.3s cubic-bezier(0.2,1,0.3,1); }
.column_header_promo_bar .promo_bar_slider .pbs_one.pbs-active.pbs-active-ends{ animation: mfnPBSFadeOut 0.3s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnPBSFadeOut 0.3s cubic-bezier(0.2,1,0.3,1); }

@keyframes mfnPBSFadeIn {
    0%   {opacity: 0; transform: scale(0.98);}
    100% {opacity: 1; transform: scale(1);}
}
@-webkit-keyframes mfnPBSFadeIn {
    0%   {opacity: 0; transform: scale(0.98);}
    100% {opacity: 1; transform: scale(1);}
}
@keyframes mfnPBSFadeOut {
    0%   {opacity: 1;transform: scale(1);}
    100% {opacity: 0;transform: scale(0.98);}
}
@-webkit-keyframes mfnPBSFadeOut {
    0%   {opacity: 1;transform: scale(1);}
    100% {opacity: 0;transform: scale(0.98);}
}

/*********** Header: Search */
.column_header_search .search_wrapper { position: relative; }
.column_header_search .search_wrapper input[type="text"] { position: relative; margin: 0; padding-left: 40px; width: 100%; color: var(--mfn-header-search-color); }
.column_header_search .search_wrapper input[type="text"]::placeholder { color: var(--mfn-header-search-color); }
.column_header_search .search_wrapper .icon_search { position: absolute; left: 22px; top: 50%; z-index: 1; transform: translate(-50%,-50%);  }
.column_header_search .search_wrapper svg.icon_search { width: var(--mfn-header-search-icon-size); }
.column_header_search .search_wrapper svg.icon_search .path { stroke: var(--mfn-header-search-icon-color); }
.column_header_search .search_wrapper .icon_search i { font-size: var(--mfn-header-search-icon-size); color: var(--mfn-header-search-icon-color); }
.column_header_search .search_wrapper .icon_close { display: none; }

.mfn-header-tmpl .column_header_search .mfn-live-search-box { position: absolute; z-index: 202; right: 0; top: calc(100% + 10px); width: 100%; min-width: 400px; border-top: 0; }
.mfn-header-tmpl .column_header_search .mfn-live-search-box { background-color: #fff; box-shadow: 0px 10px 46px 0px rgba(1,7,39,.1); border-radius: 4px; }


/*********** Header: Menu */

.mfn-header-menu { display: flex; align-items: stretch; flex-wrap: wrap; font-size: 15px; line-height: 1.5; font-weight: 500; }
.mfn-header-menu,
.mfn-header-menu .mfn-submenu { margin: 0; padding: 0; list-style: none; }
.mfn-header-menu .mfn-menu-li { position: relative; }
.mfn-header-menu .mfn-menu-li .mfn-menu-link { display: flex; text-decoration: none; }

/* 1st level */
.mfn-header-menu > .mfn-menu-li { display: inline-flex; }
.mfn-header-menu > .mfn-menu-li > .mfn-menu-link { position: relative; align-items: center; justify-content: center; padding: 8px 20px; width: 100%; box-sizing: border-box; }
.mfn-header-menu > .mfn-menu-li > .mfn-menu-link .menu-item-helper { display: flex; position: absolute; left: 0; top: 0; width: 100%; height: 100%; box-sizing: border-box;}

/* 2nd & 3rd level */
.mfn-header-menu .mfn-menu-li .mfn-submenu { display: none; position:absolute; width: 220px; border-radius: var(--mfn-header-submenu-border-radius-top) var(--mfn-header-submenu-border-radius-right) var(--mfn-header-submenu-border-radius-bottom) var(--mfn-header-submenu-border-radius-left); }
.mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-link { align-items: center; padding: 10px 20px; }
.mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-li:first-child .mfn-menu-link { border-radius: var(--mfn-header-submenu-border-radius-top) var(--mfn-header-submenu-border-radius-right) 0 0; }
.mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-li:last-child .mfn-menu-link { border-bottom: 0; border-radius: 0 0 var(--mfn-header-submenu-border-radius-bottom) var(--mfn-header-submenu-border-radius-left); }
.mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-link .menu-sub { margin-left: auto; }
.mfn-menu-submenu-on-hover .mfn-menu-li:hover > .mfn-submenu,
.mfn-menu-submenu-on-click .mfn-menu-li.mfn-li-hover > .mfn-submenu { display: block; }

/* 2nd level */
.mfn-header-menu > .mfn-menu-li .mfn-submenu {left:0;top:100%;}
.mfn-header-menu.mfn-menu-fold-last-to-right > .mfn-menu-li:nth-last-of-type(-n+2) .mfn-submenu{left:auto;right:0}

/* 3rd level */
.mfn-header-menu > .mfn-menu-li .mfn-submenu .mfn-menu-li .mfn-submenu {left:100%;top:0;}
.mfn-header-menu.mfn-menu-fold-last-to-right > .mfn-menu-li:nth-last-of-type(-n+2) .mfn-submenu .mfn-menu-li .mfn-submenu{left:auto;right:100%}

    /* Submenu type animation ------------------------------------*/
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-hover .mfn-menu-li:hover > .mfn-submenu,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-click .mfn-menu-li.mfn-li-hover > .mfn-submenu,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover .mfn-menu-item-megamenu,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover .mfn-menu-item-megamenu,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-hover .mfn-menu-li:hover > .mfn-dropdown-pointer,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-click .mfn-menu-li.mfn-li-hover > .mfn-dropdown-pointer,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover .mfn-dropdown-pointer,
    .mfn-menu-submenu-show-fade-in.mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover .mfn-dropdown-pointer { animation: mfnSubmenuFadeIn 0.5s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnSubmenuFadeIn 0.5s cubic-bezier(0.2,1,0.3,1); }
    @keyframes mfnSubmenuFadeIn {
        0%   {opacity: 0;}
        100% {opacity: 1;}
    }
    @-webkit-keyframes mfnSubmenuFadeIn {
        0%   {opacity: 0;}
        100% {opacity: 1;}
    }
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-hover .mfn-menu-li:hover > .mfn-submenu,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-click .mfn-menu-li.mfn-li-hover > .mfn-submenu,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover .mfn-menu-item-megamenu,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover .mfn-menu-item-megamenu,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-hover .mfn-menu-li:hover > .mfn-dropdown-pointer,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-click .mfn-menu-li.mfn-li-hover > .mfn-dropdown-pointer,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover .mfn-dropdown-pointer,
    .mfn-menu-submenu-show-fade-up.mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover .mfn-dropdown-pointer { animation: mfnSubmenuFadeInUp 0.5s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnSubmenuFadeInUp 0.5s cubic-bezier(0.2,1,0.3,1); }
    @keyframes mfnSubmenuFadeInUp {
        0%   {opacity: 0; transform: translateY(20px)}
        100% {opacity: 1; transform: translateY(0);}
    }
    @-webkit-keyframes mfnSubmenuFadeInUp {
        0%   {opacity: 0; transform: translateY(20px)}
        100% {opacity: 1; transform: translateY(0);}
    }

    /* Colors ------------------------------------*/
    .mfn-header-menu .mfn-menu-li > .mfn-menu-link { color: #73748c; }
    .mfn-header-menu .mfn-menu-li:hover > .mfn-menu-link { color: #2a2b39; }
    .mfn-header-menu .mfn-menu-li.current-menu-item > .mfn-menu-link { color: #0089f7; }
    .mfn-header-menu .mfn-menu-link .menu-icon i { color: #0089f7; }
    .mfn-header-menu .mfn-submenu { background-color: #fff; box-shadow: 0px 10px 20px 0px rgba(1,7,39,.05); }

    /* Colors ------------------------------------*/
    .mfn-megamenu-menu li > a { color: #73748c; }
    .mfn-megamenu-menu li > a:hover { color: #2a2b39; }
    .mfn-megamenu-menu li.current-menu-item > a { color: #0089f7; }
    .mfn-megamenu-menu a .menu-icon i { color: #0089f7; }

    .mfn-mm-menu-horizontal .sub-menu { background-color: #fff; box-shadow: 0px 10px 20px 0px rgba(1,7,39,.05); }

    /* Z-index ------------------------------------*/
    .mfn-header-menu > .mfn-menu-li > .menu-item-helper { z-index: 1; }
    .mfn-header-menu > .mfn-menu-li { z-index: 1; }
    .mfn-header-menu > .mfn-menu-li .mfn-menu-link  .menu-icon,
    .mfn-header-menu > .mfn-menu-li .mfn-menu-link  .menu-sub { z-index: 1; }
    .mfn-header-menu > .mfn-menu-li:hover { z-index: 2; }
    .mfn-header-menu > .mfn-menu-li .mfn-menu-link .sub-menu { z-index: 3; }
    .mfn-header-menu > .mfn-menu-li > .mfn-menu-link { z-index: 4; }
    .mfn-menu-separator-on > .mfn-menu-li:after { z-index: 4; }

    /* Addons ------------------------------------*/
    .mfn-header-menu > .mfn-menu-li > .mfn-menu-link > .label-wrapper > .menu-label { white-space: nowrap; }

    .mfn-header-menu > .mfn-menu-li .mfn-menu-link span.menu-sub i { font-size: var(--mfn-header-submenu-icon-size); }
    .mfn-header-menu .mfn-menu-li:not(.menu-item-has-children, .mfn-menu-item-has-megamenu) .mfn-menu-link .menu-sub { display: none !important; }
    .mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-link .menu-sub > i { font-size: var(--mfn-header-submenu-submenu-icon-size); }

    .mfn-header-menu .mfn-menu-link .menu-icon { line-height: 1; }
    .mfn-header-menu .mfn-menu-link .menu-icon i { font-size: var(--mfn-header-menu-icon-size); }
    .mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-link .menu-icon { font-size: var(--mfn-header-submenu-subicon-gap); }
    .mfn-header-menu .mfn-menu-li .mfn-submenu .mfn-menu-link .menu-icon > i { font-size: var(--mfn-header-submenu-subicon-size); }
    .mfn-header-menu .mfn-menu-link .menu-icon > svg,
    .mfn-header-menu .mfn-menu-link .menu-icon > img { height:auto; width: var(--mfn-header-menu-icon-size); }

    .mfn-header-menu .mfn-menu-link .label-wrapper { position: relative; display: flex; flex-direction: column; }
    .mfn-header-menu .mfn-menu-link .label-wrapper .menu-desc { font-size: 85%; }

    .mfn-menu-submenu-icon-off .mfn-menu-link .menu-sub { display: none; }

    /* Mega menu */
    .mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover > .mfn-submenu,
    .mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover > .mfn-submenu { display: none; }

    /* Menu item alignment ------------------------------------*/
    .mfn-items-align-left .mfn-menu-li .mfn-menu-link { justify-content: flex-start; }
    .mfn-items-align-left.mfn-menu-icon-right .mfn-menu-li .mfn-menu-link {   justify-content: flex-end; }
    .mfn-items-align-center .mfn-menu-li .mfn-menu-link { justify-content: center; }
    .mfn-items-align-right .mfn-menu-li .mfn-menu-link { justify-content: flex-end; }
    .mfn-items-align-right.mfn-menu-icon-right .mfn-menu-li .mfn-menu-link {   justify-content: flex-start; }

    /* Icon positions ------------------------------------*/
    .mfn-menu-icon-left .mfn-menu-link > .menu-icon { margin-right: var(--mfn-header-menu-icon-gap); }
    .mfn-menu-icon-left .mfn-menu-link > .menu-sub { margin-left: var(--mfn-header-menu-submenu-icon-gap); }

    .mfn-menu-icon-right > .mfn-menu-li > .mfn-menu-link { flex-direction: row-reverse; }
    .mfn-menu-icon-right .mfn-menu-link > .menu-icon { margin-left: var(--mfn-header-menu-icon-gap); }
    .mfn-menu-icon-right .mfn-menu-link > .menu-sub { margin-right: var(--mfn-header-menu-submenu-icon-gap); }

    .mfn-menu-icon-top > .mfn-menu-li > .mfn-menu-link { flex-wrap: wrap; }
    .mfn-menu-icon-top .mfn-menu-link > .menu-icon { margin-bottom: var(--mfn-header-menu-icon-gap); width: 100%; text-align: center; }
    .mfn-menu-icon-top .mfn-menu-link > .menu-sub { margin-left: var(--mfn-header-menu-submenu-icon-gap); }
    .mfn-menu-icon-top .mfn-menu-link > .label-wrapper { align-items: center; }
    .mfn-menu-icon-top .mfn-menu-li .mfn-submenu .mfn-menu-link { justify-content: center; flex-wrap: wrap; }
    .mfn-menu-icon-top .mfn-menu-li .mfn-submenu .mfn-menu-link .menu-sub { margin-left: var(--mfn-header-menu-submenu-icon-gap); }

    .mfn-menu-icon-top.mfn-items-align-left .mfn-menu-li .mfn-menu-link,
    .mfn-menu-icon-top.mfn-items-align-right .mfn-menu-li .mfn-menu-link { justify-content: center; }
    .mfn-menu-icon-top.mfn-items-align-left .mfn-menu-link > .menu-icon,
    .mfn-menu-icon-top.mfn-items-align-right .mfn-menu-link > .menu-icon { text-align: center; }

    /* Separator ------------------------------------*/
    .mfn-menu-separator-on > .mfn-menu-li:after { content: ""; width: 1px; height: 100%; background-color: var(--mfn-header-menu-sep); position: absolute; top: 0; right: calc(var(--mfn-header-menu-gap) * -0.5); }
    .mfn-menu-separator-on > .mfn-menu-li:last-child:after { display: none; }

    /* Gap ------------------------------------*/
    .mfn-header-menu > .mfn-menu-li { display: inline-flex; margin: 0 calc(var(--mfn-header-menu-gap) / 2); }
    .mfn-header-menu > .mfn-menu-li:first-child { margin-left: 0; }
    .mfn-header-menu > .mfn-menu-li:last-child { margin-right: 0; }

    /* Menu - Animation ------------------------------------*/
    .mfn-menu-animation-text-line-bottom > .mfn-menu-li > .mfn-menu-link { transition: color 0.2s ease-in-out; }
    .mfn-menu-animation-text-line-bottom > .mfn-menu-li > .mfn-menu-link .label-wrapper:before { content: ""; position: absolute; left: 0; top: 100%; width: 100%; height: var(--mfn-header-menu-animation-height); background-color: var( --mfn-header-menu-animation-color); transform: scale3d(0, 1, 1); transition: transform 0.2s ease-in-out; }
    .mfn-menu-animation-text-line-bottom > .mfn-menu-li:hover > .mfn-menu-link  .label-wrapper:before,
    .mfn-menu-animation-text-line-bottom > .mfn-menu-li.current-menu-item > .mfn-menu-link .label-wrapper:before { transform: scale3d(1, 1, 1); }

    .mfn-menu-animation-text-toggle-line-bottom > .mfn-menu-li > .mfn-menu-link { transition: color 0.4s cubic-bezier(0.8,0,0.2,1); }
    .mfn-menu-animation-text-toggle-line-bottom > .mfn-menu-li > .mfn-menu-link .label-wrapper:before { content: ''; position: absolute; left: 0; top: 100%; width: 100%; height: var(--mfn-header-menu-animation-height); background-color:  var( --mfn-header-menu-animation-color); transform: scale3d(0,1,1); transform-origin: 100% 50%; transition: transform 0.4s cubic-bezier(0.8,0,0.2,1); }
    .mfn-menu-animation-text-toggle-line-bottom > .mfn-menu-li:hover > .mfn-menu-link .label-wrapper:before,
    .mfn-menu-animation-text-toggle-line-bottom > .mfn-menu-li.current-menu-item > .mfn-menu-link .label-wrapper:before { transform: scale3d(1,1,1); transform-origin: 0% 50%; }

    .mfn-menu-animation-text-bg-line > .mfn-menu-li > .mfn-menu-link { transition: color 0.2s cubic-bezier(0.2,1,0.3,1); }
    .mfn-menu-animation-text-bg-line > .mfn-menu-li > .mfn-menu-link .label-wrapper { padding: 0 0.5em; }
    .mfn-menu-animation-text-bg-line > .mfn-menu-li > .mfn-menu-link .label-wrapper:before { content: ""; position: absolute; bottom: 0; left: 0; width: 100%; height: 50%; transform: scale3d(0,1,1); background-color: var( --mfn-header-menu-animation-color); opacity: .3; transition: transform 0.2s cubic-bezier(0.2,1,0.3,1); }
    .mfn-menu-animation-text-bg-line > .mfn-menu-li:hover > .mfn-menu-link .label-wrapper:before,
    .mfn-menu-animation-text-bg-line > .mfn-menu-li.current-menu-item > .mfn-menu-link .label-wrapper:before { transform: scale3d(1,1,1); }
    .mfn-menu-animation-text-bg-line > .mfn-menu-li:hover > .mfn-menu-link,
    .mfn-menu-animation-text-bg-line > .mfn-menu-li.current-menu-item > .mfn-menu-link { color: #0089f7; }

    .mfn-menu-animation-toggle-line-top > .mfn-menu-li > .mfn-menu-link:before,
    .mfn-menu-animation-toggle-line-bottom > .mfn-menu-li > .mfn-menu-link:before { content: ""; position: absolute; left: 0; width: 100%; height: var(--mfn-header-menu-animation-height); background-color: var( --mfn-header-menu-animation-color); transform: scale3d(0, 1, 1); transition: transform 0.2s ease-in-out; }
    .mfn-menu-animation-toggle-line-top > .mfn-menu-li > .mfn-menu-link:before { bottom: 100%;}
    .mfn-menu-animation-toggle-line-bottom > .mfn-menu-li > .mfn-menu-link:before { top: 100%;}
    .mfn-menu-animation-toggle-line-top > .mfn-menu-li:hover > .mfn-menu-link:before,
    .mfn-menu-animation-toggle-line-bottom > .mfn-menu-li:hover > .mfn-menu-link:before,
    .mfn-menu-animation-toggle-line-top > .mfn-menu-li.current-menu-item > .mfn-menu-link:before,
    .mfn-menu-animation-toggle-line-bottom > .mfn-menu-li.current-menu-item > .mfn-menu-link:before { transform: scale3d(1, 1, 1); }

    .mfn-menu-animation-bg-left > .mfn-menu-li > .mfn-menu-link { transition: color 0.2s ease-in-out; }
    .mfn-menu-animation-bg-left > .mfn-menu-li > .mfn-menu-link .menu-item-helper {  transform: scale3d(0, 1, 1); transition: transform 0.2s ease-in-out; transform-origin: 0% 50%; background-color: var(--mfn-header-menu-animation-color); }
    .mfn-menu-animation-bg-left > .mfn-menu-li:hover > .mfn-menu-link .menu-item-helper,
    .mfn-menu-animation-bg-left > .mfn-menu-li.current-menu-item >  .mfn-menu-link .menu-item-helper { transform: scale3d(1, 1, 1); }

    /* Menu icon - Animation ------------------------------------*/
    .mfn-menu-icon-rotate .mfn-menu-li > .mfn-menu-link .menu-icon { transition: transform .3s ease-out; }
    .mfn-menu-icon-rotate .mfn-menu-li:hover > .mfn-menu-link .menu-icon,
    .mfn-menu-icon-rotate .mfn-menu-li.current-menu-item > .mfn-menu-link .menu-icon { transform: rotate(180deg); }

    .mfn-menu-icon-zoom .mfn-menu-li > .mfn-menu-link .menu-icon { transition: transform .3s ease-out; }
    .mfn-menu-icon-zoom .mfn-menu-li:hover > .mfn-menu-link .menu-icon,
    .mfn-menu-icon-zoom .mfn-menu-li.current-menu-item > .mfn-menu-link .menu-icon { transform: scale(1.2) }

    /* Menu subicon - Animation ------------------------------------*/
    .mfn-menu-submenu-icon-rotate > .mfn-menu-li > .mfn-menu-link .menu-sub { transition: transform .3s ease-out; }
    .mfn-menu-submenu-icon-rotate > .mfn-menu-li:hover > .mfn-menu-link .menu-sub,
    .mfn-menu-submenu-icon-rotate > .mfn-menu-li.current-menu-item > .mfn-menu-link .menu-sub { transform: rotate(180deg); }

    .mfn-menu-submenu-icon-zoom > .mfn-menu-li > .mfn-menu-link .menu-sub { transition: transform .3s ease-out; }
    .mfn-menu-submenu-icon-zoom > .mfn-menu-li:hover > .mfn-menu-link .menu-sub,
    .mfn-menu-submenu-icon-zoom > .mfn-menu-li.current-menu-item > .mfn-menu-link .menu-sub { transform: scale(1.2) }

    /* Dropdown pointer */
    .mfn-menu-dropdown-pointer > .mfn-menu-li > .mfn-dropdown-pointer { display: none; position: absolute; top: calc(100% + 1px); width: 100%; z-index: 1; line-height: 0; }
    .mfn-menu-dropdown-pointer > .mfn-menu-li > .mfn-dropdown-pointer:after { display: inline-block; content: ""; width: 0px; height: 0px; border-left: var(--mfn-header-submenu-dropdown-size) solid transparent; border-right: var(--mfn-header-submenu-dropdown-size) solid transparent; border-bottom: var(--mfn-header-submenu-dropdown-size) solid var(--mfn-header-submenu-dropdown-color); }
    .mfn-menu-dropdown-pointer > .mfn-menu-li > .mfn-menu-item-megamenu { padding-top: var(--mfn-header-submenu-dropdown-size); }
    .mfn-menu-dropdown-pointer > .mfn-menu-li .mfn-submenu { top: calc(100% + var(--mfn-header-submenu-dropdown-size)); }

    .mfn-menu-dropdown-pointer > .mfn-menu-li:not(.mfn-menu-item-has-megamenu):not(.menu-item-has-children) .mfn-dropdown-pointer { display: none !important; }
    .mfn-menu-dropdown-pointer .mfn-menu-li .mfn-menu-li .mfn-dropdown-pointer { display: none !important; }

    .mfn-menu-dropdown-pointer-left > .mfn-menu-li > .mfn-dropdown-pointer { text-align: left; }
    .mfn-menu-dropdown-pointer-left > .mfn-menu-li > .mfn-dropdown-pointer:after { margin-left: var(--mfn-header-submenu-dropdown-offset); }
    .mfn-menu-dropdown-pointer-center > .mfn-menu-li > .mfn-dropdown-pointer { text-align: center; }
    .mfn-menu-dropdown-pointer-center > .mfn-menu-li > .mfn-dropdown-pointer:after { margin-left: var(--mfn-header-submenu-dropdown-offset); }
    .mfn-menu-dropdown-pointer-right > .mfn-menu-li > .mfn-dropdown-pointer { text-align: right; }
    .mfn-menu-dropdown-pointer-right > .mfn-menu-li > .mfn-dropdown-pointer:after { margin-right: var(--mfn-header-submenu-dropdown-offset); }

    .mfn-menu-submenu-on-hover .mfn-menu-li:hover > .mfn-dropdown-pointer,
    .mfn-menu-submenu-on-click .mfn-menu-li.mfn-li-hover > .mfn-dropdown-pointer { display: inline-block; }
    .mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover > .mfn-dropdown-pointer,
    .mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover > .mfn-dropdown-pointer{ display: inline-block; }



/*********** Header: Header Icon * Menu burger (Icon box mod) */
.mfn-header-tmpl .mfn-icon-box .icon-wrapper i { font-size: var(--mfn-header-menu-icon-size); color: var(--mfn-header-icon-color); }
.mfn-header-tmpl .mfn-icon-box .icon-wrapper svg .path { stroke: var(--mfn-header-icon-color); }
.mfn-header-tmpl .mfn-icon-box .icon-wrapper img,
.mfn-header-tmpl .mfn-icon-box .icon-wrapper svg{ width: var(--mfn-header-menu-icon-size);}
.mfn-header-tmpl .mfn-icon-box,
.mfn-header-tmpl .mfn-icon-box .icon-wrapper { overflow: unset; }

    /* Menu burger */
    .mcb-item-header_burger-inner { z-index: 2; }
    html.mfn-header-tmpl-burger-sidebar-opened{ overflow: hidden; }
    html.scrollbar-hidden,
    html.scrollbar-hidden .mfn-header-scrolled .mfn-header-tmpl.mfn-hasSticky .mfn-builder-content,
    html.scrollbar-hidden .mfn-footer-sliding .mfn-footer-tmpl .mfn-builder-content{padding-right:15px}

    .column_header_burger.mfn-header-tmpl-menu-active:before{ content: ""; display: none; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 1; background-color: rgba(0, 0, 0, 0.1); animation: mfnMmSubmenuFadeIn 1s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnMmSubmenuFadeIn 1s cubic-bezier(0.2,1,0.3,1);}

    .mfn-header-tmpl-menu-sidebar { position: fixed !important; z-index: 1; top: 0; width: var(--mfn-header-menu-sidebar-width); height: 100vh; background-color: #f6f7f9; transition: .5s ease-in-out; }
    .mfn-header-tmpl-menu-active .mfn-header-tmpl-menu-sidebar {}
    .mfn-header-tmpl-menu-sidebar .mfn-header-tmpl-menu-sidebar-wrapper { display: flex; flex-wrap: wrap; position: relative; align-items: center; overflow-y: auto; height: 100%; padding: 50px; box-sizing: border-box; }
    .mfn-header-tmpl-menu-sidebar .mfn-header-menu { flex-direction: column; width: 100%; }
    .mfn-header-tmpl-menu-sidebar .mfn-header-menu > .mfn-menu-li { flex-direction: column; }
    .mfn-header-tmpl-menu-sidebar .mfn-header-menu .mfn-submenu { display: none; position: relative; left: unset; top: unset; width: 100%; }
    .mfn-header-tmpl-menu-sidebar .mfn-header-menu .mfn-submenu .mfn-menu-li .mfn-submenu { left: unset; top: unset; }

    .mfn-header-tmpl-menu-sidebar-left { left: calc(var(--mfn-header-menu-sidebar-width) * -1); }
    .mfn-header-tmpl-menu-active .mfn-header-tmpl-menu-sidebar-left { left: 0; }
    .mfn-header-tmpl-menu-sidebar-right { right: calc(var(--mfn-header-menu-sidebar-width) * -1); }
    .mfn-header-tmpl-menu-active .mfn-header-tmpl-menu-sidebar-right { right: 0; }

    .mfn-header-tmpl-menu-sidebar .mfn-close-icon { position: absolute; top: 10px; }
    .mfn-header-tmpl-menu-sidebar-left .mfn-close-icon { left: 10px; }
    .mfn-header-tmpl-menu-sidebar-right .mfn-close-icon { right: 10px; }

        /* Custom position */
        .mfn-close-icon-pos-left .mfn-close-icon { left: 10px;  }
        .mfn-close-icon-pos-right .mfn-close-icon { right: 10px; }

    .mfn-header-tmpl-menu-sidebar .mfn-header-menu > .mfn-menu-li { display: inline-flex; margin: calc(var(--mfn-header-menu-gap) / 2) 0; }
    .mfn-header-tmpl-menu-sidebar .mfn-header-menu > .mfn-menu-li:first-child { margin-top: 0; }
    .mfn-header-tmpl-menu-sidebar .mfn-header-menu > .mfn-menu-li:last-child { margin-bottom: 0; }

    #Side_slide .menu_wrapper .mfn-header-menu { flex-direction: column; }
    #Side_slide .menu_wrapper .mfn-header-menu > .mfn-menu-li { flex-direction: column; }

    /* Cart & Wishlist */
    .column_header_icon .mfn-header-cart-link .icon-wrapper,
    .column_header_icon .mfn-header-wishlist-link .icon-wrapper { position: relative; }
    .column_header_icon .header-wishlist-count,
    .column_header_icon .header-cart-count{ display: inline-block; position: absolute; font-size: 11px; line-height: 1; background-color: #333; color: #fff; min-width: 18px; text-align: center; border-radius: 18px; padding: 3px; }

    /* Search_wrapper */
	.mfn-header-tmpl .column_header_icon .search_wrapper {display: none;position:fixed;left:50%;transform: translate(-50%,-50%);top:50%;display:none;z-index:201;width:100%;box-sizing:border-box; width: 600px; max-width: 80%; overflow: hidden;}
    .mfn-header-tmpl .column_header_icon .mfn-searchbar-active .search_wrapper { display: block; }
	.mfn-header-tmpl .column_header_icon .search_wrapper > form { position: relative; }
    .mfn-header-tmpl .column_header_icon .search_wrapper input[type="text"]{width:100%;margin:0;box-sizing:border-box;-webkit-box-shadow:0 0 0;box-shadow:0 0 0;padding: 22px 30px 22px 60px;background:none;border-width:0;font-size:15px;color:rgba(0,0,0,.8);}
	.mfn-header-tmpl .column_header_icon .search_wrapper input[type="text"]:focus{background-color:transparent!important}
	.mfn-header-tmpl .column_header_icon .search_wrapper .icon_search,
    .mfn-header-tmpl .column_header_icon .search_wrapper .icon_close {position:absolute;top:50%;transform: translateY(-50%);}
	.mfn-header-tmpl .column_header_icon .search_wrapper .icon_search{left:15px;}
    .mfn-header-tmpl .column_header_icon .search_wrapper .icon_close{right:10px;}
    .mfn-header-tmpl .column_header_icon .search_wrapper { background-color: #fff; box-shadow: 0px 10px 46px 0px rgba(1,7,39,.1); border-radius: 4px; }


    /* Link */
    .mfn-header-tmpl a.mfn-icon-box,
    .mfn-header-tmpl a:hover.mfn-icon-box { text-decoration: none; }

    /* Left * Right */
    .mfn-header-tmpl .mfn-icon-box-left .icon-wrapper { margin: 0 10px 0 0; }
    .mfn-header-tmpl .mfn-icon-box-right .icon-wrapper { margin: 0 0 0 10px; }

    /* Top & Bottom */
    .mfn-header-tmpl .mfn-icon-box-top .icon-wrapper { margin: 0 0 5px 0; }
    .mfn-header-tmpl .mfn-icon-box-bottom .icon-wrapper { margin: 5px 0 0 0; }

    /* Icon wrapper - Width */
    .column_header_icon.mfn-item-inline .icon-wrapper { width: auto; }
    .column_header_burger.mfn-item-inline .icon-wrapper { width: auto; }

    @media(max-width: 959px) {
        /* Left * Right */
        .mfn-header-tmpl .mfn-icon-box-tablet-left .icon-wrapper { margin: 0 10px 0 0; }
        .mfn-header-tmpl .mfn-icon-box-tablet-right .icon-wrapper { margin: 0 0 0 10px; }
        /* Top & Bottom */
        .mfn-header-tmpl .mfn-icon-box-tablet-top .icon-wrapper { margin: 0 0 5px 0; }
        .mfn-header-tmpl .mfn-icon-box-tablet-bottom .icon-wrapper { margin: 5px 0 0 0; }
    }
    @media(max-width: 767px) {
        /* Left * Right */
        .mfn-header-tmpl .mfn-icon-box-mobile-left .icon-wrapper { margin: 0 10px 0 0; }
        .mfn-header-tmpl .mfn-icon-box-mobile-right .icon-wrapper { margin: 0 0 0 10px; }
        /* Top & Bottom */
        .mfn-header-tmpl .mfn-icon-box-mobile-top .icon-wrapper { margin: 0 0 5px 0; }
        .mfn-header-tmpl .mfn-icon-box-mobile-bottom .icon-wrapper { margin: 5px 0 0 0; }
    }

    /* Empty */
    .mfn-header-tmpl .mfn-icon-box-empty-desc .icon-wrapper { margin: 0; }





/* --------------------------------------------------------------------------------------------------------------- */
/* ------------------------------------------------ Mega Menu ---------------------------------------------------- */
/* --------------------------------------------------------------------------------------------------------------- */

/* Variables */
.mfn-megamenu-tmpl-builder {
    --mfn-column-gap-left: 12px;
    --mfn-column-gap-right: 12px;
    --mfn-megamenu-menu-gap: 0px;
    --mfn-megamenu-menu-icon-gap: 5px;
    --mfn-megamenu-menu-submenu-icon-gap: 10px;
    --mfn-megamenu-menu-sep: rgba(1,7,39,.15);
    --mfn-megamenu-menu-animation-height: 3px;
    --mfn-megamenu-menu-icon-size: 25px;
    --mfn-megamenu-submenu-icon-size: 13px;
    --mfn-megamenu-menu-animation-color: #0089f7;
    --mfn-column-gap-bottom: 10px;
    --mfn-megamenu-submenu-border-radius-top: 4px;
    --mfn-megamenu-submenu-border-radius-right: 4px;
    --mfn-megamenu-submenu-border-radius-bottom: 4px;
    --mfn-megamenu-submenu-border-radius-left: 4px;
}


.mfn-menu-item-megamenu { position: absolute; top: 100%; }

    /* Types */
    .mfn-megamenu-full-width,
    .mfn-megamenu-grid { width: 100vw; }
    .mfn-megamenu-full-width .container{ max-width: 100%; }
    .mfn-megamenu-custom-width { width: 220px; }

    /* Positions */
    .mfn-megamenu-pos-left { left: 0; }
    .mfn-megamenu-pos-right { right: 0; left: auto; }

    /* Animation */
    .mfn-menu-item-has-megamenu .mfn-menu-item-megamenu { display: none; }
    .mfn-menu-submenu-on-hover .mfn-menu-item-has-megamenu:hover > .mfn-menu-item-megamenu{ display: block; }
    .mfn-menu-submenu-on-click .mfn-menu-item-has-megamenu.mfn-li-hover > .mfn-menu-item-megamenu{ display: block; }

    /* Mobile width mod */
    @media only screen and (max-width: 767px) {
        .mfn-menu-item-megamenu { max-width: 90vw; }
        .mfn-menu-item-megamenu > .container { padding-left: 0 !important; padding-right: 0 !important; }
    }

    /* Icon box fix */
    .mfn-menu-item-megamenu .mfn-icon-box {
        --mfn-header-icon-color: #0089F7;
        --mfn-header-menu-icon-size: 7vh;
    }
    .mfn-menu-item-megamenu .mfn-icon-box .icon-wrapper img,
    .mfn-menu-item-megamenu .mfn-icon-box .icon-wrapper svg { width: auto; }


    /* MegaMenu display */
    @media only screen and (min-width: 1240px) {
        .mfn-header-menu .mfn-menu-item-megamenu-always-on > .mfn-submenu { display: block; }
        .home .mfn-header-menu .mfn-menu-item-megamenu-always-on-home > .mfn-submenu { display: block; }
    }
    @media only screen and (max-width: 1239px) {
        .mfn-header-menu .mfn-menu-item-megamenu-always-on > .mfn-submenu .mfn-menu-item-megamenu,
        .mfn-header-menu .mfn-menu-item-megamenu-always-on-home > .mfn-submenu .mfn-menu-item-megamenu { display: none !important; }

        .mfn-header-menu .mfn-menu-item-megamenu-always-on .mfn-menu-item-has-megamenu .menu-sub,
        .mfn-header-menu .mfn-menu-item-megamenu-always-on-home .mfn-menu-item-has-megamenu .menu-sub { display: none; }
    }

    .mfn-header-menu .mfn-submenu .mfn-menu-item-has-megamenu { position: static; } /* Fix for megamenu 100% min-height */
    .mfn-header-menu .mfn-submenu .mfn-menu-item-megamenu { min-height: 100%; }
    .mfn-header-menu .mfn-submenu .mfn-megamenu-pos-left { left: 100%; top: 0; }
    .mfn-header-menu .mfn-submenu .mfn-megamenu-pos-right { right: 100%; top: 0; }


/****************************** Items ******************************/

/*********** Mega Menu: Menu */

.mfn-megamenu-menu { display: flex; align-items: stretch; flex-wrap: wrap; font-size: 15px; line-height: 1.5; font-weight: 500; }
.mfn-megamenu-menu,
.mfn-megamenu-menu .sub-menu { margin: 0; padding: 0; list-style: none; }
.mfn-megamenu-menu li { position: relative; }
.mfn-megamenu-menu li a { display: flex; text-decoration: none; }
.mfn-megamenu-menu > li:last-child li:last-child a,
.mfn-megamenu-menu > li:last-child:not(.menu-item-has-children) > a { border-bottom: 0 !important; }

/* 1st level */
.mfn-megamenu-menu > li > a { position: relative; align-items: center; padding: 8px 20px; width: 100%; box-sizing: border-box; }
.mfn-megamenu-menu > li > a .menu-item-helper { display: flex; position: absolute; left: 0; top: 0; width: 100%; height: 100%; box-sizing: border-box;}

/* 2nd & 3rd level */
.mfn-megamenu-menu li .sub-menu { display: none; position:absolute; width: 220px; }
.mfn-megamenu-menu li .sub-menu a { align-items: center; padding: 10px 20px; }
.mfn-megamenu-menu li .sub-menu a .menu-sub { margin-left: auto; }
.mfn-mm-submenu-on-hover li:hover > .sub-menu,
.mfn-mm-submenu-on-click li.mfn-li-hover > .sub-menu { display: block; }

/* 2nd level */
.mfn-megamenu-menu.mfn-mm-menu-horizontal > li .sub-menu {left:0;top:100%;}

/* 3rd level */
.mfn-megamenu-menu.mfn-mm-menu-horizontal > li .sub-menu li .sub-menu {left:100%;top:0;}


    .mfn-megamenu-menu li .sub-menu { border-radius: var(--mfn-megamenu-submenu-border-radius-top) var(--mfn-megamenu-submenu-border-radius-right) var(--mfn-megamenu-submenu-border-radius-bottom) var(--mfn-megamenu-submenu-border-radius-left); }
    .mfn-megamenu-menu li .sub-menu li:first-child a { border-radius: var(--mfn-megamenu-submenu-border-radius-top) var(--mfn-megamenu-submenu-border-radius-right) 0 0; }
    .mfn-megamenu-menu li .sub-menu li:last-child a { border-radius: 0 0 var(--mfn-megamenu-submenu-border-radius-bottom) var(--mfn-megamenu-submenu-border-radius-left); }

    /* Vertical */
    .mfn-mm-menu-vertical { flex-direction: column; }
    .mfn-mm-menu-vertical li { display: flex; flex-direction: column; }
    .mfn-mm-menu-vertical li .sub-menu { display: block; position: static; width: 100%; }

    /* Horizontal */
    .mfn-mm-menu-horizontal > li { display: inline-flex; }

    /* Menu type */
    .mfn-mm-submenu-visible li .menu-sub { display: none; }
    .mfn-mm-submenu-toggled.mfn-mm-menu-vertical li .sub-menu { display: none; }
    .mfn-mm-submenu-toggled .sub-menu { display: none; }


    /* Submenu type animation ------------------------------------*/
    .mfn-mm-submenu-show-fade-in.mfn-mm-submenu-on-hover li:hover > .sub-menu,
    .mfn-mm-submenu-show-fade-in.mfn-mm-submenu-on-click li.mfn-li-hover > .sub-menu { animation: mfnMmSubmenuFadeIn 0.5s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnMmSubmenuFadeIn 0.5s cubic-bezier(0.2,1,0.3,1); }
    @keyframes mfnMmSubmenuFadeIn {
        0%   {opacity: 0;}
        100% {top: 1;}
    }
    @-webkit-keyframes mfnMmSubmenuFadeIn {
        0%   {opacity: 0;}
        100% {top: 1;}
    }
    .mfn-mm-submenu-show-fade-up.mfn-mm-submenu-on-hover li:hover > .sub-menu,
    .mfn-mm-submenu-show-fade-up.mfn-mm-submenu-on-click li.mfn-li-hover > .sub-menu { animation: mfnMmSubmenuFadeInUp 0.5s cubic-bezier(0.2,1,0.3,1); -webkit-animation: mfnMmSubmenuFadeInUp 0.5s cubic-bezier(0.2,1,0.3,1); }
    @keyframes mfnMmSubmenuFadeInUp {
        0%   {opacity: 0; transform: translateY(20px)}
        100% {top: 1; transform: translateY(0);}
    }
    @-webkit-keyframes mfnMmSubmenuFadeInUp {
        0%   {opacity: 0; transform: translateY(20px)}
        100% {top: 1; transform: translateY(0);}
    }

    /* Colors ------------------------------------*/
    .mfn-megamenu-menu li > a { color: #73748c; }
    .mfn-megamenu-menu li > a:hover { color: #2a2b39; }
    .mfn-megamenu-menu li.current-menu-item > a { color: #0089f7; }
    .mfn-megamenu-menu a .menu-icon i { color: #0089f7; }

    .mfn-mm-menu-horizontal .sub-menu { background-color: #fff; box-shadow: 0px 10px 20px 0px rgba(1,7,39,.05); }

    /* Z-index ------------------------------------*/
    .mfn-megamenu-menu > li > .menu-item-helper { z-index: 1; }
    .mfn-megamenu-menu > li { z-index: 1; }
    .mfn-megamenu-menu > li a  .menu-icon,
    .mfn-megamenu-menu > li a  .menu-sub { z-index: 1; }
    .mfn-megamenu-menu > li:hover { z-index: 2; }
    .mfn-megamenu-menu > li a .sub-menu { z-index: 3; }
    .mfn-megamenu-menu > li > a { z-index: 4; }
    .mfn-menu-separator-on > li:after { z-index: 4; }

    /* Addons ------------------------------------*/
    .mfn-megamenu-menu a .menu-label { white-space: nowrap; }

    .mfn-megamenu-menu > li a span.menu-sub i { font-size: var(--mfn-megamenu-submenu-icon-size); }
    .mfn-megamenu-menu li:not(.menu-item-has-children) a .menu-sub { display: none !important; }
    .mfn-megamenu-menu li .sub-menu a .menu-sub > i { font-size: var(--mfn-megamenu-submenu-submenu-icon-size); }

    .mfn-megamenu-menu a .menu-icon { line-height: 1; }
    .mfn-megamenu-menu a .menu-icon i { font-size: var(--mfn-megamenu-menu-icon-size); }
    .mfn-megamenu-menu li .sub-menu a .menu-icon { margin-right: var(--mfn-megamenu-submenu-subicon-gap); }
    .mfn-megamenu-menu li .sub-menu a .menu-icon > i { font-size: var(--mfn-megamenu-submenu-subicon-size); }
    .mfn-megamenu-menu a .menu-icon > svg,
    .mfn-megamenu-menu a .menu-icon > img { height:auto; width: var(--mfn-megamenu-menu-icon-size); }

    .mfn-megamenu-menu a .label-wrapper { position: relative; display: flex; flex-direction: column; }
    .mfn-megamenu-menu a .label-wrapper .menu-desc { font-size: 85%; }

    .mfn-megamenu-menu li.menu-item-has-children > a > .decoration-icon { display: none; }
    .mfn-megamenu-menu .decoration-icon i { font-size: 13px; }
    .mfn-mm-menu-icon-left .decoration-icon { margin-left: auto; }
    .mfn-mm-menu-icon-right .decoration-icon { margin-right: auto; }
    .mfn-mm-menu-icon-top .decoration-icon { margin-left: 10px; }


    /* Submenu OFF */
    .mfn-mm-submenu-off a .menu-sub,
    .mfn-mm-submenu-off .sub-menu { display: none !important; }

    /* Icon positions ------------------------------------*/
    .mfn-mm-menu-icon-left a > .menu-icon { margin-right: var(--mfn-megamenu-menu-icon-gap); }
    .mfn-mm-menu-icon-left a > .menu-sub { margin-left: var(--mfn-megamenu-menu-submenu-icon-gap); }
    .mfn-mm-menu-icon-left.mfn-mm-menu-vertical a > .menu-sub { margin-left: auto; }

    .mfn-mm-menu-icon-right li > a { flex-direction: row-reverse; }
    .mfn-mm-menu-icon-right a > .menu-icon { margin-left: var(--mfn-megamenu-menu-icon-gap); }
    .mfn-mm-menu-icon-right a > .menu-sub { margin-right: var(--mfn-megamenu-menu-submenu-icon-gap); }
    .mfn-mm-menu-icon-right.mfn-mm-menu-vertical a > .menu-sub { margin-right: auto; margin-left: unset !important; }

    .mfn-mm-menu-icon-top > li > a { flex-wrap: wrap; justify-content: center; }
    .mfn-mm-menu-icon-top a > .menu-icon { margin-bottom: var(--mfn-megamenu-menu-icon-gap); width: 100%; text-align: center; }
    .mfn-mm-menu-icon-top a > .menu-sub { margin-left: var(--mfn-megamenu-menu-submenu-icon-gap); }
    .mfn-mm-menu-icon-top a > .label-wrapper { align-items: center; }
    .mfn-mm-menu-icon-top li .sub-menu a { justify-content: center; flex-wrap: wrap; }
    .mfn-mm-menu-icon-top li .sub-menu a .menu-sub { margin-left: var(--mfn-megamenu-menu-submenu-icon-gap); }

    .mfn-mm-menu-icon-top.mfn-items-align-left li a,
    .mfn-mm-menu-icon-top.mfn-items-align-right li a { justify-content: center; }
    .mfn-mm-menu-icon-top.mfn-items-align-left a > .menu-icon,
    .mfn-mm-menu-icon-top.mfn-items-align-right a > .menu-icon { text-align: center; }

    /* Menu icon - Animation ------------------------------------*/
    .mfn-mm-menu-icon-rotate li > a .menu-icon { transition: transform .3s ease-out; }
    .mfn-mm-menu-icon-rotate li:hover > a .menu-icon,
    .mfn-mm-menu-icon-rotate li.current-menu-item > a .menu-icon { transform: rotate(180deg); }

    .mfn-mm-menu-icon-zoom li > a .menu-icon { transition: transform .3s ease-out; }
    .mfn-mm-menu-icon-zoom li:hover > a .menu-icon,
    .mfn-mm-menu-icon-zoom li.current-menu-item > a .menu-icon { transform: scale(1.2) }

    /* Menu subicon - Animation ------------------------------------*/
    .mfn-mm-submenu-icon-rotate li > a .menu-sub { transition: transform .3s ease-out; }
    .mfn-mm-submenu-icon-rotate li:hover > a .menu-sub,
    .mfn-mm-submenu-icon-rotate li.current-menu-item > a .menu-sub,
    .mfn-mm-submenu-icon-rotate li.mfn-li-childrens-show > a .menu-sub { transform: rotate(180deg); }

    .mfn-mm-submenu-icon-zoom li > a .menu-sub { transition: transform .3s ease-out; }
    .mfn-mm-submenu-icon-zoom li:hover > a .menu-sub,
    .mfn-mm-submenu-icon-zoom li.current-menu-item > a .menu-sub,
    .mfn-mm-submenu-icon-zoom li.mfn-li-childrens-show > a .menu-sub { transform: scale(1.2) }


/* --------------------------------------------------------------------------------------------------------------- */
/* ------------------------------------------------- Footer ------------------------------------------------------ */
/* --------------------------------------------------------------------------------------------------------------- */


/* Types */
.mfn-footer-fixed .mfn-footer-tmpl { position: fixed; width: 100%; bottom: 0; left: 0; z-index: 1; }

.mfn-footer-sliding .mfn-footer-tmpl { position: fixed; width: 100%; bottom: 0; left: 0; z-index: -1; }
.mfn-footer-sliding #Wrapper { z-index: 0; }
.mfn-footer-sliding #Content { position: relative; }

.mfn-footer-stick .mfn-footer-tmpl.is-sticky { position: fixed; width: 100%; bottom: 0; left: 0; }

@media only screen and (max-width: 767px) {
    .mfn-footer-fixed .mfn-footer-tmpl,
    .mfn-footer-sliding .mfn-footer-tmpl {
        position: static;
    }
    .mfn-footer-fixed #Content,
    .mfn-footer-sliding #Content {
        margin-bottom: 0 !important;
    }
    .footer-menu-sticky .mfn-footer-tmpl { padding-bottom: 70px; }

    .mfn-footer-tmpl .mcb-section:not(.full-width) { max-width: 700px; padding-left: 33px; padding-right: 33px; }
    .mfn-footer-tmpl .mcb-section .section_wrapper { max-width: 100% !important; }
}


/****************************** Items ******************************/

/*********** Footer: Logo */
.column_footer_logo .logo-wrapper { display: flex; align-items: center; width: 100%; height: 100%; line-height: 0; }
.column_footer_logo .logo-wrapper img { max-width: 100%; height: auto; }

/*********** Header: Menu */

.mfn-footer-menu { display: flex; flex-direction: column; font-size: 15px; line-height: 1.5; font-weight: 500;  margin: 0; padding: 0; list-style: none; }
.mfn-footer-menu li { position: relative; }
.mfn-footer-menu li a { display: block; text-decoration: none; }
.mfn-footer-menu li ul { display: none; }

.mfn-footer-menu-style-horizontal { display: flex; flex-direction: row; flex-wrap: wrap; }

.mfn-footer-menu-style-vertical li a { padding: 10px 0; }
.mfn-footer-menu-style-horizontal li a {  display: flex; padding: 0 15px; }

/* --------------------------------------------------------------------------------------------------------------- */
/* ------------------------------------------------- Swiper ------------------------------------------------------ */
/* --------------------------------------------------------------------------------------------------------------- */

body {
    --mfn-swiper-pagination-bullet-active-color: #007aff;
    --mfn-swiper-pagination-bullet-inactive-color: #000;
    --mfn-swiper-pagination-bullet-width: 10px;
    --mfn-swiper-pagination-bullet-height: 10px;
    --mfn-swiper-pagination-spacing: 20px;
    --mfn-swiper-arrow-width: auto;
    --mfn-swiper-arrow-height: auto;
    --mfn-swiper-arrow-size: 50px;
    --mfn-swiper-arrow-spacing: 20px;
}

.mfn-builder-content .swiper .swiper-slide {flex-shrink: 0;width: 100%;height: 100%;position: relative;transition-property: transform; overflow: hidden;}

.mfn-builder-content .swiper-pagination { line-height: 1; }
.mfn-builder-content .swiper-pagination-bullet { margin: 0 5px; width: var(--mfn-swiper-pagination-bullet-width); height: var(--mfn-swiper-pagination-bullet-height); transition: transform .2s ease-in-out; }
.mfn-builder-content .swiper-pagination-bullet-active { background: var(--mfn-swiper-pagination-bullet-active-color); transform: scale(1.2); opacity: 1 !important; }

.mfn-builder-content .swiper-button-next,
.mfn-builder-content .swiper-button-prev { flex-shrink: 0; margin-top: 0; width: var(--mfn-swiper-arrow-width); height: var(--mfn-swiper-arrow-height); }
.mfn-builder-content .swiper-button-next:after,
.mfn-builder-content .swiper-button-prev:after { display: none; }
.mfn-builder-content .swiper-button-next i,
.mfn-builder-content .swiper-button-prev i { font-size: var(--mfn-swiper-arrow-size); }
.mfn-builder-content .swiper-button-next i:before,
.mfn-builder-content .swiper-button-prev i:before { margin: 0; }

/* Order */
.mfn-builder-content .swiper-button-prev { order: 1; }
.mfn-builder-content .swiper { order: 2; }
.mfn-builder-content .swiper-button-next { order: 3; }
.mfn-builder-content .swiper-pagination { order: 4; }

/* Arrows styles */
.mfn-arrows-standard .swiper-button-next,
.mfn-arrows-standard .swiper-button-prev { position: static; align-self: center; }
.mfn-arrows-standard .swiper { flex: 1; margin: 0 var(--mfn-swiper-arrow-spacing); }

.mfn-arrows-absolute{ position: relative; }
.mfn-arrows-absolute .mfn-swiper-arrow{ position: absolute; top: 50%; transform: translateY(-50%); z-index: 3; }
.mfn-arrows-absolute .mfn-swiper-arrow.swiper-button-prev{ left: 0; }
.mfn-arrows-absolute .mfn-swiper-arrow.swiper-button-next{ right: 0; }

.mfn-arrows-overlay .swiper-button-next,
.mfn-arrows-overlay .swiper-button-prev { position: absolute; top: 50%; transform: translateY(-50%); }
.mfn-arrows-overlay .swiper-button-prev { left: var(--mfn-swiper-arrow-spacing); }
.mfn-arrows-overlay .swiper-button-next { right: var(--mfn-swiper-arrow-spacing); }

.mfn-arrows-custom .swiper-button-next,
.mfn-arrows-custom .swiper-button-prev { left: unset; right: unset; top: unset; bottom: unset; }

/* Dots styles */
.mfn-dots-standard .swiper-pagination { width: 100%; margin-top: var(--mfn-swiper-pagination-spacing); }

.mfn-dots-overlay .swiper-pagination { position: absolute; left: 50%; bottom: var(--mfn-swiper-pagination-spacing); transform: translateX(-50%); }

.mfn-dots-custom .swiper-pagination { position: absolute; }

/* --------------------------------------------------------------------------------------------------------------- */
/* ------------------------------------------------- Query loop ------------------------------------------------------ */
/* --------------------------------------------------------------------------------------------------------------- */

.mfn-looped-items-slider-wrapper {}

.mfn-looped-items-slider{ display: block; width: 100%; position: relative;}
.mfn-queryloop-item-wrapper{ display: flex !important; flex-wrap: wrap; position: relative; width: 100%; box-sizing: border-box; }
.mfn-looped-items-slider .mfn-queryloop-item-wrapper .mcb-column:last-child .mcb-column-inner { margin-bottom: 0; } /* Remove margin bottom for the last item in slider */

.mfn-query-loop-masonry{display: block; position: relative; width: 100%;}

.mfn-ql-slider-wrapper-offset{overflow: hidden;}
.mfn-ql-slider-wrapper-offset .swiper{overflow: visible;}

/* Arrows standard * Dots standard */
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-standard .section_wrapper { justify-content: center; }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-standard .swiper{order: 1; margin: 0 0 var(--mfn-swiper-pagination-spacing); }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-standard .swiper-button-prev { order: 2;  margin-right: var(--mfn-swiper-arrow-spacing); }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-standard .swiper-pagination { order: 3; width: auto; align-self: center; margin-top: 0; }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-standard .swiper-button-next { order: 4;  margin-left: var(--mfn-swiper-arrow-spacing); }

/* Arrows standard * Dots overlay */
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-overlay .swiper-button-next,
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-overlay .swiper-button-prev { position: absolute; top: 50%; transform: translateY(-50%); }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-overlay .swiper-button-prev { left: var(--mfn-swiper-arrow-spacing); }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-overlay .swiper-button-next { right: var(--mfn-swiper-arrow-spacing); }

/* Arrows standard * Dots hidden */
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-hidden .section_wrapper { justify-content: center; }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-hidden .swiper{order: 1; margin: 0 0 var(--mfn-swiper-pagination-spacing); }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-hidden .swiper-button-prev { order: 2; margin-right: var(--mfn-swiper-arrow-spacing); }
.mfn-ql-slider-wrapper-offset.mfn-arrows-standard.mfn-dots-hidden .swiper-button-next { order: 3; }


/* Loop masonry */
.mfn-query-loop-masonry .mfn-queryloop-item-wrapper{display: block !important; float: left;}
.mfn-looped-items-masonry .section_wrapper{display: block;}


body:not(.mfn-ui) .mfn-looped-items-slider-wrapper .section_wrapper .swiper-wrapper .mfn-queryloop-item-wrapper{margin-left: 0; margin-right: 0;}

/* --------------------------------------------------------------------------------------------------------------- */
/* ------------------------------------------------- Popups ------------------------------------------------------ */
/* --------------------------------------------------------------------------------------------------------------- */

html.mfn-popup-template-visible{ overflow: hidden; max-height: 100vh; }
.mfn-popup-tmpl{display:none;position:fixed;top:0;left:0;width:100%;height:100%;z-index:10000;}
.mfn-popup-tmpl.mfn-popup-active{ display: block;}
.mfn-popup-tmpl:before{ content:""; display: block; width:200%; height: 200%; left:-50%; top: -50%; position: absolute;}
.mfn-popup-tmpl.mfn-popup-active:before{ animation-name: mfnpopupfade-in; animation-duration: 1s; }
.mfn-popup-tmpl-content{display:flex;position:fixed;max-height: calc(100vh - (2*var(--mfn-popup-tmpl-offset)) );max-width: calc(100vw - (2*var(--mfn-popup-tmpl-offset)) );box-sizing:border-box;z-index:1999;background-color:#fff;border-radius: 3px; box-shadow: 0px 2px 4px rgba(105, 103, 139, 0.03); }
.mfn-popup-tmpl-content .mfn-popup-tmpl-content-wrapper{ overflow:auto; padding: 0; box-sizing: border-box; max-height: 100%; display: block; width: 100%;}

.mfn-popup-tmpl.mfn-popup-tmpl-center .mfn-popup-tmpl-content{top:50%;transform:translateY(-50%) translateX(-50%);left:50%;}
.mfn-popup-tmpl.mfn-popup-tmpl-top-left .mfn-popup-tmpl-content{top:0;left:0;margin: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-top-center .mfn-popup-tmpl-content{top:0;transform:translateX(-50%);left:50%; margin-top: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-top-right .mfn-popup-tmpl-content{top:0;right:0;margin: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-center-left .mfn-popup-tmpl-content{top:50%;transform:translateY(-50%);left:0;margin-left: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-center-right .mfn-popup-tmpl-content{top:50%;transform:translateY(-50%);right:0;margin-right: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-bottom-left .mfn-popup-tmpl-content{bottom:0;left:0;margin: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-bottom-center .mfn-popup-tmpl-content{bottom:0;transform:translateX(-50%);left:50%;margin-bottom: var(--mfn-popup-tmpl-offset);}
.mfn-popup-tmpl.mfn-popup-tmpl-bottom-right .mfn-popup-tmpl-content{bottom:0;right:0;margin: var(--mfn-popup-tmpl-offset);}

.mfn-popup-tmpl.mfn-popup-tmpl-width-default .mfn-popup-tmpl-content{width: 640px;}
.mfn-popup-tmpl-custom-width .mfn-popup-tmpl-content{width: 640px;}
.mfn-popup-tmpl.mfn-popup-tmpl-full-width .mfn-popup-tmpl-content{width: 100%;}

.mfn-popup-tmpl .exit-mfn-popup:hover{text-decoration: none;}
.mfn-popup-tmpl .mfn-popup-tmpl-content .mcb-column a.exit-mfn-popup{ display: inline-block; font-size: var(--mfn-exitbutton-item-size); margin:0;}
.mfn-popup-tmpl .mfn-popup-tmpl-content .mcb-column a.exit-mfn-popup img{ width: var(--mfn-exitbutton-item-size); }

.mfn-popup-tmpl .exit-mfn-popup-abs{position: absolute; top: 0; z-index: 10; width: var(--mfn-exitbutton-size); height: var(--mfn-exitbutton-size); display: flex; justify-content: center; align-items: center; font-size: var(--mfn-exitbutton-font-size); transition: 0.3s; line-height: 1em;}
.mfn-popup-close-button-right .exit-mfn-popup-abs{ right: var(--mfn-exitbutton-offset-horizontal); }
.mfn-popup-close-button-left .exit-mfn-popup-abs{ left: var(--mfn-exitbutton-offset-horizontal); }
.mfn-popup-tmpl.mfn-popup-tmpl-close-button-show-delay .exit-mfn-popup{ opacity: 0; transition: 0.5s }
.mfn-popup-tmpl.mfn-popup-tmpl-close-button-hidden .exit-mfn-popup-abs{ display: none; }
.mfn-popup-tmpl.mfn-popup-tmpl-close-button-show-delay.mfn-closebutton-active .exit-mfn-popup{ opacity: 1; }

.mfn-popup-tmpl.mfn-popup-animate-fade-in.mfn-popup-active{ animation-name: mfnpopupfade-in; animation-duration: 0.5s; }
.mfn-popup-tmpl.mfn-popup-animate-zoom-in.mfn-popup-active{ animation-name: mfnpopupzoom-in; animation-duration: 0.5s; }
.mfn-popup-tmpl.mfn-popup-animate-fade-in-up.mfn-popup-active{ animation-name: mfnpopupfade-in-up; animation-duration: 0.5s; }
.mfn-popup-tmpl.mfn-popup-animate-fade-in-down.mfn-popup-active{ animation-name: mfnpopupfade-in-down; animation-duration: 0.5s; }
.mfn-popup-tmpl.mfn-popup-animate-fade-in-left.mfn-popup-active{ animation-name: mfnpopupfade-in-left; animation-duration: 0.5s; }
.mfn-popup-tmpl.mfn-popup-animate-fade-in-right.mfn-popup-active{ animation-name: mfnpopupfade-in-right; animation-duration: 0.5s; }

@keyframes mfnpopupfade-in { 0% { opacity: 0; }, 100%{ opacity: 1; } }
@keyframes mfnpopupzoom-in { 0% {transform: scale(0.9);} 100% {transform: scale(1);} }
@keyframes mfnpopupfade-in-up { 0% {transform: translateY(100px);} 100% {transform: translateY(0);} }
@keyframes mfnpopupfade-in-down { 0% {transform: translateY(-100px);} 100% {transform: translateY(0);} }
@keyframes mfnpopupfade-in-left { 0% {transform: translateX(100px);} 100% {transform: translateX(0);} }
@keyframes mfnpopupfade-in-right { 0% {transform: translateX(-100px);} 100% {transform: translateX(0);} }

@media only screen and (min-width: 960px) and (max-width: 1239px){
.mfn-popup-tmpl .section_wrapper, .mfn-popup-tmpl .container {  max-width: 100%; }
}
@media only screen and (min-width: 768px) and (max-width: 959px) {
.mfn-popup-tmpl .section_wrapper, .mfn-popup-tmpl .container{ max-width: 100%; }
}
@media only screen and (max-width: 767px) {
.mfn-popup-tmpl .section_wrapper, .mfn-popup-tmpl .container { max-width: 100% !important; }
.mfn-popup-tmpl .section_wrapper, .mfn-popup-tmpl .container { max-width: 100% !important; }
.mfn-popup-tmpl .mfn-popup-tmpl-content-wrapper .section_wrapper, .mfn-popup-tmpl .container{max-width:100%!important;padding-left:0!important;padding-right:0!important}
.mfn-popup-tmpl-content{ max-height: calc(90vh - (2*var(--mfn-popup-tmpl-offset)) ); }
}


.mfn-footer .class-my-footer span{
	color:#fff !important;
}
.class-my-menu span{
	line-height: 30px;
    font-weight: 600;
    letter-spacing: 0px;
}
.mcb-section .mcb-wrap .mcb-item-hy6rof7nc .timeline_items > li h2{
	color: #A1C518;
    font-size: 22px;
}
.woocommerce ul.products li.product h2{
    background-position: center center;
    text-align: left;
    font-family: Poppins !important;
    font-size: 20px !important;
    line-height: 26px !important;
    font-weight: 600 !important;
    height: 72px !important;
}
.woocommerce .mfn-li-product-row.mfn-li-product-row-title h2.title{
	display:block !important;
}

form input.wpcf7-form-control.wpcf7-text, form .wpcf7-textarea {
    background: transparent !important;
    border-width: 0 0 1px 0 !important;
    color: #1c252a !important;
    box-shadow: none !important;
}
form.form-black input.wpcf7-form-control.wpcf7-text, form.form-black .wpcf7-textarea {
    border-color: #d6d8d9 !important;
}

form.form-white input.wpcf7-form-control.wpcf7-text, form.form-white .wpcf7-textarea {
    color: #fff !important;
}


.wpcf7-response-output {
    color: #1c252a;
}


form input.wpcf7-form-control.wpcf7-text::placeholder, form .wpcf7-textarea::placeholder {
  color: #fff;
  opacity: 1; /* Firefox */
}
form.form-black input.wpcf7-form-control.wpcf7-text::placeholder, form.form-black .wpcf7-textarea::placeholder {
  color: #1c252a;
}


form input.wpcf7-form-control.wpcf7-text::-ms-input-placeholder, form input.wpcf7-form-control.wpcf7-text::placeholder {
  color: #fff;
}

form.form-black input.wpcf7-form-control.wpcf7-text::-ms-input-placeholder, form.form-black input.wpcf7-form-control.wpcf7-text::placeholder {
  color: #8CA815;
}

.wpcf7-textarea {
    height: 120px;
}
form input.wpcf7-form-control.wpcf7-submit {
    box-shadow: none;
    height: auto;
    font-size: 14px;
    line-height: 1.5em;
    position: relative;
    text-transform: none;
    margin: 0 auto;
    padding: 9px 18px 9px 18px;
    border: 0;
    border-radius: 2px;
    width: auto;
    cursor: pointer;
    font-weight: 600;
    font-family: inherit;
    letter-spacing: 0;
    white-space: nowrap;
    transition: padding .25s;
    -webkit-transition: padding .25s;
    padding: 8px 18px 8px 18px;
    background: #1c252a !important;
    color: #fff !important;
    display: block;
    width: 100%;
}
form.form-white input.wpcf7-form-control.wpcf7-submit {
    background: #fff !important;
    color: #1c252a !important;
}

.wpcf7 form.sent .wpcf7-response-output {
    color: #fff !important;
}
.wpcf7 form.form-black.sent .wpcf7-response-output {
  /*  color: #8CA815 !important; */
}
.column.one-s p {
    margin-bottom: 0;
}

@media only screen and (max-width: 991px) {
	.mcb-section .mcb-wrap-9f94f1c44 .mcb-wrap-inner-9f94f1c44,.mcb-section .mcb-wrap-4627dd7e5 .mcb-wrap-inner-4627dd7e5 {
		padding-right: 25px !important;
		padding-left: 25px !important;
	}
}
.form-white .wpcf7-response-output, .form-black .wpcf7-response-output {
    color: #fff;
}
@media only screen and (max-width: 767px) {

.flex11111111{
    flex: auto !important;
}
.slider_pagination a {
    margin-bottom: 17px;
}
.reverse1 .mcb-section-inner{
    flex-direction: column-reverse;
    display: flex;
}
.post-528 .title {
  margin-top: 20px;
}
	
	.mcb-section .mcb-wrap .mcb-item-1viwmxv6d .mcb-column-inner-1viwmxv6d {
    margin-left: 0px  !important;
    margin-right: 0px  !important;
}
	
	.mcb-section .mcb-wrap .mcb-item-7yx44u3bh .title {

    font-size: 29px   !important;
    line-height: 41px  !important;
}}


@media only screen and (max-width:460px) {
.button.button_theme.button_size_2 {
  margin-right: -14px !important;
  font-size: 10px !important;
}
body, .mfn-menu-item-megamenu {
    font-size: 14px   !important;
    line-height: 21px   !important;
    font-weight: 400;
    letter-spacing: 0px;
}

}



/***************** respo **************/

@media(min-width:960px){
.mfn-header-menu .mfn-menu-li .mfn-submenu {
    width: 63px !important;
}}


@media(max-width:960px){
.mfn-header-menu .mfn-menu-li > .mfn-menu-link {
    color: #73748c !important;
}
ul.sub-menu.mfn-submenu {
    text-align: center !important;
    margin: 0 auto;
}
li.menu-item.wpml-ls-slot-144.wpml-ls-item.wpml-ls-item-it.wpml-ls-current-language.wpml-ls-menu-item.wpml-ls-last-item.menu-item-type-wpml_ls_menu_item.menu-item-object-wpml_ls_menu_item.menu-item-has-children.menu-item-wpml-ls-144-it.mfn-menu-li.mfn-li-hover {
    width: 30% !important;
    margin: 0 auto !important;
}

}


@media only screen and (max-width: 767px){
.marg11111111 {
    margin-top: 80px !important;
}
	.wpcf7-form .column {
    padding: 0;
}
	input.wpcf7-form-control.wpcf7-submit.has-spinner {
    width: 100% !important;
    margin: 0 auto;
    padding: 10px 0px 9px 0px !important;
    font-size: 11px !important;
}
	.espace111 .mcb-column-inner {
    padding-bottom: 0 !important;
}
	.espace111 {
    margin-top: -70px !important;
}
	.padding11 .mcb-wrap-inner {
    padding: 30px 20px 26px 20px !important;
    margin: -25px 0px 0px 0px !important;
}
	.padding2 .mcb-wrap-inner {
    padding: 30px 20px 26px 20px !important;
    margin: 39px 0px -54px 0px !important;
}
	.width111111111 .mcb-wrap-inner {
    padding: 20px 19px 0px 19px !important;
    margin: 0px auto!important;
}
	.espace11 .mcb-wrap-inner {
    padding: 25px 19px 0px 23px !important;
}
	.mcb-section-7bggmctqh {
    background-repeat: repeat-y !important;
}
	
	.mcb-section .mcb-wrap-k4936miwi .mcb-wrap-inner-k4936miwi {
 
    padding-top: 60px  !important;
    padding-right: 0px  !important;
    padding-bottom: 0px  !important;
    padding-left: 0px  !important;

}
	.content_wrapper .section_wrapper, .container, .four.columns .widget-area {
    max-width: 700px!important;
    padding-left: 21px!important;
    padding-right: 21px!important;
}
	.mcb-column-inner.mcb-column-inner-05555a98b.mcb-item-heading-inner {
    margin-top: 35px !important;
}
	.mcb-wrap-inner.mcb-wrap-inner-c3c892615 {
    padding: 0 !important;
    margin: 0 auto !important;
}
	.mcb-wrap-inner.mcb-wrap-inner-a36580301 {
    padding: 36px 9px 0px 9px !important;
    margin: 0 auto;
}
}

@media only screen and (max-width: 1239px){
.mcb-section-banv1ax6 {
    background-repeat: repeat-y !important;
}}

.mcb-section .mcb-wrap-fq1ratrle .mcb-wrap-inner-fq1ratrle {
	background-color: #8CA815 !important;}
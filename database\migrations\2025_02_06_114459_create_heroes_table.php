<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('heroes', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('subtitle');
            $table->string('video')->nullable(); // Video file path (optional)
            $table->string('poster')->nullable(); // Video poster image (optional)
            $table->string('background_image')->nullable(); // Background image for when video is not present
            $table->boolean('has_video')->default(false); // Flag to determine if video is present
            $table->timestamps();
        });

        Schema::create('icons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hero_id')->constrained('heroes')->onDelete('cascade'); // Link to the hero section
            $table->string('image'); // Icon image path
            $table->string('label'); // Icon label/text
            $table->string('link'); // Link associated with the icon
            $table->integer('sort_order')->default(0); // Order for displaying icons
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('heroes');
    }
};

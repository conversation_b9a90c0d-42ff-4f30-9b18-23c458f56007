<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Page extends Model
{
    use HasFactory;

    protected $fillable = ['slug', 'titre', 'icon_id'];

    public function sections()
    {
        return $this->hasMany(Section::class)->orderBy('ordre');
    }

    /**
     * Relation avec l'icône associée à cette page.
     */
    public function icon()
    {
        return $this->belongsTo(Icon::class);
    }
}

<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactResource\Pages;
use App\Filament\Resources\ContactResource\RelationManagers;
use App\Models\Contact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContactResource extends Resource
{
    protected static ?string $model = Contact::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('adresse')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('lien_facebook')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('lien_linkedin')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('lien_instagram')
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('telephone')
                    ->tel()
                    
                    ->maxLength(255),
                Forms\Components\TextInput::make('titre_horaire_ouverture_1')
                    
                    ->maxLength(255),
                Forms\Components\RichEditor::make('description_horaire_ouverture_1')
                    ,
                Forms\Components\TextInput::make('titre_horaire_ouverture_2')
                    
                    ->maxLength(255),
                Forms\Components\RichEditor::make('description_horaire_ouverture_2')
                    ,
                Forms\Components\FileUpload::make('logo_white')
                    ,
                Forms\Components\FileUpload::make('logo_black')
                    ,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('adresse')
                    ->searchable(),
                Tables\Columns\TextColumn::make('lien_facebook')
                    ->searchable(),
                Tables\Columns\TextColumn::make('lien_linkedin')
                    ->searchable(),
                Tables\Columns\TextColumn::make('lien_instagram')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('telephone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('logo_white')
                    ->searchable(),
                Tables\Columns\TextColumn::make('logo_black')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContacts::route('/'),
            'create' => Pages\CreateContact::route('/create'),
            'edit' => Pages\EditContact::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ImagesPropriete extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['propriete_id', 'url_image', 'statut', 'order_column', 'is_main'];

    public function propriete()
    {
        return $this->belongsTo(Propriete::class);
    }
}

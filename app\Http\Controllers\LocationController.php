<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class LocationController extends Controller
{
    /**
     * Recherche une adresse via l'API Nominatim (OpenStreetMap)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchAddress(Request $request)
    {
        $query = $request->input('query');
        
        if (empty($query)) {
            return response()->json(['error' => 'Requête vide'], 400);
        }

        try {
            // Utilisation de l'API Nominatim pour la recherche d'adresse
            $response = Http::withHeaders([
                'User-Agent' => 'RCI-Property-App/1.0'
            ])->get('https://nominatim.openstreetmap.org/search', [
                'format' => 'json',
                'q' => $query,
                'limit' => 5,
                'addressdetails' => 1
            ]);

            if ($response->successful()) {
                return response()->json($response->json());
            } else {
                return response()->json(['error' => 'Erreur lors de la recherche'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Recherche une adresse par composants (code postal, ville, rue, numéro)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchByComponents(Request $request)
    {
        $postalCode = $request->input('postal_code');
        $city = $request->input('city');
        $street = $request->input('street');
        $streetNumber = $request->input('street_number');
        
        // Construire la requête en combinant les composants disponibles
        $queryParts = [];
        
        if (!empty($streetNumber)) {
            $queryParts[] = $streetNumber;
        }
        
        if (!empty($street)) {
            $queryParts[] = $street;
        }
        
        if (!empty($city)) {
            $queryParts[] = $city;
        }
        
        if (!empty($postalCode)) {
            $queryParts[] = $postalCode;
        }
        
        $query = implode(', ', $queryParts);
        
        if (empty($query)) {
            return response()->json(['error' => 'Aucun critère de recherche fourni'], 400);
        }

        try {
            // Utilisation de l'API Nominatim pour la recherche d'adresse
            $response = Http::withHeaders([
                'User-Agent' => 'RCI-Property-App/1.0'
            ])->get('https://nominatim.openstreetmap.org/search', [
                'format' => 'json',
                'q' => $query,
                'limit' => 5,
                'addressdetails' => 1
            ]);

            if ($response->successful()) {
                return response()->json($response->json());
            } else {
                return response()->json(['error' => 'Erreur lors de la recherche'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}

/**
 * Script pour la recherche d'adresse et la mise à jour de la carte
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Script de recherche d\'adresse chargé');
    
    // Attendre que tout soit chargé
    setTimeout(setupSearchButton, 500);
    
    // Configurer le bouton de recherche
    function setupSearchButton() {
        const searchButton = document.getElementById('search_address_button');
        const addressInput = document.getElementById('search_address_input');
        
        if (searchButton && addressInput) {
            console.log('Bouton de recherche et champ d\'adresse trouvés');
            
            searchButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const address = addressInput.value.trim();
                if (!address) {
                    alert('Veuillez entrer une adresse à rechercher');
                    return;
                }
                
                console.log('Recherche d\'adresse:', address);
                searchAddress(address);
                
                return false;
            });
            
            console.log('Écouteur d\'événements attaché au bouton de recherche');
        } else {
            console.log('Bouton de recherche ou champ d\'adresse non trouvé, nouvelle tentative dans 500ms');
            setTimeout(setupSearchButton, 500);
        }
    }
    
    // Fonction pour rechercher une adresse via l'API Nominatim
    function searchAddress(address) {
        console.log('Début de la recherche d\'adresse pour:', address);
        
        // Afficher un indicateur de chargement
        const searchButton = document.getElementById('search_address_button');
        if (searchButton) {
            searchButton.textContent = 'Recherche en cours...';
            searchButton.disabled = true;
        }
        
        // Construire l'URL de l'API Nominatim
        const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`;
        
        // Effectuer la requête
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data && data.length > 0) {
                    const result = data[0];
                    const lat = parseFloat(result.lat);
                    const lng = parseFloat(result.lon);
                    
                    console.log('Coordonnées trouvées:', lat, lng);
                    
                    // Mettre à jour les champs de formulaire
                    document.querySelector('input[name="latitude"]').value = lat;
                    document.querySelector('input[name="longitude"]').value = lng;
                    
                    // Déclencher les événements pour que Livewire détecte les changements
                    document.querySelector('input[name="latitude"]').dispatchEvent(new Event('input', { bubbles: true }));
                    document.querySelector('input[name="longitude"]').dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // Mettre à jour la carte directement
                    updateMapDirectly(lat, lng);
                } else {
                    alert('Aucun résultat trouvé pour cette adresse. Veuillez essayer avec une adresse plus précise.');
                }
            })
            .catch(error => {
                console.error('Erreur lors de la recherche d\'adresse:', error);
                alert('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
            })
            .finally(() => {
                // Restaurer le bouton
                if (searchButton) {
                    searchButton.textContent = 'Rechercher';
                    searchButton.disabled = false;
                }
            });
    }
    
    // Fonction pour mettre à jour directement la carte Leaflet
    function updateMapDirectly(lat, lng) {
        console.log('Tentative de mise à jour directe de la carte');
        
        // Attendre que la carte soit chargée
        setTimeout(function() {
            try {
                // Trouver le conteneur de la carte
                const mapContainer = document.querySelector('.leaflet-container');
                if (!mapContainer) {
                    console.log('Conteneur de carte non trouvé');
                    return;
                }
                
                // Accéder à l'objet carte via le conteneur
                const map = mapContainer._leaflet;
                if (!map) {
                    console.log('Objet carte non trouvé');
                    return;
                }
                
                // Centrer la carte sur les nouvelles coordonnées
                map.setView([lat, lng], 13);
                console.log('Carte centrée sur:', lat, lng);
                
                // Mettre à jour le marqueur
                const markers = Object.values(map._layers).filter(layer => layer instanceof L.Marker);
                if (markers.length > 0) {
                    markers[0].setLatLng([lat, lng]);
                    console.log('Marqueur mis à jour');
                } else {
                    L.marker([lat, lng]).addTo(map);
                    console.log('Nouveau marqueur ajouté');
                }
            } catch (error) {
                console.error('Erreur lors de la mise à jour de la carte:', error);
                
                // Essayer une autre approche
                try {
                    // Forcer la mise à jour en modifiant directement l'état du composant Livewire
                    const locationInput = document.querySelector('input[name="data.location"]');
                    if (locationInput) {
                        locationInput.value = JSON.stringify({ lat, lng });
                        locationInput.dispatchEvent(new Event('input', { bubbles: true }));
                        console.log('État du composant Livewire mis à jour');
                    }
                    
                    // Essayer de déclencher un événement Livewire
                    if (window.Livewire) {
                        window.Livewire.dispatch('refreshMap');
                        console.log('Événement refreshMap déclenché');
                    }
                } catch (e) {
                    console.error('Erreur lors de la tentative de secours:', e);
                }
            }
        }, 100);
    }
});

// Script amélioré pour un slider de prix personnalisé
document.addEventListener('DOMContentLoaded', function() {
    console.log("Initialisation du slider de prix personnalisé v2...");

    // Fonction pour créer le slider de prix personnalisé
    function createCustomPriceSlider() {
        // Récupérer le conteneur du slider
        const sliderContainer = document.getElementById('custom-price-slider-container');
        if (!sliderContainer) {
            console.error("Conteneur du slider non trouvé");
            return;
        }

        // Récupérer les valeurs min et max
        const minPrice = parseInt(sliderContainer.getAttribute('data-min')) || 0;
        const maxPrice = parseInt(sliderContainer.getAttribute('data-max')) || 50000000;
        console.log("Valeurs du slider:", minPrice, maxPrice);

        // Calculer un pas approprié en fonction de la plage de prix
        const range = maxPrice - minPrice;
        let step = 1000; // Pas par défaut

        if (range > 10000000) {
            step = 500000; // Pas de 500k pour les grandes plages
        } else if (range > 5000000) {
            step = 250000; // Pas de 250k pour les plages moyennes-grandes
        } else if (range > 1000000) {
            step = 100000; // Pas de 100k pour les plages moyennes
        } else if (range > 500000) {
            step = 50000; // Pas de 50k pour les plages petites-moyennes
        } else if (range > 100000) {
            step = 10000; // Pas de 10k pour les petites plages
        }

        // Créer les éléments HTML pour le slider
        sliderContainer.innerHTML = `
            <div class="price-range-info">Sélectionnez une fourchette de prix</div>
            <div class="price-display">
                <span id="min-price-display">CHF ${formatNumber(minPrice)}</span>
                <span id="max-price-display">CHF ${formatNumber(maxPrice)}</span>
            </div>
            <div class="slider-container">
                <div class="slider-track">
                    <div class="slider-track-active"></div>
                </div>
                <div class="slider-handles">
                    <div id="min-handle" class="slider-handle" tabindex="0"></div>
                    <div id="max-handle" class="slider-handle" tabindex="0"></div>
                </div>
            </div>
            <div class="loading-indicator" id="price-loading-indicator" style="display: none;">
                <div class="loading-spinner"></div>
                <span>Filtrage en cours...</span>
            </div>
        `;

        // Récupérer les éléments créés
        const minPriceDisplay = document.getElementById('min-price-display');
        const maxPriceDisplay = document.getElementById('max-price-display');
        const sliderTrack = document.querySelector('.slider-track');
        const sliderTrackActive = document.querySelector('.slider-track-active');
        const minHandle = document.getElementById('min-handle');
        const maxHandle = document.getElementById('max-handle');

        // Champs cachés pour le formulaire
        const minPriceInput = document.querySelector('input[name="rng_min_price"]');
        const maxPriceInput = document.querySelector('input[name="rng_max_price"]');

        // Variables pour le suivi des valeurs
        let currentMinValue = minPrice;
        let currentMaxValue = maxPrice;

        // Initialiser les positions des poignées
        updateHandlePositions();

        // Fonction pour mettre à jour les positions des poignées
        function updateHandlePositions() {
            const trackWidth = sliderTrack.offsetWidth;
            const minPercent = (currentMinValue - minPrice) / (maxPrice - minPrice);
            const maxPercent = (currentMaxValue - minPrice) / (maxPrice - minPrice);

            // Positionner les poignées
            minHandle.style.left = `${minPercent * 100}%`;
            maxHandle.style.left = `${maxPercent * 100}%`;

            // Mettre à jour la barre active
            sliderTrackActive.style.left = `${minPercent * 100}%`;
            sliderTrackActive.style.width = `${(maxPercent - minPercent) * 100}%`;

            // Mettre à jour l'affichage des prix
            minPriceDisplay.textContent = `CHF ${formatNumber(currentMinValue)}`;
            maxPriceDisplay.textContent = `CHF ${formatNumber(currentMaxValue)}`;

            // Mettre à jour les champs cachés
            if (currentMinValue === minPrice && currentMaxValue === maxPrice) {
                minPriceInput.value = '';
                maxPriceInput.value = '';
            } else {
                minPriceInput.value = currentMinValue;
                maxPriceInput.value = currentMaxValue;
            }
        }

        // Fonction pour calculer la valeur en fonction de la position
        function calculateValue(position) {
            const trackWidth = sliderTrack.offsetWidth;
            const trackRect = sliderTrack.getBoundingClientRect();
            const percent = Math.max(0, Math.min(1, (position - trackRect.left) / trackWidth));

            // Calculer la valeur en fonction du pourcentage et arrondir au pas le plus proche
            let value = minPrice + percent * (maxPrice - minPrice);
            value = Math.round(value / step) * step;

            return Math.max(minPrice, Math.min(maxPrice, value));
        }

        // Variables pour le suivi du glissement
        let isDragging = false;
        let currentHandle = null;

        // Fonction pour gérer le début du glissement
        function startDrag(e, handle) {
            e.preventDefault();
            isDragging = true;
            currentHandle = handle;

            // Ajouter les écouteurs pour le glissement et la fin
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchmove', handleDrag, { passive: false });
            document.addEventListener('touchend', endDrag);

            // Ajouter une classe active
            handle.classList.add('active');
        }

        // Fonction pour gérer le glissement
        function handleDrag(e) {
            if (!isDragging) return;

            // Récupérer la position
            const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
            const value = calculateValue(clientX);

            // Mettre à jour la valeur en fonction de la poignée
            if (currentHandle === minHandle) {
                currentMinValue = Math.min(value, currentMaxValue - step);
            } else {
                currentMaxValue = Math.max(value, currentMinValue + step);
            }

            // Mettre à jour les positions
            updateHandlePositions();
        }

        // Variable pour stocker le timer de délai
        let submitTimer = null;

        // Fonction pour soumettre le formulaire
        function submitForm() {
            // Récupérer l'indicateur de chargement
            const loadingIndicator = document.getElementById('price-loading-indicator');

            // Mettre à jour les champs cachés
            if (currentMinValue === minPrice && currentMaxValue === maxPrice) {
                minPriceInput.value = '';
                maxPriceInput.value = '';
            } else {
                minPriceInput.value = currentMinValue;
                maxPriceInput.value = currentMaxValue;
            }

            // Trouver le formulaire parent
            const form = sliderContainer.closest('form');
            if (form) {
                // Afficher l'indicateur de chargement
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'flex';
                }

                // Ajouter une classe au conteneur pour indiquer que le filtre est en cours d'application
                sliderContainer.classList.add('filtering');

                // Soumettre le formulaire
                form.submit();
            } else {
                console.error('Formulaire non trouvé');
            }
        }

        // Fonction pour gérer la fin du glissement
        function endDrag() {
            if (!isDragging) return;

            isDragging = false;
            currentHandle.classList.remove('active');

            // Supprimer les écouteurs
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', endDrag);
            document.removeEventListener('touchmove', handleDrag);
            document.removeEventListener('touchend', endDrag);

            // Annuler le timer précédent si existant
            if (submitTimer) {
                clearTimeout(submitTimer);
            }

            // Définir un délai court avant de soumettre le formulaire
            // Cela permet à l'utilisateur de faire plusieurs ajustements rapides sans soumettre le formulaire à chaque fois
            submitTimer = setTimeout(submitForm, 800);
        }

        // Fonction pour gérer le clic sur la piste
        function handleTrackClick(e) {
            const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
            const value = calculateValue(clientX);

            // Déterminer quelle poignée déplacer (la plus proche)
            const distToMin = Math.abs(value - currentMinValue);
            const distToMax = Math.abs(value - currentMaxValue);

            if (distToMin <= distToMax) {
                currentMinValue = Math.min(value, currentMaxValue - step);
                currentHandle = minHandle;
            } else {
                currentMaxValue = Math.max(value, currentMinValue + step);
                currentHandle = maxHandle;
            }

            // Mettre à jour les positions
            updateHandlePositions();

            // Annuler le timer précédent si existant
            if (submitTimer) {
                clearTimeout(submitTimer);
            }

            // Définir un délai court avant de soumettre le formulaire
            submitTimer = setTimeout(submitForm, 800);
        }

        // Ajouter les écouteurs d'événements pour les poignées
        minHandle.addEventListener('mousedown', (e) => startDrag(e, minHandle));
        minHandle.addEventListener('touchstart', (e) => startDrag(e, minHandle), { passive: false });

        maxHandle.addEventListener('mousedown', (e) => startDrag(e, maxHandle));
        maxHandle.addEventListener('touchstart', (e) => startDrag(e, maxHandle), { passive: false });

        // Ajouter un écouteur pour le clic sur la piste
        sliderTrack.addEventListener('click', handleTrackClick);
        sliderTrack.addEventListener('touchstart', handleTrackClick, { passive: false });

        // Gestion du clavier pour l'accessibilité
        function handleKeyDown(e, handle) {
            const isMin = handle === minHandle;
            let value = isMin ? currentMinValue : currentMaxValue;

            switch (e.key) {
                case 'ArrowLeft':
                case 'ArrowDown':
                    value = Math.max(minPrice, value - step);
                    break;
                case 'ArrowRight':
                case 'ArrowUp':
                    value = Math.min(maxPrice, value + step);
                    break;
                case 'Home':
                    value = minPrice;
                    break;
                case 'End':
                    value = maxPrice;
                    break;
                default:
                    return; // Ne rien faire pour les autres touches
            }

            // Mettre à jour la valeur
            if (isMin) {
                currentMinValue = Math.min(value, currentMaxValue - step);
            } else {
                currentMaxValue = Math.max(value, currentMinValue + step);
            }

            // Mettre à jour les positions
            updateHandlePositions();
            e.preventDefault();
        }

        // Ajouter les écouteurs pour le clavier
        minHandle.addEventListener('keydown', (e) => handleKeyDown(e, minHandle));
        maxHandle.addEventListener('keydown', (e) => handleKeyDown(e, maxHandle));

        // Ajouter un écouteur pour soumettre le formulaire après l'utilisation du clavier
        function handleKeyUp(e) {
            if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(e.key)) {
                // Annuler le timer précédent si existant
                if (submitTimer) {
                    clearTimeout(submitTimer);
                }

                // Définir un délai court avant de soumettre le formulaire
                submitTimer = setTimeout(submitForm, 800);
            }
        }

        minHandle.addEventListener('keyup', handleKeyUp);
        maxHandle.addEventListener('keyup', handleKeyUp);
    }

    // Fonction pour formater les nombres avec séparateur de milliers
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, "'");
    }

    // Créer le slider
    createCustomPriceSlider();
});

/**
 * Script pour la recherche d'adresse et la mise à jour de la carte
 * Version simplifiée et directe
 */

// Fonction exécutée quand le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé, initialisation du script de recherche d\'adresse');
    
    // Attendre un peu pour s'assurer que tous les éléments sont chargés
    setTimeout(function() {
        setupSearchButton();
    }, 500);
    
    // Réessayer plusieurs fois au cas où
    setTimeout(function() {
        setupSearchButton();
    }, 1000);
    
    setTimeout(function() {
        setupSearchButton();
    }, 2000);
});

// Configuration du bouton de recherche
function setupSearchButton() {
    // Trouver le bouton de recherche
    const searchButton = document.getElementById('direct_search_button');
    if (!searchButton) {
        console.warn('Bouton de recherche non trouvé, nouvelle tentative plus tard');
        return;
    }
    
    // Vérifier si le bouton a déjà été configuré
    if (searchButton.dataset.configured === 'true') {
        console.log('Bouton déjà configuré, ignoré');
        return;
    }
    
    console.log('Bouton de recherche trouvé, ajout de l\'écouteur d\'événements');
    
    // Ajouter l'écouteur d'événements au bouton
    searchButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        console.log('Bouton de recherche cliqué');
        
        // Trouver le champ d'adresse
        const addressInput = document.getElementById('search_address_input');
        if (!addressInput) {
            console.error('Champ d\'adresse non trouvé');
            alert('Erreur: Champ d\'adresse non trouvé');
            return;
        }
        
        // Récupérer l'adresse
        const address = addressInput.value.trim();
        if (!address) {
            alert('Veuillez entrer une adresse à rechercher');
            return;
        }
        
        // Afficher un indicateur de chargement
        const originalContent = searchButton.innerHTML;
        searchButton.innerHTML = '<span class="flex items-center gap-1"><svg class="animate-spin h-5 w-5 -ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><span>Recherche en cours...</span></span>';
        searchButton.disabled = true;
        
        // Effectuer la recherche
        searchAddress(address, function() {
            // Restaurer le bouton
            searchButton.innerHTML = originalContent;
            searchButton.disabled = false;
        });
    });
    
    // Marquer le bouton comme configuré
    searchButton.dataset.configured = 'true';
    
    console.log('Bouton de recherche configuré avec succès');
}

// Fonction pour rechercher une adresse
function searchAddress(address, callback) {
    console.log('Recherche de l\'adresse:', address);
    
    // Construire l'URL de l'API Nominatim
    const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`;
    
    // Effectuer la requête
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Données reçues de Nominatim:', data);
            
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                
                console.log('Coordonnées trouvées:', lat, lng);
                
                // Mettre à jour les champs de latitude et longitude
                updateCoordinateFields(lat, lng);
                
                // Mettre à jour la carte
                updateMap(lat, lng);
            } else {
                console.warn('Aucun résultat trouvé');
                alert('Aucun résultat trouvé pour cette adresse. Veuillez essayer avec une adresse plus précise.');
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche d\'adresse:', error);
            alert('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
        })
        .finally(() => {
            if (typeof callback === 'function') {
                callback();
            }
        });
}

// Fonction pour mettre à jour les champs de latitude et longitude
function updateCoordinateFields(lat, lng) {
    console.log('Mise à jour des champs de coordonnées:', lat, lng);
    
    // Trouver les champs de latitude et longitude
    const latitudeInput = document.querySelector('input[name="latitude"]');
    const longitudeInput = document.querySelector('input[name="longitude"]');
    
    // Mettre à jour les valeurs
    if (latitudeInput) {
        latitudeInput.value = lat;
        console.log('Champ latitude mis à jour');
        // Déclencher un événement input pour que Filament/Livewire détecte le changement
        latitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
        console.warn('Champ latitude non trouvé');
    }
    
    if (longitudeInput) {
        longitudeInput.value = lng;
        console.log('Champ longitude mis à jour');
        // Déclencher un événement input pour que Filament/Livewire détecte le changement
        longitudeInput.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
        console.warn('Champ longitude non trouvé');
    }
    
    // Mettre à jour le champ location pour la carte
    const locationInputs = [
        document.querySelector('input[name="data.location"]'),
        document.querySelector('input[name="location"]')
    ].filter(Boolean);
    
    if (locationInputs.length > 0) {
        const locationState = { lat, lng };
        
        locationInputs.forEach(input => {
            input.value = JSON.stringify(locationState);
            console.log('Champ location mis à jour');
            
            // Déclencher plusieurs événements pour s'assurer que la modification est détectée
            ['input', 'change'].forEach(eventType => {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
        });
    } else {
        console.warn('Aucun champ location trouvé');
    }
}

// Fonction pour mettre à jour la carte
function updateMap(lat, lng) {
    console.log('Tentative de mise à jour de la carte avec les coordonnées:', lat, lng);
    
    // Essayer de déclencher un événement Livewire pour rafraîchir la carte
    if (window.Livewire) {
        console.log('Livewire trouvé, déclenchement de l\'événement refreshMap');
        window.Livewire.dispatch('refreshMap');
    }
    
    // Attendre un peu pour s'assurer que la carte est mise à jour
    setTimeout(() => {
        try {
            // Trouver le conteneur de la carte
            const mapContainers = document.querySelectorAll('.leaflet-container');
            console.log('Conteneurs de carte trouvés:', mapContainers.length);
            
            if (mapContainers.length === 0) {
                console.warn('Aucun conteneur de carte trouvé');
                return;
            }
            
            // Pour chaque conteneur de carte
            mapContainers.forEach((container, index) => {
                console.log(`Traitement du conteneur de carte #${index}`);
                
                // Essayer de trouver l'instance de carte Leaflet
                let map = null;
                
                // Méthode 1: via l'objet global L
                if (window.L) {
                    // Parcourir toutes les propriétés de L pour trouver la carte
                    for (const key in window.L) {
                        if (window.L[key] && typeof window.L[key] === 'object' && window.L[key]._container === container) {
                            map = window.L[key];
                            console.log('Carte trouvée via L[key]._container');
                            break;
                        }
                    }
                }
                
                // Si la carte n'a pas été trouvée, essayer d'autres méthodes
                if (!map && container._leaflet) {
                    map = container._leaflet;
                    console.log('Carte trouvée via container._leaflet');
                }
                
                if (map) {
                    console.log('Instance de carte Leaflet trouvée, mise à jour de la vue');
                    
                    // Centrer la carte sur les nouvelles coordonnées
                    map.setView([lat, lng], 13);
                    
                    // Mettre à jour le marqueur s'il existe
                    let markerFound = false;
                    
                    if (map._layers) {
                        for (const layerId in map._layers) {
                            const layer = map._layers[layerId];
                            if (layer instanceof L.Marker) {
                                console.log('Marqueur trouvé, mise à jour de sa position');
                                layer.setLatLng([lat, lng]);
                                markerFound = true;
                                break;
                            }
                        }
                    }
                    
                    // Si aucun marqueur n'a été trouvé, en créer un nouveau
                    if (!markerFound && window.L) {
                        console.log('Aucun marqueur trouvé, création d\'un nouveau');
                        window.L.marker([lat, lng]).addTo(map);
                    }
                } else {
                    console.warn('Instance de carte Leaflet non trouvée');
                }
            });
        } catch (error) {
            console.error('Erreur lors de la mise à jour de la carte:', error);
        }
    }, 100);
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('proprietes', function (Blueprint $table) {
            $table->string('code_postal')->nullable()->after('ville');
            $table->string('numero_rue')->nullable()->after('rue');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('proprietes', function (Blueprint $table) {
            $table->dropColumn('code_postal');
            $table->dropColumn('numero_rue');
        });
    }
};

.asl-p-cont,
.asl-cont {opacity: 0;}


.property{align-items:center;background-color:#fff;border-radius:50%;color:#263238;display:flex;font-size:14px;gap:15px;height:30px;justify-content:center;padding:4px;position:relative;width:30px}
.property,.property:after{transition:all .3s ease-out}
.property:after{border-left:9px solid transparent;border-right:9px solid transparent;border-top:9px solid #fff;content:"";height:0;left:50%;position:absolute;top:95%;transform:translate(-50%);width:0;z-index:1}
.property .icon{align-items:center;color:#fff;display:flex;justify-content:center}
.property .icon svg{height:20px;width:auto}
.property .details{display:none;flex:1;flex-direction:column}
.property .address{color:#9e9e9e;font-size:10px;margin-bottom:10px;margin-top:5px}
.property .features{align-items:flex-end;display:flex;flex-direction:row;gap:10px}
.property .features>div{align-items:center;background:#f5f5f5;border:1px solid #ccc;border-radius:5px;display:flex;font-size:10px;gap:5px;padding:5px}
.property.highlight{background-color:gold;border-radius:8px;box-shadow:10px 10px 5px #0003;height:80px;padding:8px 15px;width:auto}
.property .bed{color:#ffa000}
.property .bath{color:#03a9f4}
.property .size{color:#388e3c}
.property.highlight:has(.fa-house) .icon{color:var(--house-color)}
.property:not(.highlight):has(.fa-house){background-color:var(--house-color)}
.property:not(.highlight):has(.fa-house):after{border-top:9px solid var(--house-color)}
.property.highlight:has(.fa-building) .icon{color:var(--building-color)}
.property:not(.highlight):has(.fa-building){background-color:var(--building-color)}
.property:not(.highlight):has(.fa-building):after{border-top:9px solid var(--building-color)}
.property.highlight:has(.fa-warehouse) .icon{color:var(--warehouse-color)}
.property:not(.highlight):has(.fa-warehouse){background-color:var(--warehouse-color)}
.property:not(.highlight):has(.fa-warehouse):after{border-top:9px solid var(--warehouse-color)}
.property.highlight:has(.fa-shop) .icon{color:var(--shop-color)}
.property:not(.highlight):has(.fa-shop){background-color:var(--shop-color)}
.property:not(.highlight):has(.fa-shop):after{border-top:9px solid var(--shop-color)}



.price-tag{background-color:#4285f4;border-radius:8px;color:#fff;font-size:14px;padding:10px 15px;position:relative}.price-tag:after{border-left:8px solid transparent;border-right:8px solid transparent;border-top:8px solid #4285f4;content:"";height:0;left:50%;position:absolute;top:100%;transform:translate(-50%);width:0}[class$=api-load-alpha-banner]{display:none}
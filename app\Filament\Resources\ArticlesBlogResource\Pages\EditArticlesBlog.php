<?php

namespace App\Filament\Resources\ArticlesBlogResource\Pages;

use App\Filament\Resources\ArticlesBlogResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditArticlesBlog extends EditRecord
{
    protected static string $resource = ArticlesBlogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}

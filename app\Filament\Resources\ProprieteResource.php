<?php

namespace App\Filament\Resources;

use Closure;
use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use App\Models\Propriete;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Dotswan\MapPicker\Fields\Map;
use Illuminate\Support\Facades\Log;
use Filament\Support\Enums\ActionSize;
use Filament\Support\Enums\Alignment;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Actions\Action;
use App\Filament\Resources\ProprieteResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ProprieteResource\RelationManagers;
use App\Filament\Resources\ProprieteResource\RelationManagers\ImagesProprietesRelationManager;

class ProprieteResource extends Resource
{
    protected static ?string $model = Propriete::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Propriétés';

    /**
     * Prépare les données avant de remplir le formulaire pour l'édition.
     * Initialise le champ 'location' (pour la carte) à partir des colonnes latitude/longitude.
     */
    public static function mutateFormDataBeforeFill(array $data): array
    {
        if (!empty($data['latitude']) && !empty($data['longitude'])) {
            $data['location'] = json_encode([
                'lat' => (float) $data['latitude'],
                'lng' => (float) $data['longitude'],
            ]);
            
            // Ensure latitude and longitude are set as floats to avoid type issues
            $data['latitude'] = (float) $data['latitude'];
            $data['longitude'] = (float) $data['longitude'];
            
            // Log for debugging
            \Illuminate\Support\Facades\Log::debug('ProprieteResource::mutateFormDataBeforeFill - Data with coordinates:', [
                'latitude' => $data['latitude'],
                'longitude' => $data['longitude'],
                'location' => $data['location'],
            ]);
        } else {
            \Illuminate\Support\Facades\Log::debug('ProprieteResource::mutateFormDataBeforeFill - No coordinates found in data');
        }
        return $data;
    }
    
    /**
     * Prépare les données avant de les enregistrer lors de la création.
     */
    public static function mutateFormDataBeforeCreate(array $data): array
    {
        \Illuminate\Support\Facades\Log::debug('ProprieteResource::mutateFormDataBeforeCreate - Initial data:', array_intersect_key($data, array_flip(['latitude', 'longitude', 'location', 'backup_latitude', 'backup_longitude'])));
        
        // Supprimer le champ 'location' qui n'est pas une colonne de la base de données
        if (isset($data['location'])) {
            unset($data['location']);
        }
        
        // Vérifier si les champs de secours contiennent des données valides
        if (empty($data['latitude']) && !empty($data['backup_latitude'])) {
            $data['latitude'] = $data['backup_latitude'];
            \Illuminate\Support\Facades\Log::debug('Utilisation du champ de secours pour latitude:', ['backup_latitude' => $data['backup_latitude']]);
        }
        
        if (empty($data['longitude']) && !empty($data['backup_longitude'])) {
            $data['longitude'] = $data['backup_longitude'];
            \Illuminate\Support\Facades\Log::debug('Utilisation du champ de secours pour longitude:', ['backup_longitude' => $data['backup_longitude']]);
        }
        
        // Supprimer les champs de secours avant la sauvegarde
        if (isset($data['backup_latitude'])) {
            unset($data['backup_latitude']);
        }
        
        if (isset($data['backup_longitude'])) {
            unset($data['backup_longitude']);
        }
        
        // S'assurer que latitude et longitude sont bien des nombres à virgule flottante
        if (isset($data['latitude']) && !empty($data['latitude'])) {
            $data['latitude'] = (float) $data['latitude'];
        }
        
        if (isset($data['longitude']) && !empty($data['longitude'])) {
            $data['longitude'] = (float) $data['longitude'];
        }
        
        \Illuminate\Support\Facades\Log::debug('ProprieteResource::mutateFormDataBeforeCreate - Processed data:', array_intersect_key($data, array_flip(['latitude', 'longitude'])));
        
        return $data;
    }
    
    /**
     * Prépare les données avant de les enregistrer lors de la mise à jour.
     */
    public static function mutateFormDataBeforeSave(array $data): array
    {
        \Illuminate\Support\Facades\Log::debug('ProprieteResource::mutateFormDataBeforeSave - Initial data:', array_intersect_key($data, array_flip(['latitude', 'longitude', 'location', 'backup_latitude', 'backup_longitude'])));
        
        // Supprimer le champ 'location' qui n'est pas une colonne de la base de données
        if (isset($data['location'])) {
            unset($data['location']);
        }
        
        // Vérifier si les champs de secours contiennent des données valides
        if (empty($data['latitude']) && !empty($data['backup_latitude'])) {
            $data['latitude'] = $data['backup_latitude'];
            \Illuminate\Support\Facades\Log::debug('Utilisation du champ de secours pour latitude:', ['backup_latitude' => $data['backup_latitude']]);
        }
        
        if (empty($data['longitude']) && !empty($data['backup_longitude'])) {
            $data['longitude'] = $data['backup_longitude'];
            \Illuminate\Support\Facades\Log::debug('Utilisation du champ de secours pour longitude:', ['backup_longitude' => $data['backup_longitude']]);
        }
        
        // Supprimer les champs de secours avant la sauvegarde
        if (isset($data['backup_latitude'])) {
            unset($data['backup_latitude']);
        }
        
        if (isset($data['backup_longitude'])) {
            unset($data['backup_longitude']);
        }
        
        // S'assurer que latitude et longitude sont bien des nombres à virgule flottante
        if (isset($data['latitude']) && !empty($data['latitude'])) {
            $data['latitude'] = (float) $data['latitude'];
        }
        
        if (isset($data['longitude']) && !empty($data['longitude'])) {
            $data['longitude'] = (float) $data['longitude'];
        }
        
        \Illuminate\Support\Facades\Log::debug('ProprieteResource::mutateFormDataBeforeSave - Processed data:', array_intersect_key($data, array_flip(['latitude', 'longitude'])));
        
        return $data;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Wizard::make([
                    Forms\Components\Wizard\Step::make('Général')
                        ->schema([
                            Forms\Components\TextInput::make('id')
                                ->label("No")
                                ->readOnly()
                                ->visibleOn('edit'),
                            Forms\Components\TextInput::make('reference')
                                ->label("Réf interne")
                                ->maxLength(255),
                            Forms\Components\Select::make('categorie')
                            ->label("Catégorie")
                                ->options([
                                    'Appartements' => 'Appartements',
                                    'Villas' => 'Villas',
                                    'Bureaux' => 'Bureaux',
                                    'Ateliers' => 'Ateliers',
                                    'Boxes' => 'Boxes',
                                ]),
                            Forms\Components\Select::make('transaction')
                                ->options([
                                    'Vente' => 'Vente',
                                    'Location' => 'Location',
                                ]),
                            Forms\Components\TextInput::make('agent')
                                ->label('Agent/Propriétaire')
                                ->maxLength(255),
                            Forms\Components\Select::make('statut')
                                ->options([
                                    'En attente' => 'En attente',
                                    'Actif' => 'Actif',
                                    'Actif warm' => 'Actif warm',
                                    'Actif réservé' => 'Actif réservé',
                                    'Vendu' => 'Vendu',
                                    'Retiré' => 'Retiré',
                                    'Archivé' => 'Archivé',
                                    'Non-actif' => 'Non-actif',
                                    'Incomplet' => 'Incomplet',
                                ])
                                ->helperText('Non-actif: Propriété en projet, non affichée dans la liste. Incomplet: Propriété manquant d\'informations essentielles.'),
                            Forms\Components\Select::make('disposition')
                                ->options([
                                    'exclusivité' => 'Exclusivité',
                                    'réservé' => 'Réservé',
                                    'vendu' => 'Vendu',
                                    'loué' => 'Loué',
                                ]),
                            Forms\Components\DatePicker::make('disponible_a_partir')
                                ->label('Disponible à partir du'),
                            Forms\Components\Toggle::make('disponible_de_suite')
                                ->label('Disponible de suite')
                                ->default(false),
                            Forms\Components\Toggle::make('disponibilite_a_convenir')
                                ->label('Disponibilité à convenir')
                                ->default(false),
                            Forms\Components\Toggle::make('afficher_accueil')
                                ->label('Afficher sur la page accueil')
                                ->default(false),
                            Forms\Components\Toggle::make('est_projet')
                                ->label('Projet en cours')
                                ->default(false),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Localisation')
                        ->schema([
                            Forms\Components\TextInput::make('adresse')
                                ->label('Adresse complète')
                                ->placeholder('Saisissez l\'adresse affichée sur la fiche de la propriété')
                                ->helperText('Cette adresse sera affichée sur le site.'),
                            Forms\Components\TextInput::make('code_postal')
                                ->label('Code Postal (CP)')
                                ->placeholder('Ex: 75001')
                                ->maxLength(10),
                            Forms\Components\TextInput::make('ville')
                                ->label('Localité')
                                ->placeholder('Ex: Paris')
                                ->maxLength(100),
                            Forms\Components\TextInput::make('rue')
                                ->label('Rue')
                                ->placeholder('Ex: Rue de la Paix')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('numero_rue')
                                ->label('Numéro de rue')
                                ->placeholder('Ex: 1')
                                ->maxLength(10),
                            Forms\Components\Grid::make(2)
                                ->schema([
                                    // Carte personnalisée avec recherche d'adresse intégrée
                                    Forms\Components\View::make('components.custom-map')
                                        ->columnSpanFull(),

                                    // Remplacer les champs cachés par des champs visibles
                                    Forms\Components\TextInput::make('latitude')
                                        ->label('Latitude')
                                        ->required()
                                        ->numeric()
                                        ->id('filament-lat-input')
                                        ->extraAttributes(['data-map-field' => 'latitude'])
                                        ->placeholder('Latitude (ex: 46.603354)'),
                                    Forms\Components\TextInput::make('longitude') 
                                        ->label('Longitude')
                                        ->required()
                                        ->numeric()
                                        ->id('filament-lng-input')
                                        ->extraAttributes(['data-map-field' => 'longitude'])
                                        ->placeholder('Longitude (ex: 1.888334)'),
                                    Forms\Components\Hidden::make('location'),
                                    
                                    // Suppression des champs manuels qui ne sont plus nécessaires
                                    // puisque latitude et longitude sont maintenant directs
                                ]),
                            Forms\Components\Toggle::make('afficher_localisation')
                                ->label('Afficher la localisation sur le site public')
                                ->default(true),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Caractéristiques')
                        ->schema([
                            Forms\Components\TextInput::make('piece')
                                ->label('Pièce(s)')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('chambres')
                                ->label('Chambre(s)')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('wc')
                                ->label('WC\'s')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('salles_de_bain')
                                ->label('Salle(s) de bain')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('etage')
                                ->label('Étage')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('nombre_etages_batiment')
                                ->label('Nombre étage(s) bâtiment')

                                ,
                            Forms\Components\TextInput::make('terrasse')
                                ->label('Terrasse(s)')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('parking')
                                ->label('Parking(s)')

                                ,
                            Forms\Components\TextInput::make('garages')
                                ->label('Garage(s)')

                                ,
                            Forms\Components\TextInput::make('place_parc_interieure')
                                ->label('Place(s) de parc intérieure(s)')

                                ,
                            Forms\Components\TextInput::make('place_parc_couverte')
                                ->label('Place(s) couverte(s)')

                                ,
                            Forms\Components\TextInput::make('place_parc_exterieure')
                                ->label('Place(s) de parc extérieure(s)')

                                ,
                            Forms\Components\TextInput::make('type_structure')
                                ->label('Type de structure')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('annee_construction')
                                ->label('Année de construction')
                                ->maxLength(4),
                            Forms\Components\TextInput::make('annee_renovation')
                                ->label('Année de rénovation')
                                ->maxLength(4),
                            Forms\Components\TextInput::make('type_chauffage')
                                ->label('Type de chauffage')
                                ->maxLength(100),
                            Forms\Components\TextInput::make('surface')
                                ->label('Surface')

                                ,
                            Forms\Components\TextInput::make('surface_habitable')
                                ->label('Surface habitable')

                                ,
                            Forms\Components\TextInput::make('surface_terrasse')
                                ->label('Surface terrasse(s)')

                                ,
                            Forms\Components\TextInput::make('surface_terrain')
                                ->label('Surface terrain')

                                ,
                            Forms\Components\TextInput::make('volume')
                                ->label('Volume (m³)')

                                ,
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Prix')
                        ->schema([
                            Forms\Components\TextInput::make('prix')

                                ,
                            Forms\Components\Select::make('devise')
                                ->options([
                                    'CHF' => 'CHF',
                                    'EUR' => 'EUR',
                                ])
                                ->default('CHF'),
                            Forms\Components\Toggle::make('prix_sur_demande')
                                ->label('Prix sur demande')
                                ->default(false),
                            Forms\Components\Toggle::make('prix_negociable')
                                ->label('Prix négociable')
                                ->default(false),
                            Forms\Components\TextInput::make('prix_garage')

                                ,
                            Forms\Components\TextInput::make('prix_parking')

                                ,
                            Forms\Components\TextInput::make('charges')

                                ,
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Description')
                        ->schema([
                            Forms\Components\TextInput::make('titre')
                                ->maxLength(255)
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('prestation')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description_courte')
                                ->maxLength(500)
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('note_prive')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description_promotion')
                                ->columnSpan(2),
                            Forms\Components\RichEditor::make('description_projet')
                                ->columnSpan(2),
                        ])->columns(2),
                    Forms\Components\Wizard\Step::make('Images')
                        ->schema([
                            Forms\Components\TextInput::make('lien_video')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('lien_tour_virtuel')
                                ->maxLength(255),
                            Forms\Components\Repeater::make('imagesProprietes')
                                ->label("Images de la propriété")
                                ->relationship()
                                ->schema([
                                    Forms\Components\FileUpload::make('url_image')
                                        ->label("Image")
                                        ->image()
                                        ->maxSize(5120) // 5MB max
                                        ->disk('public_uploads')
                                        ->visibility('public'),
                                    Forms\Components\Toggle::make('is_main')
                                        ->label('Image principale')
                                        ->helperText('Cette image sera utilisée comme vignette principale')
                                        ->default(false),
                                    Forms\Components\Select::make('statut')
                                        ->options([
                                            'public' => 'Public',
                                            'prive' => 'Privé',
                                        ])
                                        ->default('public'),
                                ])
                                ->defaultItems(1)
                                ->orderColumn('order_column')
                                ->reorderable()
                                ->columns(2)
                                ->columnSpan('full'),
                        ])->columns(2),
                ])->columnSpan('full')->skippable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No'),
                Tables\Columns\ImageColumn::make('main_image')
                    ->label('Encart Photo')
                    ->getStateUsing(function (Propriete $record): ?string {
                        return $record->getMainImage();
                    })
                    ->disk('public_uploads')
                    ->visibility('public')
                    ->square(),
                Tables\Columns\TextColumn::make('statut'),
                Tables\Columns\TextColumn::make('transaction'),
                Tables\Columns\TextColumn::make('categorie')
                ->label('Catégorie'),
                Tables\Columns\TextColumn::make('reference')
                    ->label('Réf. interne')
                    ->searchable(),
                Tables\Columns\TextColumn::make('prix')

                    ->sortable()
                    ->money('CHF'),
                Tables\Columns\TextColumn::make('piece')
                ->label("Pièce(s)")

                    ->sortable(),
                Tables\Columns\TextColumn::make('surface')

                    ->sortable(),
                Tables\Columns\TextColumn::make('adresse')
                    ->searchable(),
                Tables\Columns\TextColumn::make('agent') // Display Agent Name
                    ->label('Agent/Propriétaire')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                ->label('Créé')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                ->label('Modifié')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('categorie')
                    ->options([
                        'Appartements' => 'Appartements',
                        'Villas' => 'Villas',
                        'Bureaux' => 'Bureaux',
                        'Ateliers' => 'Ateliers',
                        'Boxes' => 'Boxes',
                    ]),
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'En attente' => 'En attente',
                        'Actif' => 'Actif',
                        'Actif warm' => 'Actif warm',
                        'Actif réservé' => 'Actif réservé',
                        'Vendu' => 'Vendu',
                        'Retiré' => 'Retiré',
                        'Perdu' => 'Perdu',
                    ]),
                Tables\Filters\SelectFilter::make('transaction')
                    ->options([
                        'Vente' => 'Vente',
                        'Location' => 'Location',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ReplicateAction::make()
                    ->excludeAttributes(['reference', 'url_image'])
                    ->beforeReplicaSaved(function (Propriete $record): void {
                        $record->titre = ' [NEW] ' . $record->titre;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProprietes::route('/'),
            'create' => Pages\CreatePropriete::route('/create'),
            'edit' => Pages\EditPropriete::route('/{record}/edit'),
        ];
    }
}

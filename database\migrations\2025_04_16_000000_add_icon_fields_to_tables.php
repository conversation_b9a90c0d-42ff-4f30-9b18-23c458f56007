<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter un champ icon_id à la table sections
        Schema::table('sections', function (Blueprint $table) {
            if (!Schema::hasColumn('sections', 'icon_id')) {
                $table->foreignId('icon_id')->nullable()->after('titre')->constrained('icons')->nullOnDelete();
            }
        });

        // Ajouter un champ icon_id à la table contenus_sections
        Schema::table('contenus_sections', function (Blueprint $table) {
            if (!Schema::hasColumn('contenus_sections', 'icon_id')) {
                $table->foreignId('icon_id')->nullable()->after('titre')->constrained('icons')->nullOnDelete();
            }
        });

        // Ajouter un champ icon_id à la table pages
        Schema::table('pages', function (Blueprint $table) {
            if (!Schema::hasColumn('pages', 'icon_id')) {
                $table->foreignId('icon_id')->nullable()->after('titre')->constrained('icons')->nullOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer le champ icon_id de la table sections
        Schema::table('sections', function (Blueprint $table) {
            if (Schema::hasColumn('sections', 'icon_id')) {
                $table->dropForeign(['icon_id']);
                $table->dropColumn('icon_id');
            }
        });

        // Supprimer le champ icon_id de la table contenus_sections
        Schema::table('contenus_sections', function (Blueprint $table) {
            if (Schema::hasColumn('contenus_sections', 'icon_id')) {
                $table->dropForeign(['icon_id']);
                $table->dropColumn('icon_id');
            }
        });

        // Supprimer le champ icon_id de la table pages
        Schema::table('pages', function (Blueprint $table) {
            if (Schema::hasColumn('pages', 'icon_id')) {
                $table->dropForeign(['icon_id']);
                $table->dropColumn('icon_id');
            }
        });
    }
};

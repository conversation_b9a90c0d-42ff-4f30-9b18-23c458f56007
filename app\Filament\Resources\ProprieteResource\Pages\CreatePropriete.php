<?php

namespace App\Filament\Resources\ProprieteResource\Pages;

use App\Filament\Resources\ProprieteResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePropriete extends CreateRecord
{
    protected static string $resource = ProprieteResource::class;

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    protected function getFooterActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCancelFormAction(),
        ];
    }
}

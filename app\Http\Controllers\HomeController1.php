<?php

namespace App\Http\Controllers;

use App\Models\ArticlesBlog;
use App\Models\Contact;
use App\Models\Developpement;
use App\Models\Hero;
use App\Models\Icon;
use App\Models\OffMarket;
use App\Models\Page;
use App\Models\Section;
use App\Models\Propriete;
use Illuminate\Http\Request;

class HomeController extends Controller
{

    public function index()
    {

        // Récupérer la page d'accueil en utilisant son slug
        // $page = Page::where('slug', 'home')->firstOrFail();

        // Récupérer toutes les sections associées à la page, triées par ordre
        // $sections = $page->sections; // ou Section::where('page_id', $page->id)->orderBy('ordre')->get();

        // Récupère le premier enregistrement dans la table heroes avec ses icônes associées.
        $hero = Hero::first();

        // Vous pouvez ajouter une gestion d'erreur si aucun hero n'est trouvé
        if (!$hero) {
            abort(404, 'Section hero non trouvée');
        }

        // Récupère la section par son nom (par exemple "proprietes_vedettes")
        $sections = Section::with('contenuSections')->where('page_id', 1)->get();
        $icons = Icon::where('type', 'hero')->get();
        // dd($section);
        // Exemples de récupération d'autres contenus dynamiques
        // $proprietes = Propriete::latest()->limit(3)->get();
        // $developpements = Developpement::latest()->limit(1)->get();
        // $articles = ArticlesBlog::latest()->limit(3)->get();
        // $contact = Contact::first();
        // Récupérer les propriétés avec leurs images associées
        $statuses = ['Actif', 'Actif warm', 'Actif réservé']; // Les statuts souhaités

        $proprietes = Propriete::with('imagesProprietes')
            ->whereIn('statut', $statuses) // Filtre sur plusieurs statuts
            ->latest()
            ->limit(3)
            ->get();
        $actualites = ArticlesBlog::latest()->limit(3)->get();
        $off_market = OffMarket::where('afficher_sur_acceuil', 1)->latest()->limit(2)->get();

        // Passage des données à la vue index.blade.php
        // return view('web.index', compact('page', 'sections', 'proprietes', 'developpements', 'articles', 'contact'));
        return view('web.index', compact('hero', 'sections', 'proprietes', 'actualites', 'icons', 'off_market'));
    }
}

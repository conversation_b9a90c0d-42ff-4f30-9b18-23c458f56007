/**
 * Script pour la recherche d'adresse et la mise à jour de la carte
 */
document.addEventListener('DOMContentLoaded', function() {
    // Écouter l'événement searchAddress envoyé par Livewire
    document.addEventListener('livewire:initialized', () => {
        Livewire.on('searchAddress', ({ address }) => {
            if (!address) return;
            
            // Utiliser le service de géocodage Nominatim d'OpenStreetMap
            searchAddress(address);
        });
    });

    // Ajouter un écouteur d'événement pour le bouton de recherche
    document.addEventListener('click', function(event) {
        if (event.target.id === 'search_address_button' || event.target.closest('#search_address_button')) {
            const addressInput = document.getElementById('search_address_input');
            if (addressInput) {
                const address = addressInput.value;
                if (address) {
                    searchAddress(address);
                }
            }
        }
    });

    // Fonction pour rechercher une adresse via l'API Nominatim
    function searchAddress(address) {
        // Afficher un indicateur de chargement
        const searchButton = document.getElementById('search_address_button');
        if (searchButton) {
            const originalText = searchButton.textContent;
            searchButton.textContent = 'Recherche en cours...';
            searchButton.disabled = true;
        }

        // Construire l'URL de l'API Nominatim
        const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`;

        // Effectuer la requête
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data && data.length > 0) {
                    const result = data[0];
                    const lat = parseFloat(result.lat);
                    const lng = parseFloat(result.lon);

                    // Mettre à jour la carte
                    updateMap(lat, lng);
                } else {
                    // Aucun résultat trouvé
                    alert('Aucun résultat trouvé pour cette adresse. Veuillez essayer avec une adresse plus précise.');
                }
            })
            .catch(error => {
                console.error('Erreur lors de la recherche d\'adresse:', error);
                alert('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
            })
            .finally(() => {
                // Restaurer le bouton
                if (searchButton) {
                    searchButton.textContent = 'Rechercher';
                    searchButton.disabled = false;
                }
            });
    }

    // Fonction pour mettre à jour la carte avec les nouvelles coordonnées
    function updateMap(lat, lng) {
        // Mettre à jour les champs de formulaire
        const latitudeInput = document.querySelector('input[name="latitude"]');
        const longitudeInput = document.querySelector('input[name="longitude"]');
        const locationInput = document.querySelector('input[name="data.location"]');

        if (latitudeInput) latitudeInput.value = lat;
        if (longitudeInput) longitudeInput.value = lng;

        // Mettre à jour l'état du composant Map
        if (locationInput) {
            // Créer un nouvel objet d'état pour le champ location
            const locationState = {
                lat: lat,
                lng: lng
            };
            
            // Mettre à jour la valeur du champ location
            locationInput.value = JSON.stringify(locationState);
            
            // Déclencher un événement de changement pour que Livewire détecte la modification
            locationInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Déclencher l'événement refreshMap pour mettre à jour la carte
        Livewire.dispatch('refreshMap');
    }
});

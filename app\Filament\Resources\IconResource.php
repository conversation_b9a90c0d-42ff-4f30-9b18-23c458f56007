<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Icon;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use App\Filament\Resources\IconResource\Pages;

class IconResource extends Resource
{
    protected static ?string $model = Icon::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Icônes / pictogrammes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Tabs')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Informations générales')
                            ->schema([
                                Forms\Components\Select::make('type')
                                    ->label('Type d\'utilisation')
                                    ->options([
                                        'hero' => 'Hero',
                                        'propriete' => 'Propriété',
                                        'navigation' => 'Navigation',
                                        'footer' => 'Pied de page',
                                        'autre' => 'Autre',
                                    ])
                                    ,
                                Forms\Components\TextInput::make('label')
                                    ->label('Libellé')
                                    
                                    ->maxLength(255),
                                Forms\Components\Textarea::make('description')
                                    ->label('Description')
                                    
                                    ->maxLength(1000)
                                    ->columnSpanFull(),
                                Forms\Components\TextInput::make('link')
                                    ->label('Lien')
                                    
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('sort_order')
                                    ->label('Ordre de tri')
                                    
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\Select::make('content_type')
                                    ->label('Type de contenu associé')
                                    ->options([
                                        'propriete' => 'Propriété',
                                        'section' => 'Section',
                                        'page' => 'Page',
                                        'autre' => 'Autre',
                                    ])
                                    ,
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Actif')
                                    ->default(true),
                            ]),
                        Forms\Components\Tabs\Tab::make('Apparence')
                            ->schema([
                                Forms\Components\Select::make('icon_type')
                                    ->label('Type d\'icône')
                                    ->options([
                                        'image' => 'Image',
                                        'fontawesome' => 'Font Awesome',
                                        'svg' => 'SVG',
                                    ])
                                    ->default('image')
                                    ->reactive()
                                    ,
                                Forms\Components\FileUpload::make('image')
                                    ->label('Image')
                                    
                                    ->image()
                                    ->directory('icons')
                                    ->visible(fn (callable $get) => $get('icon_type') === 'image' || $get('icon_type') === null),
                                Forms\Components\TextInput::make('icon_value')
                                    ->label('Classe Font Awesome')
                                    
                                    ->placeholder('fas fa-home')
                                    ->helperText('Exemple: fas fa-home, far fa-user, etc.')
                                    ->visible(fn (callable $get) => $get('icon_type') === 'fontawesome'),
                                Forms\Components\Textarea::make('icon_value')
                                    ->label('Code SVG')
                                    
                                    ->placeholder('<svg>...</svg>')
                                    ->helperText('Collez ici le code SVG complet')
                                    ->visible(fn (callable $get) => $get('icon_type') === 'svg'),
                                Forms\Components\ColorPicker::make('color')
                                    ->label('Couleur')
                                    
                                    ->visible(fn (callable $get) => $get('icon_type') === 'fontawesome' || $get('icon_type') === 'svg'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Prévisualisation')
                            ->schema([
                                Forms\Components\View::make('filament.resources.icon-resource.preview')
                                    ->visible(function () {
                                        // Vérifier si nous sommes sur une page d'édition ou de création
                                        $currentRoute = request()->route()->getName();
                                        return $currentRoute !== 'filament.admin.resources.icons.index';
                                    }),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('label')
                    ->label('Libellé')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Type d\'utilisation'),
                Tables\Columns\TextColumn::make('content_type')
                    ->label('Type de contenu')
                    ->searchable(),
                Tables\Columns\IconColumn::make('icon_type')
                    ->label('Type d\'icône')
                    ->icons([
                        'image' => 'heroicon-o-photo',
                        'fontawesome' => 'heroicon-o-code-bracket',
                        'svg' => 'heroicon-o-code-bracket-square',
                        'default' => 'heroicon-o-question-mark-circle', // Valeur par défaut
                    ]),
                Tables\Columns\ViewColumn::make('preview')
                    ->label('Aperçu')
                    ->view('filament.tables.columns.icon-preview'),
                Tables\Columns\ImageColumn::make('image')
                    ->label('Image')
                    ->visible(fn ($record) => $record && $record->icon_type === 'image'),
                Tables\Columns\TextColumn::make('icon_value')
                    ->label('Valeur')
                    ->visible(fn ($record) => $record && $record->icon_type !== 'image')
                    ->limit(30),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Ordre')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Modifié le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Type d\'utilisation')
                    ->options([
                        'hero' => 'Hero',
                        'propriete' => 'Propriété',
                        'navigation' => 'Navigation',
                        'footer' => 'Pied de page',
                        'autre' => 'Autre',
                    ]),
                Tables\Filters\SelectFilter::make('icon_type')
                    ->label('Type d\'icône')
                    ->options([
                        'image' => 'Image',
                        'fontawesome' => 'Font Awesome',
                        'svg' => 'SVG',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIcons::route('/'),
        ];
    }
}

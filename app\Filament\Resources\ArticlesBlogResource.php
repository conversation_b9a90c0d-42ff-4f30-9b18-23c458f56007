<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ArticlesBlogResource\Pages;
use App\Filament\Resources\ArticlesBlogResource\RelationManagers;
use App\Models\ArticlesBlog;
use App\Models\BlogCategory;
use App\Models\BlogTag;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class ArticlesBlogResource extends Resource
{
    protected static ?string $model = ArticlesBlog::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Blog';

    protected static ?string $navigationLabel = 'Articles';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations principales')
                            ->schema([
                                Forms\Components\TextInput::make('titre')
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                                        $operation === 'create' ? $set('slug', Str::slug($state)) : null),

                                Forms\Components\TextInput::make('slug')
                                    ->maxLength(255)
                                    ->unique(ArticlesBlog::class, 'slug', ignoreRecord: true),

                                Forms\Components\RichEditor::make('contenu')
                                    ->columnSpanFull(),

                                Forms\Components\RichEditor::make('extrait')
                                    ->helperText('Un court résumé de l\'article qui sera affiché dans les listes d\'articles.')
                                    ->columnSpanFull(),
                            ]),

                        Forms\Components\Section::make('Image')
                            ->schema([
                                Forms\Components\FileUpload::make('url_image')
                                    ->label('Image principale')
                                    ->image()
                                    ->disk('public_uploads')
                                    ->directory('blog')
                                    ->visibility('public')
                                    ->imageEditor()
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Statut')
                            ->schema([
                                Forms\Components\Select::make('statut')
                                    ->options([
                                        'brouillon' => 'Brouillon',
                                        'publie' => 'Publié',
                                    ])
                                    ->default('brouillon'),

                                Forms\Components\DatePicker::make('date_publication')
                                    ->label('Date de publication')
                                    ->default(now()),
                            ]),

                        Forms\Components\Section::make('Catégorisation')
                            ->schema([
                                Forms\Components\Select::make('category_id')
                                    ->label('Catégorie')
                                    ->relationship('category', 'nom')
                                    ->options(BlogCategory::all()->pluck('nom', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('nom')
                                            ->maxLength(255)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(fn ($state, Forms\Set $set) => $set('slug', Str::slug($state))),
                                        Forms\Components\TextInput::make('slug')
                                            ->maxLength(255)
                                            ->unique(BlogCategory::class, 'slug'),
                                    ]),

                                Forms\Components\Select::make('tags')
                                    ->relationship()
                                    ->multiple()
                                    ->preload()
                                    ->options(BlogTag::all()->pluck('nom', 'id'))
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('nom')
                                            ->maxLength(255)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(fn ($state, Forms\Set $set) => $set('slug', Str::slug($state))),
                                        Forms\Components\TextInput::make('slug')
                                            ->maxLength(255)
                                            ->unique(BlogTag::class, 'slug'),
                                    ]),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('url_image')
                    ->label('Image')
                    ->disk('public_uploads')
                    ->circular(),
                Tables\Columns\TextColumn::make('titre')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category.nom')
                    ->label('Catégorie')
                    ->sortable(),
                Tables\Columns\TextColumn::make('date_publication')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('statut')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'publie' => 'success',
                        'brouillon' => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'brouillon' => 'Brouillon',
                        'publie' => 'Publié',
                    ]),
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Catégorie')
                    ->relationship('category', 'nom'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArticlesBlogs::route('/'),
            'create' => Pages\CreateArticlesBlog::route('/create'),
            'edit' => Pages\EditArticlesBlog::route('/{record}/edit'),
        ];
    }
}

// Script personnalisé pour initialiser le slider de prix
document.addEventListener('DOMContentLoaded', function() {
    // Attendre que le plugin ionRangeSlider soit chargé
    if (typeof $.fn.ionRangeSlider !== 'undefined') {
        // Récupérer les valeurs min et max du slider
        var priceSlider = $("#prdctfltr_rng_price");
        var minPrice = parseInt(priceSlider.data('min'));
        var maxPrice = parseInt(priceSlider.data('max'));

        // Calculer un pas approprié en fonction de la plage de prix
        var range = maxPrice - minPrice;
        var step = 1000; // Pas par défaut

        if (range > 10000000) {
            step = 500000; // Pas de 500k pour les grandes plages
        } else if (range > 5000000) {
            step = 250000; // Pas de 250k pour les plages moyennes-grandes
        } else if (range > 1000000) {
            step = 100000; // Pas de 100k pour les plages moyennes
        } else if (range > 500000) {
            step = 50000; // Pas de 50k pour les plages petites-moyennes
        } else if (range > 100000) {
            step = 10000; // Pas de 10k pour les petites plages
        }

        // Initialiser le slider de prix
        priceSlider.ionRangeSlider({
            type: "double",
            grid: true,
            min: minPrice,
            max: maxPrice,
            from: minPrice,
            to: maxPrice,
            prefix: "CHF ",
            step: step,
            prettify_separator: "'",
            prettify: function(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, "'");
            },
            onChange: function(data) {
                $('input[name="rng_min_price"]').val(data.from);
                $('input[name="rng_max_price"]').val(data.to);
            },
            onFinish: function(data) {
                if (data.from === minPrice && data.to === maxPrice) {
                    $('input[name="rng_min_price"]').val('');
                    $('input[name="rng_max_price"]').val('');
                } else {
                    $('input[name="rng_min_price"]').val(data.from);
                    $('input[name="rng_max_price"]').val(data.to);
                }
            }
        });
    }
});

/**
 * Styles pour le module de localisation
 */

/* Conteneur principal du module */
.location-module {
    margin: 20px 0;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Titre de section */
.location-module h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* Conteneur de la carte */
.location-map-container {
    width: 100%;
    aspect-ratio: 1/1; /* Format carré */
    margin: 15px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

/* Carte */
#location-map {
    width: 100%;
    height: 100%;
}

/* Formulaires */
.location-form {
    margin-bottom: 15px;
}

.location-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.location-form input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 10px;
}

.location-form button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.location-form button:hover {
    background-color: #45a049;
}

.location-form button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Grille pour les champs de recherche par composants */
.location-form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

/* Coordonnées */
.coordinates-container {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.coordinates-container .coordinate-field {
    flex: 1;
}

.coordinates-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.coordinates-container input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Séparateur */
.location-separator {
    margin: 20px 0;
    border-top: 1px solid #ddd;
}

/* Responsive */
@media (max-width: 768px) {
    .location-form-grid {
        grid-template-columns: 1fr;
    }
    
    .coordinates-container {
        flex-direction: column;
    }
}

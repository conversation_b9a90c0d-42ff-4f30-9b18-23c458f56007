<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('images_proprietes', function (Blueprint $table) {
            if (!Schema::hasColumn('images_proprietes', 'order_column')) {
                $table->integer('order_column')->nullable()->after('statut');
            }
            if (!Schema::hasColumn('images_proprietes', 'is_main')) {
                $table->boolean('is_main')->default(false)->after('order_column');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('images_proprietes', function (Blueprint $table) {
            $table->dropColumn(['order_column', 'is_main']);
        });
    }
};

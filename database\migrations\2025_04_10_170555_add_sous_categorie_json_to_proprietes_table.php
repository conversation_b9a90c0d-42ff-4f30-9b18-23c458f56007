<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter une nouvelle colonne temporaire pour stocker les données JSON
        Schema::table('proprietes', function (Blueprint $table) {
            $table->json('sous_categorie_json')->nullable()->after('sous_categorie');
        });

        // Mettre à jour le modèle Propriete pour qu'il gère correctement le casting JSON
        $modelPath = app_path('Models/Propriete.php');
        if (file_exists($modelPath)) {
            $content = file_get_contents($modelPath);

            // Vérifier si le casting existe déjà
            if (strpos($content, "'sous_categorie' => 'array'") === false) {
                // Trouver la section des casts
                $pattern = "/protected\s+\$casts\s*=\s*\[([^\]]+)\]/";

                if (preg_match($pattern, $content, $matches)) {
                    // Ajouter le casting pour sous_categorie
                    $existingCasts = $matches[1];
                    $newCasts = $existingCasts . ",\n        'sous_categorie' => 'array'";
                    $content = str_replace($existingCasts, $newCasts, $content);

                    // Sauvegarder le fichier modifié
                    file_put_contents($modelPath, $content);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('proprietes', function (Blueprint $table) {
            // Supprimer la colonne temporaire
            $table->dropColumn('sous_categorie_json');
        });

        // Restaurer le modèle Propriete
        $modelPath = app_path('Models/Propriete.php');
        if (file_exists($modelPath)) {
            $content = file_get_contents($modelPath);

            // Supprimer le casting pour sous_categorie
            $content = preg_replace("/,\s*'sous_categorie'\s*=>\s*'array'/", '', $content);

            // Sauvegarder le fichier modifié
            file_put_contents($modelPath, $content);
        }
    }
};

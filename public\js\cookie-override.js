/**
 * <PERSON><PERSON> Override Script
 * This script overrides cookie-related functions to prevent cookies from being set
 */

// Create a mock Cookies object to prevent errors
window.Cookies = {
    get: function(name) {
        console.log('<PERSON><PERSON> get operation disabled for: ' + name);
        return null;
    },
    set: function(name, value, options) {
        console.log('<PERSON><PERSON> set operation disabled for: ' + name);
        return null;
    },
    remove: function(name, options) {
        console.log('<PERSON><PERSON> remove operation disabled for: ' + name);
        return null;
    },
    // Add compatibility with js-cookie library
    withAttributes: function() {
        return window.Cookies;
    },
    withConverter: function() {
        return window.Cookies;
    },
    noConflict: function() {
        return window.Cookies;
    }
};

// Override document.cookie
Object.defineProperty(document, 'cookie', {
    get: function() {
        console.log('Cookie access disabled');
        return '';
    },
    set: function(value) {
        console.log('Cookie set operation disabled: ' + value);
        return '';
    }
});

// Override localStorage for cookie-related items
const originalLocalStorageSetItem = localStorage.setItem;
localStorage.setItem = function(key, value) {
    if (key.includes('cookie') || key.includes('consent') || key.includes('gdpr') ||
        key.includes('woocommerce') || key.includes('wp-settings')) {
        console.log('LocalStorage set operation disabled for cookie-related item: ' + key);
        return;
    }
    originalLocalStorageSetItem.call(localStorage, key, value);
};

// Override sessionStorage for cookie-related items
const originalSessionStorageSetItem = sessionStorage.setItem;
sessionStorage.setItem = function(key, value) {
    if (key.includes('cookie') || key.includes('consent') || key.includes('gdpr') ||
        key.includes('woocommerce') || key.includes('wp-settings') || key.includes('wc_fragments')) {
        console.log('SessionStorage set operation disabled for cookie-related item: ' + key);
        return;
    }
    originalSessionStorageSetItem.call(sessionStorage, key, value);
};

// Disable GDPR/cookie consent banners and WordPress plugin elements
document.addEventListener('DOMContentLoaded', function() {
    // Remove any cookie consent elements
    const possibleConsentElements = [
        '.cookie-notice',
        '.cookie-banner',
        '.cookie-consent',
        '.gdpr-banner',
        '.gdpr-notice',
        '#cookie-law-info-bar',
        '#cookie-notice',
        '#gdpr-cookie-notice',
        '#mfn-gdpr',
        '.mfn-gdpr-button',
        '.cc-window',
        '.cc-banner',
        '.cc-popup',
        '.cc-floating',
        '.wt-cli-cookie-bar',
        '#cookie-law-info-again',
        '#CONSTANT_OPEN_URL',
        '#wp-consent-tools-gdpr-banner',
        '.cli-modal',
        '.cli-plugin-main-link',
        '.cli-tab-footer',
        '.cli-tab-header',
        '.cli-user-preference-checkbox',
        '.cli-tab-wrapper',
        '.cli-style-v2',
        '.cli-bar-container',
        '.cli-modal-backdrop',
        '.cli-modal-content',
        '.cli-modal-close',
        '.cli-tab-container',
        '.cli-privacy-overview',
        '.cli-privacy-content-text',
        '.cli-tab-content',
        '.cli-tab-pane',
        '.cli-tab-section',
        '.cli-tab-section-title',
        '.cli-tab-section-container',
        '.cli-tab-section-header',
        '.cli-tab-section-body',
        '.cli-tab-section-footer',
        '.cli-tab-section-title-text',
        '.cli-tab-section-title-status',
        '.cli-tab-section-title-status-icon',
        '.cli-tab-section-title-status-text',
        '.cli-tab-section-title-status-active',
        '.cli-tab-section-title-status-inactive',
        '.cli-tab-section-title-status-active-icon',
        '.cli-tab-section-title-status-inactive-icon',
        '.cli-tab-section-title-status-active-text',
        '.cli-tab-section-title-status-inactive-text',
        '.cli-tab-section-title-status-active-text-active',
        '.cli-tab-section-title-status-inactive-text-active',
        '.cli-tab-section-title-status-active-text-inactive',
        '.cli-tab-section-title-status-inactive-text-inactive'
    ];

    possibleConsentElements.forEach(function(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(function(element) {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });
    });

    // Disable WordPress plugin-related cookies
    if (typeof wpml_cookies !== 'undefined') {
        wpml_cookies = {};
    }

    // Disable WooCommerce cookies
    if (typeof woocommerce_params !== 'undefined') {
        console.log('WooCommerce parameters found - disabling cookie functionality');
    }

    // Override jQuery cookie functions if jQuery is available
    if (typeof jQuery !== 'undefined') {
        if (jQuery.cookie) {
            jQuery.cookie = function() { return null; };
        }
        if (jQuery.removeCookie) {
            jQuery.removeCookie = function() { return true; };
        }
    }

    // Disable WordPress specific cookies
    window.wpCookies = {
        set: function() { return null; },
        get: function() { return null; },
        remove: function() { return true; }
    };

    // Disable Google Tag Manager
    window.dataLayer = [];
    window.gtag = function() {
        console.log('Google Tag Manager disabled');
    };

    // Remove WordPress plugin scripts that might be dynamically added
    const removePluginScripts = function() {
        const scripts = document.querySelectorAll('script[src*="wp-content/plugins"]');
        scripts.forEach(function(script) {
            if (script && script.parentNode) {
                script.parentNode.removeChild(script);
            }
        });
    };

    // Run once and then periodically to catch dynamically added scripts
    removePluginScripts();
    setInterval(removePluginScripts, 2000);
});

console.log('Cookie and WordPress plugin functionality disabled');

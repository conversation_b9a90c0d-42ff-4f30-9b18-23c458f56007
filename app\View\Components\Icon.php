<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Icon extends Component
{
    /**
     * The icon instance or identifier.
     *
     * @var mixed
     */
    public $icon;

    /**
     * Additional CSS classes.
     *
     * @var string
     */
    public $class;

    /**
     * Icon color.
     *
     * @var string
     */
    public $color;

    /**
     * Icon size.
     *
     * @var string
     */
    public $size;

    /**
     * Create a new component instance.
     *
     * @param mixed $icon Icon instance or identifier
     * @param string $class Additional CSS classes
     * @param string $color Icon color
     * @param string $size Icon size
     */
    public function __construct($icon, $class = '', $color = null, $size = null)
    {
        $this->icon = $icon;
        $this->class = $class;
        $this->color = $color;
        $this->size = $size;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        // Si l'icône est une instance du modèle Icon, utiliser ses propriétés
        if ($this->icon instanceof \App\Models\Icon) {
            return view('components.icon', [
                'iconHtml' => $this->renderIconFromModel(),
            ]);
        }

        // Sinon, traiter comme un identifiant ou une classe d'icône
        return view('components.icon', [
            'iconHtml' => $this->renderIconFromIdentifier(),
        ]);
    }

    /**
     * Render icon HTML from an Icon model instance.
     *
     * @return string
     */
    protected function renderIconFromModel(): string
    {
        $icon = $this->icon;
        $style = $this->buildStyleAttribute();

        // Utiliser la méthode renderHtml du modèle Icon
        return $icon->renderHtml();
    }

    /**
     * Render icon HTML from an identifier or class name.
     *
     * @return string
     */
    protected function renderIconFromIdentifier(): string
    {
        $style = $this->buildStyleAttribute();
        $class = trim($this->class . ' ' . $this->icon);

        // Détecter si c'est une classe FontAwesome
        if (strpos($this->icon, 'fa-') !== false || strpos($this->icon, 'fas ') !== false || strpos($this->icon, 'far ') !== false || strpos($this->icon, 'fab ') !== false) {
            return '<i class="' . $class . '"' . $style . '></i>';
        }

        // Détecter si c'est une URL d'image
        if (strpos($this->icon, '/') !== false || strpos($this->icon, '.') !== false) {
            return '<img src="' . $this->icon . '" class="' . $this->class . '" alt="Icon">';
        }

        // Par défaut, considérer comme une classe d'icône générique
        return '<i class="' . $class . '"' . $style . '></i>';
    }

    /**
     * Build the style attribute based on color and size.
     *
     * @return string
     */
    protected function buildStyleAttribute(): string
    {
        $styles = [];

        if ($this->color) {
            $styles[] = 'color: ' . $this->color;
        }

        if ($this->size) {
            $styles[] = 'font-size: ' . $this->size;
        }

        return !empty($styles) ? ' style="' . implode('; ', $styles) . '"' : '';
    }
}

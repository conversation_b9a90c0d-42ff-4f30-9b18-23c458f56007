<?php

namespace App\Filament\Resources;

use App\Filament\Forms\Components\IconSelect;
use App\Filament\Resources\PageResource\Pages;
use App\Filament\Resources\PageResource\RelationManagers;
use App\Models\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PageResource extends Resource
{
    protected static ?string $model = Page::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    
    protected static ?string $navigationLabel = 'Pages';
    
    protected static ?string $modelLabel = 'Page';
    
    protected static ?string $pluralModelLabel = 'Pages';
    
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Tabs')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Informations générales')
                            ->schema([
                                Forms\Components\Section::make('Détails de la page')
                                    ->schema([
                                        Forms\Components\TextInput::make('titre')
                                            ->label('Titre')
                                            ->placeholder('Entrez le titre de la page')
                                            ->required()
                                            ->maxLength(255),
                                        IconSelect::make('icon_id')
                                            ->label('Icône')
                                            ->helperText('Sélectionnez une icône pour cette page')
                                            ->required(),
                                    ])->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('Sections')
                            ->schema([
                                Forms\Components\Repeater::make('sections')
                                    ->relationship()
                                    ->label('Sections de la page')
                                    ->schema([
                                        Forms\Components\Section::make('Informations de base')
                                            ->schema([
                                                Forms\Components\TextInput::make('titre')
                                                    ->label('Titre')
                                                    ->placeholder('Titre de la section')
                                                    ->required()
                                                    ->maxLength(255),
                                                IconSelect::make('icon_id')
                                                    ->label('Icône')
                                                    ->helperText('Sélectionnez une icône pour cette section'),
                                                Forms\Components\TextInput::make('sous_titre')
                                                    ->label('Sous-titre')
                                                    ->placeholder('Sous-titre de la section')
                                                    ->maxLength(255),
                                            ])->columns(3),
                                        
                                        Forms\Components\Section::make('Contenu')
                                            ->schema([
                                                Forms\Components\RichEditor::make('description')
                                                    ->label('Description')
                                                    ->placeholder('Description de la section')
                                                    ->toolbarButtons([
                                                        'blockquote',
                                                        'bold',
                                                        'bulletList',
                                                        'heading',
                                                        'italic',
                                                        'link',
                                                        'orderedList',
                                                        'redo',
                                                        'strike',
                                                        'underline',
                                                        'undo',
                                                    ])
                                                    ->columnSpanFull(),
                                            ]),
                                        
                                        Forms\Components\Section::make('Image et bouton')
                                            ->schema([
                                                Forms\Components\FileUpload::make('image_fond')
                                                    ->label('Image de fond')
                                                    ->image()
                                                    ->imageResizeMode('cover')
                                                    ->imageCropAspectRatio('16:9')
                                                    ->imageResizeTargetWidth('1920')
                                                    ->imageResizeTargetHeight('1080')
                                                    ->directory('sections')
                                                    ->helperText('Format recommandé: 1920x1080px'),
                                                Forms\Components\Grid::make()
                                                    ->schema([
                                                        Forms\Components\TextInput::make('etiquette_bouton')
                                                            ->label('Texte du bouton')
                                                            ->placeholder('Ex: En savoir plus')
                                                            ->maxLength(255),
                                                        Forms\Components\TextInput::make('lien_bouton')
                                                            ->label('Lien du bouton')
                                                            ->placeholder('Ex: /contact')
                                                            ->maxLength(255),
                                                    ])->columns(2),
                                            ])->columns(1),
                                        
                                        Forms\Components\Section::make('Contenus de la section')
                                            ->schema([
                                                Forms\Components\Repeater::make('contenuSections')
                                                    ->relationship()
                                                    ->label('Contenus')
                                                    ->schema([
                                                        Forms\Components\TextInput::make('titre')
                                                            ->label('Titre')
                                                            ->placeholder('Titre du contenu')
                                                            ->required()
                                                            ->maxLength(255),
                                                        IconSelect::make('icon_id')
                                                            ->label('Icône')
                                                            ->helperText('Icône pour ce contenu'),
                                                        Forms\Components\FileUpload::make('image')
                                                            ->label('Image')
                                                            ->image()
                                                            ->imageResizeMode('cover')
                                                            ->imageCropAspectRatio('1:1')
                                                            ->directory('contenu-sections')
                                                            ->helperText('Image carrée recommandée'),
                                                        Forms\Components\RichEditor::make('description')
                                                            ->label('Description')
                                                            ->placeholder('Description du contenu')
                                                            ->toolbarButtons([
                                                                'blockquote',
                                                                'bold',
                                                                'bulletList',
                                                                'heading',
                                                                'italic',
                                                                'link',
                                                                'orderedList',
                                                                'redo',
                                                                'strike',
                                                                'underline',
                                                                'undo',
                                                            ])
                                                            ->columnSpanFull(),
                                                    ])
                                                    ->itemLabel(fn (array $state): ?string => $state['titre'] ?? 'Nouveau contenu')
                                                    ->reorderableWithButtons()
                                                    ->collapsible()
                                                    ->addable(true)
                                                    ->deletable(true)
                                                    ->defaultItems(0)
                                                    ->columns(3)
                                                    ->columnSpan('full'),
                                            ]),
                                    ])
                                    ->itemLabel(fn (array $state): ?string => $state['titre'] ?? 'Nouvelle section')
                                    ->reorderableWithButtons()
                                    ->collapsible()
                                    ->addable(false)
                                    ->deletable(false)
                                    ->defaultItems(0)
                                    ->columnSpan('full'),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('titre')
                    ->label('Titre')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ViewColumn::make('icon_id')
                    ->label('Icône')
                    ->view('filament.tables.columns.icon-column'),
                Tables\Columns\TextColumn::make('sections_count')
                    ->label('Nombre de sections')
                    ->counts('sections')
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Dernière modification')
                    ->dateTime('d/m/Y à H:i')
                    ->sortable(),
            ])
            ->defaultSort('updated_at', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Modifier'),
                Tables\Actions\ViewAction::make()
                    ->label('Voir'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Supprimer la sélection'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPages::route('/'),
            'create' => Pages\CreatePage::route('/create'),
            'edit' => Pages\EditPage::route('/{record}/edit'),
        ];
    }
}

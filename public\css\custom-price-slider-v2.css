/* Styles pour le slider de prix personnalisé v2 */
#custom-price-slider-container {
    width: 100%;
    padding: 20px 10px;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

/* Texte d'information */
.price-range-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 15px;
    text-align: center;
}

/* Affichage des prix */
.price-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

/* Conteneur du slider */
.slider-container {
    position: relative;
    height: 40px;
    margin: 10px 0 30px;
}

/* Piste du slider */
.slider-track {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: 6px;
    background-color: #e1e4e9;
    border-radius: 3px;
    cursor: pointer;
}

/* Partie active de la piste */
.slider-track-active {
    position: absolute;
    top: 0;
    height: 100%;
    background-color: #8ac473;
    border-radius: 3px;
}

/* Conteneur des poignées */
.slider-handles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Poignées du slider */
.slider-handle {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background-color: white;
    border: 2px solid #8ac473;
    border-radius: 50%;
    cursor: grab;
    z-index: 1;
    transition: background-color 0.2s, border-color 0.2s, box-shadow 0.2s;
}

/* État actif des poignées */
.slider-handle.active {
    cursor: grabbing;
    background-color: #8ac473;
    border-color: #6ca353;
    box-shadow: 0 0 0 5px rgba(138, 196, 115, 0.2);
}

/* État hover des poignées */
.slider-handle:hover {
    background-color: #f5f5f5;
    box-shadow: 0 0 0 3px rgba(138, 196, 115, 0.1);
}

/* Focus pour l'accessibilité */
.slider-handle:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(138, 196, 115, 0.3);
}

/* Indicateur de chargement */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
    color: #666;
    font-size: 14px;
}

/* Spinner de chargement */
.loading-spinner {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid rgba(138, 196, 115, 0.3);
    border-radius: 50%;
    border-top-color: #8ac473;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Conteneur en cours de filtrage */
.filtering {
    opacity: 0.7;
    pointer-events: none;
}



/* Styles pour les appareils tactiles */
@media (pointer: coarse) {
    .slider-handle {
        width: 24px;
        height: 24px;
    }

    .slider-track {
        height: 8px;
    }

    .apply-filter-btn {
        padding: 10px 20px;
        font-size: 16px;
    }
}

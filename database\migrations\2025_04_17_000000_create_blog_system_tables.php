<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Get all foreign keys for a table
     */
    protected function getForeignKeys(string $tableName): array
    {
        $conn = Schema::getConnection()->getDoctrineSchemaManager();
        $foreignKeys = [];

        try {
            $foreignKeys = array_map(function($key) {
                return $key->getName();
            }, $conn->listTableForeignKeys($tableName));
        } catch (\Exception $e) {
            // Table might not exist
        }

        return $foreignKeys;
    }
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Créer la table des catégories si elle n'existe pas déjà
        if (!Schema::hasTable('blog_categories')) {
            Schema::create('blog_categories', function (Blueprint $table) {
                $table->id();
                $table->string('nom');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->timestamps();
            });
        }

        // Créer la table des tags si elle n'existe pas déjà
        if (!Schema::hasTable('blog_tags')) {
            Schema::create('blog_tags', function (Blueprint $table) {
                $table->id();
                $table->string('nom');
                $table->string('slug')->unique();
                $table->timestamps();
            });
        }

        // Modifier la table articles_blog existante - Étape 1: Ajouter les nouvelles colonnes
        Schema::table('articles_blog', function (Blueprint $table) {
            // Ajouter les nouvelles colonnes
            if (!Schema::hasColumn('articles_blog', 'slug')) {
                $table->string('slug')->nullable()->after('titre');
            }

            if (!Schema::hasColumn('articles_blog', 'category_id')) {
                $table->unsignedBigInteger('category_id')->nullable()->after('slug');
            }

            if (!Schema::hasColumn('articles_blog', 'statut')) {
                $table->enum('statut', ['brouillon', 'publie'])->default('brouillon')->after('url_image');
            }

            // Ajouter la clé étrangère si elle n'existe pas déjà
            $foreignKeys = $this->getForeignKeys('articles_blog');
            if (!in_array('articles_blog_category_id_foreign', $foreignKeys)) {
                $table->foreign('category_id')->references('id')->on('blog_categories')->onDelete('set null');
            }
        });

        // Étape 2: Générer des slugs pour les articles existants
        $hasData = DB::table('articles_blog')->count() > 0;
        if ($hasData) {
            $articles = DB::table('articles_blog')->get();
            foreach ($articles as $article) {
                $slug = Str::slug($article->titre);
                $originalSlug = $slug;
                $count = 1;

                // Vérifier si le slug existe déjà (maintenant que la colonne existe)
                while (DB::table('articles_blog')->where('slug', $slug)->where('id', '!=', $article->id)->exists()) {
                    $slug = $originalSlug . '-' . $count++;
                }

                // Mettre à jour l'article avec le slug généré
                DB::table('articles_blog')->where('id', $article->id)->update(['slug' => $slug]);
            }
        }

        // Créer la table pivot pour les tags si elle n'existe pas déjà
        if (!Schema::hasTable('article_tag')) {
            Schema::create('article_tag', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('article_id');
                $table->unsignedBigInteger('tag_id');
                $table->timestamps();

                $table->foreign('article_id')->references('id')->on('articles_blog')->onDelete('cascade');
                $table->foreign('tag_id')->references('id')->on('blog_tags')->onDelete('cascade');
            });
        }

        // Étape 3: Ajouter l'index unique et supprimer l'ancienne colonne
        Schema::table('articles_blog', function (Blueprint $table) {
            // Rendre le slug obligatoire maintenant qu'il est rempli
            if (Schema::hasColumn('articles_blog', 'slug')) {
                $table->string('slug')->nullable(false)->change();

                // Vérifier si l'index unique existe déjà
                $conn = Schema::getConnection()->getDoctrineSchemaManager();
                $indexes = [];
                try {
                    $indexes = array_map(function($idx) {
                        return $idx->getName();
                    }, $conn->listTableIndexes('articles_blog'));
                } catch (\Exception $e) {
                    // Table might not exist
                }

                // Ajouter l'index unique sur le slug après avoir généré les slugs
                if (!in_array('articles_blog_slug_unique', $indexes)) {
                    $table->unique('slug');
                }
            }

            // Supprimer la colonne categorie existante si elle existe
            if (Schema::hasColumn('articles_blog', 'categorie')) {
                $table->dropColumn('categorie');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les clés étrangères
        Schema::table('articles_blog', function (Blueprint $table) {
            if (Schema::hasColumn('articles_blog', 'category_id')) {
                $table->dropForeign(['category_id']);
            }

            if (Schema::hasColumn('articles_blog', 'slug')) {
                $table->dropUnique(['slug']);
                $table->dropColumn('slug');
            }

            if (Schema::hasColumn('articles_blog', 'category_id')) {
                $table->dropColumn('category_id');
            }

            if (Schema::hasColumn('articles_blog', 'statut')) {
                $table->dropColumn('statut');
            }

            if (!Schema::hasColumn('articles_blog', 'categorie')) {
                $table->string('categorie')->nullable();
            }
        });

        // Supprimer les tables
        Schema::dropIfExists('article_tag');
        Schema::dropIfExists('blog_tags');
        Schema::dropIfExists('blog_categories');
    }
};

/**
 * Module de localisation pour les propriétés RCI
 * Permet de rechercher et afficher des adresses sur une carte interactive
 */
document.addEventListener('DOMContentLoaded', function() {
    // Éléments DOM
    const mapContainer = document.getElementById('location-map');
    const addressInput = document.getElementById('property-address');
    const searchButton = document.getElementById('search-address-btn');
    const postalCodeInput = document.getElementById('postal-code');
    const cityInput = document.getElementById('city');
    const streetInput = document.getElementById('street');
    const streetNumberInput = document.getElementById('street-number');
    const componentSearchButton = document.getElementById('search-components-btn');
    const latitudeInput = document.getElementById('latitude');
    const longitudeInput = document.getElementById('longitude');
    
    // Vérifier si les éléments nécessaires existent
    if (!mapContainer) {
        console.error("Élément de carte manquant");
        return;
    }
    
    // Configuration de la carte
    let map = null;
    let marker = null;
    const defaultZoom = 15;
    let defaultCenter = [46.603354, 1.888334]; // Centre de la France par défaut
    
    // Initialiser la carte avec les coordonnées existantes si disponibles
    if (latitudeInput && longitudeInput && 
        latitudeInput.value && longitudeInput.value && 
        !isNaN(parseFloat(latitudeInput.value)) && !isNaN(parseFloat(longitudeInput.value))) {
        defaultCenter = [parseFloat(latitudeInput.value), parseFloat(longitudeInput.value)];
    }
    
    // Initialisation de la carte
    initMap();
    
    // Attacher les gestionnaires d'événements
    if (searchButton) {
        searchButton.addEventListener('click', searchAddress);
    }
    
    if (addressInput) {
        addressInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchAddress();
            }
        });
    }
    
    if (componentSearchButton) {
        componentSearchButton.addEventListener('click', searchByComponents);
    }
    
    if (latitudeInput && longitudeInput) {
        latitudeInput.addEventListener('change', updateMapFromCoordinates);
        longitudeInput.addEventListener('change', updateMapFromCoordinates);
    }
    
    /**
     * Initialise la carte Leaflet
     */
    function initMap() {
        // Créer la carte
        map = L.map(mapContainer, {
            center: defaultCenter,
            zoom: defaultZoom
        });
        
        // Ajouter la couche de tuiles OpenStreetMap
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Ajouter un marqueur si des coordonnées sont disponibles
        if (defaultCenter[0] !== 46.603354 || defaultCenter[1] !== 1.888334) {
            marker = L.marker(defaultCenter).addTo(map);
        }
        
        // Permettre de cliquer sur la carte pour placer un marqueur
        map.on('click', function(e) {
            setMarker(e.latlng.lat, e.latlng.lng);
        });
    }
    
    /**
     * Place un marqueur sur la carte et met à jour les champs de latitude/longitude
     */
    function setMarker(lat, lng) {
        // Arrondir à 6 décimales pour la précision
        lat = parseFloat(lat).toFixed(6);
        lng = parseFloat(lng).toFixed(6);
        
        // Mettre à jour les champs de coordonnées
        if (latitudeInput && longitudeInput) {
            latitudeInput.value = lat;
            longitudeInput.value = lng;
        }
        
        // Mettre à jour ou créer le marqueur
        if (marker) {
            marker.setLatLng([lat, lng]);
        } else {
            marker = L.marker([lat, lng]).addTo(map);
        }
        
        // Centrer la carte sur le marqueur
        map.setView([lat, lng], map.getZoom());
    }
    
    /**
     * Recherche une adresse via l'API
     */
    function searchAddress() {
        if (!addressInput || !addressInput.value.trim()) {
            alert('Veuillez entrer une adresse à rechercher');
            return;
        }
        
        const query = addressInput.value.trim();
        
        // Afficher un indicateur de chargement
        if (searchButton) {
            searchButton.disabled = true;
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Recherche...';
        }
        
        // Appel à l'API de recherche
        fetch('/location/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ query: query })
        })
        .then(response => response.json())
        .then(data => {
            // Réinitialiser le bouton
            if (searchButton) {
                searchButton.disabled = false;
                searchButton.innerHTML = 'Rechercher';
            }
            
            if (data && data.length > 0) {
                const result = data[0]; // Prendre le premier résultat
                setMarker(result.lat, result.lon);
                
                // Remplir les champs de composants si disponibles
                if (result.address) {
                    if (postalCodeInput && result.address.postcode) {
                        postalCodeInput.value = result.address.postcode;
                    }
                    if (cityInput && result.address.city) {
                        cityInput.value = result.address.city || result.address.town || result.address.village || '';
                    }
                    if (streetInput) {
                        streetInput.value = result.address.road || '';
                    }
                    if (streetNumberInput) {
                        streetNumberInput.value = result.address.house_number || '';
                    }
                }
            } else {
                alert('Aucun résultat trouvé pour cette adresse');
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche:', error);
            alert('Erreur lors de la recherche. Veuillez réessayer.');
            
            // Réinitialiser le bouton
            if (searchButton) {
                searchButton.disabled = false;
                searchButton.innerHTML = 'Rechercher';
            }
        });
    }
    
    /**
     * Recherche par composants d'adresse (code postal, ville, rue, numéro)
     */
    function searchByComponents() {
        // Vérifier qu'au moins un champ est rempli
        if ((!postalCodeInput || !postalCodeInput.value.trim()) && 
            (!cityInput || !cityInput.value.trim()) && 
            (!streetInput || !streetInput.value.trim()) && 
            (!streetNumberInput || !streetNumberInput.value.trim())) {
            alert('Veuillez remplir au moins un champ de recherche');
            return;
        }
        
        // Afficher un indicateur de chargement
        if (componentSearchButton) {
            componentSearchButton.disabled = true;
            componentSearchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Recherche...';
        }
        
        // Préparer les données
        const data = {
            postal_code: postalCodeInput ? postalCodeInput.value.trim() : '',
            city: cityInput ? cityInput.value.trim() : '',
            street: streetInput ? streetInput.value.trim() : '',
            street_number: streetNumberInput ? streetNumberInput.value.trim() : ''
        };
        
        // Appel à l'API de recherche par composants
        fetch('/location/search-components', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            // Réinitialiser le bouton
            if (componentSearchButton) {
                componentSearchButton.disabled = false;
                componentSearchButton.innerHTML = 'Rechercher';
            }
            
            if (data && data.length > 0) {
                const result = data[0]; // Prendre le premier résultat
                setMarker(result.lat, result.lon);
                
                // Mettre à jour le champ d'adresse complète
                if (addressInput && result.display_name) {
                    addressInput.value = result.display_name;
                }
            } else {
                alert('Aucun résultat trouvé pour ces critères');
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche:', error);
            alert('Erreur lors de la recherche. Veuillez réessayer.');
            
            // Réinitialiser le bouton
            if (componentSearchButton) {
                componentSearchButton.disabled = false;
                componentSearchButton.innerHTML = 'Rechercher';
            }
        });
    }
    
    /**
     * Met à jour la carte à partir des valeurs de latitude/longitude saisies manuellement
     */
    function updateMapFromCoordinates() {
        if (!latitudeInput || !longitudeInput) return;
        
        const lat = parseFloat(latitudeInput.value);
        const lng = parseFloat(longitudeInput.value);
        
        if (!isNaN(lat) && !isNaN(lng)) {
            setMarker(lat, lng);
        }
    }
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projet_en_cours', function (Blueprint $table) {
            $table->string('image_fond_prestation')->nullable();
            $table->string('image_fond_contact')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projet_en_cours', function (Blueprint $table) {
            $table->dropColumn(['image_fond_prestation', 'image_fond_contact']);
        });
    }
};

Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFA8F0, 0007FFFF97F0) msys-2.0.dll+0x1FEBA
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (000210285FF9, 0007FFFFA7A8, 0007FFFFA8F0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA8F0  0002100690B4 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFABD0  00021006A49D (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD20F50000 ntdll.dll
7FFD1FE50000 KERNEL32.DLL
7FFD1E1E0000 KERNELBASE.dll
7FFD1EC50000 USER32.dll
7FFD1E7D0000 win32u.dll
7FFD1F720000 GDI32.dll
7FFD1E800000 gdi32full.dll
7FFD1E730000 msvcp_win.dll
7FFD1E0C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD1FF20000 advapi32.dll
7FFD203E0000 msvcrt.dll
7FFD1F750000 sechost.dll
7FFD1E930000 bcrypt.dll
7FFD1EEA0000 RPCRT4.dll
7FFD1D8C0000 CRYPTBASE.DLL
7FFD1E960000 bcryptPrimitives.dll
7FFD1F6E0000 IMM32.DLL

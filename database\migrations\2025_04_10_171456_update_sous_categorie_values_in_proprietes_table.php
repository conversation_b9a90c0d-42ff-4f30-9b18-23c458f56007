<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour les valeurs de sous_categorie_json à partir de sous_categorie
        $proprietes = DB::table('proprietes')->get();

        foreach ($proprietes as $propriete) {
            if (!empty($propriete->sous_categorie)) {
                // Si sous_categorie est une chaîne, essayer de la convertir en tableau
                $sousCategorie = $propriete->sous_categorie;

                // Si c'est déjà un format JSON valide, l'utiliser directement
                if (is_array($sousCategorie) || is_object($sousCategorie)) {
                    $sousCategorie = json_encode($sousCategorie);
                } else {
                    // <PERSON>n, essayer de convertir la chaîne en tableau
                    // Supprimer les crochets s'ils existent
                    $sousCategorie = trim($sousCategorie, '[]');
                    // Diviser par virgule et nettoyer
                    $items = array_map('trim', explode(',', $sousCategorie));
                    // Supprimer les guillemets
                    $items = array_map(function($item) {
                        return trim($item, '"\' ');
                    }, $items);
                    // Filtrer les valeurs vides
                    $items = array_filter($items);
                    // Encoder en JSON
                    $sousCategorie = json_encode($items);
                }

                // Mettre à jour la colonne sous_categorie_json
                DB::table('proprietes')
                    ->where('id', $propriete->id)
                    ->update(['sous_categorie_json' => $sousCategorie]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rien à faire ici, car nous ne voulons pas annuler les mises à jour de données
    }
};

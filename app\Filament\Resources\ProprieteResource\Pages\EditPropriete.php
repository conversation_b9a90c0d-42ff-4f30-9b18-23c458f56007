<?php

namespace App\Filament\Resources\ProprieteResource\Pages;

use App\Filament\Resources\ProprieteResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPropriete extends EditRecord
{
    protected static string $resource = ProprieteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Le bouton Supprimer a été retiré selon les spécifications
        ];
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    protected function getFooterActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
        ];
    }
}

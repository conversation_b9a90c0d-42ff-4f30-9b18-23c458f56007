<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modifier l'application pour utiliser sous_categorie_json au lieu de sous_categorie
        // Nous allons mettre à jour le modèle pour qu'il utilise la colonne sous_categorie_json
        // mais nous gardons la colonne sous_categorie pour la compatibilité ascendante

        // Mettre à jour le modèle Propriete pour qu'il utilise sous_categorie_json
        $modelPath = app_path('Models/Propriete.php');
        if (file_exists($modelPath)) {
            $content = file_get_contents($modelPath);

            // Mettre à jour les accesseurs/mutateurs pour sous_categorie
            if (strpos($content, 'getSousCategorieAttribute') === false) {
                // Ajouter les méthodes accesseur/mutateur après la dernière accolade fermante de la classe
                $newMethods = <<<'EOD'

    // Accesseur pour sous_categorie qui utilise sous_categorie_json
    public function getSousCategorieAttribute($value)
    {
        return $this->sous_categorie_json;
    }

    // Mutateur pour sous_categorie qui met à jour sous_categorie_json
    public function setSousCategorieAttribute($value)
    {
        $this->attributes['sous_categorie_json'] = $value;
    }
EOD;

                // Insérer les nouvelles méthodes avant la dernière accolade
                $content = preg_replace('/}\s*$/', $newMethods . '\n}', $content);

                // Sauvegarder le fichier modifié
                file_put_contents($modelPath, $content);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restaurer le modèle Propriete
        $modelPath = app_path('Models/Propriete.php');
        if (file_exists($modelPath)) {
            $content = file_get_contents($modelPath);

            // Supprimer les méthodes accesseur/mutateur pour sous_categorie
            $content = preg_replace('/\n\s*\/\/\s*Accesseur pour sous_categorie.*?\n\s*public function getSousCategorieAttribute.*?\}\n/s', '', $content);
            $content = preg_replace('/\n\s*\/\/\s*Mutateur pour sous_categorie.*?\n\s*public function setSousCategorieAttribute.*?\}\n/s', '', $content);

            // Sauvegarder le fichier modifié
            file_put_contents($modelPath, $content);
        }
    }
};

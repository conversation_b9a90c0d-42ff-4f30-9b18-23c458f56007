<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Developpement extends Model
{
    use HasFactory;

    protected $fillable = [
        'titre',
        'description',
        'date_livraison',
        'lien_bouton',
        'libelle_bouton',
    ];

    public function imagesDeveloppements()
    {
        return $this->hasMany(ImagesDeveloppement::class);
    }
}

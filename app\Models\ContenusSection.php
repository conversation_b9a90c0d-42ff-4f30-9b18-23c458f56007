<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContenusSection extends Model
{
    protected $fillable = [
        'titre',
        'description',
        'image',
        'section_id',
        'icon_id',
    ];

    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Relation avec l'icône associée à ce contenu de section.
     */
    public function icon()
    {
        return $this->belongsTo(Icon::class);
    }
}

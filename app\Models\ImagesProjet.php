<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ImagesProjet extends Model
{
    // Explicitly define the table name
    protected $table = 'images_projets';

    // List the attributes that are mass assignable (adjust as needed)
    protected $fillable = [
        'projet_en_cour_id',
        'url_image',
        'statut',
        'type',
        'order_column'
    ];

    // Define the relationship: one ImagesProjet belongs to one ProjetEnCours
    public function projetEnCours()
    {
        return $this->belongsTo(ProjetEnCours::class, 'projet_en_cour_id');
    }
}

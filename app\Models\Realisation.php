<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Realisation extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'titre',
        'description',
        'adresse',
        'date_realisation',
    ];

    public function imagesRealisation()
    {
        return $this->hasMany(ImagesRealisation::class);
    }
}

